<style>
	.screen .layui-colla-content .goods-category-container .layui-input{width: 240px !important;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加活动</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">活动名称</label>
					<div class="layui-input-inline">
						<input type="text" name="name" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">活动时间</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab"  lay-filter="activity_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">全部</li>
		<li lay-id="0">未开始</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="activity_list" lay-filter="activity_list"></table>
	</div>
</div>

<!--时间-->
<script type="text/html" id="time">
	<div class="layui-elip">开始时间：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束时间：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 批量操作 -->
<script type="text/html" id="toolbarAction">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
	<button class="layui-btn layui-btn-primary" lay-event="close">批量关闭</button>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		{{# if(d.status == 1){ }}
		<a class="layui-btn text-color" lay-event="select">推广</a>
		{{# } }}
		<a class="layui-btn" lay-event="detail">详情</a>
		<a class="layui-btn" lay-event="edit">编辑</a>
		{{# if(d.status == 1){ }}
		<a class="layui-btn" lay-event="cole">关闭</a>
		{{# } }}
		{{# if(d.status == 2){ }}
		<a class="layui-btn" lay-event="del">删除</a>
		{{# } }}
	</div>
</script>

<!-- 推广 -->
{include file="app/shop/view/component/promote_show.html"}

<script>
	layui.use(['form','laytpl','element','laydate'], function() {
		var table,
			laytpl = layui.laytpl,
			form = layui.form,
			laydate = layui.laydate,
			element = layui.element,
			repeat_flag = false; //防重复标识

		form.render();

		element.on('tab(activity_tab)', function () {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('lay-id')
				}
			});
		});

		table = new Table({
			elem: '#activity_list',
			url: ns.url("bale://shop/bale/lists"),
			cols: [
				[{
					type: 'checkbox',
					width: '3%',
				},{
					field: 'name',
					title: '名称',
					unresize: 'false',
					width: '22%'
				}, {
					title: '活动规则',
					unresize: 'false',
					width: '12%',
					templet: function (data) {
						return data.price + '元' + data.num + '件';
					}
				}, {
					title: '活动时间',
					unresize: 'false',
					width: '27%',
					templet: '#time'
				}, {
					title: '状态',
					unresize: 'false',
					width: '10%',
					templet: function (data) {
						if (data.status == 0) {
							return '未开始';
						} else if (data.status == 1) {
							return '进行中';
						} else {
							return '已结束';
						}
					}
				}, {
					field: 'update_time',
					title: '创建时间',
					unresize: 'false',
					width: '17%',
					templet: function (data) {
						return ns.time_to_date(data.create_time);
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align: 'right'
				}]
			],
			toolbar: '#toolbarAction'
		});

		// 监听工具栏操作
		table.toolbar(function (obj) {
			var data = obj.data;
			if(data.length <= 0) return;
			var baleIdAll = [];
			for (var i in data){
				baleIdAll.push(data[i].bale_id);
			}

			switch (obj.event) {
				case 'delete':
					deleteBaleAll(baleIdAll)
					break;
				case 'close':
					closeBaleAll(baleIdAll)
					break;
			}
		})

		function deleteBaleAll(data){
			layer.confirm('确定要删除活动吗?', function (index) {
				if (repeat_flag) return false;
				repeat_flag = true;
				layer.close(index);
				$.ajax({
					url: ns.url("bale://shop/bale/deleteAll"),
					data: {
						"bale_id": data
					},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						repeat_flag = false;
						table.reload({
							page: {
								curr: 1
							},
						});
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		function closeBaleAll(data){
			layer.confirm('确定要关闭活动吗?', function (index) {
				if (repeat_flag) return false;
				repeat_flag = true;
				layer.close(index);
				$.ajax({
					url: ns.url("bale://shop/bale/closeBaleAll"),
					data: {
						"bale_id": data
					},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 监听工具栏操作
		 */
		table.tool(function (obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'select': //推广
					baleUrl(data);
					break;
				case 'detail': //详情
					location.hash = ns.hash("bale://shop/bale/detail", {"bale_id": data.bale_id});
					break;
				case 'edit': //编辑
					location.hash = ns.hash("bale://shop/bale/edit", {"bale_id": data.bale_id});
					break;
				case 'cole':
					coleBale(data.bale_id);
					break;
				case 'del': //删除
					deleteBale(data.bale_id);
					break;
			}
		});

		//开始时间
		laydate.render({
			elem: '#start_time',//指定元素
			type: 'datetime'
		});
		//结束时间
		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime'
		});

		/**
		 * 删除
		 */
		function deleteBale(id) {
			layer.confirm('确定要删除该活动吗?', function (index) {
				if (repeat_flag) return false;
				repeat_flag = true;
				layer.close(index);
				$.ajax({
					url: ns.url("bale://shop/bale/delete"),
					data: {
						"id": id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload({
								page: {
									curr: 1
								},
							});
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function (data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		function baleUrl(data){
			new PromoteShow({
				url:ns.url("bale://shop/bale/baleUrl"),
				param:{bale_id:data.bale_id},
			})
		}

		function coleBale(id) {
			layer.confirm('确定要关闭该活动吗?', function (index) {
				if (repeat_flag) return false;
				repeat_flag = true;
				layer.close(index);
				$.ajax({
					url: ns.url("bale://shop/bale/closeBale"),
					data: {
						"id": id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
	});
	
	function add() {
		location.hash = ns.hash("bale://shop/bale/add");
	}
</script>
