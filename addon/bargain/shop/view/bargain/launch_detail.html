<style>
	.layui-table[lay-skin=line] {margin-top: 15px; border-width: 0;}
	.title-pic{flex-shrink: 0;display: inline-block;width: 50px;height: 50px;text-align: center;line-height: 50px;margin-left: 5px;}
	.layui-layout-admin .layui-table-header{background-color: transparent;}
	.layui-layout-admin .layui-table-view .layui-table-body .layui-table[lay-skin=line]{margin-top: 0;}
</style>

<div class="layui-form tips-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label">发起人信息：</label>
		<div class="table-title layui-input-inline">
			<div class="title-pic">
				<img layer-src src="{:img($info['headimg'])}" onerror="this.src = '{:img('public/static/img/default_img/head.png')}' "/>
			</div>
			<div class="title-content">
				<a href="javascript:;" class="multi-line-hiding text-color" title="{$info['nickname']}">{$info['nickname']}</a>
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">商品信息：</label>
		<div class="table-title layui-input-inline">
			<div class="title-pic">
				<img layer-src src="{:img($info['sku_image'])}"/>
			</div>
			<div class="title-content">
				<a href="javascript:;" class="multi-line-hiding text-color" title="{$info['sku_name']}">{$info['sku_name']}</a>
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">发起砍价时间：</label>
		<div class="layui-input-inline">{:time_to_date($info.start_time)}</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">最少砍价数/已砍人数：</label>
		<div class="layui-input-inline">{$info.need_num} / {$info.curr_num}</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">底价：</label>
		<div class="layui-input-inline">￥{$info.floor_price}</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">结束时间：</label>
		<div class="layui-input-inline">{:time_to_date($info.end_time)}</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">砍价状态：</label>
		<div class="layui-input-inline">
			{if $info.status == 2}
			砍价失败
			{elseif $info.status == 0}
			砍价中
			{else/}
			砍价成功
			{/if}
		</div>
	</div>

</div>

<!-- 列表 -->
<table id="order_list" lay-filter="order_list"></table>

<!-- 参与人 -->
<script type="text/html" id="member_info">
	<div class="table-title">
		<div class="title-pic">
			{{#  if(d.headimg){  }}
			<img layer-src src="{{ns.img(d.headimg)}}" onerror="this.src = '{:img('public/static/img/default_img/head.png')}' "/>
			{{# } else{ }}
			<img layer-src src="{:img('public/static/img/default_img/head.png')}"/>
			{{#  }  }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color" title="{{d.nickname}}">{{d.nickname}}</a>
		</div>
	</div>
</script>

<script>
	var launch_id = "{$launch_id}";

	layui.use(['form'], function() {
		var table,form = layui.form;
		form.render();

		table = new Table({
			elem: '#order_list',
			url: ns.url("bargain://shop/bargain/launchdetail"),
			where: {
				"launch_id": launch_id
			},
			cols: [
				[{
					title: '帮砍人信息',
					unresize: 'false',
					templet:'#member_info'
				}, {
					title: '帮砍金额',
					unresize: 'false',
					templet:function(data){
						return '￥' + data.money;
					}
				}, {
					title: '帮砍时间',
					unresize: 'false',
					templet: function(data) {
						return ns.time_to_date(data.bargain_time);
					}
				}]
			],

		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'check': //查看
					window.open(ns.href("shop/order/detail", {"order_id": data.order_id}));
					break;
			}
		});
	});
</script>