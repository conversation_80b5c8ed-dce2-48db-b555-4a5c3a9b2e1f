<style>
	.layui-layout-admin .screen{margin-bottom: 15px;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
</style>

{if condition="$bargain_id"}
<input type="hidden" class="bargain-id" value="{$bargain_id}" />
{else/}
<input type="hidden" class="bargain-id" value="" />
{/if}

<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-form layui-colla-content layui-form layui-show"  lay-filter="order_list">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="goods_name">
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">会员昵称</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="nickname">
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">砍价状态</label>
					<div class="layui-input-inline">
						<select name="status" lay-filter="status">
							<option value="">全部</option>
							<option value="1">砍价成功</option>
							<option value="0">砍价中</option>
							<option value="2">砍价失败</option>
						</select>
					</div>
				</div>
				
			</div>
			
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">发起时间</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(7, this);return false;">近7天</button>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(30, this);return false;">近30天</button>
				</div>
			</div>
			
			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<table id="team_list" lay-filter="team_list"></table>

<!-- 商品 -->
<script type="text/html" id="goods">
	<div class="table-title">
		<div class="title-pic">
			{{#  if(d.sku_image){  }}
			<img layer-src="{{ns.img(d.sku_image.split(',')[0],'big')}}" src="{{ns.img(d.sku_image.split(',')[0],'small')}}"/>
			{{#  }  }}
		</div>

		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.sku_name}}">{{d.sku_name}}</a>
		</div>
	</div>
</script>

<!-- 会员 -->
<script type="text/html" id="member_info">
	<div class="table-title">
		<div class="title-pic">
			{{#  if(d.headimg){  }}
			<img layer-src src="{{ns.img(d.headimg)}}"/>
			{{#  }else if(d.headimg == ''){  }}
			<img layer-src src="{:img('public/static/img/default_img/head.png')}">
			{{#  }  }}
		</div>
		<!-- <div class='title-pic'>
			<img layer-src src="{{ns.img(d.sku_image.split(',')[0],'small')}}" onerror="this.src = '{:img('public/static/img/default_img/head.png')}' ">
		</div> -->
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.nickname}}">{{d.nickname}}</a>
		</div>
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{#  if(d.status == 0){  }}
	砍价中
	{{#  }else if(d.status == 1){  }}
	砍价成功
	{{#  }else if(d.status == 2){  }}
	砍价失败
	{{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="record">帮砍记录</a>
		{{# if(d.status == 1 && d.order_id > 0){ }}
		<a class="layui-btn" lay-event="order">查看订单</a>
		{{# } }}
	</div>
</script>

<script>
	layui.use(['form', 'element', 'laydate'], function() {
		var table,
			form = layui.form,
			laydate = layui.laydate;

		form.render();

		//渲染时间
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

		table = new Table({
			elem: '#team_list',
			url: ns.url("bargain://shop/bargain/launchlist"),
			where: {
				"bargain_id": $(".bargain-id").val()
			},
			cols: [
				[ {
					title: '会员信息',
					unresize: 'false',
					width: '12%',
					templet: '#member_info'
				},{
					title: '砍价商品',
					unresize: 'false',
					width: '15%',
					templet: '#goods'
				}, {
					title: '发起砍价时间',
					unresize: 'false',
					width: '10%',
					templet:function(data){
						return ns.time_to_date(data.start_time);
					}
				}, {
					title: '最少砍价数/已砍人数',
					unresize: 'false',
					align:'center',
					width: '12%',
					templet: function(data) {
						return data.need_num + ' / ' + data.curr_num;
					}
				}, {
					title: '底价',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return '￥' + data.floor_price;
					}
				}, {
					title: '结束时间',
					unresize: 'false',
					width: '12%',
					templet: function(data) {
						return ns.time_to_date(data.end_time);
					}
				}, {
					title: '砍价状态',
					unresize: 'false',
					width: '8%',
					templet: '#status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align : 'right'
				}]
			],

		});

		//监听筛选事件
       	form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'record': //帮砍记录
					location.hash = ns.hash("bargain://shop/bargain/launchdetail?launch_id="+ data.launch_id);
					break;
				case 'order': //订单
					window.open(ns.href("shop/order/detail", {"order_id": data.order_id}));
					break;
			}
		});
	});

	function datePick(date_num,event_obj){
        $(".date-picker-btn").removeClass("selected");
        $(event_obj).addClass('selected');

        Date.prototype.Format = function (fmt,date_num) { //author: meizz
            this.setDate(this.getDate()-date_num);
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };
        var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
        var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
        $("input[name=start_time]").val(before_time,0);
        $("input[name=end_time]").val(now_time,date_num-1);
    }
</script>
