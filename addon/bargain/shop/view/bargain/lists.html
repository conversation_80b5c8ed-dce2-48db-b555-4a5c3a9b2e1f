<link rel="stylesheet" type="text/css" href="SHOP_ADDON_CSS/list.css" />

<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加砍价</button>
</div>
<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称</label>
					<div class="layui-input-inline">
						<input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">活动时间</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab" lay-filter="bargain_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		{foreach $bargain_status as $k=>$v}
		<li data-status="{$k}">{$v}</li>
		{/foreach}
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="bargain_list" lay-filter="bargain_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods_info">
	<div class="table-title">

		<div class="contraction" data-id="{{d.bargain_id}}" data-open="0">
			<span>+</span>
		</div>

		<div class="title-pic">
			{{#  if(d.goods_image){  }}
			<img layer-src="{{ns.img(d.goods_image.split(',')[0],'big')}}" src="{{ns.img(d.goods_image.split(',')[0],'small')}}"/>
			{{#  }  }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 推广 -->
{include file="app/shop/view/component/promote_show.html"}

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-bargain-id="{{d.bargain_id}}">
		<div class="popup-qrcode-wrap" style="display: none">
			<img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif"/>
		</div>
		<div class="table-btn">
			{{# if(d.status == 1){ }}
			<a class="layui-btn text-color" lay-event="select">推广</a>
			{{# } }}
			<a class="layui-btn" lay-event="detail">详情</a>
			<a class="layui-btn" lay-event="edit">编辑</a>
			{{# if(d.status == 0){ }}
			<a class="layui-btn" lay-event="del">删除</a>
			{{# }else if(d.status == 1){ }}
			<a class="layui-btn" lay-event="launch">砍价列表</a>
			<a class="layui-btn" lay-event="close">结束</a>
			{{# }else{ }}
			<a class="layui-btn" lay-event="launch">砍价列表</a>
			<a class="layui-btn" lay-event="del">删除</a>
			{{# } }}
		</div>
	</div>
</script>

<!-- 批量操作 -->
<script type="text/html" id="toolbarAction">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
	<button class="layui-btn layui-btn-primary" lay-event="invalid">批量关闭</button>
</script>

<!-- 商品sku -->
<script type="text/html" id="skuList">
	<tr class="js-list-{{d.index}}" id="sku_img_{{d.index}}">
		<td colspan="10">
			<ul class="sku-list">
				{{# for(var i=0; i<d.list.length; i++){ }}
				<li>
					<div class="img-wrap">
						<img layer-src src="{{ns.img(d.list[i].sku_image)}}">
					</div>
					<div class="info-wrap">
						<span class="sku-name" title="{{d.list[i].sku_name}}">{{d.list[i].sku_name}}</span>
						<span class="price">商品价格：￥{{d.list[i].price}}</span>
						<span class="price">底价：￥{{d.list[i].floor_price}}</span>
						<span class="price">库存：￥{{d.list[i].bargain_stock}}</span>
						<span class="sale_num">销量：{{d.list[i].sale_num}}</span>
						<span class="sale_num">参与人数：{{d.list[i].join_num}}</span>
					</div>
				</li>
				{{# } }}
			</ul>
		</td>
	</tr>
</script>

<script type="text/javascript" src="SHOP_ADDON_JS/list.js?time=20240603" ></script>
