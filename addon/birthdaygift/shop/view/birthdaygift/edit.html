<link rel="stylesheet" href="SHOP_CSS/game.css">
<style>
    .layui-table-body{max-height: 480px !important;}
	.flex {
		flex: 1;
	}
	.birthdaygift-flex {
		display: flex;
		justify-content: space-between;
	}
	.birthdaygift-preview img {
		width: 300px;
		height: 617px;
		margin-left: 20px;
	}
	.birthdaygift-preview {
		margin: 50px 50px 0 0;
	}

	.coupon-box .layui-form{
		padding: 0!important;
	}

	.layui-layer-page .layui-layer-content{
		overflow: auto !important;
	}

	.del-btn {
		cursor: pointer;
	}
	.level-equity .layui-input {
		display: inline-block;
	}
	.gods-box table:first-of-type{
		margin-bottom: 0;
	}
	.gods-box table:last-of-type{
		margin-top: 0;
		display: block;
		max-height: 323px;
		overflow: auto;
	}
	.coupon-box .single-filter-box{
		padding-top: 0;
	}
	.coupon-box .select-coupon-btn{
		margin-top: 10px;
	}
	.layui-layer-page .layui-layer-content{
		overflow-y: scroll!important;
	}
</style>

<div class="layui-form birthdaygift-flex">
	<div class="flex">
		<div class="layui-card card-common card-brief">
		    <div class="layui-card-header">
		        <span class="card-title">活动设置</span>
		    </div>
		
		    <div class="layui-card-body">
		
		        <div class="layui-form-item">
		            <label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		            <div class="layui-input-block">
		                <input type="text" name="activity_name" lay-verify="required" maxlength="15" placeholder="最多可填写15个字" autocomplete="off" class="layui-input len-long" value="{$info.activity_name}">
		            </div>
		        </div>
				<div class="layui-form-item">
					<label class="layui-form-label"><span class="required">*</span>开始时间：</label>
					<div class="layui-inline">
						<div class="layui-input-inline len-mid">
							<input type="text" {if condition="$info.status == 1"}disabled {/if} value="{:date('Y-m-d H:i:s', $info.start_time)}" class="layui-input" name="start_time" lay-verify="required" id="start_time" autocomplete="off" readonly>
							<i class=" iconrili iconfont calendar"></i>
						</div>
						<span class="layui-form-mid">-</span>
						<div class="layui-input-inline len-mid end-time">
							<input type="text" {if condition="$info.status == 1"}disabled {/if} value="{:date('Y-m-d H:i:s', $info.end_time)}" class="layui-input" name="end_time" lay-verify="required|times" id="end_time" autocomplete="off" readonly>
							<i class=" iconrili iconfont calendar"></i>
						</div>
					</div>
					{if condition="$info.status == 1"}
					<div class="word-aux">
						<p>活动进行中时间不可更改</p>
					</div>
					{/if}
				</div>
		        <div class="layui-form-item">
		            <label class="layui-form-label"><span class="required">*</span>发放时间：</label>
		            <div class="layui-inline">
		                <div class="layui-input-inline">
		                    <input type="radio"  name="activity_time_type" lay-verify="required" {if $info.activity_time_type == 1} checked {/if} value="1" title="生日当天" class="layui-input len-mid" autocomplete="off" disabled>
		                    <input type="radio"  name="activity_time_type" lay-verify="required" {if $info.activity_time_type == 2} checked {/if} value="2" title="生日当周(自然周)" class="layui-input len-mid" autocomplete="off" disabled>
		                    <input type="radio"  name="activity_time_type" lay-verify="required" {if $info.activity_time_type == 3} checked {/if} value="3" title="生日当月(自然月)" class="layui-input len-mid" autocomplete="off" disabled>
		                </div>
		            </div>
					<div class="word-aux">在活动期间内，积分、余额、优惠劵只赠送一次</div>
		        </div>
		        <div class="layui-form-item">
		            <label class="layui-form-label">祝福语：</label>
		            <div class="layui-input-inline">
		                <textarea name="blessing_content" class="layui-textarea len-long" maxlength="150">{$info.blessing_content}</textarea>
		            </div>
		        </div>
		
		        <div class="layui-form-item participation-condition">
		            <label class="layui-form-label">参与条件：</label>
		            <div class="layui-input-block">
		                <input type="radio" name="level_id" value="0" lay-filter="participation" title="全部会员" {if !$info.level_id}checked{/if}>
		                <input type="radio" name="level_id" value="1" lay-filter="participation" title="部分会员" {if $info.level_id}checked{/if}>
		            </div>
		            <div class="layui-inline {if !$info.level_id}layui-hide{/if}">
		                <label class="layui-form-label"></label>
		                <div class="layui-input-block">
		                    {foreach $member_level_list as $k =>$v}
		                    <input type="checkbox" class="level-id" value="{$v.level_id}" title="{$v.level_name}" lay-skin="primary">
		                    {/foreach}
		                </div>
		            </div>
		            <div class="word-aux">选择参与的会员等级，默认为所有会员都可参与</div>
		        </div>
		    </div>
		</div>
		
		<div class="layui-card card-common card-brief">
		    <div class="layui-card-header">
		        <span class="card-title">奖励设置</span>
		    </div>
		    <div class="layui-card-body reward-wrap">
		        <div class="layui-form-item">
		            <label class="layui-form-label"><span class="required">*</span>奖励内容：</label>
		            <div class="layui-input-block">
		                <input type="checkbox" name="type" value="point" title="积分" lay-skin="primary" lay-filter="type" lay-verify="type" {if in_array('point', $info['type']) }checked{/if}>
		                <input type="checkbox" name="type" value="balance" title="余额" lay-skin="primary" lay-filter="type" lay-verify="type" {if in_array('balance', $info['type']) }checked{/if}>
		                <input type="checkbox" name="type" value="coupon" title="优惠券" lay-skin="primary" lay-filter="type" lay-verify="type" {if in_array('coupon', $info['type']) }checked{/if}>
		            </div>
		        </div>
		
		        <div class="point-wrap {if !in_array('point', $info['type']) }layui-hide{/if}">
		            <div class="layui-form-item">
		                <label class="layui-form-label"><span class="required">*</span>奖励积分：</label>
		                <div class="layui-input-block">
		                    <input name="point" value="{$info.point}" onchange="detectionNumType(this,'positiveInteger')" id="point" type="number" lay-verify="{if in_array('point', $info['type']) }required|num{/if}" class="layui-input len-short">
		                </div>
		            </div>
		        </div>
		
		        <div class="balance-wrap {if !in_array('balance', $info['type']) }layui-hide{/if}">
		            <div class="layui-form-item">
		                <label class="layui-form-label"><span class="required">*</span>奖励红包：</label>
						<div class="layui-input-block len-long">
							<input type="hidden" name="balance_type" lay-verify="balance_type" value="0" {if $info.balance_type == 0} checked {/if} title="不可提现"><input name="balance" onchange="detectionNumType(this,'positiveNumber')" value="{$info.balance}" type="number" lay-verify="" class="layui-input len-short">元
						</div>

		            </div>
		            <div class="word-aux"><p>红包为储值余额，仅在消费时可用</p></div>
		        </div>
		
		        <div class="coupon-wrap {if !in_array('coupon', $info['type']) }layui-hide{/if}">
		            <div class="layui-form-item">
		                <label class="layui-form-label"><span class="required">*</span>奖励优惠券：</label>
		                <div class="layui-input-block">
							<div id="coupon_list"></div>
							<div class="word-aux text-color" style="margin-left: 0">
								<p>活动优惠券发放，不受优惠券自身数量和领取数量的限制</p>
							</div>
							<button class="layui-btn" id="select_coupon">选择优惠券</button>
		                </div>
		            </div>
		        </div>
		        <input type="hidden" name="id" value="{$info.id}">
		        <div class="form-row">
		            <button class="layui-btn" lay-submit lay-filter="save">保存</button>
		            <button class="layui-btn layui-btn-primary" onclick="backBirthdayGiftList()">返回</button>
		        </div>
		    </div>
		</div>
	</div>
<!--    <div class="birthdaygift-preview">
    	<img src="__STATIC__/img/birthday_gift.png" >
    </div>-->
</div>
<script type="text/javascript" src="STATIC_JS/coupon_select.js"></script>
<script>
    var form,laydate,laytpl,
    repeat_flag = false,
        awardId = 0,
        minDate = "",
		currentDate = new Date(),
        coupon_id = [], addCoupon;
	var coupon_select = new CouponSelect({
		tableElem:'#coupon_list',
		selectElem:'#select_coupon',
		selectedIds:'{$info.coupon}',
	})

    layui.use(['form', 'laydate', 'laytpl'], function() {
        form = layui.form;
        laydate = layui.laydate;
        laytpl = layui.laytpl;
		currentDate.setDate(currentDate.getDate() + 30);
		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime',
			done: function(value) {
				reRender();
			}
		});

		//结束时间
		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime',
		});

		/**
		 * 重新渲染结束时间
		 * */
		function reRender() {
			$("#end_time").remove();
			$(".end-time").html('<input type="text" id="end_time" name="end_time" placeholder="请输入结束时间" lay-verify="required|time" class = "layui-input len-mid" autocomplete="off"> ');
			laydate.render({
				elem: '#end_time',
				type: 'datetime',
				min: minDate
			});
		}

        form.render();

        initTableData();

        //参与条件
        form.on('radio(participation)', function(data){
            if (parseInt(data.value))
                $('.participation-condition .layui-inline').removeClass('layui-hide');
            else
                $('.participation-condition .layui-inline').addClass('layui-hide');
        });

        /**
         * 表单验证
         */
        form.verify({
            type: function(){
                if ($('.reward-wrap [name="type"]:checked').length == 0) {
                    return '请选择邀请人可得奖励';
                }
            },
            mum: function(value, item){
                if (isNaN(parseInt(value))) {
                    return '请输入大于0的数字，支持小数点后两位';
                }
                value = parseInt(value);
                if (/^\d{0,10}$/.test(value) === false || value <= 0) {
                    return '请输入大于0的整数';
                }
            },
			time: function(value) {
				var now_time = (new Date()).getTime();
				var start_time = (new Date($("#start_time").val())).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
				if (start_time > end_time) {
					return '结束时间不能小于开始时间!';
				}
			},
        });

        form.on('checkbox(type)', function(data) {
            $('[name="type"]').each(function(){
                var type = $(this).val();
                if ($(this).is(':checked')) {
                    $('.reward-wrap .' + type + '-wrap').removeClass('layui-hide');
                    if (type == 'point' || type == 'coupon') {
                        $('.reward-wrap .' + type + '-wrap [lay-verify]').attr('lay-verify', 'required|mum');
                    }
                    if (type == 'balance') {
                        $('.reward-wrap .' + type + '-wrap [lay-verify]').attr('lay-verify', 'required|float');
                    }
                } else {
                    $('.reward-wrap .' + type + '-wrap').addClass('layui-hide');
                    $('.reward-wrap .' + type + '-wrap [lay-verify]').attr('lay-verify', '');
                }
            })
        });

        /**
         * 表单提交
         */
        form.on('submit(save)', function(data){
			let coupon_selected_ids = coupon_select.getSelectedData().selectedIds;
            if (parseInt(data.field.level_id)){
                var levelId = [],
                    levelName = [];
                $('.level-id').each(function(){
                    if($(this).prop('checked')){
                        levelId.push($(this).val());
                        levelName.push($(this).attr("title"));
                    }
                });
                data.field.level_id = levelId.toString();
                data.field.level_name = levelName.toString();
            }
			if($("input[name='level_id']:checked").val() == 1 && data.field.level_id.length == 0){
				layer.msg('请选择会员等级', {icon: 5});
				return;
			}

            var type = [];
            $('.reward-wrap [name="type"]:checked').each(function(){
                type.push($(this).val());
            });
			
			$($("input[name='checked_id']")).each(function(){
			    coupon_id.push($(this).val());
			});
			
            if ($.inArray('coupon', type) != -1 && coupon_selected_ids.length == 0) {
                layer.msg('请选择优惠券', {icon: 5});
                return;
            }

			if ($.inArray('point', type) != -1 ) {
				if($('#point').val() <= 0){
					layer.msg('积分请输入正整数', {icon: 5});
					return;
				}
			}

            if(data.field.max_fetch == ''){
                layer.msg('请输入邀请奖励上限', {icon: 5});
                return;
            }else if (data.field.max_fetch < 0){
                layer.msg('请输入大于或等于0的整数', {icon: 5});
                return;
            }

            data.field.type = type.toString();
            data.field.coupon = coupon_selected_ids.toString();

            if(repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("birthdaygift://shop/birthdaygift/edit"),
                data: data.field,
                async: false,
                success: function(res){
                    repeat_flag = false;

                    if (res.code == 0) {
                        layer.confirm('编辑成功', {
                            title:'操作提示',
                            btn: ['返回列表', '继续编辑'],
                            yes: function(index, layero) {
                                location.hash = ns.hash("birthdaygift://shop/birthdaygift/lists");
								layer.close(index);
                            },
                            btn2: function(index, layero) {
								layer.close(index);
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            })
        });
    });

    //初始化数据
    function initTableData(){
        var levelIdStr = '{$info.level_id}',
            levelIdArr = levelIdStr.split(",");
        if (levelIdArr.length >= 1){
            $(".participation-condition input.level-id").each(function (index,item) {
                for (var i = 0; i < levelIdArr.length; i++){
                    if (parseInt($(item).val()) == levelIdArr[i]){
                        $(item).prop('checked',true);
                    }
                }
            });
            form.render();
        }
    }

    function backBirthdayGiftList() {
        location.hash = ns.hash("birthdaygift://shop/birthdaygift/lists");
    }

	//检测数据类型
	function detectionNumType(el,type){
		var value = $(el).val();

		//大于零 且 不是小数
		if (value < 0 && type == 'integral')
			$(el).val(0);
		else if(type == 'integral')
			$(el).val(Math.round(value));

		//大于1 且 不是小数
		if (value < 1 && type == 'positiveInteger'){
			$(el).val(1);
		} else if (type == 'positiveInteger'){
			var val = Math.round(value);
			if(Object.is(val,NaN)){
				$(el).val(1);
			}else{
				$(el).val(val);
			}
		}

		//大于零可以是小数
		if (type == 'positiveNumber'){
			value = parseFloat(value).toFixed(2);
			if (value < 0)
				$(el).val(0);
			else
				$(el).val(value);
		}
	}

</script>