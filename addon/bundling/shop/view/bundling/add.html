<style>
	#goods thead th{ background-color: #f7f7f7;}
	/* 优惠商品 */
	.goods-title{display: flex;align-items: center;}
	.goods-title .goods-img{display: flex;align-items: center;justify-content: center;width: 55px;height: 55px;margin-right: 5px;}
	.goods-title .goods-img img{max-height: 100%;max-width: 100%;}
	.goods-title .goods-name{flex: 1;line-height: 1.6;}
	.goods_num {padding-left: 20px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>优惠套餐名称：</label>
		<div class="layui-input-block">
			<input type="text" name="bl_name" lay-verify="required" autocomplete="off" class="layui-input len-long" maxlength="40">
		</div>
		<div class="word-aux">
			<p>请认真填写组合优惠套餐名称，使顾客能从名称了解该套餐</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>商品：</label>
		<div class="layui-input-block">
			<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
				<colgroup>
					<col width="45%">
					<col width="20%">
					<col width="20%">
					<col width="15%">
				</colgroup>
				<thead>
					<tr>
						<th>商品名称</th>
						<th>价格</th>
						<th>库存</th>
						<th class="operation">操作</th>
					</tr>
				</thead>
				<tbody>
					<tr class="goods-empty">
						<td colspan="4">
							<div>未添加商品</div>
						</td>
					</tr>
				</tbody>
			</table>
			<button class="layui-btn" onclick="selectGoods()">选择商品</button> <span class="goods_num">已选商品（<span id="goods_num" class="text-color">0</span>）</span>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">优惠套餐价格：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				<input type="number" name="bl_price" autocomplete="off" class="layui-input combined-price len-short" value="0.00" step="1"  min="0.00" >
			</div>
			<span class="layui-form-mid">元</span>
		</div>
		<div class="word-aux">
			<p>价格不能小于0，可保留两位小数</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">原价：</label>
		<div class="layui-input-block">
			<p class="input-text"><span class="original-price">0.00</span>元</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">节省价：</label>
		<div class="layui-input-block">
			<p class="input-text"><span class="save-prices">0.00</span>元</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">运费承担：</label>
		<div class="layui-input-block">
			<input type="radio" name="shipping_fee_type" value="1" title="卖家承担运费" checked>
			<input type="radio" name="shipping_fee_type" value="2" title="买家承担运费（快递）">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否上下架：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="status" lay-filter="isOpen" value="1" lay-skin="switch" checked />
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backBundlingList()">返回</button>
	</div>
</div>

<script>
	var form, selectGoodsSkuId = [];
	layui.use("form", function() {
		form = layui.form;
		var repeat_flag = false; //防重复标识
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {

			var num = $("#goods").find("tbody tr").length;
			var combinedPrice = $('.combined-price').val(),
				originalPrice = Number($(".original-price").text());
			if (num < 2) {  //判断提交时商品数量是否在2-6之间
				layer.msg("商品数不可小于2个！", {
					icon: 5
				});
				return;
			}
			
			if (data.field.status == undefined) {
				data.field.status = 0;
			}
			
			if(combinedPrice > originalPrice){
				layer.msg("组合套餐价格不能高于原价");
				$(".save-prices").text(0).focus();
				return false
			}

			data.field.sku_ids = selectGoodsSkuId.toString();

			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("bundling://shop/bundling/add"),
				data: data.field,
				async: false,
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title: '操作提示',
							btn: ['返回列表', '继续添加'],
							closeBtn: 0,
							yes: function(index, layero) {
								location.hash = ns.hash("bundling://shop/bundling/lists");
								layer.close(index);
							},
							btn2: function(index, layero) {
								listenerHash(); // 刷新页面
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			})
		});

		form.verify({
			flo: function(value) {
				if (value == '') {
					return;
				}
				var reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
				if (!reg.test(value)) {
					return '价格不能小于0，可保留两位小数！'
				}
			}
		});
	});

	/**
	 * 添加商品
	 */
	function selectGoods() {
		goodsSelect(function (data) {
			selectGoodsSkuId = [];
			var html = '';
			if(Object.values(data).length > 0){
				for (var key in data) {
					for (var sku in data[key].selected_sku_list) {
						var item = data[key].selected_sku_list[sku];
						html += `<tr data-sku_id="${item.sku_id}">`;
						html += `
							<td>
								<div class="goods-title">
									<div class="goods-img">
										<img layer-src src="${item.sku_image ? ns.img(item.sku_image) : ''}" alt="">
									</div>
									<p class="multi-line-hiding goods-name">${item.sku_name}</p>
								</div>
							</td>
						`;
						html += `<td class='price-one'>${item.price }</td>`;
						html += `<td>${item.stock}</td>`;
						html += `<td class='operation'> <div class='table-btn '><a href='javascript:;' class='layui-btn' onclick='deleteGoods(this)'>删除商品</a></div></td>`;
						html += `</tr>`;
						selectGoodsSkuId.push(item.sku_id);
					}
				}
			}else{
				html += `<tr class="goods-empty">
							<td colspan="4">
								<div>未添加商品</div>
							</td>
						</tr>`;
			}
			$("#goods tbody").html(html);
			$("#goods_num").text(selectGoodsSkuId.length);
			priceCount();
		}, selectGoodsSkuId, {mode: "sku", max_num: 0, min_num: 2});
	}

	/**
	 * 删除商品
	 */
	function deleteGoods(data) {
		var obj = $(data).parent().parent().parent();
		$(obj).remove();
		priceCount(); //计算出当前总价格
		for (var i in selectGoodsSkuId) {
			if (selectGoodsSkuId[i] == Number($(obj).attr("data-sku_id"))) {
				selectGoodsSkuId.splice(i, 1);
			}
		}
		$("#goods_num").html(selectGoodsSkuId.length)

		if(selectGoodsSkuId.length) $('.goods-empty').hide();
		else $('.goods-empty').show();
	}

	/**
	 * 计算总价
	 */
	function priceCount() {
		var price_count = 0;
		$("#goods").find("tbody td.price-one").each(function(i) {
			var price_one = Number($(this).text());
			price_count += price_one;
		});
		$(".original-price").text(price_count.toFixed(2));
		$(".save-prices").text(0);
		$(".combined-price").val(price_count.toFixed(2));

		if (price_count == 0) {
			$('.goods-empty').show();
		}
	}
	
	$("#bl_price").blur(function() {
		var bl_price = $(this).val();

		if (bl_price < 0) {
			layer.msg("价格不能小于0，可保留两位小数");
		}else{
			var originalPrice = Number($(".original-price").text());
			if (bl_price > originalPrice) {
				$(this).val(originalPrice);
				layer.msg("优惠套餐价格不能高于原价");
				$(".save-prices").text(0);
			} else {
				var num = accSub(originalPrice, bl_price);
				$(".save-prices").text(num);
			}
		}
	});
	
	/**
	 * 计算组合套餐价格、原价、节省价
	 */
	// $(".combined-price").blur(function() {
	// 	var combinedPrice = $(this).val(),
	// 		originalPrice = Number($(".original-price").text());

	// 	if (combinedPrice > originalPrice) {
	// 		$(this).val(originalPrice);
	// 		layer.msg("优惠套餐价格不能高于原价");
	// 		$(".save-prices").text(0);
	// 	} else {
	// 		var num = accSub(originalPrice, combinedPrice);
	// 		$(".save-prices").text(num);
	// 	}
	// });
	
	// 两个浮点数相减
	function accSub(num1, num2){
		var r1, r2, m;
		
		try{
			r1 = num1.toString().split(".")[1].length;
		}catch(e){
			r1 = 0;
		}
		
		try{
			r2 = num2.toString().split(".")[1].length;
		}catch(e){
			r2 = 0;
		}
		
		m = Math.pow(10, Math.max(r1, r2));
		n = (r1 >= r2) ? r1 : r2;
		return (Math.round(num1 * m - num2 * m) / m).toFixed(2);
    }

	//返回按钮
	function backBundlingList() {
		location.hash = ns.hash("bundling://shop/bundling/lists");
	}
</script>
