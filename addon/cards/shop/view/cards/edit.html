<link rel="stylesheet" href="SHOP_CSS/game.css">
<style>
	.layui-table-body{max-height: 480px !important;}
</style>

<div class="layui-form">

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">活动设置</span>
		</div>

		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
				<div class="layui-input-block">
					<input type="text" name="game_name" lay-verify="required" maxlength="15" placeholder="最多可填写15个字" autocomplete="off" class="layui-input len-long" value="{$info.game_name}">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>活动时间：</label>
				<div class="layui-inline">
					<div class="layui-input-inline">
						<input type="text" id="start_time" name="start_time" lay-verify="required" class="layui-input len-mid" autocomplete="off" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-input-inline end-time">
						<input type="text" id="end_time" name="end_time" lay-verify="required|time" class="layui-input len-mid" autocomplete="off" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">活动说明：</label>
				<div class="layui-input-inline">
					<textarea name="remark" class="layui-textarea len-long" maxlength="150">{$info.remark}</textarea>
				</div>
			</div>

			<div class="layui-form-item participation-condition">
				<label class="layui-form-label">参与条件：</label>
				<div class="layui-input-block">
					<input type="radio" name="level_id" value="0" lay-filter="participation" title="全部会员" {if $info.level_id == 0}checked{/if}>
					<input type="radio" name="level_id" value="1" lay-filter="participation" title="部分会员" {if $info.level_id != 0}checked{/if}>
				</div>
				<div class="layui-inline {if $info.level_id == 0}layui-hide{/if}">
					<label class="layui-form-label"></label>
					<div class="layui-input-block">
						{foreach $member_level_list as $k =>$v}
						<input type="checkbox" class="level-id" value="{$v.level_id}" title="{$v.level_name}" lay-skin="primary">
						{/foreach}
					</div>
				</div>
				<div class="word-aux">选择参与的会员等级，默认为所有会员都可参与</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>消耗积分：</label>
				<div class="layui-input-block">
					<input type="number" min="0" name="points" lay-verify="required" onchange="detectionNumType(this,'integral')" value="{$info.points}" class="layui-input len-short" autocomplete="off">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>参与次数：</label>
				<div class="layui-input-block">
					<input type="radio" name="join_type" value="1" lay-verify="required" title="每天N次" {if $info.join_type == 1} checked{/if}>
					<input type="radio" name="join_type" value="0" lay-verify="required" title="一人N次" {if $info.join_type == 0} checked{/if}>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"></label>
				<div class="layui-input-block">
					<input type="number" name="join_frequency" min="0" value="{$info.join_frequency}" onchange="detectionNumType(this,'positiveInteger')" lay-verify="required" autocomplete="off" class="layui-input len-short"> 次
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">中奖设置</span>
		</div>

		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>整体中奖概率：</label>
				<div class="layui-input-block">
					<input type="text" name="winning_rate" min="0" max="100" value="{$info.winning_rate}" onchange="detectionNumType(this,'positiveNumber')" lay-verify="winning_rate" autocomplete="off" class="layui-input len-short"> %
				</div>
				<div class="word-aux">
					<span class="aux-title">注意：</span>
					<div class="aux-item">若整体中奖概率为100%，需满足以下条件：</div>
					<div class="aux-item">1、奖品对应的活动不能失效，奖品状态正常。</div>
					<div class="aux-item">2、保持奖品库存需充足，活动库存充足，当奖项被领取完，此时中奖概率已经非100%。</div>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">未中奖提示语：</label>
				<div class="layui-input-block">
					<input type="text" name="no_winning_desc" value="{$info.no_winning_desc}" lay-verify="required" autocomplete="off" class="layui-input len-long" maxlength="40">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">中奖名单：</label>
				<div class="layui-input-block">
					<input type="radio" name="is_show_winner" value="0" lay-verify="required" title="不显示" {if $info.is_show_winner == 0} checked{/if}>
					<input type="radio" name="is_show_winner" value="1" lay-verify="required" title="显示" {if $info.is_show_winner == 1} checked{/if}>
				</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">奖品设置</span>
		</div>

		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label">奖品明细：</label>
				<div class="layui-input-block">
					<table id="award_list"></table>
				</div>
				<div class="word-aux">
					<span class="aux-title">注意：</span>
					<div class="aux-item">1、奖项发放完毕之后活动将自动关闭</div>
					<div class="aux-item">2、奖品奖项不能少于1项。</div>
				</div>
				<div class="word-aux">
					<button class="layui-btn" onclick="addAward()">添加奖品</button>
				</div>
			</div>

		</div>
	</div>
	<input type="hidden" name="game_id" value="{$info.game_id}">
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backCardsList()">返回</button>
	</div>
</div>

<!-- 添加奖品 -->
{include file="cards/award_select" /}
<script>
	var form,laydate,laytpl,tableData = {:json_encode($info.game_award)},
			repeat_flag = false,
			awardId = 0,
			minDate = "",
			deleteAwardIds = [];

	layui.use(['form', 'laydate', 'laytpl'], function() {

		form = layui.form;
		laydate = layui.laydate;
		laytpl = layui.laytpl;

		form.render();

		//开始时间
		laydate.render({
			elem: '#start_time', //指定元素
			type: 'datetime',
			value: ns.time_to_date("{$info.start_time}"),
			done: function(value) {
				minDate = value;
				reRender();
			}
		});

		//结束时间
		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime',
			value: ns.time_to_date("{$info.end_time}")
		});

		initTableData();

		/**
		 * 重新渲染结束时间
		 * */
		function reRender() {
			$("#end_time").remove();
			$(".end-time").html('<input type="text" id="end_time" name="end_time" placeholder="请输入结束时间" lay-verify="required|time" class = "layui-input len-mid" autocomplete="off"> ');
			laydate.render({
				elem: '#end_time',
				type: 'datetime',
				min: minDate
			});
		}

		//参与条件
		form.on('radio(participation)', function(data){
			if (parseInt(data.value))
				$('.participation-condition .layui-inline').removeClass('layui-hide');
			else
				$('.participation-condition .layui-inline').addClass('layui-hide');
		});

		/**
		 * 表单验证
		 */
		form.verify({
			time: function(value) {
				var now_time = (new Date()).getTime();
				var start_time = (new Date($("#start_time").val())).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
				if (start_time > end_time) {
					return '结束时间不能小于开始时间!';
				}
			},
			flnum: function(value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位！'
				}
			},
			int: function(value) {
				if (value <= 1 || value % 1 != 0) {
					return '请输入大于1的正整数！'
				}
			},
			detectionNum: function(value, item) {
				var str = $(item).parents(".layui-form-item").find("label").text().split("*").join("");
				str = str.substring(0, str.length - 1);

				if (value < 0) {
					return str + "不能小于0";
				}
			},
			winning_rate:function (value) {
				var reg = /^\d{0,2}(.?\d{0,2})$/;
				if (value.length > 0) {
					if (isNaN(value)) {
						return '中奖概率输入错误';
					}
					if (!reg.test(value) || value < 0 || value > 100) {
						return '中奖概率范围:0~100%';
					}
				}
			}
		});

		/**
		 * 表单提交
		 */
		form.on('submit(save)', function(data){
			if (parseInt(data.field.level_id)){
				var levelId = [],
					levelName = [];
				$('.level-id').each(function(){
					if($(this).prop('checked')){
						levelId.push($(this).val());
						levelName.push($(this).attr("title"));
					}
				});
				data.field.level_id = levelId.toString();
				data.field.level_name = levelName.toString();
			}

			data.field.award_json = JSON.stringify(tableData);

			data.field.delete_award_ids = deleteAwardIds.toString();

			if (!tableData.length){
				layer.msg("奖品奖项不能少于1项。");
				return false;
			}

			if(repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("cards://shop/cards/edit"),
				data: data.field,
				async: false,
				success: function(res){
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续编辑'],
                            yes: function(index, layero) {
								location.hash = ns.hash("cards://shop/cards/lists");
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			})
		});
	});

	/*
    * 渲染表格
    * */
	renderTable(tableData);
	function renderTable(data = []){
		var table = new Table({
			elem: "#award_list",
			page: false,
			limit: Number.MAX_VALUE,
			cols: [
				[{
					field: 'award_name',
					title: '名称',
					width: '30%',
					unresize: 'false'
				},{
					field: 'award_type',
					title: '奖品',
					unresize: 'false',
					width: '20%',
					templet: function(data) {
						var type = data.award_type == 1 ? '积分' : data.award_type == 2 ? '红包' : '优惠券';
						return type;
					}
				}, {
					field: 'award_num',
					title: '奖品数量',
					unresize: 'false',
					width: '15%',
				}, {
					field: 'award_winning_rate',
					title: '奖项权重',
					unresize: 'false',
					width: '15%',
				},{
					title: '操作',
					toolbar: '#operation',
					align: 'right',
					unresize: 'false'
				}]
			],
			data: data
		});
	}

	//添加奖品
	function addAward(){
		awardPop();
	}

	//编辑奖品
	function ediAward(el) {
		var data = $(el).attr("data-value");
		awardPop(JSON.parse(data));
	}

	//删除奖品
	function delAward(data) {
		for (var i = 0; i < tableData.length; i++){
			if (tableData[i].ident == data){
				deleteAwardIds.push((tableData[i].award_id));
				tableData.splice(i,1);
				renderTable(tableData);
			}
		}
	}

	//初始化数据
	function initTableData(){
		for (var i = 0; i < tableData.length; i++){
			tableData[i].ident = ++awardId;
		}

		var levelIdStr = '{$info.level_id}',
			levelIdArr = levelIdStr.split(",");

		if (levelIdArr.length > 1){
			$(".participation-condition input.level-id").each(function (index,item) {
				for (var i = 0; i < levelIdArr.length; i++){
					if (parseInt($(item).val()) == levelIdArr[i]){
						$(item).prop('checked',true);
					}
				}
			});
			form.render();
		}
	}

	function backCardsList() {
		location.hash = ns.hash("cards://shop/cards/lists");
	}

	//检测数据类型
	function detectionNumType(el,type){
		var value = $(el).val();

		//大于零 且 不是小数
		if (value < 0 && type == 'integral')
			$(el).val(0);
		else if(type == 'integral')
			$(el).val(Math.round(value));

		//大于1 且 不是小数
		if (value < 1 && type == 'positiveInteger'){
			$(el).val(1);
		} else if (type == 'positiveInteger'){
			var val = Math.round(value);
			if(Object.is(val,NaN)){
				$(el).val(1);
			}else{
				$(el).val(val);
			}
		}

		//大于零可以是小数
		if (type == 'positiveNumber'){
			value = parseFloat(value);
			if (value < 0)
				$(el).val(0);
			else
				$(el).val(value);
		}
		//自然整数 包括0
		if(type == 'nativeInteger'){
			value = parseInt(value);
			if(value < 0) value = 0;
			$(el).val(value);
		}
	}

</script>

<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" data-value='{{JSON.stringify(d)}}' onclick="ediAward(this)">编辑</a>
		<a class="layui-btn" onclick="delAward('{{d.ident}}')">删除</a>
	</div>
</script>
