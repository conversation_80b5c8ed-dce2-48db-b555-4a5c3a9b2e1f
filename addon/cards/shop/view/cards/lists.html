<style>
	.layui-table-view td:last-child>div{overflow: inherit;}
	.operation-wrap{position: relative;}
	.layui-table-box{overflow: inherit;}
	.layui-table-body{overflow: inherit;}
	.popup-qrcode-wrap{text-align: center;background: #fff;border-radius: 2px;box-shadow: 0 2px 8px 0 rgba(200,201,204,.5);padding: 10px;position: absolute;z-index: 1;top: -70px;left: -190px;display: none;width: 170px;height: 230px;}
	.popup-qrcode-wrap:before, .popup-qrcode-wrap:after {left: 100%;top: 50%;border: solid transparent;content: " ";height: 0;width: 0;position: absolute;pointer-events: none;}
	.popup-qrcode-wrap:before {border-color: transparent;border-left-color: #e5e5e5;border-width: 8px;margin-top: -29px;}
	.popup-qrcode-wrap:after {border-color: transparent;border-left-color: #ffffff;border-width: 7px;margin-top: -31px;}
	.popup-qrcode-wrap img{width: 150px;height: 150px;max-width: initial;}
	.popup-qrcode-wrap p{font-size: 12px;margin: 5px 0;line-height: 1.8!important;}
	.popup-qrcode-wrap a{font-size: 12px;}
	.popup-qrcode-wrap input{opacity: 0;position: absolute;}
	.popup-qrcode-wrap .popup-qrcode-loadimg {width: 16px!important; height: 16px!important; margin-top: 107px;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加刮刮乐</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">活动名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="game_name" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">活动时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab" lay-filter="cards_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		<li data-status="0">未开始</li>
		<li data-status="1">进行中</li>
		<li data-status="2">已结束</li>
		<li data-status="3">已关闭</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="cards_list" lay-filter="cards_list"></table>
	</div>
</div>

<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{#  if(d.status == 0){  }}
	未开始
	{{#  }else if(d.status == 1){  }}
	进行中
	{{#  }else if(d.status == 2){  }}
	已结束
	{{#  }else if(d.status == 3){  }}
	已关闭
	{{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-game-id="{{d.game_id}}">
		<div class="popup-qrcode-wrap"><img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif" /></div>
		<div class="table-btn">
			<a class="layui-btn" lay-event="detail">详情</a>
			{{# if(d.status == 0){ }}
			<a class="layui-btn text-color" lay-event="select">推广</a>
			<a class="layui-btn" lay-event="edit">编辑</a>
			<a class="layui-btn" lay-event="del">删除</a>
			{{# }else if(d.status == 1){ }}
			<a class="layui-btn text-color" lay-event="select">推广</a>
			<a class="layui-btn" lay-event="close">关闭</a>
			{{# }else if(d.status == 2){ }}
			<a class="layui-btn" lay-event="del">删除</a>
			{{# }else if(d.status == 3){ }}
			<a class="layui-btn" lay-event="del">删除</a>
			<a class="layui-btn" lay-event="start">开启</a>
			{{# } }}
		</div>
	</div>
</script>

<!-- 推广 -->
{include file="app/shop/view/component/promote_show.html"}

<script>
	var laytpl;
	layui.use(['form','laytpl' , 'element','laydate'], function() {
		var table,
			form = layui.form,
            laytpl = layui.laytpl,
			element = layui.element,
            laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

		element.on('tab(cards_tab)', function() {
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': this.getAttribute('data-status')
				}
			});
		});

		table = new Table({
			elem: '#cards_list',
			url: ns.url("cards://shop/cards/lists"),
			cols: [
				[{
			    	field:'game_name',
					title: '活动名称',
					unresize: 'false',
					width:'15%'
				},{
					title: '中奖概率',
					unresize: 'false',
					templet: function(data) {
						return data.winning_rate + '%';
					}
				}, {
					field: 'points',
					title: '每次消耗积分',
					unresize: 'false'
				}, {
					title: '参与次数',
					unresize: 'false',
                    templet: function(data) {
                        if(data.join_type == 1){
							return '每天' + data.join_frequency + '次';
						}else{
                            return data.join_frequency + '次';
						}
                    }
				}, {
			    	field:'join_num',
					title: '抽奖人数',
					unresize: 'false'
				}, {
                    field:'winning_num',
                    title: '中奖人数',
                    unresize: 'false'
                }, {
					title: '状态',
					unresize: 'false',
					templet: '#status'
				}, {
                    title: '活动时间',
                    unresize: 'false',
                    width: '14%',
                    templet: '#time'
                }, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]

		});

        //开始时间
        laydate.render({
            elem: '#start_time', //指定元素
			type: 'datetime'
        });
        //结束时间
        laydate.render({
            elem: '#end_time', //指定元素
            type: 'datetime'
        });
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		//监听Tab切换
		element.on('tab(status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload({
				page: {
					curr: 1
				},
				where: {
					'status': status
				}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
                case 'detail': //详情
                    location.hash = ns.hash("cards://shop/cards/detail", {"game_id": data.game_id});
                    break;
				case 'edit': //编辑
					location.hash = ns.hash("cards://shop/cards/edit", {"game_id": data.game_id});
					break;
				case 'del': //删除
					deleteCards(data.game_id);
					break;
				case 'close': // 结束
					closeCards(data.game_id);
					break;
				case 'record'://抽奖记录
                    location.hash = ns.hash("cards://shop/record/lists", {"game_id": data.game_id});
                    break;
                case 'select'://推广
                    gameUrl(data);
                    break;
				case 'start'://重新开启
					start(data.game_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteCards(game_id) {
			layer.confirm('确定要删除该刮刮乐活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("cards://shop/cards/delete"),
					data: {
                        game_id: game_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload({
								page: {
									curr: 1
								},
							});
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		// 结束
		function closeCards(game_id) {

			layer.confirm('确定要结束该刮刮乐活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("cards://shop/cards/finish"),
					data: {
                        game_id: game_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		//重新打开
		function start(game_id) {

			layer.confirm('确定要重启该刮刮乐活动吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("cards://shop/cards/start"),
					data: {
						game_id: game_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		function gameUrl(data){
			new PromoteShow({
				url:ns.url("cards://shop/cards/gameUrl"),
				param:{game_id:data.game_id},
			})
		}
	});

    function add() {
		location.hash = ns.hash("cards://shop/cards/add");
	}
</script>
