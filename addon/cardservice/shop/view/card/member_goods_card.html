<!-- 搜索框 -->
<div class="single-filter-box" style="margin:15px 0;">

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_text" placeholder="请输入商品名称" class="layui-input" autocomplete="off">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="goods_card_search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="card_list" lay-filter="card_list"></table>

<!-- 商品 -->
<script type="text/html" id="goodsCardInfo">
	<div class="table-title">
		<div class="title-pic">
			{{# if(d.goods_image){ }}
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0],'small')}}"/>
			{{# } }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 工具栏操作 -->
<script type="text/html" id="goods_card_operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="detail">详情</a>
	</div>
</script>
<script>
	var form, goods_card_table, laytpl;
	layui.use(['form', 'laytpl'], function () {
		form = layui.form;
		laytpl = layui.laytpl;
		form.render();

		goods_card_table = new Table({
			elem: '#card_list',
			url: ns.url("cardservice://shop/card/membergoodscard"),
			async: false,
			where: {'member_id': "{$member_id}"},
			parseData: function (res) {
				return {
					"code": res.code,
					"msg": res.message,
					"count": res.data.count,
					"data": res.data.list,
				};
			},
			cols: [
				[{
					title: '商品',
					unresize: 'false',
					width: '25%',
					templet: '#goodsCardInfo'
				}, {
					field: 'card_code',
					title: '卡号',
					unresize: 'false',
					width: '15%',
					align: 'left',
				}, {
					field: 'card_type_name',
					title: '卡类型',
					unresize: 'false',
					width: '10%',
					align: 'left',
				}, {
					field: '',
					title: '总次数/已使用',
					templet: function (data) {
						return data.total_num + '/' + data.total_use_num;
					}
				}, {
					title: '创建时间',
					width: '15%',
					templet: function (data) {
						return ns.time_to_date(data.create_time)
					}
				}, {
					title: '到期时间',
					width: '15%',
					templet: function (data) {
						return data.end_time > 0 ? ns.time_to_date(data.end_time) : '永久有效';
					}
				}, {
					title: '操作',
					toolbar: '#goods_card_operation',
					unresize: 'false',
					align: 'right'
				}]
			]
		});

		goods_card_table.tool(function (obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'detail':
					window.open(ns.href("cardservice://shop/card/detail?card_id=" + data.card_id))
					break;
			}
		});

		/**
		 * 搜索功能
		 */
		form.on('submit(goods_card_search)', function (data) {
			goods_card_table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});
</script>