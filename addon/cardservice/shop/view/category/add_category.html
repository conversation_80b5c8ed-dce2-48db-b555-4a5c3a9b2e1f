<style>
	.layui-input-inline.js-pid a{margin-left: 20px;}
	.link-url-show{margin-right: 10px}
	.click-link{height: 34px;line-height: 34px;display: inline-block;white-space: nowrap;text-align: center;border-radius: 2px;cursor: pointer;padding: 0 16px;border: 1px solid #C9C9C9;background-color: #fff;color: #555;}
</style>

<form class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分类名称：</label>
		<div class="layui-input-block len-long">
			<input name="category_name" type="text" placeholder="请输入分类名称" maxlength="30" lay-verify="required" class="layui-input" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>分类名称最长不超过30个字符</p>
		</div>
	</div>
	
	<div class="layui-form-item layui-hide">
		<label class="layui-form-label">上级分类：</label>
		<div class="layui-input-block js-pid">
			<span class="input-text">顶级分类</span>
			<input type="hidden" name="pid" value="0">
			<input type="hidden" name="level" value="1">
			<input type="hidden" name="category_id_1" value="0">
			<input type="hidden" name="category_id_2" value="0">
			<a class="text-color" href="javascript:selectedCategoryPopup();">选择分类</a>
		</div>
		<div class="word-aux">
			<p>如果选择上级分类，那么新增的分类则为被选择上级分类的子分类，不选择上级分类默认为顶级分类</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">分类图片：</label>
		<div class="layui-input-block">
			<div class="upload-img-block">
				<div class="upload-img-box">
					<div class="upload-default" id="imgUpload">
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image">
				</div>
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：不能大于100k。图片格式：jpg、png、jpeg。</p>
		</div>
		<a id="imageUploadAction"></a>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label">分类广告图：</label>
        <div class="layui-input-block">
            <div class="upload-img-block">
                <div class="upload-img-box">
					<div class="upload-default" id="imgUploadAdv">
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image_adv" value="">
                </div>
            </div>
        </div>
	    <div class="word-aux">
		    <p>该图片只对一级使用, 建议图片尺寸：550px * 250px。图片格式：jpg、png、jpeg。</p>
	    </div>
		<a id="imageAdvUploadAction"></a>
    </div>
	<div class="layui-form-item link-url-show-wrap">
		<label class="layui-form-label">广告链接：</label>
		<div class="layui-input-block">
			<input name="link_url" type="hidden" class="layui-input len-long" autocomplete="off">
			<a class="click-link" onclick="selectedLink()">选择链接</a>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">是否显示</label>
		<div class="layui-input-block">
			<input type="radio" name="is_show" value="0" title="显示" checked="">
			<input type="radio" name="is_show" value="-1" title="不显示">
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn bg-color" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="backCardServiceCategoryList()">返回</button>
	</div>
</form>

<script type="text/html" id="selectedCategory">

	<form class="layui-form">

		<div class="layui-form-item">
			<label class="layui-form-label sm">一级分类</label>
			<div class="layui-input-block len-mid">
				<select name="category_id_1" lay-filter="category_id_1">
					<option value="0" data-level="0">顶级分类</option>
					{{# for(var i=0;i<d.category_list_1.length;i++){ }}
						{{# if(d.category_id_1 ==d.category_list_1[i].category_id){ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}" selected>{{ d.category_list_1[i].category_name }}</option>
						{{# }else{ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}">{{ d.category_list_1[i].category_name }}</option>
						{{# } }}
					{{# } }}
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label sm">二级分类</label>
			<div class="layui-input-block len-mid">
				<select name="category_id_2" lay-filter="category_id_2"></select>
			</div>
		</div>

		<div class="form-row sm">
			<button class="layui-btn bg-color" lay-submit lay-filter="save_pid">保存</button>
		</div>
		
	</form>
</script>

<script src="ADDON_CARDSERVICE_JS/edit_category.js"></script>
