<style>
	.table_body{font-family: arial;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}
	.goods-category-list .layui-table td{border-left: 0;border-right: 0;}
	.goods-category-list .layui-table .switch{font-size: 16px;cursor: pointer;width: 12px;line-height: 1;display: inline-block;text-align: center;vertical-align: middle;}
	.goods-category-list .layui-table img{width: 40px;}
	/* 分类样式*/
	.table_div{color:#666}
	.table_head{display: flex;font-weight: bold;background-color: #F7F7F7;}
	.table_head li{height: 50px;line-height: 50px;border: 0;padding: 0 15px;font-size: 14px;font-weight: 400;color: #333;}
	.table_head .operate{text-align: right;}
	.table_head li:first-child{padding-right: 0;}
	.table_tr{display: flex;border-bottom: 1px solid #e6e6e6;background: #fff;align-items: center;}
	.table_tr .table_td{position: relative;padding: 5px 15px;font-size: 14px;}
	.table_tr .table_td span{cursor: pointer;}
	.table_tr .table_td span>img{width:12px;height:12px}
	.table_tr .table_td span>img.rotate{transform:rotate(90deg);}
	.table_tr .table_td .img-box{width:30px;height:30px;line-height: 30px;}
	.table_tr .table_td:first-child{padding-right:0}
	.table_tr .table-btn{display: flex;flex-wrap: wrap;justify-content: flex-end;}
	.table_tr .layui-btn{display: flex;justify-content: center;align-items: center;height: 23px;border-radius: 50px;background-color: transparent;color: var(--base-color);text-align: center;padding: 2px 0;margin: 5px 0 5px 10px;position: relative;}
	.table_two_div{display: none;}
	.table_three{display: none;}
	.empty_switch{display: inline-block;width:30px;height:25px;padding-right:15px;}
	.js-switch{display: inline-block;height:30px;width:30px;text-align: center;}
	.table_move{cursor: move;float:left;margin-right: 10px;}
	.table_moves{cursor: move;float:left;margin-right: 10px;}
	.tables_move{cursor: move;float:left;margin-right: 20px;padding-left: 70px;}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="addCategory()">添加项目分类</button>
</div>

<div class="goods-category-list">
	<div class="table_div" >
		<ul class="table_head">
			<li style="width:60px"></li>
			<li style="flex:6">分类名称</li>
			<li style="flex:2">图片</li>
			<!-- <li style="flex:2">排序</li> -->
			<li style="flex:2">是否显示</li>
			<li class="operate" style="flex:2">操作</li>
		</ul>
		<div class="table_body">
			{if condition="$list"}
			{foreach name="$list" item="vo" key="index"}
			<li class="table_one" data-index="{$index}" data-sort="{$vo['sort']}" data-cateid="{$vo['category_id']}">
				<div class="table_tr">
					<div class="table_td" style="width:60px">
						<div class="table_move iconfont icontuodong"></div>
						{notempty name="$vo['child_list']"}
						<span class="switch text-color js-switch" data-category-id="{$vo['category_id']}" data-level="{$vo['level']}" data-open="0">+</span>
						{/notempty}
					</div>
					<div class="table_td" style="flex:6">{$vo['category_name']}</div>
					<div class="table_td" style="flex:2">
						{notempty name="$vo['image']"}
						<div class="img-box">
							<img layer-src src="{:img($vo['image'])}"/>
						</div>
						{/notempty}
					</div>
					<!-- <div class="table_td" style="flex:2">
						<input type="number" class="layui-input len-short" value="{$vo['sort']}" onchange="editSort('{$vo.category_id}')" id="category_sort{$vo.category_id}">
					</div> -->
<!--					<div class="table_td" style="flex:2">-->
<!--						{if $vo['is_show'] == 0} 显示 {else /} 不显示 {/if}-->
<!--					</div>-->

					<div class="table_td layui-form" style="flex:2">
						<input type="checkbox" name="is_show" value="{$vo['category_id']}"  lay-skin="switch" lay-text="" lay-filter="is_show" {if $vo['is_show'] == 0} checked {/if}>
					</div>
					<div class="table_td" style="flex:2">
						<div class="table-btn">
							<a class="layui-btn" href="{:href_url('cardservice://shop/servicecategory/editcategory',['category_id'=>$vo['category_id']])}">编辑</a>
							<a class="layui-btn" href="javascript:deleteCategory({$vo['category_id']});">删除</a>
						</div>
					</div>
				</div>
				{notempty name="$vo['child_list']"}
					<div class="table_two_div">
					{foreach name="$vo['child_list']" item="second"}

					<div class="table_two" data-index="{$index}" data-sort="{$second['sort']}" data-cateid="{$second['category_id']}">

						<div class="table_tr">
						<div class="table_td" style="width: 36px">
						</div>
						<div class="table_td" style="flex:6.2">
							<div class="table_moves iconfont icontuodong"></div>
							{notempty name="$second['child_list']"}
							<span class="switch text-color js-switch" data-category-id="{$second['category_id']}" data-level="{$second['level']}" data-open="0" style="padding-right: 15px;">+</span>
							{else /}
							<span class="switch text-color empty_switch" >  </span>
							{/notempty}
							<span>{$second['category_name']}</span>
						</div>
						<div class="table_td" style="flex:2">
							{notempty name="$second['image']"}
							<div class="img-box">
								<img layer-src src="{:img($second['image'])}"/>
							</div>
							{/notempty}
						</div>
						<!-- <div class="table_td" style="flex:2">
							<input type="number" class="layui-input len-short" value="{$second['sort']}" onchange="editSort('{$second.category_id}')" id="category_sort{$second.category_id}">
						</div> -->
						<div class="table_td" style="flex:2">
							{if $second['is_show'] == 0} 显示 {else /} 不显示 {/if}
						</div>
						<div class="table_td" style="flex:2">
							<div class="table-btn">
								<a class="layui-btn" href="{:href_url('cardservice://shop/servicecategory/editcategory',['category_id'=>$second['category_id']])}">编辑</a>
								<a class="layui-btn" href="javascript:deleteCategory({$second['category_id']});">删除</a>
							</div>
						</div>
					</div>
					{notempty name="$second['child_list']"}
						
					<div class="table_three">
						{foreach name="$second['child_list']" item="third"}
						<div class="table_tr table_three_tr" data-sort="{$third['sort']}" data-cateid="{$third['category_id']}">
							<div class="table_td" style="width: 36px">
							</div>
							<div class="table_td" style="flex:6.2">
								<div class="tables_move iconfont icontuodong"></div>
								<div>{$third['category_name']}</div>
							</div>
							<div class="table_td" style="flex:2">
								{notempty name="$third['image']"}
								<div class="img-box">
									<img layer-src src="{:img($third['image'])}"/>
								</div>
								{/notempty}
							</div>
							<!-- <div class="table_td" style="flex:2">
								<input type="number" class="layui-input len-short" value="{$second['sort']}" onchange="editSort('{$second.category_id}')" id="category_sort{$second.category_id}">
							</div> -->
							<div class="table_td" style="flex:2">
								{if $third['is_show'] == 0} 显示 {else /} 不显示 {/if}
							</div>
							<div class="table_td" style="flex:2">
								<div class="table-btn">
									<a class="layui-btn" href="{:href_url('cardservice://shop/servicecategory/editcategory',['category_id'=>$third['category_id']])}">编辑</a>
									<a class="layui-btn" href="javascript:deleteCategory({$third['category_id']});">删除</a>
								</div>
							</div>
						</div>
						{/foreach}
						
					</div>
					{/notempty}
					</div>
					{/foreach}
					</div>
				{/notempty}
			</li>
			
			{/foreach}
			{else/}
			<div class="table_tr">
				<div class="table_td" style="flex:1;text-align: center;">暂无数据</div>
			</div>
			{/if}
		</div>
	</div>
</div>

<script src="STATIC_EXT/drag-arrange.js"></script>
<script src="STATIC_EXT/diyview/js/ddsort.js"></script>
<script>


	var form;
	layui.use(['form'], function(){

		form = layui.form;
		form.on('switch(is_show)', function(data){

			$.ajax({
				url: ns.url("cardservice://shop/servicecategory/modifyShow"),
				data: {id:data.value,is_show:data.elem.checked ? 0 : -1},
				dataType: 'JSON',
				type: 'POST',
				async: false,
				success: function (res) {
					layer.msg(res.message);
				}
			});

			console.log(data.elem); // 获取当前 checkbox 的 DOM 对象
			console.log(data.elem.checked); // 获取当前 checkbox 的选中状态，true 或 false
			console.log(data.value); // 获取当前 checkbox 的 value 值，即 name 属性的值或者自定义的 value 值（如果有的话）
			console.log(data.othis); // 获取当前 checkbox 的 jQuery 对象
		});
		form.render();
	});


$(function() {
	var tempPos = '';
	$('li').arrangeable({
		dragSelector: '.table_move',
		callback:function(e){
			var temparr = [];
			$('.table_one').each(function(index,item){
				var tempObj = {};
				tempObj.category_id = item.getAttribute('data-cateid');
				tempObj.sort = index;
				temparr.push(tempObj)
			})
			setTimeout(function(){
				$.ajax({
					url: ns.url("cardservice://shop/servicecategory/modifySort"),
					data: {category_sort_array : JSON.stringify(temparr)},
					dataType: 'JSON',
					type: 'POST',
					async: false,
					success: function (res) {
						layer.msg(res.message);
					}
				});
			},100);
		}
	});
	$('.table_two').arrangeable({
		dragSelector: '.table_moves',
		callback:function(e){
			var temparrs = [];
			$('.table_two').each(function(index,item){
				var tempObjs = {};
				tempObjs.category_id = item.getAttribute('data-cateid');
				tempObjs.sort = index;
				temparrs.push(tempObjs)
			});
			setTimeout(function(){
				$.ajax({
					url: ns.url("cardservice://shop/servicecategory/modifySort"),
					data: {category_sort_array : JSON.stringify(temparrs)},
					dataType: 'JSON',
					type: 'POST',
					async: false,
					success: function (res) {
						layer.msg(res.message);
					}
				});
			},100);
		}
	});
	$('.table_three_tr').arrangeable({
		dragSelector: '.tables_move',
		callback:function(e){
			var temparres = [];
			$('.table_three_tr').each(function(index,item){
				var tempObjes = {};
				tempObjes.category_id = item.getAttribute('data-cateid');
				tempObjes.sort = index;
				temparres.push(tempObjes)
			});
			setTimeout(function(){
				$.ajax({
					url: ns.url("cardservice://shop/servicecategory/modifySort"),
					data: {category_sort_array : JSON.stringify(temparres)},
					dataType: 'JSON',
					type: 'POST',
					async: false,
					success: function (res) {
						layer.msg(res.message);
					}
				});
			},100);
		}
	});
});
// var tempPos = '';
// bindDragSort('.table_body' ,'.table_one');
// bindDragSort('.table_two_div' ,'.table_two');
// bindDragSort('.table_three' ,'.table_tr');
// function bindDragSort(paremtElem,childElem){
// 	$(paremtElem ).DDSort({
// 	    target: childElem,
// 	    floatStyle: {
// 	        'border': '1px solid #ccc',
// 	        'background-color': '#fff'
// 	    },
// 		down:function(e){
// 			tempPos = $(this).data('sort');
// 		},
// 		up:function(e){
// 			var index = $(this).index(),self = $(this);
// 			if(index != tempPos){
// 				var temparr = [];
// 				$(childElem).each(function(index,item){
// 					var tempObj = {};
// 					tempObj.category_id = item.getAttribute('data-cateid');
// 					tempObj.sort = index;
// 					temparr.push(tempObj)
// 				})
// 				setTimeout(function(){
// 					$.ajax({
// 						url: ns.url("cardservice://shop/servicecategory/modifySort"),
// 						data: {category_sort_array : JSON.stringify(temparr)},
// 						dataType: 'JSON',
// 						type: 'POST',
// 						async: false,
// 						success: function (res) {
// 							self.data('sort',index)
// 							layer.msg(res.message);
// 						}
// 					});
// 				},100);
// 			}
//
// 		}
// 	});
// }

$(function () {
	loadImgMagnify();  //图片放大

	//展开收齐点击事件
	$(".js-switch").click(function (event) {
        event.stopPropagation();
		var category_id = $(this).attr("data-category-id");
		var level = $(this).attr("data-level");
		var open = parseInt($(this).attr("data-open").toString());
		if(open){
			$(".goods-category-list .layui-table tr[data-category-id-"+ level+"='" + category_id + "']").hide();
			// $(this).children("img").removeClass('rotate');
			$(this).text("+");
			if(level == 1) $(this).parents('.table_tr').siblings('.table_two_div').hide();
			else if(level == 2) $(this).parents('.table_tr').siblings('.table_three').hide();
			
		}else{
			$(".goods-category-list .layui-table tr[data-category-id-"+ level+"='" + category_id + "']").show();
			$(this).text("-");
			// $(this).children("img").addClass('rotate');
			if(level == 1) $(this).parents('.table_tr').siblings('.table_two_div').show();
			else if(level == 2) $(this).parents('.table_tr').siblings('.table_three').show();
			
		}
		$(this).attr("data-open", (open ? 0 : 1));
		
	});
});

function deleteCategory(category_id) {
	layer.confirm('确认要删除该分类吗，请谨慎操作', function(index) {
		$.ajax({
			url: ns.url("cardservice://shop/servicecategory/deleteCategory"),
			data: {category_id : category_id},
			dataType: 'JSON',
			type: 'POST',
			async: false,
			success: function (res) {
				layer.msg(res.message);
				layer.close(index);
				if (res.code == 0) {
					listenerHash(); // 刷新页面
				}
			}
		});
	});
}
function addCategory() {
	location.hash = ns.hash("cardservice://shop/servicecategory/addcategory");
}
</script>
