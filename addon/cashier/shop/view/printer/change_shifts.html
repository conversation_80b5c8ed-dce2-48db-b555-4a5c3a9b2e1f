<style>
    .printer-box{display: flex;}
    .printer-box > .layui-form{flex: 1;}
    .printer-box .preview{width: 310px;margin: 0 20px;}
    .printer-box .preview .layui-card-body{margin: 20px;padding: 0 10px;border: 1px solid #ededed;-webkit-border-radius: 5px;-moz-border-radius: 5px;border-radius: 5px;}
    .printer-box .preview .layui-card-body div{font-size: 12px;color: #333;}
    .printer-box .preview .layui-card-body div ~ div{border-top: 1px dashed #ededed ;}
    .printer-box .preview .receipt-name{text-align: center;line-height: 40px;}
    .printer-box .preview .shopping-name{line-height: 40px;font-size: 16px !important;text-align: center;}
    .printer-box .preview .order-info, .printer-box .preview .goods-info, .printer-box .preview .price-info, .printer-box .preview .buyer-info, .printer-box .preview .shopping-info{padding: 8px 0;}
    .printer-box .preview .order-info span{display: block;line-height: 2.5;}
    .printer-box .preview .goods-info table{width: 100%;}
    .printer-box .preview .goods-info table tr{line-height: 2.5;}
    .printer-box .preview .goods-info table th{font-weight: normal;}
    .printer-box .preview .price-info p{display: flex;line-height: 2.5;justify-content: space-between;}
    .printer-box .preview .price-info p:first-child {font-size: 16px}
    .printer-box .preview .buyer-info span, .printer-box .preview .shopping-info span{display: block;line-height: 2;}
    .printer-box .preview .buyer-message,.printer-box .preview .merchant-message{padding: 10px 0;line-height: 1.5;}
    .preview .button-info{height: 40px;line-height: 40px;text-align: center}
    .preview .shopping-code{text-align: center;}
    .preview .shopping-code img{width: 100px;height: 100px;margin: 10px 0;}
    .commodity-type-box{display: flex;}
    .commodity-type-item{margin-right: 15px;padding: 0px 20px;display: flex;justify-content: center;align-items: center;flex-direction: column;border: 1px solid #e5e5e5;cursor: pointer;}
    .goods_code_show td{line-height: 1;padding-bottom: 5px}
</style>

<div class="layui-collapse tips-wrap">
    <div class="layui-colla-item">
        <h2 class="layui-colla-title">操作提示</h2>
        <ul class="layui-colla-content layui-show">
            <li>管理员可以在此页操作打印模板</li>
        </ul>
    </div>
</div>

<div class="printer-box">
    <div class="layui-form form-wrap">
        <div class="layui-card card-common card-brief">
            <div class="layui-card-header">
                <span class="card-title">模板信息</span>
            </div>
            <div class="layui-card-body">
                {if empty($info)}
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>模板类型：</label>
                    <div class="layui-input-block commodity-type-box">
                        {foreach $template_type as $k => $v}
                        <div class="commodity-type-item {if $type == $k} border-color{/if}" onclick="location.hash = ns.hash('printer://shop/template/add', {'type' : '{$v['type']}'})">
                            <span class="{if $type == $k} text-color{/if}">{$v['type_name']}</span>
                        </div>
                        {/foreach}
                    </div>
                </div>
                <input type="hidden" name="type_name" value="{$template_type[$type]['type_name']}">
                <input type="hidden" name="type" value="{$type}">
                {/if}
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required"></span>模板名称：</label>
                    <div class="layui-input-block">
                        <input type="text"  name="template_name" lay-verify="required" {if !empty($info)} value="{$info['template_name']}" {/if} autocomplete="off" class="layui-input len-long">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card card-common card-brief">

            <div class="layui-card-header">
                <span class="card-title">打印信息</span>
            </div>

            <div class="layui-card-body">

                <div class="layui-form-item">
                    <label class="layui-form-label">小票名称：</label>
                    <div class="layui-input-block">
                        <input type="text"  name="title" {if !empty($info)} value="{$info['title']}"{else/}  value="小票名称" {/if}   lay-verify="required" autocomplete="off" class="layui-input len-long">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">商城名称：</label>
                    <div class="layui-input-block">
                        <input type="radio" lay-filter="shop_name"  name="head" value="1" title="显示" {if (!empty($info) && $info['head'] == 1) || empty($info) } checked {/if}  autocomplete="off" class="layui-input len-long">
                        <input type="radio" lay-filter="shop_name" name="head" value="0" title="不显示" {if !empty($info) && !$info['head']} checked {/if} autocomplete="off" class="layui-input len-long">
                    </div>
                </div>

            </div>
        </div>
        {if !empty($info)}
        <input type="hidden" name="template_id" value="{$info['template_id']}">
        {/if}
        <div class="form-row">
            <button class="layui-btn" lay-submit lay-filter="save">保存</button>
            <button class="layui-btn layui-btn-primary" onclick="backPrinterTemplateList()">返回</button>
        </div>
    </div>

    <div class="preview">
        <div class="layui-card card-common card-brief">

            <div class="layui-card-header">
                <span class="card-title">预览图</span>
            </div>

            <div class="layui-card-body">
                <div class="receipt-name">小票名称</div>
                <div class="shopping-name">商城名称</div>
                <div class="order-info">
                    <span>交班员工：***</span>
                    <span>上班时间：2022-11-17 08:00:00</span>
                    <span>交班时间：2022-11-17 12:00:00</span>
                </div>
                <div class="price-info">
                    <p>
                        <span>销售</span>
                    </p>
                    <p>
                        <span>开单销售</span>
                        <span>￥100</span>
                    </p>
                    <p>
                        <span>售卡销售</span>
                        <span>￥95</span>
                    </p>
                </div>
                <div class="price-info">
                    <p>
                        <span>会员充值</span>
                    </p>
                    <p>
                        <span>会员充值</span>
                        <span>￥100</span>
                    </p>
                </div>
                <div class="price-info">
                    <p>
                        <span>应收金额</span>
                    </p>
                    <p>
                        <span>开单销售</span>
                        <span>￥100</span>
                    </p>
                    <p>
                        <span>售卡销售</span>
                        <span>￥95</span>
                    </p>
                    <p>
                        <span>会员充值</span>
                        <span>￥100</span>
                    </p>
                    <p>
                        <span>订单退款</span>
                        <span>￥100</span>
                    </p>
                </div>
                <div class="price-info">
                    <p>
                        <span>支付统计</span>
                    </p>
                    <p>
                        <span>现金收款</span>
                        <span>￥100</span>
                    </p>
                </div>
            </div>

        </div>
    </div>
</div>

<script>
    layui.use(['form'], function() {
        var form = layui.form,
            repeat_flag = false;

        form.render();

        /**
         * 表单验证
         */
        form.verify({
            time: function(value) {
                var now_time = (new Date()).getTime();
                var start_time = (new Date($("#start_time").val())).getTime();
                var end_time = (new Date(value)).getTime();
                if (now_time > end_time) {
                    return '结束时间不能小于当前时间!'
                }
                if (start_time > end_time) {
                    return '结束时间不能小于开始时间!';
                }
            },
            flnum: function(value) {
                var arrMen = value.split(".");
                var val = 0;
                if (arrMen.length == 2) {
                    val = arrMen[1];
                }
                if (val.length > 2) {
                    return '保留小数点后两位！'
                }
            },
            int: function(value) {
                if (value <= 1 || value % 1 != 0) {
                    return '请输入大于1的正整数！'
                }
            }
        });

        form.on('checkbox(shop_qrcode)', function(data){

            var value = data.elem.checked;
            if(value == true){
                $(".preview .shopping-code").removeClass("layui-hide");
                $(".qrcode_url").removeClass('layui-hide');
                $("input[name='qrcode_url']").attr("lay-verify", "required");
            }else{
                $(".preview .shopping-code").addClass("layui-hide");
                $(".qrcode_url").addClass('layui-hide');
                $("input[name='qrcode_url']").attr("lay-verify", "");
            }
        });

        /**
         * 表单提交
         */
        form.on('submit(save)', function(data){
            var field = data.field;
            if(repeat_flag) return;
            repeat_flag = true;
            var url = ns.url("printer://shop/template/add");
            if(parseInt(field.template_id) > 0 ) url = ns.url("printer://shop/template/edit");

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: url,
                data: field,
                async: false,
                success: function(res){
                    repeat_flag = false;

                    if (res.code == 0) {
                        layer.confirm('操作成功', {
                            title:'操作提示',
                            btn: ['返回列表', '{$info ? "继续编辑" : "继续添加"}'],
                            closeBtn: 0,
                            yes: function(index, layero) {
                                location.hash = ns.hash("printer://shop/template/lists");
								layer.close(index);
                            },
                            btn2: function(index, layero) {
                                listenerHash(); // 刷新页面
                                layer.close(index);
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            })
        });

        // 小票打印
        $("input[name='title']").bind("input propertychange",function(event){
            $(".preview .receipt-name").text($("input[name='title']").val());
        });

        //商城名称
        form.on('radio(shop_name)', function(data){
            if (parseInt(data.value)) $(".shopping-name").removeClass("layui-hide");
            else $(".shopping-name").addClass("layui-hide");
        });
        //商品价格
        form.on('checkbox(goods_price_show)', function(data){
            if (data.elem.checked) {
                $(".goods_price_show").removeClass("layui-hide");
                $('.goods_price_type').removeClass("layui-hide");
            }else {
                $('.goods_price_type').addClass("layui-hide");
                $(".goods_price_show").addClass("layui-hide");
            }
        });
        //商品编码
        form.on('checkbox(goods_code_show)', function(data){
            if (data.elem.checked) $(".goods_code_show").removeClass("layui-hide");
            else $(".goods_code_show").addClass("layui-hide");
        });
        //表单
        form.on('checkbox(form_show)', function(data){
            if (data.elem.checked) $(".form_show").removeClass("layui-hide");
            else $(".form_show").addClass("layui-hide");
        });
        //买家留言
        form.on('checkbox(buyer_message)', function(data){
            if(data.elem.checked)
                $(".buyer-message").removeClass("layui-hide");
            else
                $(".buyer-message").addClass("layui-hide");
        });
        //卖家留言
        form.on('checkbox(merchant_message)', function(data){
            if(data.elem.checked)
                $(".merchant-message").removeClass("layui-hide");
            else
                $(".merchant-message").addClass("layui-hide");
        });

        var buyerName = true,buyerPhone=true,buyerAddres=true;
        //买家姓名
        form.on('checkbox(buyer_name)', function(data){
            buyerName = data.elem.checked;

            if(data.elem.checked)
                $(".buyer-info .name").removeClass("layui-hide");
            else
                $(".buyer-info .name").addClass("layui-hide");

            buyerFn();
        });
        //买家手机号
        form.on('checkbox(buyer_phone)', function(data){
            buyerPhone = data.elem.checked;

            if(data.elem.checked)
                $(".buyer-info .phone").removeClass("layui-hide");
            else
                $(".buyer-info .phone").addClass("layui-hide");

            buyerFn();
        });
        //买家地址
        form.on('checkbox(buyer_addres)', function(data){
            buyerAddres= data.elem.checked;
            if(data.elem.checked)
                $(".buyer-info .addres").removeClass("layui-hide");
            else
                $(".buyer-info .addres").addClass("layui-hide");
            buyerFn();
        });

        function buyerFn() {
            if (!buyerName )
                $(".buyer-info").addClass("layui-hide");
            else
                $(".buyer-info").removeClass("layui-hide");
        }

        // 底部信息
        $("input[name='bottom']").bind("input propertychange",function(event){
            $(".preview .button-info").text($("input[name='bottom']").val());
        });
    });

    function backPrinterTemplateList() {
        location.hash = ns.hash("printer://shop/template/lists");
    }

</script>
{if !empty($info)}
<script>
    var shop_qrcode = {$info['shop_qrcode']};
    if(shop_qrcode){
        $(".preview .shopping-code").removeClass("layui-hide");
        $(".qrcode_url").removeClass('layui-hide');
        $("input[name='qrcode_url']").attr("lay-verify", "required");
    }else{
        $(".preview .shopping-code").addClass("layui-hide");
        $(".qrcode_url").addClass('layui-hide');
        $("input[name='qrcode_url']").attr("lay-verify", "");
    }

    var head = {$info['head']};
    if (head) {
        $(".shopping-name").removeClass("layui-hide");
    }else {
        $(".shopping-name").addClass("layui-hide");
    }

    var goods_price_show = {$info['goods_price_show']};
    if (goods_price_show) {
        $(".goods_price_show").removeClass("layui-hide");
        $('.goods_price_type').removeClass("layui-hide");
    }else {
        $('.goods_price_type').addClass("layui-hide");
        $(".goods_price_show").addClass("layui-hide");
    }

    var goods_code_show = {$info['goods_code_show']};
    if (goods_code_show) $(".goods_code_show").removeClass("layui-hide");
    else $(".goods_code_show").addClass("layui-hide");

    var form_show = {$info['form_show']};
    if (form_show) $(".form_show").removeClass("layui-hide");
    else $(".form_show").addClass("layui-hide");

    var buy_notes = {$info['buy_notes']};
    if(buy_notes) $(".buyer-message").removeClass("layui-hide");
    else $(".buyer-message").addClass("layui-hide");

    var seller_notes = {$info['seller_notes']};
    if(seller_notes) $(".merchant-message").removeClass("layui-hide");
    else $(".merchant-message").addClass("layui-hide");

    buyerName = {$info['buy_name']},buyerPhone={$info['buy_mobile']},buyerAddres={$info['buy_address']};
    if(buyerName) $(".buyer-info .name").removeClass("layui-hide");
    else $(".buyer-info .name").addClass("layui-hide");

    if(buyerPhone) $(".buyer-info .phone").removeClass("layui-hide");
    else $(".buyer-info .phone").addClass("layui-hide");

    if(buyerAddres) $(".buyer-info .addres").removeClass("layui-hide");
    else $(".buyer-info .addres").addClass("layui-hide");

    if (!buyerName ) $(".buyer-info").addClass("layui-hide");
    else $(".buyer-info").removeClass("layui-hide");

</script>
{/if}
