<style type="text/css">
	.tree-line{padding:10px 0;background:#ededed;margin-bottom:2px;line-height: 1.8;}
	.tree-line .layui-form{padding-left: 10px !important;}
	.tree-line .layui-form-checkbox{margin:0 10px !important;vertical-align:middle;}
	.group-tree-block .layui-table tbody tr:hover {background-color: white;}
</style>

<div class="layui-form form-wrap">

	<div class="layui-form-item">
		<label class="layui-form-label">创建用户：</label>
		<div class="layui-input-inline">
			{php}$create_user_data = json_decode($group_info['create_user_data'], true);{/php}
			{:join(' -> ', array_column($create_user_data, 'name'))}
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>角色名称：</label>
		<div class="layui-input-block">
			<input name="group_name" type="text" lay-verify="required" class="layui-input len-long" placeholder="请输入角色名称" value="{$group_info.group_name}" autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">适用门店：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				{$group_info.store_name}
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">描述：</label>
		<div class="layui-input-block">
			<textarea name ="desc" class="layui-textarea len-long" placeholder="请输入角色的相关描述" maxlength="150">{$group_info.desc}</textarea>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>设置权限：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline group-tree-block" id="tree_box"></div>
		</div>
	</div>
	
	<!-- 操作 -->
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backCashierUserGroup()">返回</button>
	</div>
	
	<!-- 隐藏域 -->
	<input name="group_id" type="text"  class="layui-input layui-hide" value="{$group_id}">
</div>

<script>
	var tree_data = JSON.parse('{:json_encode($tree_data, JSON_UNESCAPED_UNICODE)}'),
		form,
		repeat_flag = false;//防重复标识
	
    layui.use('form', function() {
        form = layui.form;
		form.render();
        
        form.on('submit(save)', function (data) {

            var obj = $("#tree_box input:checked"),
				group_array = [];
				
            for (var i = 0; i < obj.length; i++) {
                group_array.push(obj.eq(i).val());
            }
			
            data.field.menu_array = group_array.toString();
            
			if (repeat_flag) return;
            repeat_flag = true;
            
			$.ajax({
				type: "POST",
				dataType: "JSON",
                url: ns.url("cashier://shop/user/editgroup"),
                data: data.field,
                success: function (res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续编辑'],
							yes: function(index, layero) {
								location.hash = ns.hash("cashier://shop/user/group")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						})
					}else{
						layer.msg(res.message);
					}
                }
            });
        });

        form.verify({
            title: function (value) {
                if (value.length == 0) {
                    return '请输入角色名称';
                }
            }
        });
    });
	
	function backCashierUserGroup() {
		location.hash = ns.hash("cashier://shop/user/group");
	}
</script>
<script src="SHOP_JS/tree.js"></script>

