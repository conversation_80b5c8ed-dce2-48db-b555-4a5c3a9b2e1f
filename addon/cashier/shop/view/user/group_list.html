<style>
	.system-tag {padding: 3px 5px;font-size: 12px;border: 1px solid #ddd;color: #999;border-radius: 4px;margin-left: 3px;}
</style>

<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加角色</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入角色名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="group_list" lay-filter="group_list"></table>

<!-- 工具栏操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		{{#  if(d.keyword != ''){ }}
		<span class="cursor">系统角色不可编辑</span>
		{{#  }else if(curr_user_group_ids.indexOf(d.group_id) > -1){ }}
		<span class="cursor">所属角色不可编辑</span>
		{{#  }else{ }}
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
		{{#  } }}
	</div>
</script>

<script>
	var curr_user_group_ids = {:json_encode($curr_user_group_ids)};
	layui.use('form', function() {
		var table,
			form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		/**
		 * 加载表格
		 */
		table = new Table({
			elem: '#group_list',
			url: ns.url("cashier://shop/user/group"), //数据接口
			cols: [
				[{
					field: 'group_name',
					title: '角色名称',
					width: '20%',
					unresize: 'false',
					templet: function (data) {
						var h = data.group_name;
						if (data.keyword) h += '<span class="system-tag">系统</span>'
						return h;
					}
				}, {
					field: 'desc',
					title: '描述',
					width: '40%',
					unresize: 'false',
				},{
					field: 'store_name',
					title: '适用门店',
					width: '12%',
					unresize: 'false',
				},{
					field: 'create_user_data',
					title: '创建用户',
					width: '8%',
					unresize: 'false',
					templet: function (data){
						let create_user_data = JSON.parse(data.create_user_data);
						if(create_user_data.length > 0){
							return '<div class="text">'+create_user_data[create_user_data.length-1].name+'</div>';
						}else{
							return '';
						}
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.hash = ns.hash("cashier://shop/user/editGroup", {"group_id": data.group_id});
					break;
				case 'delete': //删除
					deleteGroup(data.group_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteGroup(group_id) {
			layer.confirm('确定要删除该员工角色吗?', function(index) {
				if (repeat_flag) return false;
				repeat_flag = true;
				layer.close(index);
				$.ajax({
					dataType: 'JSON',
					type: 'POST',
					url: ns.url("cashier://shop/user/deleteGroup"),
					data: {group_id},
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;

						if (res.code == 0) {
							table.reload();
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});

	function add() {
		location.hash = ns.hash("cashier://shop/user/addGroup");
	}
</script>