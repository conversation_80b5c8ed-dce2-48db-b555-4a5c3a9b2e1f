(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-collectmoney-config"],{"0fc6":function(e,a,i){"use strict";i.d(a,"b",(function(){return t})),i.d(a,"c",(function(){return o})),i.d(a,"a",(function(){}));var t=function(){var e=this,a=e.$createElement,i=e._self._c||a;return i("base-page",[i("v-uni-view",{staticClass:"collect-money-config"},[i("v-uni-view",{staticClass:"common-wrap common-form fixd common-scrollbar"},[i("v-uni-view",{staticClass:"common-title"},[e._v("收款设置")]),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("优惠减现")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.reduction=a.detail.value}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"1",checked:1==e.config.reduction}}),e._v("启用")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"0",checked:0==e.config.reduction}}),e._v("关闭")],1)],1)],1)],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("积分抵扣")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.point=a.detail.value}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"1",checked:1==e.config.point}}),e._v("启用")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"0",checked:0==e.config.point}}),e._v("关闭")],1)],1)],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("积分抵扣需要平台开启，同时配置积分抵扣金额比率")])],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("使用余额")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.balance=a.detail.value}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"1",checked:1==e.config.balance}}),e._v("启用")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"0",checked:0==e.config.balance}}),e._v("关闭")],1)],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.config.balance,expression:"config.balance == 1"}]},[i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("余额使用安全验证")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.balance_safe=a.detail.value}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"1",checked:1==e.config.balance_safe}}),e._v("启用")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"0",checked:0==e.config.balance_safe}}),e._v("关闭")],1)],1)],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("关闭之后直接使用余额进行抵扣，无需会员验证")])],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.config.balance_safe,expression:"config.balance_safe == 1"}],staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("手机号验证")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.sms_verify=a.detail.value}}},[i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"1",checked:1==e.config.sms_verify}}),e._v("启用")],1),i("v-uni-label",{staticClass:"radio form-radio-item"},[i("v-uni-radio",{attrs:{value:"0",checked:0==e.config.sms_verify}}),e._v("关闭")],1)],1)],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("使用余额安全验证时是否可以使用短信验证码验证")])],1)],1),i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-label",{staticClass:"form-label"},[e._v("收款方式")]),i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-checkbox-group",{staticClass:"form-checkbox-group",on:{change:function(a){arguments[0]=a=e.$handleEvent(a),e.config.pay_type=a.detail.value}}},[i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:"third",checked:-1!=e.config.pay_type.indexOf("third")}}),e._v("付款码支付")],1),i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:"cash",checked:-1!=e.config.pay_type.indexOf("cash")}}),e._v("现金支付")],1),i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:"own_wechatpay",checked:-1!=e.config.pay_type.indexOf("own_wechatpay")}}),e._v("个人微信")],1),i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:"own_alipay",checked:-1!=e.config.pay_type.indexOf("own_alipay")}}),e._v("个人支付宝")],1),i("v-uni-label",{staticClass:"form-checkbox-item"},[i("v-uni-checkbox",{attrs:{value:"own_pos",checked:-1!=e.config.pay_type.indexOf("own_pos")}}),e._v("个人POS刷卡")],1)],1)],1),i("v-uni-text",{staticClass:"form-word-aux-line"},[e._v("付款码支付：扫描会员微信或支付宝付款码进行收款")])],1),i("v-uni-view",{staticClass:"common-btn-wrap"},[i("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.saveFn.apply(void 0,arguments)}}},[e._v("保存")])],1)],1)],1)],1)},o=[]},"11b9":function(e,a,i){"use strict";var t=i("fe68"),o=i.n(t);o.a},"3c2c":function(e,a,i){"use strict";i("6a54"),Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,i("d4b5");var t=i("2aef"),o={data:function(){return{config:{reduction:1,point:1,balance:1,balance_safe:0,sms_verify:0,pay_type:["third","cash","own_wechatpay","own_alipay","own_pos"]},isRepeat:!1}},onLoad:function(){this.getData()},onShow:function(){},methods:{getData:function(){var e=this;(0,t.getCollectMoneyConfig)().then((function(a){a.code>=0&&(e.config=a.data)}))},saveFn:function(){var e=this;if(this.config.pay_type.length){if(!this.isRepeat){this.isRepeat=!0;var a=this.$util.deepClone(this.config);a.pay_type=JSON.stringify(a.pay_type),(0,t.setCollectMoneyConfig)(a).then((function(a){e.isRepeat=!1,a.code>=0?e.$util.showToast({title:"设置成功"}):e.$util.showToast({title:a.message})}))}}else this.$util.showToast({title:"至少需启用一种收款方式"})}}};a.default=o},6271:function(e,a,i){"use strict";i.r(a);var t=i("3c2c"),o=i.n(t);for(var n in t)["default"].indexOf(n)<0&&function(e){i.d(a,e,(function(){return t[e]}))}(n);a["default"]=o.a},"92b6":function(e,a,i){"use strict";i.r(a);var t=i("0fc6"),o=i("6271");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(a,e,(function(){return o[e]}))}(n);i("11b9");var c=i("828b"),r=Object(c["a"])(o["default"],t["b"],t["c"],!1,null,"7e4140e0",null,!1,t["a"],void 0);a["default"]=r.exports},a48b:function(e,a,i){var t=i("c86c");a=t(!1),a.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-7e4140e0]{display:none}\r\n/* 收银台相关 */uni-text[data-v-7e4140e0],\r\nuni-view[data-v-7e4140e0]{font-size:.14rem}body[data-v-7e4140e0]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-7e4140e0]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-7e4140e0]::-webkit-scrollbar-button{display:none}body[data-v-7e4140e0]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-7e4140e0]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-7e4140e0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-7e4140e0]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-7e4140e0]{color:var(--primary-color)!important}.collect-money-config[data-v-7e4140e0]{position:relative}.collect-money-config .common-btn-wrap[data-v-7e4140e0]{position:absolute;left:0;bottom:0;right:0;padding:.24rem .2rem}.collect-money-config .common-btn-wrap .screen-btn[data-v-7e4140e0]{margin:0}.collect-money-config .common-wrap.fixd[data-v-7e4140e0]{padding:%?30?%;height:calc(100vh - .4rem);overflow-y:auto;box-sizing:border-box}.collect-money-config .form-input[data-v-7e4140e0]{font-size:.16rem}.collect-money-config .form-input-inline.btn[data-v-7e4140e0]{height:.37rem;line-height:.35rem;box-sizing:border-box;border:.01rem solid #e6e6e6;text-align:center;cursor:pointer}.collect-money-config .common-title[data-v-7e4140e0]{font-size:.18rem;margin-bottom:.2rem}.collect-money-config .common-form .common-form-item .form-label[data-v-7e4140e0]{width:1.5rem}.collect-money-config .common-form .common-form-item .form-word-aux-line[data-v-7e4140e0]{margin-left:1.5rem}',""]),e.exports=a},fe68:function(e,a,i){var t=i("a48b");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);var o=i("967d").default;o("88566e50",t,!0,{sourceMap:!1,shadowMode:!1})}}]);