(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{"25ed":function(e,r,a){e.exports=a.p+"static/login_bg.png"},"37fa":function(e,r,a){"use strict";a.r(r);var t=a("d295"),n=a("faef");for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(r,e,(function(){return n[e]}))}(i);a("6abe");var o=a("828b"),s=Object(o["a"])(n["default"],t["b"],t["c"],!1,null,"f834460e",null,!1,t["a"],void 0);r["default"]=s.exports},"52a7":function(e,r,a){a("23f4"),a("7d2f"),a("5c47"),a("9c4e"),a("ab80"),a("0506"),a("64aa"),a("5ef2"),e.exports={error:"",check:function(e,r){for(var a=0;a<r.length;a++){if(!r[a].checkType)return!0;if(!r[a].name)return!0;if(!r[a].errorMsg)return!0;if(!e[r[a].name])return this.error=r[a].errorMsg,!1;switch(r[a].checkType){case"custom":if("function"==typeof r[a].validate&&!r[a].validate(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"required":var t=new RegExp("/[S]+/");if(t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"string":t=new RegExp("^.{"+r[a].checkRule+"}$");if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"int":t=new RegExp("^(-[1-9]|[1-9])[0-9]{"+r[a].checkRule+"}$");if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"between":if(!this.isNumber(e[r[a].name]))return this.error=r[a].errorMsg,!1;var n=r[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[a].name]>n[1]||e[r[a].name]<n[0])return this.error=r[a].errorMsg,!1;break;case"betweenD":t=/^-?[1-9][0-9]?$/;if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;n=r[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[a].name]>n[1]||e[r[a].name]<n[0])return this.error=r[a].errorMsg,!1;break;case"betweenF":t=/^-?[0-9][0-9]?.+[0-9]+$/;if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;n=r[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[a].name]>n[1]||e[r[a].name]<n[0])return this.error=r[a].errorMsg,!1;break;case"same":if(e[r[a].name]!=r[a].checkRule)return this.error=r[a].errorMsg,!1;break;case"notsame":if(e[r[a].name]==r[a].checkRule)return this.error=r[a].errorMsg,!1;break;case"email":t=/^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"phoneno":t=/^\d{11}$/;if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"zipcode":t=/^[0-9]{6}$/;if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"reg":t=new RegExp(r[a].checkRule);if(!t.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"in":if(-1==r[a].checkRule.indexOf(e[r[a].name]))return this.error=r[a].errorMsg,!1;break;case"notnull":if(0==e[r[a].name]||void 0==e[r[a].name]||null==e[r[a].name]||e[r[a].name].length<1)return this.error=r[a].errorMsg,!1;break;case"lengthMin":if(e[r[a].name].length<r[a].checkRule)return this.error=r[a].errorMsg,!1;break;case"lengthMax":if(e[r[a].name].length>r[a].checkRule)return this.error=r[a].errorMsg,!1;break;case"numberId":if(!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(e[r[a].name]))return this.error=r[a].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"6abe":function(e,r,a){"use strict";var t=a("d3cb"),n=a.n(t);n.a},"8f16":function(e,r,a){"use strict";a("6a54");var t=a("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,a("5c47"),a("a1c1");var n=t(a("52a7")),i=a("0f8c"),o={data:function(){return{passShow:!1,formData:{username:"",password:"",vercode:"",dynacode:"",key:""},captcha:{id:"",img:""},isSub:!1}},onLoad:function(){this.loadThemeColor(),this.getCaptchaFn()},methods:{getCaptchaFn:function(){var e=this;(0,i.getCaptcha)(this.captcha.id).then((function(r){r.code>=0&&(e.captcha=r.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))}))},loginFn:function(){var e=this,r={username:this.formData.username,password:this.formData.password};if(""!=this.captcha.id&&(r.captcha_id=this.captcha.id,r.captcha_code=this.formData.vercode),this.verify(r)){if(this.isSub)return;this.isSub=!0,uni.showLoading({}),(0,i.login)(r).then((function(r){uni.hideLoading(),r.code>=0?(uni.setStorageSync("siteId",r.data.site_id),e.$store.commit("app/setGlobalStoreId",r.data.store_id),uni.setStorage({key:"cashierToken",data:r.data.token,success:function(){e.$store.dispatch("app/getStoreInfoFn"),e.$store.dispatch("app/getUserInfoFn"),e.$store.dispatch("app/getUserGroupFn")}})):(e.isSub=!1,e.getCaptchaFn(),e.$util.showToast({title:r.message}))}))}},verify:function(e){var r=n.default.check(e,[{name:"username",checkType:"required",errorMsg:"请输入用户名"},{name:"password",checkType:"required",errorMsg:"请输入密码"},{name:"captcha_code",checkType:"required",errorMsg:"请输入验证码"}]);return!!r||(this.$util.showToast({title:n.default.error}),!1)}},watch:{menu:function(e){e&&e.length&&(e[0].path?this.$util.redirectTo(e[0].path,{},"redirectTo"):this.$util.redirectTo("/pages/stat/index",{},"redirectTo"))}}};r.default=o},b424:function(e,r,a){var t=a("c86c"),n=a("2ec5"),i=a("25ed");r=t(!1);var o=n(i);r.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-f834460e]{display:none}\r\n/* 收银台相关 */uni-text[data-v-f834460e],\r\nuni-view[data-v-f834460e]{font-size:.14rem}body[data-v-f834460e]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-f834460e]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-f834460e]::-webkit-scrollbar-button{display:none}body[data-v-f834460e]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-f834460e]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-f834460e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-f834460e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-f834460e]{color:var(--primary-color)!important}uni-page-body[data-v-f834460e]{width:100vw;height:100vh;background:#f7f8fa}body.?%PAGE?%[data-v-f834460e]{background:#f7f8fa}.container[data-v-f834460e]{position:absolute;left:0;top:0;bottom:0;right:0;background-size:cover;background-repeat:no-repeat;background-position:50%;display:flex;align-items:center;justify-content:center;background-image:url('+o+")}.container .login-bg[data-v-f834460e]{margin-right:1.5rem;width:5.9rem;height:4.3rem}.container .login-bg uni-image[data-v-f834460e]{width:5.9rem;height:4.3rem}.login-wrap[data-v-f834460e]{padding:.3rem 0;width:3.48rem;background-color:#fff;box-shadow:0 .01rem .09rem 0 rgba(15,92,251,.12);border-radius:.05rem}.login-wrap .header[data-v-f834460e]{text-align:center}.login-wrap .header uni-image[data-v-f834460e]{width:2.13rem;height:.78rem}.login-wrap .header .title[data-v-f834460e]{font-weight:700;font-size:.25rem;margin-top:.1rem;color:#222}.login-wrap .header .desc[data-v-f834460e]{font-size:.16rem;color:#969799;margin-top:.1rem}.form-wrap[data-v-f834460e]{display:flex;flex-direction:column;align-items:center;margin:.2rem 0}.form-wrap .input-wrap[data-v-f834460e]{width:2.85rem;padding:0 .1rem;border-bottom:.01rem solid #e6e6e6;margin-top:.25rem;display:flex;align-items:center;box-sizing:border-box;border-radius:.05rem}.form-wrap .input-wrap .iconfont[data-v-f834460e]{margin-right:.1rem;font-size:.2rem;color:#46586e}.form-wrap .input-wrap uni-input[data-v-f834460e]{flex:1;height:.4rem;line-height:.4rem;font-size:.14rem}.form-wrap .input-wrap .placeholder[data-v-f834460e]{font-size:.14rem;color:#999;font-weight:500}.form-wrap .input-wrap .send-code[data-v-f834460e]{color:var(--primary-color);cursor:pointer}.form-wrap .input-wrap .send-code.disabled[data-v-f834460e]{cursor:not-allowed;color:#999}.form-wrap .input-wrap .captcha[data-v-f834460e]{width:.8rem;height:.3rem;cursor:pointer}.form-wrap .input-wrap .pass-show[data-v-f834460e]{font-size:.14rem;cursor:pointer;color:#333}.form-wrap .login-btn[data-v-f834460e]{width:2.85rem;margin-top:.3rem;height:.4rem;line-height:.4rem;border-radius:.05rem}",""]),e.exports=r},d295:function(e,r,a){"use strict";a.d(r,"b",(function(){return n})),a.d(r,"c",(function(){return i})),a.d(r,"a",(function(){return t}));var t={pageMeta:a("7854").default},n=function(){var e=this,r=e.$createElement,a=e._self._c||r;return a("v-uni-view",[a("page-meta",{attrs:{"root-font-size":e.rootSize}}),a("v-uni-view",{staticClass:"container",style:e.themeColor},[a("v-uni-view",{staticClass:"login-wrap"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-view",{staticClass:"title"},[e._v("智慧零售平台登录")])],1),a("v-uni-view",{staticClass:"form-wrap"},[a("v-uni-view",{staticClass:"input-wrap"},[a("v-uni-text",{staticClass:"iconfont icona-xingzhuang2"}),a("v-uni-input",{attrs:{type:"text",placeholder:"请输入用户名","placeholder-class":"placeholder"},on:{confirm:function(r){arguments[0]=r=e.$handleEvent(r),e.loginFn.apply(void 0,arguments)}},model:{value:e.formData.username,callback:function(r){e.$set(e.formData,"username",r)},expression:"formData.username"}})],1),a("v-uni-view",{staticClass:"input-wrap"},[a("v-uni-text",{staticClass:"iconfont iconmima"}),a("v-uni-input",{directives:[{name:"show",rawName:"v-show",value:e.passShow,expression:"passShow"}],attrs:{type:"text",placeholder:"请输入密码","placeholder-class":"placeholder"},on:{confirm:function(r){arguments[0]=r=e.$handleEvent(r),e.loginFn.apply(void 0,arguments)}},model:{value:e.formData.password,callback:function(r){e.$set(e.formData,"password",r)},expression:"formData.password"}}),a("v-uni-input",{directives:[{name:"show",rawName:"v-show",value:!e.passShow,expression:"!passShow"}],attrs:{type:"password",placeholder:"请输入密码","placeholder-class":"placeholder"},on:{confirm:function(r){arguments[0]=r=e.$handleEvent(r),e.loginFn.apply(void 0,arguments)}},model:{value:e.formData.password,callback:function(r){e.$set(e.formData,"password",r)},expression:"formData.password"}}),a("v-uni-view",{staticClass:"iconfont pass-show",class:{iconyanjing5:e.passShow,iconinvisible:!e.passShow},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.passShow=!e.passShow}}})],1),a("v-uni-view",{staticClass:"input-wrap"},[a("v-uni-text",{staticClass:"iconfont iconyanzhengma"}),a("v-uni-input",{attrs:{type:"number",placeholder:"请输入验证码","placeholder-class":"placeholder",maxlength:"4"},on:{confirm:function(r){arguments[0]=r=e.$handleEvent(r),e.loginFn.apply(void 0,arguments)}},model:{value:e.formData.vercode,callback:function(r){e.$set(e.formData,"vercode",r)},expression:"formData.vercode"}}),a("v-uni-image",{staticClass:"captcha",attrs:{src:e.captcha.img},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.getCaptchaFn.apply(void 0,arguments)}}})],1),a("v-uni-button",{staticClass:"login-btn primary-btn",attrs:{type:"default"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.loginFn.apply(void 0,arguments)}}},[e._v("登录")])],1)],1)],1)],1)},i=[]},d3cb:function(e,r,a){var t=a("b424");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[e.i,t,""]]),t.locals&&(e.exports=t.locals);var n=a("967d").default;n("490215ee",t,!0,{sourceMap:!1,shadowMode:!1})},faef:function(e,r,a){"use strict";a.r(r);var t=a("8f16"),n=a.n(t);for(var i in t)["default"].indexOf(i)<0&&function(e){a.d(r,e,(function(){return t[e]}))}(i);r["default"]=n.a}}]);