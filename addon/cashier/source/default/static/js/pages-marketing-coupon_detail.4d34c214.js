(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-marketing-coupon_detail"],{"128b":function(t,e,a){var i=a("e5de");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("20352f32",i,!0,{sourceMap:!1,shadowMode:!1})},"13de":function(t,e,a){"use strict";a.r(e);var i=a("d4c2"),o=a("6963");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("1e87");var s=a("828b"),l=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"d5735674",null,!1,i["a"],void 0);e["default"]=l.exports},1655:function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("bf0f"),a("2797"),a("dc8a");var i=a("9d02"),o={data:function(){var t=this;return{couponsData:{coupon_type_id:"",coupon_name:"",type:"reward",money:"",discount:"",discount_limit:"",at_least:"",is_show:1,count:"",max_fetch:"",image:"",validity_type:0,end_time:this.$util.timeFormat(Date.parse(new Date)/1e3),fixed_term:0,goods_type:1,lead_count:0,used_count:0,use_channel:"",use_store:"",use_store_list:[],goods_list:[]},loading:!1,cols:[{field:"account_data",width:20,title:"会员信息",align:"left",templet:function(e){var a=t.$util.img(e.headimg),i='\n\t\t\t\t\t\t\t<view class="member-content flex">\n\t\t\t\t\t\t\t\t<image class="member-img" src="'.concat(a,'" mode="aspectFit"/>\n                                <view class="flex flex-col justify-between">\n                                    <text class="member-nickname multi-hidden">').concat(e.nickname,'</text>\n                                    <text class="member-mobile multi-hidden">').concat(e.mobile,"</text>\n                                </view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t");return i}},{field:"coupon_name",width:15,title:"优惠券",align:"left"},{title:"类型",width:10,unresize:"false",templet:function(t){return"reward"==t.type?"满减券":"折扣券"}},{field:"get_type_name",width:15,title:"获取方式",align:"left"},{title:"状态",width:10,unresize:"false",templet:function(t){var e="";switch(t.state){case 1:e="已领取";break;case 2:e="已使用";break;case 3:e="已过期";break}return e}},{title:"领取时间",width:15,unresize:"false",templet:function(e){return t.$util.timeFormat(Date.parse(new Date(e.fetch_time)))}},{title:"使用时间",width:15,templet:function(e){return e.use_time?t.$util.timeFormat(Date.parse(new Date(e.use_time))):""}}],statusList:[{value:"",label:"全部"},{value:"1",label:"已领取"},{value:"2",label:"已使用"},{value:"3",label:"已过期"}],option:{page_size:9,coupon_type_id:"",state:""},goodsListCols:[{field:"goods_name",width:60,title:"商品名称",align:"left"},{title:"价格",width:20,unresize:"false",templet:function(t){return t.price||"0.00"}},{title:"库存",width:20,unresize:"false",templet:function(t){return t.goods_stock||0}}]}},onLoad:function(t){t.coupon_type_id&&(this.couponsData.coupon_type_id=t.coupon_type_id,this.option.coupon_type_id=t.coupon_type_id,this.getData(t.coupon_type_id))},methods:{getData:function(t){var e=this;this.loading=!0,(0,i.getCouponDetail)(t).then((function(t){var a=t.data;t.code>=0&&a&&Object.keys(e.couponsData).forEach((function(t){e.couponsData[t]=a.info[t],"end_time"==t&&(e.couponsData[t]=e.couponsData.end_time=e.$util.timeFormat(Date.parse(new Date(a.info[t]))))})),e.loading=!1}))},queryRecord:function(t){this.option.state=t,this.$refs.couponListTable.load({page:1})},backFn:function(){this.$util.redirectTo("/pages/marketing/coupon_list")}}};e.default=o},"1e87":function(t,e,a){"use strict";var i=a("128b"),o=a.n(i);o.a},6963:function(t,e,a){"use strict";a.r(e);var i=a("add7"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},"9d02":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addCoupon=function(t){return o.default.post("/coupon/storeapi/coupon/add",{data:t})},e.closeCoupon=function(t){return o.default.post("/coupon/storeapi/coupon/close",{data:{coupon_type_id:t}})},e.deleteCoupon=function(t){return o.default.post("/coupon/storeapi/coupon/delete",{data:{coupon_type_id:t}})},e.editCoupon=function(t){return o.default.post("/coupon/storeapi/coupon/edit",{data:t})},e.getCouponDetail=function(t){return o.default.post("/coupon/storeapi/coupon/detail",{data:{coupon_type_id:t}})},e.getReceiveCouponPageList=function(t){return o.default.post("/coupon/storeapi/membercoupon/getReceiveCouponPageList",{data:{coupon_type_id:t}})};var o=i(a("4e01"))},add7:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("1655")),n=i(a("43ca")),s={components:{uniDataTable:n.default},mixins:[o.default]};e.default=s},d4c2:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"coupons-detail"},[t.loading?t._e():a("v-uni-view",{staticClass:"common-wrap common-form fixd common-scrollbar"},[a("v-uni-view",{staticClass:"title-back flex items-center cursor-pointer",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backFn.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"iconfont iconqianhou1"}),a("v-uni-text",{staticClass:"left"},[t._v("返回")]),a("v-uni-text",{staticClass:"content"},[t._v("|")]),a("v-uni-text",[t._v("优惠券详情")])],1),a("v-uni-view",{staticClass:"common-title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"flex flex-wrap"},[a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("优惠券名称：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s(t.couponsData.coupon_name))])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("优惠券类型：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s("reward"==t.couponsData.type?"满减":"折扣"))])],1),"reward"==t.couponsData.type?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("优惠面额：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v("￥"+t._s(t.couponsData.money)+"元")])],1):a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("优惠券折扣：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s(t.couponsData.discount)+"折")])],1),"discount"==t.couponsData.type&&0!=t.couponsData.discount_limit?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("最多优惠：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v("￥"+t._s(t.couponsData.discount_limit)+"元")])],1):t._e(),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("使用门槛：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v("￥"+t._s(t.couponsData.at_least)+"元")])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("是否允许直接领取：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s(1===t.couponsData.is_show?"是":"否"))])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("发放数量：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s(0==t.couponsData.is_show||-1==t.couponsData.count?"无限制":t.couponsData.count+"张"))])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("最大领取数量：")]),0==t.couponsData.is_show||0==t.couponsData.max_fetch?a("v-uni-view",{staticClass:"form-input-inline"},[t._v("无领取限制")]):a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s(t.couponsData.max_fetch)+"张/人")])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("有效期：")]),0==t.couponsData.validity_type?a("v-uni-view",{staticClass:"form-input-inline radio-list"},[t._v(t._s(t.couponsData.end_time))]):1==t.couponsData.validity_type?a("v-uni-view",{staticClass:"form-input-inline radio-list"},[t._v("领取后 "+t._s(t.couponsData.fixed_term)+"天 有效")]):a("v-uni-view",{staticClass:"form-input-inline radio-list"},[t._v("长期有效")])],1),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("使用渠道：")]),a("v-uni-view",{staticClass:"form-input-inline"},[t._v(t._s("all"===t.couponsData.use_channel?"线上线下使用":"online"===t.couponsData.use_channel?"线上使用":"线下使用"))])],1),"online"!=t.couponsData.use_channel?a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("适用门店：")]),a("v-uni-view",{staticClass:"form-input-inline truncate"},["all"===t.couponsData.use_store?a("v-uni-text",[t._v("全部门店")]):a("v-uni-text",{attrs:{title:t.couponsData.use_store_list.map((function(t){return t.store_name})).join("、")}},[t._v(t._s(t.couponsData.use_store_list.map((function(t){return t.store_name})).join("、")))])],1)],1):t._e(),a("v-uni-view",{staticClass:"common-form-item"},[a("v-uni-label",{staticClass:"form-label"},[t._v("活动商品：")]),a("v-uni-view",{staticClass:"form-input-inline radio-list"},[t._v(t._s(1==t.couponsData.goods_type?"全部商品参与":2==t.couponsData.goods_type?"指定商品参与":"指定不参与商品"))])],1),a("v-uni-view",{staticClass:"common-form-item coupons-img"},[a("v-uni-label",{staticClass:"form-label"},[t._v("优惠券图片：")]),a("v-uni-view",{staticClass:"form-input-inline upload-box"},[a("v-uni-view",{staticClass:"upload"},[a("v-uni-image",{attrs:{src:t.$util.img(t.couponsData.image),mode:"heightFix"}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"common-title"},[t._v("数据统计")]),a("v-uni-view",{staticClass:"data flex flex-wrap"},[a("v-uni-view",{staticClass:"data-item"},[a("v-uni-view",{staticClass:"title"},[t._v("发放数")]),a("v-uni-view",{staticClass:"content"},[t._v(t._s(t.couponsData.count||0))])],1),a("v-uni-view",{staticClass:"data-item"},[a("v-uni-view",{staticClass:"title"},[t._v("领取数")]),a("v-uni-view",{staticClass:"content"},[t._v(t._s(t.couponsData.lead_count||0))])],1),a("v-uni-view",{staticClass:"data-item"},[a("v-uni-view",{staticClass:"title"},[t._v("使用数")]),a("v-uni-view",{staticClass:"content"},[t._v(t._s(t.couponsData.used_count||0))])],1)],1),a("v-uni-view",{staticClass:"common-title mt-20"},[t._v("领取记录")]),a("v-uni-view",{staticClass:"record flex"},[t._l(t.statusList,(function(e){return[a("v-uni-view",{class:{active:e.value==t.option.state},attrs:{type:"default"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.queryRecord(e.value)}}},[t._v(t._s(e.label))])]}))],2),a("uniDataTable",{ref:"couponListTable",attrs:{url:"/coupon/storeapi/membercoupon/getReceiveCouponPageList",option:t.option,cols:t.cols}}),1!=t.couponsData.goods_type?[a("v-uni-view",{staticClass:"common-title mt-20"},[t._v(t._s(2==t.couponsData.goods_type?"指定商品参与":"指定不参与商品"))]),a("uniDataTable",{attrs:{cols:t.goodsListCols,data:t.couponsData.goods_list,classType:!0}})]:t._e()],2)],1)],1)},o=[]},e5de:function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-d5735674]{display:none}\r\n/* 收银台相关 */uni-text[data-v-d5735674],\r\nuni-view[data-v-d5735674]{font-size:.14rem}body[data-v-d5735674]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-d5735674]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-d5735674]::-webkit-scrollbar-button{display:none}body[data-v-d5735674]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-d5735674]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-d5735674]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-d5735674]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-d5735674]{color:var(--primary-color)!important}.coupons-detail[data-v-d5735674]{position:relative;height:calc(100vh - .4rem);background-color:#fff}.coupons-detail .common-wrap.fixd[data-v-d5735674]{padding:%?30?%;height:100%;overflow-y:auto;padding-bottom:.85rem!important;box-sizing:border-box}.coupons-detail .common-wrap.fixd .form-label[data-v-d5735674]{width:1.7rem!important;height:.3rem!important;line-height:.3rem!important;padding:0 .15rem}.coupons-detail .common-wrap.fixd .common-form-item[data-v-d5735674]{width:33.333%;height:.3rem;margin-bottom:0}.coupons-detail .common-wrap.fixd .common-form-item .form-input-inline[data-v-d5735674]{border-width:0!important;width:calc(100% - 1.8rem)}.coupons-detail .common-wrap.fixd .common-form-item.coupons-img[data-v-d5735674]{-webkit-box-align:start;-ms-flex-align:start;-webkit-align-items:flex-start;align-items:flex-start;width:100%!important;height:auto}.coupons-detail .common-wrap.fixd .common-form-item.coupons-img .upload-box[data-v-d5735674]{border:.01rem dashed #e6e6e6!important;width:2.5rem!important;height:1.2rem!important;display:flex;align-items:center;justify-content:center}.coupons-detail .common-wrap.fixd .common-form-item.coupons-img .upload-box .upload[data-v-d5735674]{text-align:center;color:#5a5a5a}.coupons-detail .common-wrap.fixd .common-form-item.coupons-img .upload-box .upload .iconfont[data-v-d5735674]{font-size:.3rem}.coupons-detail .common-wrap.fixd .common-form-item.coupons-img .upload-box .upload uni-image[data-v-d5735674]{max-width:100%;height:1.2rem!important}.coupons-detail .common-title[data-v-d5735674]{font-size:.18rem;margin-bottom:.2rem}.coupons-detail .common-title.mt-20[data-v-d5735674]{margin-top:.2rem}.coupons-detail .data[data-v-d5735674]{margin-top:.1rem}.coupons-detail .data .data-item[data-v-d5735674]{width:33.333%;text-align:center}.coupons-detail .data .data-item .title[data-v-d5735674]{color:#909399;margin-bottom:.2rem}.coupons-detail .data .data-item .content[data-v-d5735674]{font-size:.26rem;color:#303133}.coupons-detail[data-v-d5735674] .member-img{width:.6rem;height:.6rem;margin-right:.1rem;-ms-flex-negative:0;-webkit-flex-shrink:0;flex-shrink:0}.coupons-detail[data-v-d5735674] .member-nickname{width:2.3rem}.coupons-detail[data-v-d5735674] .member-mobile{width:2.3rem}.coupons-detail .record[data-v-d5735674]{margin-bottom:.2rem}.coupons-detail .record uni-view[data-v-d5735674]{width:1rem;height:.35rem;line-height:.35rem;text-align:center;font-size:.14rem;border:.01rem solid #e6e6e6;border-left-width:0;transition:all .3s;cursor:pointer}.coupons-detail .record uni-view[data-v-d5735674]:hover, .coupons-detail .record uni-view.active[data-v-d5735674]{border-color:var(--primary-color);color:var(--primary-color);background-color:var(--primary-color-light-9);box-shadow:-.01rem 0 0 0 var(--primary-color)}.coupons-detail .record uni-view[data-v-d5735674]:first-child{border-left-width:.01rem;box-shadow:none}',""]),t.exports=e}}]);