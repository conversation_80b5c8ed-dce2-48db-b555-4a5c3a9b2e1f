(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-orderlist"],{"03fa":function(e,t,i){"use strict";i.r(t);var o=i("af3f"),r=i("7f870");for(var a in r)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(a);i("121b");var s=i("828b"),n=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,"0dfa2d4c",null,!1,o["a"],void 0);t["default"]=n.exports},"10be":function(e,t,i){"use strict";var o=i("cd74"),r=i.n(o);r.a},"121b":function(e,t,i){"use strict";var o=i("8911"),r=i.n(o);r.a},"16dc":function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getExpressCompanyList=function(){return r.default.post("/cashier/storeapi/order/expresscompany")},t.getOrderDeliverList=function(e){return r.default.post("/cashier/storeapi/order/deliverlist",{data:e})},t.getOrderDetail=function(e){return r.default.post("/cashier/storeapi/cashierorder/detail",{data:e})},t.getOrderInfoById=function(e){return r.default.post("/cashier/storeapi/order/info",{data:{order_id:e}})},t.getOrderList=function(e){return r.default.post("/cashier/storeapi/cashierorder/lists",{data:e})},t.getorderCondition=function(){return r.default.post("/cashier/storeapi/order/condition")},t.orderAdjustPrice=function(e){return r.default.post("/cashier/storeapi/cashierorder/adjustPrice",{data:e})},t.orderClose=function(e){return r.default.post("/cashier/storeapi/order/close",{data:e})},t.orderExpressDelivery=function(e){return r.default.post("/cashier/storeapi/order/expressdelivery",{data:e})},t.orderLocalDelivery=function(e){return r.default.post("/cashier/storeapi/order/localdelivery",{data:e})},t.orderPrintTicket=function(e){var t={order_id:e},i=a.default.getLocalConfig();return t.printer_ids="all"==i.printerSelectType?"all":i.printerSelectIds.toString(),r.default.post("/cashier/storeapi/cashierorder/printticket",{data:t})},t.orderRemark=function(e){return r.default.post("/cashier/storeapi/cashierorder/orderRemark",{data:e})},t.orderStoreDelivery=function(e){return r.default.post("/cashier/storeapi/order/storedelivery",{data:{order_id:e}})},i("c9b5"),i("bf0f"),i("ab80");var r=o(i("4e01")),a=o(i("a07f"))},"1bf0":function(e,t,i){"use strict";var o=i("39fd"),r=i.n(o);r.a},3523:function(e,t,i){"use strict";i.r(t);var o=i("6e0b"),r=i("53f4");for(var a in r)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(a);i("1bf0");var s=i("828b"),n=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,"3387bb78",null,!1,o["a"],void 0);t["default"]=n.exports},"39fd":function(e,t,i){var o=i("48fc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var r=i("967d").default;r("2cfa7e01",o,!0,{sourceMap:!1,shadowMode:!1})},"3db4":function(e,t,i){"use strict";i.r(t);var o=i("4abe"),r=i("62a7");for(var a in r)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(a);i("10be");var s=i("828b"),n=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,"16008ec4",null,!1,o["a"],void 0);t["default"]=n.exports},"48fc":function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3387bb78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3387bb78],\r\nuni-view[data-v-3387bb78]{font-size:.14rem}body[data-v-3387bb78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3387bb78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3387bb78]::-webkit-scrollbar-button{display:none}body[data-v-3387bb78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3387bb78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3387bb78]{color:var(--primary-color)!important}.uni-select-lay[data-v-3387bb78]{position:relative;z-index:999;box-sizing:border-box}.uni-select-lay .uni-select-input[data-v-3387bb78]{opacity:0;position:absolute;z-index:-111}.uni-select-lay .uni-select-lay-select[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:relative;z-index:3;height:.32rem;padding:0 .3rem 0 .1rem;box-sizing:border-box;border-radius:.02rem;border:.01rem solid #e5e5e5;display:flex;align-items:center;font-size:.14rem;color:#999}.uni-select-lay .uni-select-lay-select .uni-disabled[data-v-3387bb78]{position:absolute;left:0;width:100%;height:100%;z-index:19;cursor:no-drop;background:hsla(0,0%,100%,.5)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close[data-v-3387bb78]{position:absolute;right:.35rem;top:0;height:100%;width:.15rem;display:flex;align-items:center;justify-content:center;z-index:3;cursor:pointer}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]{position:relative;background:#fff;width:.13rem;height:.13rem;border-radius:50%;border:.01rem solid #bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::before, .uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{content:"";position:absolute;left:20%;top:50%;height:.01rem;width:60%;-webkit-transform:rotate(45deg);transform:rotate(45deg);background-color:#bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input[data-v-3387bb78]{font-size:.14rem;color:#999;display:block;width:98%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:.3rem;box-sizing:border-box}.uni-select-lay .uni-select-lay-select .uni-select-lay-input.active[data-v-3387bb78]{color:#333}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]{cursor:pointer;position:absolute;right:0;top:0;height:100%;width:.3rem;display:flex;align-items:center;justify-content:center}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]::before{content:"";width:.01rem;height:100%;position:absolute;left:0;top:0;background-color:#e5e5e5}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon uni-text[data-v-3387bb78]{display:block;width:0;height:0;border-width:.07rem .07rem 0;border-style:solid;border-color:#bbb transparent transparent;transition:.3s}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled[data-v-3387bb78]{cursor:no-drop}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]{width:.2rem;height:.2rem;border:.02rem solid red;border-radius:50%;transition:.3s;position:relative;z-index:999}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]::after{content:"";position:absolute;top:50%;left:0;width:100%;height:.02rem;margin-top:-.01rem;background-color:red;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.uni-select-lay .uni-select-lay-select.active .uni-select-lay-icon uni-text[data-v-3387bb78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.uni-select-lay .uni-select-lay-options[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:absolute;top:calc(100% + .05rem);left:0;width:100%;max-height:2.5rem;border-radius:.02rem;border:1px solid #e5e5e5;background:#fff;padding:.05rem 0;box-sizing:border-box;z-index:9}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]{padding:0 .1rem;box-sizing:border-box;cursor:pointer;line-height:2.5;transition:.3s;font-size:.14rem}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]:hover{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.disabled[data-v-3387bb78]{color:#999;cursor:not-allowed}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]:hover{background-color:#f5f5f5}.uni-select-lay .uni-select-lay-options .nosearch[data-v-3387bb78]{font-size:.16rem;line-height:3;text-align:center;color:#666}.uni-date-mask[data-v-3387bb78]{position:fixed;bottom:0;top:0;left:0;right:0;background-color:transparent;transition-duration:.3s;z-index:8}',""]),e.exports=t},"4abe":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){}));var o=function(){var e=this.$createElement,t=this._self._c||e;return this.isShow?t("v-uni-view",{staticClass:"loading-layer",style:this.layerBackground},[t("v-uni-view",{staticClass:"loading-anim"},[t("v-uni-view",{staticClass:"box item"},[t("v-uni-view",{staticClass:"border out item color-base-border-top color-base-border-left"})],1)],1)],1):this._e()},r=[]},"53f4":function(e,t,i){"use strict";i.r(t);var o=i("6c5f"),r=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},"5c93":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={props:{list:{type:Array,default:function(){return[]}}},data:function(){return{}},mounted:function(){},methods:{}};t.default=o},"62a7":function(e,t,i){"use strict";i.r(t);var o=i("eb39"),r=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},"6c5f":function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("64aa"),i("bf0f"),i("2797"),i("8f71"),i("4626"),i("5ac7");var o={name:"select-lay",props:{disabled:{type:Boolean,default:!1},zindex:{type:Number,default:999},options:{type:Array,default:function(){return[]}},name:{type:String,default:""},value:{type:[String,Number],default:""},placeholder:{type:String,default:"请选择"},showplaceholder:{type:Boolean,default:!0},slabel:{type:String,default:"label"},svalue:{type:String,default:"value"}},data:function(){return{active:!1,isfocus:!1,isremove:!1,ismove:!1,changevalue:"",oldvalue:"",changes:!1,changesValue:"",vlist:[],settimer:null}},mounted:function(){this.itemcheck()},watch:{value:function(){this.itemcheck()},options:function(){this.itemcheck()}},methods:{itemcheck:function(){var e=this;""!=this.value?this.options.length>0&&this.options.forEach((function(t){e.value!=t[e.svalue]||(e.oldvalue=e.changevalue=t[e.slabel])})):this.oldvalue=this.changevalue=""},select:function(){this.disabled||(this.active=!this.active,this.active?this.changes=!1:this.changevalue=this.oldvalue)},unifocus:function(){this.disabled||(this.active=!0,this.changes=!1,this.isfocus=!0)},uniblur:function(){var e=this;this.isfocus=!1,setTimeout((function(){e.isremove||e.ismove?(e.isremove=!1,e.ismove=!1):(e.changevalue=e.oldvalue,e.isremove=!1,e.active=!1)}),153)},movetouch:function(){var e=this;setTimeout((function(){e.isfocus?e.ismove=!1:e.ismove||(e.ismove=!0)}),100)},selectmove:function(){var e=this;setTimeout((function(){e.isfocus?e.ismove=!1:e.ismove||(e.ismove=!0)}),100)},removevalue:function(){this.isremove=!0,this.changes=!1,this.changevalue=""},intchange:function(){var e=this;""!=this.changevalue?this.oldvalue!=this.changevalue&&(this.vlist=[],this.changes=!0,this.changesValue="正在搜索...",this.settimer&&clearTimeout(this.settimer),this.settimer=setTimeout((function(){e.vlist=e.options.filter((function(t){return t[e.slabel].includes(e.changevalue)})),0===e.vlist.length&&(e.changesValue="暂无匹配内容！")}),600)):this.changes=!1},selectitem:function(e,t){if(t&&t.disabled)return!1;this.changevalue=this.oldvalue,this.active=!1,this.$emit("selectitem",e,t)}}};t.default=o},"6e0b":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"uni-select-lay",style:{"z-index":e.zindex}},[i("v-uni-input",{staticClass:"uni-select-input",attrs:{type:"text",name:e.name,readonly:!0},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}}),i("v-uni-view",{staticClass:"uni-select-lay-select",class:{active:e.active}},[e.disabled?i("v-uni-view",{staticClass:"uni-disabled"}):e._e(),""!=e.changevalue&&this.active?i("v-uni-view",{staticClass:"uni-select-lay-input-close"},[i("v-uni-text",{on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.removevalue.apply(void 0,arguments)}}})],1):e._e(),i("v-uni-input",{staticClass:"uni-select-lay-input",class:{active:""!=e.changevalue&&e.changevalue!=e.placeholder},attrs:{type:"text",readonly:!0,disabled:"true",placeholder:e.placeholder},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.unifocus.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.intchange.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.uniblur.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.select.apply(void 0,arguments)}},model:{value:e.changevalue,callback:function(t){e.changevalue=t},expression:"changevalue"}}),i("v-uni-view",{staticClass:"uni-select-lay-icon",class:{disabled:e.disabled},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.select.apply(void 0,arguments)}}},[i("v-uni-text")],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}],staticClass:"uni-date-mask",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.select.apply(void 0,arguments)}}}),i("v-uni-scroll-view",{directives:[{name:"show",rawName:"v-show",value:e.active,expression:"active"}],staticClass:"uni-select-lay-options",attrs:{"scroll-y":!0},on:{scroll:function(t){arguments[0]=t=e.$handleEvent(t),e.selectmove.apply(void 0,arguments)},touchstart:function(t){arguments[0]=t=e.$handleEvent(t),e.movetouch.apply(void 0,arguments)}}},[e.changes?[e.vlist.length>0?e._l(e.vlist,(function(t,o){return i("v-uni-view",{key:o,staticClass:"uni-select-lay-item",class:{active:e.value==t[e.svalue]},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.selectitem(o,t)}}},[e._v(e._s(t[e.slabel]))])})):[i("v-uni-view",{staticClass:"nosearch"},[e._v(e._s(e.changesValue))])]]:[e.showplaceholder?i("v-uni-view",{staticClass:"uni-select-lay-item",class:{active:""==e.value},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.selectitem(-1,null)}}},[e._v(e._s(e.placeholder))]):e._e(),e._l(e.options,(function(t,o){return i("v-uni-view",{key:o,staticClass:"uni-select-lay-item",class:{active:e.value==t[e.svalue],disabled:t.disabled},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.selectitem(o,t)}}},[e._v(e._s(t[e.slabel]))])}))]],2)],1)},r=[]},"7f870":function(e,t,i){"use strict";i.r(t);var o=i("5c93"),r=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},8911:function(e,t,i){var o=i("b50a");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var r=i("967d").default;r("7480055b",o,!0,{sourceMap:!1,shadowMode:!1})},"9b78":function(e,t,i){"use strict";var o=i("9e62"),r=i.n(o);r.a},"9e62":function(e,t,i){var o=i("fbd5");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var r=i("967d").default;r("773a02c3",o,!0,{sourceMap:!1,shadowMode:!1})},a083:function(e,t,i){e.exports=i.p+"static/goods/goods.png"},af3f:function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"journal"},e._l(e.list,(function(t,o){return i("v-uni-view",{key:o,staticClass:"item"},[i("v-uni-view",{staticClass:"time"},[i("v-uni-view",[e._v(e._s(e.$util.timeFormat(t.action_time).split(" ")[0]))]),i("v-uni-view",[e._v(e._s(e.$util.timeFormat(t.action_time).split(" ")[1]))])],1),i("v-uni-view",{staticClass:"unit"},[i("v-uni-view",{staticClass:"top"},[i("v-uni-view",{staticClass:"core"}),i("v-uni-view",{staticClass:"unit-separate"})],1)],1),i("v-uni-view",{staticClass:"message"},[e._v(e._s(t.action))])],1)})),1)},r=[]},b50a:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-0dfa2d4c]{display:none}\r\n/* 收银台相关 */uni-text[data-v-0dfa2d4c],\r\nuni-view[data-v-0dfa2d4c]{font-size:.14rem}body[data-v-0dfa2d4c]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-0dfa2d4c]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-0dfa2d4c]::-webkit-scrollbar-button{display:none}body[data-v-0dfa2d4c]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-0dfa2d4c]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-0dfa2d4c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-0dfa2d4c]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-0dfa2d4c]{color:var(--primary-color)!important}.journal[data-v-0dfa2d4c]{padding-left:.1rem;box-sizing:border-box}.journal .item[data-v-0dfa2d4c]{width:100%;height:.7rem;display:flex}.journal .item .time[data-v-0dfa2d4c]{margin-right:.1rem;min-width:1rem}.journal .item .time uni-view[data-v-0dfa2d4c]:nth-child(1){font-size:.16rem;margin-bottom:.1rem;text-align:right}.journal .item .time uni-view[data-v-0dfa2d4c]:nth-child(2){font-size:.14rem;color:#999;text-align:right}.journal .unit[data-v-0dfa2d4c]{width:.18rem;height:100%;margin-right:.1rem}.journal .unit .top[data-v-0dfa2d4c]{width:.18rem;height:.18rem;border-radius:50%;background:var(--primary-color);position:relative}.journal .unit .top .core[data-v-0dfa2d4c]{background:#fff;width:.08rem;height:.08rem;border-radius:50%;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:999}.journal .unit .top .unit-separate[data-v-0dfa2d4c]{position:absolute;width:.01rem;height:.7rem;top:0;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);background:var(--primary-color);z-index:555}.journal .message[data-v-0dfa2d4c]{font-size:.14rem}',""]),e.exports=t},b988:function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.getOrderRefundDetail=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/detail",{data:e})},t.getOrderRefundLists=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/lists",{data:e})},t.getRefundApplyData=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/getrefundapplydata",{data:e})},t.orderRefund=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/refund",{data:e})},t.orderRefundAgree=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/agree",{data:e})},t.orderRefundClose=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/close",{data:e})},t.orderRefundComplete=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/complete",{data:e})},t.orderRefundReceive=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/receive",{data:e})},t.orderRefundRefuse=function(e){return r.default.post("/cashier/storeapi/cashierorderrefund/refuse",{data:e})};var r=o(i("4e01"))},cd74:function(e,t,i){var o=i("fdd3");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var r=i("967d").default;r("4869299f",o,!0,{sourceMap:!1,shadowMode:!1})},cf73:function(e,t,i){"use strict";i.r(t);var o=i("ef6b"),r=i.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},e424:function(e,t,i){"use strict";i.d(t,"b",(function(){return r})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return o}));var o={uniDatetimePicker:i("ea9b").default,nsOrderLog:i("03fa").default,nsLoading:i("3db4").default,selectLay:i("3523").default},r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("base-page",[o("v-uni-view",{staticClass:"goodslist"},[o("v-uni-view",{staticClass:"goodslist-box"},[o("v-uni-view",{staticClass:"goodslist-left"},[o("v-uni-view",{staticClass:"goods-title"},[e._v("订单管理"),o("v-uni-view",{staticClass:"screen-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showScreen=!e.showScreen}}},[e._v(e._s(e.showScreen?"关闭":"筛选"))])],1),e.showScreen?o("v-uni-view",{staticClass:"screen-content"},[o("v-uni-scroll-view",{staticClass:"screen-box",attrs:{"scroll-y":"true"}},[o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("创建时间")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:e.conditions.start_time_val||e.conditions.end_time_val||""!=e.conditions.time_type?"":"active",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("time_type","")}}},[e._v("全部")]),o("v-uni-view",{staticClass:"value",class:e.conditions.start_time_val||e.conditions.end_time_val||"7"!=e.conditions.time_type?"":"active",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("time_type","7")}}},[e._v("近7天")]),o("v-uni-view",{staticClass:"value",class:e.conditions.start_time_val||e.conditions.end_time_val||"30"!=e.conditions.time_type?"":"active",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("time_type","30")}}},[e._v("近30天")]),o("v-uni-view",{staticClass:"time-range"},[o("uni-datetime-picker",{staticClass:"time-value",attrs:{inputDisabled:!1,type:"datetime",placeholder:"开始时间"},model:{value:e.conditions.start_time_val,callback:function(t){e.$set(e.conditions,"start_time_val",t)},expression:"conditions.start_time_val"}}),o("v-uni-view",{staticClass:"line"},[e._v("-")]),o("uni-datetime-picker",{staticClass:"time-value",attrs:{inputDisabled:!1,type:"datetime",placeholder:"结束时间"},model:{value:e.conditions.end_time_val,callback:function(t){e.$set(e.conditions,"end_time_val",t)},expression:"conditions.end_time_val"}})],1)],1)],1),"online"==e.currOrderList?o("v-uni-view",[o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("订单类型")]),o("v-uni-view",{staticClass:"values"},e._l(e.orderConditionList.order_type_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.order_type==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("order_type",t.type)}}},[e._v(e._s(t.name))])})),1)],1),o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("订单状态")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:""==e.conditions.order_status?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("order_status","")}}},[e._v("全部")]),e._l(e.orderConditionList.order_status_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.order_status==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("order_status",t.type)}}},[e._v(e._s(t.name))])}))],2)],1),o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("付款方式")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:""==e.conditions.pay_type?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("pay_type","")}}},[e._v("全部")]),e._l(e.orderConditionList.pay_type_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.pay_type==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("pay_type",t.type)}}},[e._v(e._s(t.name))])}))],2)],1),o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("订单来源")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:""==e.conditions.order_from?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("order_from","")}}},[e._v("全部")]),e._l(e.orderConditionList.order_from_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.order_from==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("order_from",t.type)}}},[e._v(e._s(t.name))])}))],2)],1)],1):o("v-uni-view",[o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("订单类型")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:"all"==e.conditions.order_type?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("order_type","all")}}},[e._v("全部")]),e._l(e.orderConditionList.cashier_order_type_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.order_type==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("order_type",t.type)}}},[e._v(e._s(t.name))])}))],2)],1),o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("订单状态")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:""==e.conditions.order_status?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("order_status","")}}},[e._v("全部")]),e._l(e.orderConditionList.cashier_order_status_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.order_status==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("order_status",t.type)}}},[e._v(e._s(t.name))])}))],2)],1),o("v-uni-view",{staticClass:"screen-item"},[o("v-uni-view",{staticClass:"tit"},[e._v("付款方式")]),o("v-uni-view",{staticClass:"values"},[o("v-uni-view",{staticClass:"value",class:""==e.conditions.pay_type?"active":"",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeCondition("pay_type","")}}},[e._v("全部")]),e._l(e.orderConditionList.cashier_pay_type_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"value",class:e.conditions.pay_type==t.type?"active":"",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeCondition("pay_type",t.type)}}},[e._v(e._s(t.name))])}))],2)],1)],1)],1),o("v-uni-view",{staticClass:"search-btn"},[o("v-uni-view",{staticClass:"btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.resetCondition()}}},[e._v("重置")]),o("v-uni-view",{staticClass:"btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.searchOrder()}}},[e._v("确定")])],1)],1):e._e(),e.showScreen?e._e():o("v-uni-view",{staticClass:"goods-search"},[o("v-uni-view",{staticClass:"search"},[o("v-uni-text",{staticClass:"iconfont icon31sousuo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search("")}}}),o("v-uni-input",{attrs:{type:"text",placeholder:"输入订单号/商品名称/收货人姓名/手机号/留言/备注"},on:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;arguments[0]=t=e.$handleEvent(t),e.search("enter")}},model:{value:e.search_text,callback:function(t){e.search_text=t},expression:"search_text"}})],1)],1),e.showScreen?e._e():o("v-uni-view",{staticClass:"order-type-list"},[o("v-uni-view",{staticClass:"class-item",class:{active:"cashier"==e.currOrderList},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectOrderList("cashier")}}},[e._v("收银订单")]),o("v-uni-view",{staticClass:"class-item",class:{active:"online"==e.currOrderList},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectOrderList("online")}}},[e._v("商城订单")])],1),!e.showScreen&&!e.one_judge&&e.order_list.length>0?[o("v-uni-scroll-view",{staticClass:"goods-list-scroll",attrs:{"scroll-top":e.scrollTop,"scroll-y":"true","show-scrollbar":!1},on:{scroll:function(t){arguments[0]=t=e.$handleEvent(t),e.scroll.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e.getOrderListFn.apply(void 0,arguments)}}},e._l(e.order_list,(function(t,i){return o("v-uni-view",{key:i,staticClass:"item",class:i==e.selectGoodsKeys?"itemhover":"",on:{click:function(o){arguments[0]=o=e.$handleEvent(o),e.getOrderDetailFn(t.order_id,i)}}},[o("v-uni-view",{staticClass:"title"},[o("v-uni-view",[e._v("订单编号："+e._s(t.order_no))]),5==t.order_type?o("v-uni-view",[e._v(e._s(t.cashier_order_type_name))]):o("v-uni-view",[e._v(e._s(t.order_type_name))])],1),o("v-uni-view",{staticClass:"total-money-num"},[o("v-uni-view",{staticClass:"flex-shrink-0"},[e._v(e._s(t.order_status_name))]),o("v-uni-view",{staticClass:"member-info"},[o("v-uni-view",[e._v("买家：")]),t.member_id?o("v-uni-view",{staticClass:"member-info-name",attrs:{title:t.nickname}},[e._v(e._s(t.nickname))]):o("v-uni-view",{staticClass:"member-info-name"},[e._v("散客")])],1),o("v-uni-view",{staticClass:"box"},[o("v-uni-view",[e._v("实付金额")]),o("v-uni-view",[e._v("￥"+e._s(t.pay_money))])],1),parseFloat(t.refund_money)>0?o("v-uni-view",{staticClass:"refund-state flex-shrink-0"},[e._v("退款")]):e._e()],1)],1)})),1)]:e.showScreen||e.one_judge||0!=e.order_list.length?e._e():o("v-uni-view",{staticClass:"notYet"},[e._v("暂无数据")])],2),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"detail"==e.type,expression:"type == 'detail'"}],staticClass:"goodslist-right"},[o("v-uni-view",{staticClass:"goods-title"},[e._v("订单详情")]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.one_judge,expression:"!one_judge"}],staticClass:"order-information tab-wrap"},[o("v-uni-view",{staticClass:"tab-head"},e._l(e.tabObj.list,(function(t,i){return 3==t.value&&e.order_detail.order_log&&e.order_detail.order_log.length>0||3!=t.value?o("v-uni-text",{key:i,class:{active:e.tabObj.index==t.value},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.tabObj.index=t.value}}},[e._v(e._s(t.name))]):e._e()})),1),"{}"!=JSON.stringify(e.order_detail)?o("v-uni-view",{staticClass:"tab-content"},[1==e.tabObj.index?o("v-uni-view",{staticClass:"other-information"},[o("v-uni-view",{staticClass:"item-info"},[o("v-uni-view",{staticClass:"info-tit"},[e._v("收货信息")]),o("v-uni-view",{staticClass:"infos"},[o("v-uni-view",{staticClass:"info"},[e._v("收货人："+e._s(e.order_detail.name))]),o("v-uni-view",{staticClass:"info"},[e._v("收货电话："+e._s(e.order_detail.mobile))]),o("v-uni-view",{staticClass:"info"},[e._v("收货地址："+e._s(e.order_detail.full_address)+e._s(e.order_detail.address))])],1)],1),o("v-uni-view",{staticClass:"item-info"},[o("v-uni-view",{staticClass:"info-tit"},[e._v("用户信息")]),o("v-uni-view",{staticClass:"infos"},[e.order_detail.member_id?o("v-uni-view",{staticClass:"info"},[e._v("用户昵称："+e._s(e.order_detail.nickname)),o("v-uni-text",{staticClass:"look",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.viewMember()}}},[e._v("查看会员")])],1):o("v-uni-view",{staticClass:"info"},[e._v("用户昵称：散客")])],1)],1),o("v-uni-view",{staticClass:"item-info"},[o("v-uni-view",{staticClass:"info-tit"},[e._v("订单信息")]),o("v-uni-view",{staticClass:"infos"},[o("v-uni-view",{staticClass:"info"},[e._v("订单类型："+e._s(e.order_detail.order_type_name))]),o("v-uni-view",{staticClass:"info"},[e._v("订单状态："+e._s(e.order_detail.order_status_name))]),o("v-uni-view",{staticClass:"info"},[e._v(e._s(2==e.order_detail.order_type||3==e.order_detail.order_type?2==e.order_detail.order_type?"买家预计上门时间：":"买家要求送达时间：":"")+e._s(2==e.order_detail.order_type||3==e.order_detail.order_type?e.order_detail.buyer_ask_delivery_time:""))]),o("v-uni-view",{staticClass:"info"},[e._v("订单编号："+e._s(e.order_detail.order_no))]),o("v-uni-view",{staticClass:"info"},[e._v("外部交易号："+e._s(e.order_detail.out_trade_no))]),o("v-uni-view",{staticClass:"info"},[e._v("订单来源："+e._s(e.order_detail.order_from_name))]),o("v-uni-view",{staticClass:"info"},[e._v("创建时间："+e._s(e.$util.timeFormat(e.order_detail.create_time)))]),o("v-uni-view",{staticClass:"info"},[e._v("支付时间："+e._s(e.$util.timeFormat(e.order_detail.pay_time)))]),o("v-uni-view",{staticClass:"info"},[e._v("支付方式："+e._s(e.order_detail.pay_type_name))]),o("v-uni-view",{staticClass:"info"},[e._v("商品总价：￥"+e._s(e.order_detail.goods_money))]),o("v-uni-view",{staticClass:"info"},[e._v("店铺优惠：-￥"+e._s(e.order_detail.promotion_money))]),o("v-uni-view",{staticClass:"info"},[e._v("订单减免：-￥"+e._s(e.order_detail.reduction))]),o("v-uni-view",{staticClass:"info"},[e._v("优惠券：-￥"+e._s(e.order_detail.coupon_money))]),o("v-uni-view",{staticClass:"info"},[e._v("积分抵扣：-￥"+e._s(e.order_detail.point_money))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.online_money>0,expression:"order_detail.online_money > 0"}],staticClass:"info"},[e._v("线上支付：￥"+e._s(e._f("moneyFormat")(e.order_detail.online_money)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.cash>0,expression:"order_detail.cash > 0"}],staticClass:"info"},[e._v("现金支付：￥"+e._s(e._f("moneyFormat")(e.order_detail.cash)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.cash_change>0,expression:"order_detail.cash_change > 0"}],staticClass:"info"},[e._v("找零：-￥"+e._s(e._f("moneyFormat")(e.order_detail.cash_change)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:parseFloat(e.order_detail.balance_money)>0,expression:"parseFloat(order_detail.balance_money) > 0"}],staticClass:"info"},[e._v("余额抵扣：￥"+e._s(e.$util.moneyFormat(e.order_detail.balance_money)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.own_wechatpay>0,expression:"order_detail.own_wechatpay > 0"}],staticClass:"info"},[e._v("个人微信收款：￥"+e._s(e._f("moneyFormat")(e.order_detail.own_wechatpay)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.own_alipay>0,expression:"order_detail.own_alipay > 0"}],staticClass:"info"},[e._v("个人支付宝收款：￥"+e._s(e._f("moneyFormat")(e.order_detail.own_alipay)))]),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.order_detail.own_pos>0,expression:"order_detail.own_pos > 0"}],staticClass:"info"},[e._v("个人POS收款：￥"+e._s(e._f("moneyFormat")(e.order_detail.own_pos)))]),o("v-uni-view",{staticClass:"info"},[e._v("实付金额：￥"+e._s(e.order_detail.pay_money))]),parseFloat(e.order_detail.refund_money)>0?o("v-uni-view",{staticClass:"info"},[e._v("退款金额：￥"+e._s(e.order_detail.refund_money))]):e._e()],1)],1),o("v-uni-view",{staticClass:"item-info"},[o("v-uni-view",{staticClass:"info-tit"},[e._v("订单备注")]),o("v-uni-view",{staticClass:"infos remark"},[o("v-uni-view",{staticClass:"info"},[e._v("买家留言："+e._s(e.order_detail.buyer_message))]),o("v-uni-view",{staticClass:"info"},[e._v("商家备注："+e._s(e.order_detail.remark))])],1)],1)],1):e._e(),2==e.tabObj.index?o("v-uni-view",{staticClass:"goods-info"},[o("v-uni-view",{staticClass:"table"},[o("v-uni-view",{staticClass:"table-th table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"55%"}},[e._v("商品（元）")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[e._v("价格")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[e._v("数量")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%","justify-content":"flex-end"}},[e._v("小计（元）")])],1),e._l(e.order_detail.order_goods,(function(t,r){return o("v-uni-view",{key:r,staticClass:"table-tr table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"55%"}},["@/static/goods/goods.png"==t.sku_image?o("v-uni-image",{attrs:{src:i("a083"),mode:"widthFix"}}):o("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"small"}),mode:"widthFix"},on:{error:function(i){arguments[0]=i=e.$handleEvent(i),t.sku_image="@/static/goods/goods.png"}}}),o("v-uni-view",{staticClass:"content-text"},[o("v-uni-view",[t.is_gift?o("v-uni-text",{staticClass:"gift-tag"},[e._v("赠品")]):e._e(),e._v(e._s(t.goods_name))],1),o("v-uni-view",{staticClass:"text-color-gray"},[e._v(e._s(t.spec_name))]),0!=t.refund_status?o("v-uni-view",[o("v-uni-text",{staticClass:"refun-status"},[e._v(e._s(t.refund_status_name))])],1):e._e()],1)],1),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[e._v(e._s(t.price))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[e._v(e._s(t.num))]),o("v-uni-view",{staticClass:"table-td uni-column",staticStyle:{width:"15%","align-items":"flex-end"}},[o("v-uni-view",[e._v(e._s(t.goods_money))]),"refund_complete"==t.refund_status?o("v-uni-view",{staticClass:"refund-success"},[e._v("退款成功")]):e._e()],1)],1)}))],2)],1):e._e(),e.order_detail.order_log&&e.order_detail.order_log.length>0&&3==e.tabObj.index?o("v-uni-view",{staticClass:"other-information journal"},[o("ns-order-log",{attrs:{list:e.order_detail.order_log}})],1):e._e()],1):e._e(),"{}"==JSON.stringify(e.order_detail)?o("v-uni-view",{staticClass:"notYet"},[e._v("暂无数据")]):e._e(),o("ns-loading",{ref:"detailLoading"})],1),"{}"!=JSON.stringify(e.order_detail)?o("v-uni-view",{staticClass:"remarks-box"},[o("v-uni-button",{staticClass:"default-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.printTicket.apply(void 0,arguments)}}},[e._v("打印小票")]),e.order_detail.is_enable_refund?o("v-uni-button",{staticClass:"default-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.type="refund"}}},[e._v("退款")]):e._e(),o("v-uni-button",{staticClass:"default-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.open("remark")}}},[e._v("备注")]),e.order_detail.order_status_action.action?[e._l(e.order_detail.order_status_action.action,(function(t,i){return["orderLocalDelivery"==t["action"]||"orderDelivery"==t["action"]||"orderClose"==t["action"]||"orderAdjustMoney"==t["action"]?o("v-uni-button",{key:i+"_0",staticClass:"primary-btn btn remarks",attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.open(t["action"])}}},[e._v(e._s(t.title))]):e._e()]}))]:e._e(),2==e.order_detail.order_type&&2==e.order_detail.order_status?o("v-uni-button",{staticClass:"primary-btn btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.open("storeOrderTakeDelivery")}}},[e._v("提货")]):e._e(),e._l(e.order_detail.order_action,(function(t,i){return o("v-uni-button",{key:i,staticClass:"primary-btn btn remarks",attrs:{type:"primary"},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.open(t["action"])}}},[e._v(e._s(t.title))])}))],2):e._e()],1),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:"refund"==e.type,expression:"type == 'refund'"}],staticClass:"goodslist-right refund-wrap"},[o("v-uni-view",{staticClass:"goods-title"},[e._v("退款")]),o("v-uni-view",{staticClass:"content common-scrollbar"},[o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==e.refundStep,expression:"refundStep == 0"}]},[e._l(e.order_detail.order_goods,(function(t,r){return[0==t.refund_status?o("v-uni-view",{key:r+"_0",staticClass:"goods-item"},[o("v-uni-view",{staticClass:"iconfont",class:-1==e.refundGoods.indexOf(t.order_goods_id)?"iconyuan_checkbox":"iconyuan_checked",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectOrderGoods(t)}}}),o("v-uni-view",{staticClass:"image"},["@/static/goods/goods.png"==t.sku_image?o("v-uni-image",{attrs:{src:i("a083"),mode:"widthFix"}}):o("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"small"}),mode:"widthFix"},on:{error:function(i){arguments[0]=i=e.$handleEvent(i),t.sku_image="@/static/goods/goods.png"}}})],1),o("v-uni-view",{staticClass:"info"},[o("v-uni-view",{staticClass:"content-text"},[e._v(e._s(t.goods_name))])],1),o("v-uni-view",[o("v-uni-view",{staticClass:"price"},[e._v("￥"+e._s(t.price))]),o("v-uni-view",{staticClass:"num"},[e._v("x "+e._s(t.num))])],1)],1):e._e()]}))],2),e.refundDetail?[o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.refundStep,expression:"refundStep == 1"}],staticClass:"bg-grey"},[e._l(e.refundDetail.refund_list,(function(t,r){return[o("v-uni-view",{key:r+"_0",staticClass:"refund-goods-item"},[o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"title"},[e._v("退款商品")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-view",{staticClass:"goods-item"},[o("v-uni-view",{staticClass:"image"},["@/static/goods/goods.png"==t.order_goods_info.sku_image?o("v-uni-image",{attrs:{src:i("a083"),mode:"widthFix"}}):o("v-uni-image",{attrs:{src:e.$util.img(t.order_goods_info.sku_image,{size:"small"}),mode:"widthFix"},on:{error:function(i){arguments[0]=i=e.$handleEvent(i),t.order_goods_info.sku_image="@/static/goods/goods.png"}}})],1),o("v-uni-view",{staticClass:"info"},[o("v-uni-view",{staticClass:"content-text"},[e._v(e._s(t.order_goods_info.sku_name))])],1)],1)],1)],1),o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"title"},[e._v("退款金额")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-view",{staticClass:"money-box"},[o("v-uni-input",{attrs:{type:"number"},model:{value:e.refundApply.refund_array[t.order_goods_info.order_goods_id].refund_money,callback:function(i){e.$set(e.refundApply.refund_array[t.order_goods_info.order_goods_id],"refund_money",i)},expression:"refundApply.refund_array[refundItem.order_goods_info.order_goods_id].refund_money"}}),e._v("元")],1),o("v-uni-view",{staticClass:"refund-money"},[e._v("可退金额："),o("v-uni-text",[e._v("￥"+e._s(e._f("moneyFormat")(t.order_goods_info.refund_apply_money)))])],1)],1)],1),o("v-uni-view",{staticClass:"row",staticStyle:{"margin-top":"8px"}},[o("v-uni-view",{staticClass:"title"},[e._v("是否返还库存")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(i){arguments[0]=i=e.$handleEvent(i),e.changeIsRefundStock(t,i)}}},[o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"1",checked:1==e.refundApply.refund_array[t.order_goods_info.order_goods_id].is_refund_stock}}),e._v("是")],1),o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"0",checked:0==e.refundApply.refund_array[t.order_goods_info.order_goods_id].is_refund_stock}}),e._v("否")],1)],1)],1)],1),1==e.refundApply.refund_array[t.order_goods_info.order_goods_id].is_refund_stock?o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"title"},[e._v("退还数量")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-view",{staticClass:"money-box"},[o("v-uni-input",{attrs:{type:"number",max:Number(t.order_goods_info.num)},model:{value:e.refundApply.refund_array[t.order_goods_info.order_goods_id].refund_stock_num,callback:function(i){e.$set(e.refundApply.refund_array[t.order_goods_info.order_goods_id],"refund_stock_num",i)},expression:"refundApply.refund_array[refundItem.order_goods_info.order_goods_id].refund_stock_num"}})],1)],1)],1):e._e(),o("v-uni-view",{staticClass:"row",staticStyle:{"margin-top":"8px"}},[o("v-uni-view",{staticClass:"title"},[e._v("完成状态")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(i){arguments[0]=i=e.$handleEvent(i),e.refundApply.refund_array[t.order_goods_info.order_goods_id].refund_status=i.detail.value}}},[o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"PARTIAL_REFUND",checked:"PARTIAL_REFUND"==e.refundApply.refund_array[t.order_goods_info.order_goods_id].refund_status}}),e._v("部分退款状态")],1),o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"REFUND_COMPLETE",checked:"REFUND_COMPLETE"==e.refundApply.refund_array[t.order_goods_info.order_goods_id].refund_status}}),e._v("退款完成状态")],1)],1)],1)],1),o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"title"}),o("v-uni-view",{staticClass:"cont tips"},[o("v-uni-view",[e._v("1、如果是退部分金额，退款后可以是部分退款状态或退款完成状态")]),o("v-uni-view",[e._v("2、如果是退全部金额，则退款后一定是退款完成状态")]),o("v-uni-view",[e._v("3、退款完成才会执行相关业务如核销码失效，卡包失效等操作")])],1)],1)],1)]})),o("v-uni-view",{staticClass:"refund-goods-item"},[o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"title"},[e._v("退款说明")]),o("v-uni-view",{staticClass:"cont"},[o("v-uni-textarea",{attrs:{placeholder:"请输入退款说明"},model:{value:e.refundApply.refund_remark,callback:function(t){e.$set(e.refundApply,"refund_remark",t)},expression:"refundApply.refund_remark"}})],1)],1)],1)],2),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==e.refundStep,expression:"refundStep == 2"}]},e._l(e.refundDetail.refund_transfer_type,(function(t,i){return o("v-uni-view",{key:i,staticClass:"refund-type",class:{active:e.refundApply.refund_transfer_type==i},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.refundApply.refund_transfer_type=i}}},[o("v-uni-view",{staticClass:"title"},[e._v(e._s(t.name))]),o("v-uni-view",{staticClass:"desc"},[e._v(e._s(t.desc))])],1)})),1)]:e._e()],2),o("v-uni-view",{staticClass:"remarks-box"},[o("v-uni-button",{staticClass:"default-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.type="detail"}}},[e._v("取消")]),o("v-uni-button",{directives:[{name:"show",rawName:"v-show",value:e.refundStep>0,expression:"refundStep > 0"}],staticClass:"primary-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.refundStep-=1}}},[e._v("上一步")]),o("v-uni-button",{staticClass:"primary-btn comp-btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.refundNext.apply(void 0,arguments)}}},[e._v(e._s(2==e.refundStep?"确认退款":"下一步"))])],1)],1)],1),o("unipopup",{ref:"remark",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"message"},[o("v-uni-view",{staticClass:"title"},[e._v("备注"),o("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.remark.close()}}})],1),o("v-uni-view",{staticClass:"textarea-box"},[o("v-uni-textarea",{staticClass:"textarea",attrs:{maxlength:"200",placeholder:"输入请不多于200字"},model:{value:e.order_detail.remark,callback:function(t){e.$set(e.order_detail,"remark",t)},expression:"order_detail.remark"}})],1),o("v-uni-button",{staticClass:"primary-btn btn save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.saveRemark.apply(void 0,arguments)}}},[e._v("保存")])],1)],1),o("unipopup",{ref:"orderClose",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"order-close"},[o("v-uni-view",{staticClass:"title"},[e._v("是否要关闭订单？")]),o("v-uni-view",{staticClass:"btn"},[o("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderOperation("close")}}},[e._v("取消")]),o("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderOperation("save")}}},[e._v("确定")])],1)],1)],1),o("unipopup",{ref:"storeOrderTakeDelivery",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"order-close"},[o("v-uni-view",{staticClass:"title"},[e._v("确定要直接提货吗？")]),o("v-uni-view",{staticClass:"btn"},[o("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.storeOrderTakeDelivery.close()}}},[e._v("取消")]),o("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.storeOrderTakeDelivery()}}},[e._v("确定")])],1)],1)],1),o("unipopup",{ref:"orderLocalDelivery",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"order-delivery local"},[o("v-uni-view",{staticClass:"title"},[e._v("订单发货")]),o("v-uni-view",{staticClass:"content"},[o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("收货地址：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-text",[e._v(e._s(e.order_detail.name))]),o("v-uni-text",[e._v(e._s(e.order_detail.mobile))]),o("v-uni-text",[e._v(e._s(e.order_detail.full_address)+e._s(e.order_detail.address))])],1)],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("配送员：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-view",{staticClass:"select"},[o("select-lay",{attrs:{zindex:10,value:e.localDelivery.deliverer,name:"names",placeholder:"请选择配送员",options:e.deliverer},on:{selectitem:function(t){arguments[0]=t=e.$handleEvent(t),e.selectDeliverer.apply(void 0,arguments)}}})],1)],1)],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("配送员手机号：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.localDelivery.deliverer_mobile,callback:function(t){e.$set(e.localDelivery,"deliverer_mobile",t)},expression:"localDelivery.deliverer_mobile"}})],1)],1)],1),o("v-uni-view",{staticClass:"btn"},[o("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.orderLocalDelivery.close()}}},[e._v("取消")]),o("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderLocalDeliveryFn()}}},[e._v("确定")])],1)],1)],1),o("unipopup",{ref:"orderDelivery",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"order-delivery express"},[o("v-uni-view",{staticClass:"title"},[e._v("订单发货")]),o("v-uni-view",{staticClass:"content"},[o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("收货地址：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-text",[e._v(e._s(e.order_detail.name))]),o("v-uni-text",[e._v(e._s(e.order_detail.mobile))]),o("v-uni-text",[e._v(e._s(e.order_detail.full_address)+e._s(e.order_detail.address))])],1)],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("发货方式：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-radio-group",{staticClass:"form-radio-group",on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.expresDelivery.delivery_type=t.detail.value}}},[o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"1",checked:1==e.expresDelivery.delivery_type}}),e._v("物流发货")],1),o("v-uni-label",{staticClass:"radio form-radio-item"},[o("v-uni-radio",{attrs:{value:"0",checked:0==e.expresDelivery.delivery_type}}),e._v("无需物流")],1)],1)],1)],1),o("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==e.expresDelivery.delivery_type,expression:"expresDelivery.delivery_type == 1"}]},[o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("物流公司：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-view",{staticClass:"select"},[o("select-lay",{attrs:{zindex:10,value:e.expresDelivery.express_company_id,name:"names",placeholder:"请选择物流公司",options:e.expressCompany},on:{selectitem:function(t){arguments[0]=t=e.$handleEvent(t),e.selectExpressCompany.apply(void 0,arguments)}}})],1)],1)],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"title"},[e._v("快递单号：")]),o("v-uni-view",{staticClass:"info"},[o("v-uni-input",{staticClass:"input",attrs:{type:"text"},model:{value:e.expresDelivery.delivery_no,callback:function(t){e.$set(e.expresDelivery,"delivery_no",t)},expression:"expresDelivery.delivery_no"}})],1)],1)],1),o("v-uni-view",{staticClass:"content-item"},[o("v-uni-view",{staticClass:"info goods-info common-scrollbar"},[o("v-uni-view",{staticClass:"table"},[o("v-uni-checkbox-group",{on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.expresDelivery.order_goods_ids=t.detail.value}}},[o("v-uni-view",{staticClass:"table-th table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%"}}),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"45%"}},[e._v("商品（元）")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[e._v("数量")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"20%","justify-content":"flex-end"}},[e._v("物流单号")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"20%","justify-content":"flex-end"}},[e._v("物流状态")])],1),e._l(e.order_detail.order_goods,(function(t,r){return o("v-uni-view",{key:r,staticClass:"table-tr table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%"}},[o("v-uni-checkbox",{attrs:{value:String(t.order_goods_id),disabled:0!=t.delivery_status}})],1),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"45%"}},["@/static/goods/goods.png"==t.sku_image?o("v-uni-image",{attrs:{src:i("a083"),mode:"widthFix"}}):o("v-uni-image",{attrs:{src:e.$util.img(t.sku_image,{size:"small"}),mode:"widthFix"},on:{error:function(i){arguments[0]=i=e.$handleEvent(i),t.sku_image="@/static/goods/goods.png"}}}),o("v-uni-view",{staticClass:"content-text"},[o("v-uni-view",[e._v(e._s(t.goods_name)+" "+e._s(t.spec_name))])],1)],1),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[e._v(e._s(t.num))]),o("v-uni-view",{staticClass:"table-td uni-column",staticStyle:{width:"20%","align-items":"flex-end"}},[e._v(e._s(t.delivery_no))]),o("v-uni-view",{staticClass:"table-td uni-column",staticStyle:{width:"20%","align-items":"flex-end"}},[e._v(e._s(t.delivery_status_name))])],1)}))],2)],1)],1)],1)],1),o("v-uni-view",{staticClass:"btn"},[o("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$refs.orderDelivery.close()}}},[e._v("取消")]),o("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.orderDelivery()}}},[e._v("确定")])],1)],1)],1),o("unipopup",{ref:"orderAdjustMoney",attrs:{type:"center"}},[o("v-uni-view",{staticClass:"order-adjust-money"},[o("v-uni-view",{staticClass:"title"},[o("v-uni-text",[e._v("调整价格")]),o("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clear.apply(void 0,arguments)}}})],1),o("v-uni-view",{staticClass:"tip"},[e._v("注意 : 只有订单未付款时才支持改价,改价后请联系买家刷新订单核实订单金额后再支付。")]),o("v-uni-view",{staticClass:"table"},[o("v-uni-view",{staticClass:"table-th table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[e._v("商品信息")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%",padding:"0 0.07rem"}},[e._v("单价")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%",padding:"0 0.07rem"}},[e._v("数量")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%",padding:"0 0.07rem"}},[e._v("小计")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"9%",padding:"0 0.07rem"}},[e._v("商品总额")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%",padding:"0 0.07rem"}},[e._v("优惠")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"8%",padding:"0 0.07rem"}},[e._v("优惠券")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"8%",padding:"0 0.07rem"}},[e._v("积分抵现")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"9%",padding:"0 0.07rem"}},[e._v("发票费用")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[e._v("发票邮寄费用")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[e._v("调整金额")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[e._v("运费")]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"6%",padding:"0 0.07rem"}},[e._v("总计")])],1),o("v-uni-view",{staticClass:"table-tr table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"25%"}},e._l(e.order_detail.order_goods,(function(t,i){return o("v-uni-view",{key:i,staticClass:"table-tr table-all"},[o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"40%",padding:"0 0.07rem"}},[e._v(e._s(t.sku_name))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"20%",padding:"0 0.07rem"}},[e._v(e._s(t.price))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"20%",padding:"0 0.07rem"}},[e._v(e._s(t.num))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"20%",padding:"0 0.07rem"}},[e._v(e._s(t.goods_money))])],1)})),1),o("v-uni-view",{staticClass:"table-td left",staticStyle:{width:"9%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.goods_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"5%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.promotion_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"8%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.coupon_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"8%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.point_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"9%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.invoice_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[e._v(e._s(e.order_detail.invoice_delivery_money))]),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[o("v-uni-input",{attrs:{type:"number"},model:{value:e.adjustParams.adjust_money,callback:function(t){e.$set(e.adjustParams,"adjust_money",t)},expression:"adjustParams.adjust_money"}})],1),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%",padding:"0 0.07rem"}},[o("v-uni-input",{attrs:{type:"number",min:"0"},model:{value:e.adjustParams.delivery_money,callback:function(t){e.$set(e.adjustParams,"delivery_money",t)},expression:"adjustParams.delivery_money"}})],1),o("v-uni-view",{staticClass:"table-td",staticStyle:{width:"6%",padding:"0 0.07rem"}},[e._v(e._s(parseFloat(parseFloat(e.order_detail.goods_money)-parseFloat(e.order_detail.promotion_money||0)-parseFloat(e.order_detail.coupon_money||0)-parseFloat(e.order_detail.point_money||0)+parseFloat(e.adjustParams.adjust_money||0)+parseFloat(e.adjustParams.delivery_money||0)).toFixed(2)))])],1)],1),o("v-uni-view",{staticClass:"tip m-0"},[o("v-uni-text",{staticClass:"Highlight"},[e._v(e._s("实际商品金额 "))]),e._v("= 商品总额 - 优惠金额 - 优惠券金额 - 积分抵现 + 调价")],1),o("v-uni-view",{staticClass:"tip m-0"},[o("v-uni-text",{staticClass:"Highlight"},[e._v(e._s("发票费用 "))]),e._v("= 实际商品金额 * 发票比率")],1),o("v-uni-view",{staticClass:"tip m-0"},[e._v("订单总额 ="),o("v-uni-text",{staticClass:"Highlight"},[e._v(e._s(" 实际商品金额 "))]),e._v("+"),o("v-uni-text",{staticClass:"Highlight"},[e._v(e._s("发票费用 "))]),e._v("+ 运费 + 发票邮寄费用")],1),o("v-uni-view",{staticClass:"footer"},[o("v-uni-button",{staticClass:"primary-btn btn remarks",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.adjustSave.apply(void 0,arguments)}}},[e._v("确定")]),o("v-uni-button",{staticClass:"default-btn comp-btn remarks clear",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clear.apply(void 0,arguments)}}},[e._v("取消")])],1)],1)],1)],1)],1)},a=[]},e4f7:function(e,t,i){"use strict";i.r(t);var o=i("e424"),r=i("cf73");for(var a in r)["default"].indexOf(a)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(a);i("9b78");var s=i("828b"),n=Object(s["a"])(r["default"],o["b"],o["c"],!1,null,"b89d75ee",null,!1,o["a"],void 0);t["default"]=n.exports},e605:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("bf0f"),i("2797"),i("aa9c"),i("c223"),i("dc8a"),i("e838"),i("5ef2"),i("dd2b"),i("d4b5"),i("7a76"),i("c9b5"),i("fd3c"),i("22b6"),i("e966"),i("ab80");var o=i("16dc"),r=i("b988"),a={data:function(){return{selectGoodsKeys:0,otherInfoValue:{order_no:{title:"订单编号：",value:""},out_trade_no:{title:"订单交易号：",value:""},create_time:{title:"消费时间：",value:""},pay_status:{title:"支付状态：",value:""},order_status:{title:"订单状态：",value:""},pay_type:{title:"付款方式：",value:""},operator_name:{title:"收银员：",value:""},pay_time:{title:"付款时间：",value:""}},list:[],page:1,page_size:8,search_text:"",conditions:{order_status:"",time_type:"",start_time:"",end_time:"",start_time_val:"",end_time_val:"",order_type:"all",order_from:"",pay_type:""},currOrderList:"cashier",trade_type:"",one_judge:!0,listLock:!1,scrollTop:0,order_list:[],order_detail:{},type:"detail",refundStep:0,refundGoods:[],refundDetail:null,refundRepeat:!1,refundApply:{refund_remark:"",refund_transfer_type:""},localDelivery:{deliverer_mobile:"",deliverer:""},expresDelivery:{delivery_type:1,express_company_id:0,delivery_no:"",order_goods_ids:[]},expressCompany:[],deliverer:[],tabObj:{list:[{value:1,name:"基础信息"},{value:2,name:"商品信息"},{value:3,name:"订单日志"}],index:1},isLogisticsRepeat:!1,adjustParams:{order_id:null,adjust_money:0,delivery_money:0},currGlobalStoreId:"",showScreen:!1,orderConditionList:[]}},onLoad:function(e){this.search_text=e.order_no||"",this.currOrderList="online"==e.order_from?"online":"cashier",uni.getStorageSync("globalStoreId")&&(this.currGlobalStoreId=uni.getStorageSync("globalStoreId")),this.getOrderListFn(),this.getExpressCompany(),this.getDeliver(),this.getOrderCondition()},watch:{type:function(e,t){"refund"==t&&(this.refundStep=0,this.refundGoods=[],this.refundDetail=null,this.refundRepeat=!1,this.refundApply={refund_remark:"",refund_transfer_type:""})}},methods:{changeIsRefundStock:function(e,t){this.refundApply.refund_array[e.order_goods_info.order_goods_id].is_refund_stock=t.detail.value,this.$forceUpdate()},searchOrder:function(){(this.conditions.start_time_val||this.conditions.end_time_val)&&new Date(this.conditions.end_time_val).getTime()<=new Date(this.conditions.start_time_val).getTime()?this.$util.showToast({title:"结束时间不能早于开始时间"}):(this.page=1,this.order_list=[],this.one_judge=!0,this.listLock=!1,this.showScreen=!1,this.getOrderListFn())},initCondition:function(){this.conditions.order_status="",this.conditions.time_type="",this.conditions.start_time="",this.conditions.end_time="",this.conditions.start_time_val="",this.conditions.end_time_val="",this.conditions.order_type="all",this.conditions.order_from="",this.conditions.pay_type=""},resetCondition:function(){this.page=1,this.order_list=[],this.one_judge=!0,this.listLock=!1,this.initCondition(),this.showScreen=!1,this.getOrderListFn()},changeCondition:function(e,t){var i=this;if(this.conditions[e]=t,"order_type"==e&&"online"==this.currOrderList&&(this.orderConditionList.order_type_list.forEach((function(e,o){e.type==t&&(i.orderConditionList.order_status_list=e.status)})),this.conditions.order_status=""),"time_type"==e)switch(this.conditions.start_time_val="",this.conditions.end_time_val="",t){case"":this.conditions.start_time="",this.conditions.end_time="";break;case"7":this.conditions.start_time=this.getDay(t),this.conditions.end_time=this.getNowDate();break;case"30":this.conditions.start_time=this.getDay(t),this.conditions.end_time=this.getNowDate();break;default:break}},getDay:function(e){var t=new Date;t.setDate(t.getDate()-e);var i=t.getFullYear(),o=t.getMonth()+1;o<10&&(o="0"+o);var r=t.getDate();return r<10&&(r="0"+r),i+"-"+o+"-"+r+" 00:00:00"},getNowDate:function(){var e=new Date,t=e.getFullYear(),i=e.getMonth()+1;i<10&&(i="0"+i);var o=e.getDate();return o<10&&(o="0"+o),t+"-"+i+"-"+o+" 23:59:59"},getOrderCondition:function(){var e=this;(0,o.getorderCondition)().then((function(t){if(t.code>=0){var i=t.data;for(var o in i){var r=[];if("order_label_list"!=o&&"order_status_list"!=o&&"pay_type_list"!=o&&"cashier_pay_type_list"!=o&&"cashier_order_type_list"!=o)for(var a in i[o]){var s={type:a};s=Object.assign(s,i[o][a]),r.push(s)}else for(var a in i[o]){s={type:a,name:i[o][a]};r.push(s)}i[o]=r}e.orderConditionList=i,e.orderConditionList.order_type_list.forEach((function(t,i){var o=[];for(var r in t.status){var a={type:r,name:t.status[r]};o.push(a)}t.status=o,"all"==t.type&&(e.orderConditionList.order_status_list=t.status)}))}}))},switchStoreAfter:function(){this.currGlobalStoreId!=uni.getStorageSync("globalStoreId")&&(this.currGlobalStoreId=uni.getStorageSync("globalStoreId"),this.page=1,this.order_list=[],this.one_judge=!0,this.listLock=!1,this.getOrderListFn(),this.getDeliver())},search:function(e){var t=this;this.page=1,this.order_list=[],this.one_judge=!0,this.listLock=!1,"enter"==e?document.onkeydown=function(e){13===e.keyCode&&t.getOrderListFn()}:this.getOrderListFn()},selectOrderList:function(e){this.currOrderList=e,this.page=1,this.order_list=[],this.one_judge=!0,this.listLock=!1,this.initCondition(),this.selectGoodsKeys=0,this.getOrderListFn()},getOrderListFn:function(){var e=this;if(this.listLock)return!1;this.listLock=!0,(0,o.getOrderList)({page:this.page,page_size:this.page_size,search_text:this.search_text,order_scene:this.currOrderList,order_status:this.conditions.order_status,start_time:this.conditions.start_time_val?this.conditions.start_time_val:this.conditions.start_time,end_time:this.conditions.end_time_val?this.conditions.end_time_val:this.conditions.end_time,order_type:this.conditions.order_type,order_from:this.conditions.order_from,pay_type:this.conditions.pay_type}).then((function(t){0==t.data.list.length&&e.one_judge&&(e.order_detail={},e.one_judge=!1,e.$refs.detailLoading&&e.$refs.detailLoading.hide()),t.code>=0&&0!=t.data.list.length&&(0==e.order_list.length?e.order_list=t.data.list:e.order_list=e.order_list.concat(t.data.list),e.one_judge&&e.getOrderDetailFn(e.order_list[0].order_id)),1==e.page&&(e.scrollTop=0),t.data.list.length>=e.page_size&&(e.page++,e.listLock=!1)}))},scroll:function(e){this.scrollTop=e.detail.scrollTop},getOrderDetailFn:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2?arguments[2]:void 0;this.localDelivery={deliverer_mobile:"",deliverer:""},this.selectGoodsKeys=i,this.type="detail",this.$refs.detailLoading.show(),(0,o.getOrderDetail)({order_id:e}).then((function(e){e.code>=0&&(t.order_detail=e.data,t.order_detail.order_status_action=JSON.parse(e.data.order_status_action),t.otherInfoValue.order_no.value=e.data.order_no,t.otherInfoValue.out_trade_no.value=e.data.out_trade_no,t.otherInfoValue.create_time.value=t.$util.timeFormat(e.data.create_time),t.otherInfoValue.operator_name.value=e.data.operator_name,t.otherInfoValue.pay_type.value=e.data.pay_type_name,t.otherInfoValue.order_status.value=e.data.order_status_name,1==e.data.pay_status?(t.otherInfoValue.pay_status.value="已支付",t.otherInfoValue.pay_time.value=t.$util.timeFormat(e.data.pay_time)):(t.otherInfoValue.pay_status.value="待支付",t.otherInfoValue.pay_time.value=""),"function"==typeof r&&r(),Object.keys(t.adjustParams).forEach((function(e){t.adjustParams[e]=parseFloat(t.order_detail[e])})),t.$forceUpdate(),t.one_judge=!1,t.$refs.detailLoading&&t.$refs.detailLoading.hide())}))},open:function(e){this.$refs[e].open()},close:function(e){this.$refs[e].close()},adjustSave:function(){var e=this;return parseFloat(this.adjustParams.delivery_money+0)<0?(this.$util.showToast({title:"运费不可小于0"}),!1):(parseFloat(this.adjustParams.delivery_money+0)||(this.adjustParams.delivery_money=0),parseFloat(parseFloat(this.order_detail.goods_money)-parseFloat(this.order_detail.promotion_money||0)-parseFloat(this.order_detail.coupon_money||0)-parseFloat(this.order_detail.point_money||0)+parseFloat(this.adjustParams.adjust_money||0)+parseFloat(this.adjustParams.delivery_money||0)).toFixed(2)<0?(this.$util.showToast({title:"真实商品价格不可小于0"}),!1):void(0,o.orderAdjustPrice)(this.adjustParams).then((function(t){e.$util.showToast({title:t.message}),t.code>=0&&(e.getOrderDetailFn(e.order_list[e.selectGoodsKeys].order_id),e.$refs.orderAdjustMoney.close())})))},clear:function(){var e=this;Object.keys(this.adjustParams).forEach((function(t){e.adjustParams[t]=e.adjustParams[t]=parseFloat(e.order_detail[t])})),this.$refs.orderAdjustMoney.close()},saveRemark:function(){var e=this;(0,o.orderRemark)({order_id:this.order_detail.order_id,remark:this.order_detail.remark}).then((function(t){e.$util.showToast({title:t.message}),t.code>=0&&(e.getOrderDetailFn(e.order_list[e.selectGoodsKeys].order_id),e.$refs.remark.close())}))},orderCloseFn:function(){var e=this;(0,o.orderClose)({order_id:this.order_detail.order_id}).then((function(t){e.$util.showToast({title:t.message}),t.code>=0&&(e.getOrderDetailFn(e.order_list[e.selectGoodsKeys].order_id,e.selectGoodsKeys,(function(){e.order_list[e.selectGoodsKeys]=e.order_detail,e.$forceUpdate()})),e.$refs.orderClose.close())}))},orderOperation:function(e){switch(e){case"save":this.orderCloseFn();break;case"close":this.$refs.orderClose.close();break}},selectOrderGoods:function(e){var t=this.refundGoods.indexOf(e.order_goods_id);-1==t?this.refundGoods.push(e.order_goods_id):this.refundGoods.splice(t,1)},refundNext:function(){var e=this;if(0==this.refundStep){if(!this.refundGoods.length)return void this.$util.showToast({title:"请选择要退款的商品"});(0,r.getRefundApplyData)({refund_array:JSON.stringify(this.refundGoods)}).then((function(t){if(0==t.code){e.refundDetail=t.data,e.refundStep=1;var i={};e.refundDetail.refund_list.forEach((function(t){i[t.order_goods_info.order_goods_id]={refund_pay_money:e.$util.moneyFormat(t.order_goods_info.refund_apply_money),refund_money:e.$util.moneyFormat(t.order_goods_info.refund_apply_money),refund_status:"PARTIAL_REFUND",is_refund_stock:1,refund_stock_num:t.order_goods_info.num}})),Object.assign(e.refundApply,{order_id:e.order_detail.order_id,refund_array:i,refund_transfer_type:Object.keys(e.refundDetail.refund_transfer_type)[0]})}else e.$util.showToast({title:t.message})}))}else 1==this.refundStep?this.refundVerify()&&(this.refundStep=2):2==this.refundStep&&this.createRefund()},refundVerify:function(){var e=this;try{this.refundDetail.refund_list.forEach((function(t){var i=e.refundApply.refund_array[t.order_goods_info.order_goods_id];if(isNaN(parseFloat(i.refund_money)))throw e.$util.showToast({title:"退款金额输入错误"}),new Error("");if(parseFloat(i.refund_money)<0)throw e.$util.showToast({title:"退款金额不能小于0"}),new Error("");if(parseFloat(i.refund_money)>parseFloat(i.refund_pay_money))throw e.$util.showToast({title:"退款金额超出可退金额"}),new Error("")}))}catch(t){return!1}return!0},createRefund:function(){var e=this;if(!this.refundRepeat){this.refundRepeat=!0,uni.showLoading({title:""});var t=this.$util.deepClone(this.refundApply);t.refund_array=JSON.stringify(t.refund_array),(0,r.orderRefund)(t).then((function(t){uni.hideLoading(),0==t.code?(e.$util.showToast({title:"退款成功"}),e.getOrderDetailFn(e.order_detail.order_id),e.type="detail"):(e.refundRepeat=!1,e.$util.showToast({title:t.message}))}))}},storeOrderTakeDelivery:function(){var e=this;this.isRepeat||(this.isRepeat=!0,uni.showLoading({title:""}),(0,o.orderStoreDelivery)(this.order_detail.order_id).then((function(t){uni.hideLoading(),e.isRepeat=!1,0==t.code?(e.getOrderDetailFn(e.order_detail.order_id),e.$refs.storeOrderTakeDelivery.close()):e.$util.showToast({title:t.message})})))},orderLocalDeliveryFn:function(){var e=this;this.localDelivery.deliverer?this.localDelivery.deliverer_mobile?this.isRepeat||(this.isRepeat=!0,uni.showLoading({title:""}),(0,o.orderLocalDelivery)({order_id:this.order_detail.order_id,deliverer:this.localDelivery.deliverer,deliverer_mobile:this.localDelivery.deliverer_mobile}).then((function(t){uni.hideLoading(),e.isRepeat=!1,0==t.code?(e.getOrderDetailFn(e.order_detail.order_id),e.localDelivery={deliverer_mobile:"",deliverer:""},e.$refs.orderLocalDelivery.close()):e.$util.showToast({title:t.message})}))):this.$util.showToast({title:"请输入配送员联系方式"}):this.$util.showToast({title:"请选择配送员"})},getDeliver:function(){var e=this;(0,o.getOrderDeliverList)().then((function(t){0==t.code&&t.data&&(e.deliverer=t.data.map((function(e){return{label:e.deliver_name,value:e.deliver_name,mobile:e.deliver_mobile}})))}))},selectDeliverer:function(e,t){e>=0?(this.localDelivery.deliverer_mobile=this.deliverer[e].mobile,this.localDelivery.deliverer=t.value):(this.localDelivery.deliverer_mobile="",this.localDelivery.deliverer="")},viewMember:function(){this.$util.redirectTo("/pages/member/list",{member_id:this.order_detail.member_id})},printTicket:function(){var e=this;(0,o.orderPrintTicket)(this.order_detail.order_id).then((function(t){if(0==t.code)if(Object.values(t.data).length){var i=Object.values(t.data);try{var o={printer:[]};i.forEach((function(e){o.printer.push({printer_type:e.printer_info.printer_type,host:e.printer_info.host,ip:e.printer_info.ip,port:e.printer_info.port,content:e.content,print_width:e.printer_info.print_width})})),e.$pos.send("Print",JSON.stringify(o))}catch(r){console.log("err",r,t.data)}}else e.$util.showToast({title:"未开启订单小票打印"});else e.$util.showToast({title:t.message?t.message:"小票打印失败"})}))},getExpressCompany:function(){var e=this;(0,o.getExpressCompanyList)().then((function(t){0==t.code&&t.data&&(e.expressCompany=t.data.map((function(e){return{label:e.company_name,value:e.company_id}})))}))},selectExpressCompany:function(e,t){this.expresDelivery.express_company_id=e>=0?parseInt(t.value):0},orderDelivery:function(){var e=this;if(1==this.expresDelivery.delivery_type){if(!this.expresDelivery.express_company_id)return void this.$util.showToast({title:"请选择物流公司"});if(!this.expresDelivery.delivery_no)return void this.$util.showToast({title:"请输入物流单号"})}this.expresDelivery.order_goods_ids.length?this.isLogisticsRepeat||(this.isLogisticsRepeat=!0,uni.showLoading({title:""}),(0,o.orderExpressDelivery)({order_id:this.order_detail.order_id,delivery_type:this.expresDelivery.delivery_type,express_company_id:this.expresDelivery.express_company_id,delivery_no:this.expresDelivery.delivery_no,order_goods_ids:this.expresDelivery.order_goods_ids.toString()}).then((function(t){uni.hideLoading(),0==t.code?(e.isLogisticsRepeat=!1,e.getOrderDetailFn(e.order_detail.order_id),e.expresDelivery={delivery_type:1,express_company_id:0,delivery_no:"",order_goods_ids:[]},e.$refs.orderDelivery.close()):(e.isLogisticsRepeat=!1,e.$util.showToast({title:t.message}))}))):this.$util.showToast({title:"请选择要发货的商品"})}}};t.default=a},eb39:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={name:"nsLoading",props:{layerBackground:{type:Object,default:function(){return{}}},defaultShow:{type:Boolean,default:!0}},data:function(){return{isShow:!0}},created:function(){this.isShow=this.defaultShow},methods:{show:function(){this.isShow=!0},hide:function(){this.isShow=!1}}};t.default=o},ef6b:function(e,t,i){"use strict";i("6a54");var o=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(i("03fa")),a=o(i("3db4")),s=o(i("2166")),n=o(i("e605")),d={components:{nsOrderLog:r.default,nsLoading:a.default,unipopup:s.default},mixins:[n.default]};t.default=d,window.POS_PRINT_CALLBACK=function(e){uni.showToast({title:e,icon:"none"})}},fbd5:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-b89d75ee]{display:none}\r\n/* 收银台相关 */uni-text[data-v-b89d75ee],\r\nuni-view[data-v-b89d75ee]{font-size:.14rem}body[data-v-b89d75ee]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-b89d75ee]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-b89d75ee]::-webkit-scrollbar-button{display:none}body[data-v-b89d75ee]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-b89d75ee]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-b89d75ee]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-b89d75ee]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-b89d75ee]{color:var(--primary-color)!important}.goodslist[data-v-b89d75ee]{width:100%;height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.goodslist .goodslist-box[data-v-b89d75ee]{width:100%;height:100%;background:#fff;display:flex}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee]{width:5rem;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;overflow:hidden;position:relative}.goodslist .goodslist-box .goodslist-left .notYet[data-v-b89d75ee]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center}.goodslist .goodslist-box .goodslist-left .screen-content[data-v-b89d75ee]{padding:.1rem 0;box-sizing:border-box;display:flex;flex-direction:column;height:calc(100% - .6rem)}.goodslist .goodslist-box .goodslist-left .screen-content .search-btn[data-v-b89d75ee]{display:flex;align-items:center;padding:.2rem .2rem;background-color:#fff;box-shadow:0 -.02rem .05rem #e5e5e5}.goodslist .goodslist-box .goodslist-left .screen-content .search-btn .btn[data-v-b89d75ee]{flex:1;font-size:.18rem;line-height:.5rem;text-align:center;background-color:#f2f3f5;border-radius:.5rem;cursor:pointer}.goodslist .goodslist-box .goodslist-left .screen-content .search-btn .btn[data-v-b89d75ee]:last-child{color:#fff;background-color:var(--primary-color);margin-left:.15rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box[data-v-b89d75ee]{padding:0 .2rem;flex:1;height:0;box-sizing:border-box}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box[data-v-b89d75ee] .uni-scroll-view::-webkit-scrollbar{display:none}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item[data-v-b89d75ee]{margin:.2rem 0}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .tit[data-v-b89d75ee]{font-weight:700;font-size:.16rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values[data-v-b89d75ee]{margin-top:.16rem;display:flex;flex-wrap:wrap}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .value[data-v-b89d75ee]{cursor:pointer;font-size:.15rem;line-height:1;padding:.12rem .24rem;background-color:#f2f3f5;border-radius:.5rem;margin-right:.14rem;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .value.active[data-v-b89d75ee]{color:#fff;background-color:var(--primary-color)}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-range[data-v-b89d75ee]{display:flex;align-items:center;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-range .line[data-v-b89d75ee]{margin:0 .1rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] {cursor:pointer;font-size:.15rem;line-height:1;padding:.12rem .1rem;background-color:#f2f3f5;border-radius:.5rem}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date__icon-clear{border:none}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date-x{background-color:initial}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date__x-input{height:.15rem;min-height:auto}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date-single--x{top:2.43rem!important;position:fixed}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date-editor--x{border:none}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-icons.uniui-calendar{display:none}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-input-placeholder{text-align:center}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uni-date__x-input{line-height:1}.goodslist .goodslist-box .goodslist-left .screen-content .screen-box .screen-item .values .time-value[data-v-b89d75ee] .uniui-clear{line-height:.15rem}.goodslist .goodslist-box .goodslist-left .goods-title[data-v-b89d75ee]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.goodslist .goodslist-box .goodslist-left .goods-title .screen-btn[data-v-b89d75ee]{cursor:pointer;position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.14rem;color:var(--primary-color)}.goodslist .goodslist-box .goodslist-left .goods-search[data-v-b89d75ee]{width:100%;height:.6rem;border-bottom:.01rem solid #e6e6e6;display:flex;align-items:center;justify-content:center;padding:0 .2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-left .goods-search .search[data-v-b89d75ee]{width:5.6rem;height:.4rem;border-radius:.04rem;background:#f5f5f5;display:flex;align-items:center;padding:0 .2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-left .goods-search .search .iconfont[data-v-b89d75ee]{font-size:.16rem;color:#909399;margin-right:.11rem}.goodslist .goodslist-box .goodslist-left .goods-search .search uni-input[data-v-b89d75ee]{width:80%;height:60%;border:none;font-size:.14rem}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll{width:100%;height:calc(100% - 1.95rem)}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .itemhover{background:var(--primary-color-light-9)}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item{padding:.2rem}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item .title{display:flex;align-items:center;justify-content:space-between;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item .title uni-view{font-size:.16rem}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item .title uni-view:nth-child(2){color:var(--primary-color)}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item .total-money-num{display:flex;align-items:center;justify-content:space-between;width:100%}.goodslist .goodslist-box .goodslist-left[data-v-b89d75ee] .goods-list-scroll .item .total-money-num .refund-state{border:.01rem solid var(--primary-color);color:var(--primary-color);line-height:1;padding:.03rem;border-radius:.02rem}.goodslist .goodslist-box .goodslist-right[data-v-b89d75ee]{flex:1;width:0;height:100%;box-sizing:border-box;position:relative;padding-bottom:.8rem;overflow:hidden}.goodslist .goodslist-box .goodslist-right .goods-title[data-v-b89d75ee]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.goodslist .goodslist-box .goodslist-right .cart-empty[data-v-b89d75ee]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.goodslist .goodslist-box .goodslist-right .order-information[data-v-b89d75ee]{width:100%;height:calc(100% - .6rem);background:#f8f8f8;padding:.2rem;box-sizing:border-box;overflow-y:auto;-webkit-transform:translate(0);transform:translate(0)}.goodslist .goodslist-box .goodslist-right .order-information .notYet[data-v-b89d75ee]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center}.goodslist .goodslist-box .goodslist-right .order-information .order-status[data-v-b89d75ee]{font-size:.24rem;font-weight:700;margin-bottom:.24rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types[data-v-b89d75ee]{width:100%;min-height:1rem;background:#fff;padding:.2rem .3rem;display:flex;flex-direction:column;justify-content:space-between;margin-bottom:.2rem;box-sizing:border-box;line-height:1.8}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type[data-v-b89d75ee]{padding-left:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view[data-v-b89d75ee]{font-size:.14rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view .look[data-v-b89d75ee]{color:var(--primary-color);margin-left:.24rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view[data-v-b89d75ee]:nth-child(1){width:1.3rem;text-align:right;margin-right:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type1[data-v-b89d75ee]{display:flex;align-items:center}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type2[data-v-b89d75ee]{display:flex}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type2 uni-view[data-v-b89d75ee]{float:left;font-size:.14rem;width:20%}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type2 uni-view[data-v-b89d75ee]:last-child{flex:1;width:0;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamporder-types:2;line-clamp:2;-webkit-box-orient:vertical}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type3[data-v-b89d75ee]{margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-right .order-information .other-information[data-v-b89d75ee]{width:100%;background:#fff;padding:.2rem .3rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .order-information .other-information .title[data-v-b89d75ee]{font-size:.18rem;font-weight:550;margin-bottom:.25rem}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-box[data-v-b89d75ee]{width:100%;display:flex;flex-wrap:wrap;justify-content:space-between}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-box .item[data-v-b89d75ee]{display:flex;align-items:center;width:50%;margin-top:.2rem;padding-left:.1rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-box .item uni-view[data-v-b89d75ee]:nth-child(1){width:1.3rem;text-align:right;margin-right:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-box .look[data-v-b89d75ee]{margin-left:.15rem;color:var(--primary-color)}.goodslist .goodslist-box .goodslist-right .order-information .other-information .img-box[data-v-b89d75ee]{display:flex;flex-wrap:wrap}.goodslist .goodslist-box .goodslist-right .order-information .other-information .img-box .img[data-v-b89d75ee]{width:1rem;height:1rem;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:.15rem;margin-bottom:.15rem;position:relative;border-radius:.05rem;line-height:1;overflow:hidden}.goodslist .goodslist-box .goodslist-right .order-information .other-information .img-box .img uni-image[data-v-b89d75ee]{width:100%}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info[data-v-b89d75ee]{padding:.25rem 0;border-bottom:.01rem solid #f5f5f5}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info[data-v-b89d75ee]:last-child{border-bottom:none}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info[data-v-b89d75ee]:first-child{padding-top:0}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .info-tit[data-v-b89d75ee]{font-weight:700;font-size:.16rem;margin-bottom:.16rem}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .infos[data-v-b89d75ee]{display:flex;align-items:flex-start;flex-wrap:wrap;font-size:.14rem}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .infos.remark[data-v-b89d75ee]{flex-direction:column;align-items:flex-start}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .infos.remark .info[data-v-b89d75ee]{flex:1;width:auto}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .infos .info[data-v-b89d75ee]{color:#666;margin-right:.16rem;margin-bottom:.16rem;width:calc((100% - .16rem * 3) / 3)}.goodslist .goodslist-box .goodslist-right .order-information .other-information .item-info .infos .info .look[data-v-b89d75ee]{margin-left:.15rem;color:var(--primary-color);cursor:pointer}.goodslist .goodslist-box .goodslist-right .remarks-box[data-v-b89d75ee]{width:100%;position:absolute;bottom:0;right:0;background:#fff;padding:.24rem .2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .remarks-box .remarks[data-v-b89d75ee]{min-width:.9rem;height:.4rem;text-align:center;line-height:.4rem;float:right;margin-left:.1rem}.goodslist .goodslist-box .goodslist-right .total-money-num[data-v-b89d75ee]{width:100%;display:flex;flex-wrap:wrap;justify-content:space-between;padding:.1rem}.goodslist .goodslist-box .goodslist-right .total-money-num .box[data-v-b89d75ee]{color:#333}.goodslist .goodslist-box .goodslist-right .total-money-num .box uni-view[data-v-b89d75ee]:nth-child(1){width:.9rem;text-align:right;margin-right:-.6rem}.goodslist .goodslist-box .goodslist-right .total-money-num .money[data-v-b89d75ee]{text-align:right;width:1.2rem;color:#333;font-size:.14rem}.goodslist .goodslist-box .goodslist-right .total-money-num .total .money[data-v-b89d75ee]{color:#fe2278;font-size:.18rem}.total-money-num .member-info[data-v-b89d75ee]{display:flex;align-items:center;float:left;-ms-flex-negative:0;-webkit-flex-shrink:0;flex-shrink:0;margin:0 .1rem}.total-money-num .member-info .member-info-name[data-v-b89d75ee]{width:%?140?%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-money-num .box[data-v-b89d75ee]{display:flex;align-items:center;width:50%}.total-money-num .box uni-view[data-v-b89d75ee]{font-size:.14rem}.total-money-num .box uni-view[data-v-b89d75ee]:nth-child(2){color:#fe2278;font-size:.18rem}uni-view[data-v-b89d75ee]{color:#303133}[data-v-b89d75ee] .uni-scroll-view::-webkit-scrollbar{width:.05rem;height:.3rem}[data-v-b89d75ee] .uni-scroll-view::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.order-information[data-v-b89d75ee]::-webkit-scrollbar{width:.05rem;height:.3rem}.order-information[data-v-b89d75ee]::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.order-close[data-v-b89d75ee]{width:3rem;min-height:1.5rem;border-radius:.06rem;background:#fff;padding:.4rem .15rem 0;box-sizing:border-box}.order-close .title[data-v-b89d75ee]{font-size:.16rem;text-align:center}.order-close .btn[data-v-b89d75ee]{width:100%;display:flex;align-items:center;justify-content:center;margin-top:.3rem}.order-close .btn .btn[data-v-b89d75ee]{width:auto;padding:0 .15rem;margin:0;height:.35rem}.order-close .btn .btn[data-v-b89d75ee]:last-child{margin-left:.25rem}.order-delivery[data-v-b89d75ee]{width:50vw;border-radius:.06rem;background:#fff;padding:.2rem;box-sizing:border-box}.order-delivery.local[data-v-b89d75ee]{width:50vw}.order-delivery.express[data-v-b89d75ee]{width:65vw}.order-delivery > .title[data-v-b89d75ee]{text-align:left;font-size:.16rem;margin-bottom:.15rem}.order-delivery .content-item[data-v-b89d75ee]{display:flex;margin-top:.15rem;align-items:center}.order-delivery .content-item .title[data-v-b89d75ee]{width:1.5rem;text-align:right}.order-delivery .content-item .info[data-v-b89d75ee]{flex:1;width:0;padding-left:.1rem}.order-delivery .content-item .info uni-text[data-v-b89d75ee]{margin-right:.05rem}.order-delivery .content-item .info .select[data-v-b89d75ee]{width:2.3rem}.order-delivery .content-item .info .input[data-v-b89d75ee]{width:2.3rem;border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6;box-sizing:border-box}.order-delivery .common-scrollbar[data-v-b89d75ee]{overflow-y:auto}.order-delivery .btn[data-v-b89d75ee]{width:100%;display:flex;align-items:center;justify-content:center;margin-top:.3rem}.order-delivery .btn .btn[data-v-b89d75ee]{width:auto;padding:0 .15rem;margin:0;height:.35rem}.order-delivery .btn .btn[data-v-b89d75ee]:last-child{margin-left:.25rem}.refund-wrap[data-v-b89d75ee]{padding-bottom:0!important;background:#fff;display:flex;flex-direction:column}.refund-wrap .content[data-v-b89d75ee]{flex:1;height:0;overflow-y:scroll}.refund-wrap .content > uni-view[data-v-b89d75ee]{min-height:100%}.refund-wrap .content .bg-grey[data-v-b89d75ee]{background:#f8f8f8}.refund-wrap .remarks-box[data-v-b89d75ee]{position:unset!important;border-top:.01rem solid #e6e6e6;text-align:center;display:flex;align-items:center;justify-content:flex-end}.refund-wrap .remarks-box .remarks[data-v-b89d75ee]{margin:0 0 0 .1rem}.refund-wrap .goods-item[data-v-b89d75ee]{margin:0 .15rem;padding:.15rem 0;border-bottom:.01rem solid #e6e6e6;display:flex}.refund-wrap .goods-item .iconfont[data-v-b89d75ee]{font-size:.2rem;cursor:pointer;display:flex;align-items:center}.refund-wrap .goods-item .iconyuan_checked[data-v-b89d75ee]{color:var(--primary-color)}.refund-wrap .goods-item .image[data-v-b89d75ee]{width:.8rem;height:.8rem;overflow:hidden;display:flex;align-items:center;justify-content:center;margin:0 .15rem}.refund-wrap .goods-item .image uni-image[data-v-b89d75ee]{width:inherit}.refund-wrap .goods-item .info[data-v-b89d75ee]{flex:1;margin-right:.15rem}.refund-wrap .goods-item .price[data-v-b89d75ee]{text-align:right;font-weight:bolder}.refund-wrap .goods-item .num[data-v-b89d75ee]{text-align:right}.refund-wrap .refund-goods-item[data-v-b89d75ee]{padding:.15rem;background:#fff;margin-bottom:.15rem}.refund-wrap .refund-goods-item .row[data-v-b89d75ee]{display:flex}.refund-wrap .refund-goods-item .row .title[data-v-b89d75ee]{width:1rem;text-align:right;margin-right:.15rem}.refund-wrap .refund-goods-item .row .cont[data-v-b89d75ee]{flex:1;width:0;display:flex}.refund-wrap .refund-goods-item .row .cont.tips[data-v-b89d75ee]{flex-direction:column}.refund-wrap .refund-goods-item .row .cont.tips uni-view[data-v-b89d75ee]{color:#999}.refund-wrap .refund-goods-item .row .goods-item[data-v-b89d75ee]{margin:0;padding:0 0 .15rem 0;border:none}.refund-wrap .refund-goods-item .row .goods-item .image[data-v-b89d75ee]{margin:0 .15rem 0 0}.refund-wrap .refund-goods-item .row .money-box[data-v-b89d75ee]{width:1rem;display:flex;border:.01rem solid #e6e6e6;padding:.05rem .1rem;align-items:center}.refund-wrap .refund-goods-item .row .money-box uni-input[data-v-b89d75ee]{padding-right:.1rem}.refund-wrap .refund-goods-item .row .refund-money[data-v-b89d75ee]{margin-top:.1rem;color:#999;line-height:1;font-size:.13rem;margin-left:.1rem}.refund-wrap .refund-goods-item .row .refund-money uni-text[data-v-b89d75ee]{font-weight:700;color:#fe2278;font-size:.13rem}.refund-wrap .refund-goods-item .row uni-textarea[data-v-b89d75ee]{height:1rem;font-size:.14rem}.refund-wrap .refund-goods-item .row .placeholder[data-v-b89d75ee]{color:#999;font-size:.14rem}.refund-wrap .refund-type[data-v-b89d75ee]{margin:.15rem;padding:.15rem;border:.01rem solid #e6e6e6;border-radius:.06rem;text-align:center;cursor:pointer}.refund-wrap .refund-type .title[data-v-b89d75ee]{font-size:.16rem;font-weight:700}.refund-wrap .refund-type .desc[data-v-b89d75ee]{color:#999;margin-top:.1rem}.refund-wrap .refund-type.active[data-v-b89d75ee]{border-color:var(--primary-color);background-color:var(--primary-color-light-9)}.order-type-list[data-v-b89d75ee]{padding:.1rem .15rem;display:flex}.order-type-list .class-item[data-v-b89d75ee]{padding:.05rem .8rem;color:#303133;border:.01rem solid #e6e6e6;margin-right:.15rem}.order-type-list .class-item.active[data-v-b89d75ee]{border-color:var(--primary-color);color:var(--primary-color)}.goods-info[data-v-b89d75ee]{min-height:2.7rem;background:#fff;padding:.2rem;box-sizing:border-box}.goods-info .title[data-v-b89d75ee]{font-size:.18rem;font-weight:550;margin-bottom:.2rem}.goods-info .table[data-v-b89d75ee]{width:100%;box-sizing:border-box;margin-bottom:.2rem}.goods-info .table .table-all[data-v-b89d75ee]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:0 .1rem 0 .38rem;box-sizing:border-box}.goods-info .table .table-all .table-td[data-v-b89d75ee]{font-size:.14rem;text-align:left;display:flex;align-items:center}.goods-info .table .table-all .table-td uni-image[data-v-b89d75ee]{margin-right:.1rem}.goods-info .table .table-th[data-v-b89d75ee]{height:.56rem;background:#f7f8fa}.goods-info .table .table-tr[data-v-b89d75ee]{height:.7rem;border-bottom:.01rem solid #e6e6e6}.goods-info .table .table-tr .table-td uni-image[data-v-b89d75ee]{width:.5rem;height:.5rem}.goods-info .table .table-tr .table-td .gift-tag[data-v-b89d75ee]{font-size:.12rem;background:red;margin-right:.05rem;color:#fff;line-height:1;padding:.01rem .05rem;border-radius:.03rem}.goods-info .table .table-tr .table-td .refun-status[data-v-b89d75ee]{color:var(--primary-color);font-size:.13rem;margin-top:.05rem}.goods-info .table .table-tr .table-td .content-text[data-v-b89d75ee]{width:80%;height:.4rem;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.goods-info .table .table-tr .table-td .content-text .text-color-gray[data-v-b89d75ee]{color:#909399}.goods-info .table .refund-success[data-v-b89d75ee]{color:var(--primary-color);font-size:.12rem;margin-top:.05rem}.goods-info .table .goods-form[data-v-b89d75ee]{height:auto;padding:.1rem}.goods-info .table .goods-form .table-td[data-v-b89d75ee]{flex-wrap:wrap}.goods-info .table .goods-form .order-cell[data-v-b89d75ee]{display:flex;flex:1;min-width:100%;max-width:100%;line-height:1.5}.goods-info .table .goods-form .img-box[data-v-b89d75ee]{display:flex;flex-wrap:wrap}.goods-info .table .goods-form .img-box .img[data-v-b89d75ee]{width:1rem;height:1rem;display:flex;flex-direction:column;justify-content:center;align-items:center;margin-right:.15rem;margin-bottom:.15rem;position:relative;border-radius:.05rem;line-height:1;overflow:hidden}.goods-info .table .goods-form .img-box .img uni-image[data-v-b89d75ee]{width:100%}.order-refund-agree[data-v-b89d75ee]{width:40vw;border-radius:.06rem;background:#fff;padding:.2rem;box-sizing:border-box}.order-refund-agree > .title[data-v-b89d75ee]{text-align:left;font-size:.16rem;margin-bottom:.15rem}.order-refund-agree .content-item[data-v-b89d75ee]{display:flex;margin-top:.15rem;align-items:center}.order-refund-agree .content-item.textarea-wrap[data-v-b89d75ee]{align-items:baseline}.order-refund-agree .content-item .title[data-v-b89d75ee]{width:1.5rem;text-align:right}.order-refund-agree .content-item .info[data-v-b89d75ee]{flex:1;width:0;padding-left:.1rem}.order-refund-agree .content-item .info uni-text[data-v-b89d75ee]{margin-right:.05rem}.order-refund-agree .content-item .info.textarea-box[data-v-b89d75ee]{height:2rem;border:.01rem solid #e6e6e6;border-radius:.06rem;padding:.15rem;box-sizing:border-box}.order-refund-agree .content-item .info.textarea-box .textarea[data-v-b89d75ee]{width:100%;height:100%}.order-refund-agree .content-item .info .money-box[data-v-b89d75ee]{width:2rem;display:flex;border:.01rem solid #e6e6e6;padding:.05rem .1rem;align-items:center}.order-refund-agree .content-item .info .money-box uni-input[data-v-b89d75ee]{padding-right:.1rem}.order-refund-agree .content-item .info .form-radio-item[data-v-b89d75ee]{margin-right:.1rem;display:inline-flex;align-items:center}.order-refund-agree .btn[data-v-b89d75ee]{width:100%;display:flex;align-items:center;justify-content:center;margin-top:.3rem}.order-refund-agree .btn .btn[data-v-b89d75ee]{width:auto;padding:0 .15rem;margin:0;height:.35rem}.order-refund-agree .btn .btn[data-v-b89d75ee]:last-child{margin-left:.25rem}.order-adjust-money[data-v-b89d75ee]{width:60vw;border-radius:.06rem;background:#fff;padding:.2rem;box-sizing:border-box}.order-adjust-money > .title[data-v-b89d75ee]{width:100%;height:.5rem;border-bottom:.01rem solid #e6e6e6;line-height:.5rem;font-size:.16rem;font-weight:700;display:flex;justify-content:space-between}.order-adjust-money > .tip[data-v-b89d75ee]{font-size:.15rem;margin:.2rem 0;color:#666}.order-adjust-money > .tip .Highlight[data-v-b89d75ee]{color:var(--primary-color)}.order-adjust-money > .tip.m-0[data-v-b89d75ee]{margin:0 0 .04rem 0}.order-adjust-money > .table[data-v-b89d75ee]{width:100%;box-sizing:border-box;margin-bottom:.15rem}.order-adjust-money > .table .table-all[data-v-b89d75ee]{width:100%;display:flex;align-items:stretch;box-sizing:border-box}.order-adjust-money > .table .table-all .table-td[data-v-b89d75ee]{font-size:.14rem;text-align:left;display:flex;align-items:center;box-sizing:border-box;flex-wrap:wrap}.order-adjust-money > .table .table-all .table-td.left[data-v-b89d75ee]{border-left:.01rem solid #e6e6e6}.order-adjust-money > .table .table-all .table-td .table-tr[data-v-b89d75ee]{-ms-flex-negative:0;-webkit-flex-shrink:0;flex-shrink:0}.order-adjust-money > .table .table-all .table-td .table-tr[data-v-b89d75ee]:last-child{border-width:0}.order-adjust-money > .table .table-th[data-v-b89d75ee]{height:.56rem;background:#f7f8fa}.order-adjust-money > .table .table-tr[data-v-b89d75ee]{min-height:.7rem;border-bottom:.01rem solid #e6e6e6}.order-adjust-money > .table .table-tr .table-td uni-image[data-v-b89d75ee]{width:.5rem;height:.5rem}.order-adjust-money > .table .table-tr .table-td .gift-tag[data-v-b89d75ee]{font-size:.12rem;background:red;margin-right:.05rem;color:#fff;line-height:1;padding:.01rem .05rem;border-radius:.03rem}.order-adjust-money > .table .table-tr .table-td .refun-status[data-v-b89d75ee]{color:var(--primary-color);font-size:.13rem;margin-top:.05rem}.order-adjust-money > .table .table-tr .table-td .content-text[data-v-b89d75ee]{width:80%;height:.4rem;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.order-adjust-money > .table .refund-success[data-v-b89d75ee]{color:var(--primary-color);font-size:.12rem;margin-top:.05rem}.order-adjust-money > .table uni-input[data-v-b89d75ee]{height:.3rem;border:.01rem solid #ccc;text-align:center;padding:0 .1rem;box-sizing:border-box;transition:all .3s}.order-adjust-money > .table uni-input.focus[data-v-b89d75ee]{border-color:var(--primary-color);box-shadow:0 0 .02rem .02rem var(--primary-color-light-7)}.order-adjust-money .footer[data-v-b89d75ee]{-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;display:flex}.order-adjust-money .footer uni-button[data-v-b89d75ee]{margin:0}.order-adjust-money .footer uni-button.clear[data-v-b89d75ee]{margin-left:.15rem}.tab-wrap[data-v-b89d75ee]{padding:0!important;background-color:#fff!important}.tab-wrap .tab-head[data-v-b89d75ee]{display:flex;background-color:#f8f8f8}.tab-wrap .tab-head uni-text[data-v-b89d75ee]{width:1.15rem;height:.55rem;line-height:.55rem;text-align:center;font-size:.16rem}.tab-wrap .tab-head uni-text.active[data-v-b89d75ee]{background-color:#fff}.item-box[data-v-b89d75ee]{padding:.1rem}.form-radio-item[data-v-b89d75ee]{margin-right:.1rem;display:inline-flex;align-items:center}.message[data-v-b89d75ee]{width:5.2rem;min-height:3.2rem;border-radius:.06rem;background:#fff;padding-bottom:.15rem}.message .title[data-v-b89d75ee]{width:100%;height:.5rem;border-bottom:.01rem solid #e6e6e6;text-align:center;line-height:.5rem;font-size:.16rem;font-weight:700;position:relative}.message .title .iconguanbi1[data-v-b89d75ee]{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);right:.15rem;font-size:.18rem}.message .textarea-box[data-v-b89d75ee]{margin:.15rem;height:2.2rem;border:.01rem solid #e6e6e6;border-radius:.06rem;padding:.15rem;box-sizing:border-box}.message .textarea-box .textarea[data-v-b89d75ee]{width:100%;height:100%}.message .save[data-v-b89d75ee]{width:auto!important;float:right;margin-right:.15rem}.message[data-v-b89d75ee]:after{overflow:hidden;display:block;content:"";height:0;clear:both}',""]),e.exports=t},fdd3:function(e,t,i){var o=i("c86c");t=o(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-16008ec4]{display:none}\r\n/* 收银台相关 */uni-text[data-v-16008ec4],\r\nuni-view[data-v-16008ec4]{font-size:.14rem}body[data-v-16008ec4]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-16008ec4]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-16008ec4]::-webkit-scrollbar-button{display:none}body[data-v-16008ec4]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-16008ec4]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-16008ec4]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-16008ec4]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-16008ec4]{color:var(--primary-color)!important}@-webkit-keyframes spin-data-v-16008ec4{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes spin-data-v-16008ec4{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.loading-layer[data-v-16008ec4]{width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:#fff}.loading-anim[data-v-16008ec4]{position:absolute;left:50%;top:40%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.loading-anim > .item[data-v-16008ec4]{position:relative;width:.3rem;height:.3rem;-webkit-perspective:8rem;perspective:8rem;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;transition:all .2s ease-out}.loading-anim .border[data-v-16008ec4]{position:absolute;border-radius:50%;border:.03rem solid var(--primary-color)}.loading-anim .out[data-v-16008ec4]{top:15%;left:15%;width:70%;height:70%;border-right-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-16008ec4 .6s linear normal infinite;animation:spin-data-v-16008ec4 .6s linear normal infinite}.loading-anim .in[data-v-16008ec4]{top:25%;left:25%;width:50%;height:50%;border-top-color:transparent!important;border-bottom-color:transparent!important;-webkit-animation:spin-data-v-16008ec4 .8s linear infinite;animation:spin-data-v-16008ec4 .8s linear infinite}.loading-anim .mid[data-v-16008ec4]{top:40%;left:40%;width:20%;height:20%;border-left-color:transparent;border-right-color:transparent;-webkit-animation:spin-data-v-16008ec4 .6s linear infinite;animation:spin-data-v-16008ec4 .6s linear infinite}',""]),e.exports=t}}]);