(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-scale-list"],{"1beb":function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return l})),i.d(e,"a",(function(){return a}));var a={uniPopup:i("cea0").default},s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"scalelist"},[a("v-uni-view",{staticClass:"scalelist-box"},[a("v-uni-view",{staticClass:"scalelist-left"},[a("v-uni-view",{staticClass:"scale-title"},[t._v("电子秤"),a("v-uni-text",{staticClass:"iconfont icongengduo1"})],1),a("v-uni-view",{staticClass:"scale-list-wrap"},[t.list.length>0?[a("v-uni-scroll-view",{staticClass:"scale-list-scroll all-scroll",attrs:{"scroll-y":"true"},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getList.apply(void 0,arguments)}}},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"item",class:i==t.selectScaleKeys?"itemhover":"",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.scaleSelect(e,i)}}},[a("v-uni-view",{staticClass:"item-right w-full"},[a("v-uni-view",{staticClass:"flex justify-between w-full"},[a("v-uni-view",{staticClass:"scale-name"},[t._v(t._s(e.name)),a("v-uni-text",{staticClass:"scale-type-tag"},[t._v(t._s("cashier"==e.type?"收银秤":"条码秤"))])],1),e.connect_status?a("v-uni-view",{staticClass:"flex items-center"},[a("v-uni-text",{staticClass:"status-icon success"}),t._v("已连接")],1):a("v-uni-view",{staticClass:"flex items-center"},[a("v-uni-text",{staticClass:"status-icon fail"}),t._v("未连接")],1)],1),a("v-uni-view",{staticClass:"scale-money"},[t._v(t._s(e.brand_name)+"-"+t._s(e.model_name))])],1)],1)})),1)]:t.one_judge||0!=t.list.length?t._e():a("v-uni-view",{staticClass:"notYet"},[t._v("暂无电子秤")])],2),a("v-uni-view",{staticClass:"add-printer"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addScale.apply(void 0,arguments)}}},[t._v("添加电子秤")])],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.one_judge,expression:"!one_judge"}],staticClass:"scalelist-right"},[a("v-uni-view",{staticClass:"scale-title"},[t._v("电子秤详情")]),a("v-uni-view",{staticClass:"scale-information"},["{}"!=JSON.stringify(t.detail)?[a("v-uni-view",{staticClass:"title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"information-box"},[a("v-uni-view",{staticClass:"box-left"},[a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("电子秤名称：")]),a("v-uni-view",[t._v(t._s(t.detail.name))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("电子秤类型：")]),a("v-uni-view",[t._v(t._s("cashier"==t.detail.type?"收银秤":"条码秤"))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("电子秤品牌：")]),a("v-uni-view",[t._v(t._s(t.detail.brand_name))])],1),a("v-uni-view",{staticClass:"information"},[a("v-uni-view",[t._v("电子秤型号：")]),a("v-uni-view",[t._v(t._s(t.detail.model_name))])],1)],1)],1)]:[a("v-uni-image",{staticClass:"cart-empty",attrs:{src:i("be06"),mode:"widthFix"}})]],2),"{}"!=JSON.stringify(t.detail)?a("v-uni-view",{staticClass:"button-box"},[a("v-uni-button",{staticClass:"default-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.deletePop.open()}}},[t._v("删除")]),a("v-uni-button",{staticClass:"default-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editScale(t.detail.scale_id)}}},[t._v("修改")])],1):t._e()],1)],1)],1),a("uni-popup",{ref:"deletePop",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"confirm-pop"},[a("v-uni-view",{staticClass:"title"},[t._v("确定要删除吗？")]),a("v-uni-view",{staticClass:"btn"},[a("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.deletePop.close()}}},[t._v("取消")]),a("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteScaleFn(t.detail.scale_id)}}},[t._v("确定")])],1)],1)],1)],1)},l=[]},"44e7":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("4626"),i("5ac7"),i("c223"),i("e966"),i("fd3c"),i("d4b5");var a,s=i("8b8b"),l={data:function(){return{selectScaleKeys:0,search_text:"",page:1,page_size:8,one_judge:!0,detail:{},brandList:{yilianyun:"易联云",365:"365"},flag:!1,template:{},list:[],connectSuccess:[]}},onLoad:function(){this.getList(),a=this},methods:{switchStoreAfter:function(){this.page=1,this.list=[],this.detail={},this.one_judge=!1,this.getList()},getList:function(){var t=this;this.addon.includes("scale")?(0,s.getScaleList)({page:this.page,page_size:this.page_size}).then((function(e){0==e.data.list.length&&t.one_judge&&(t.detail={},t.one_judge=!1),e.code>=0&&0!=e.data.list.length&&(t.page+=1,0==t.list.length?t.list=e.data.list:t.list=t.list.concat(e.data.list),t.checkConnect(),t.one_judge&&t.getDetailFn(t.list[0].scale_id))})):this.$util.showToast({title:"未安装电子秤插件"})},scaleSelect:function(t,e){this.selectScaleKeys=e,this.getDetailFn(t.scale_id)},addScale:function(){this.$util.redirectTo("/pages/scale/add")},editScale:function(t){this.$util.redirectTo("/pages/scale/add",{scale_id:t})},getDetailFn:function(t){var e=this;(0,s.getScaleDetail)({scale_id:t}).then((function(t){0==t.code&&(e.detail=t.data,e.one_judge=!1)}))},deleteScaleFn:function(t){var e=this;this.flag||(this.flag=!0,(0,s.deleteScale)({scale_id:t}).then((function(t){e.flag=!1,t.code>=0?(e.page=1,e.list=[],e.one_judge=!0,e.$refs.deletePop.close(),e.getList()):e.$util.showToast({title:t.message})})))},checkConnect:function(){"function"==typeof window.POS_DATA_CALLBACK&&delete window.POS_DATA_CALLBACK,window.POS_DATA_CALLBACK=function(t){var e=t.split(":"),i=parseInt(e[0]);switch(e[1]){case"PingWeigher":a.$set(a.list[i],"connect_status",parseInt(e[3]));break}};try{var t=this.list.map((function(t){return t.config="string"==typeof t.config?JSON.parse(t.config):scale.config,t}));this.$pos.send("PingWeigher",JSON.stringify({weigher:t}))}catch(e){}}}};e.default=l},"8b8b":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addScale=function(t){return s.default.post("/scale/storeapi/scale/add",{data:t})},e.deleteScale=function(t){return s.default.post("/scale/storeapi/scale/delete",{data:t})},e.editScale=function(t){return s.default.post("/scale/storeapi/scale/edit",{data:t})},e.getScaleBrand=function(){return s.default.post("/scale/storeapi/scale/scaleBrand")},e.getScaleDetail=function(t){return s.default.post("/scale/storeapi/scale/detail",{data:t})},e.getScaleList=function(t){return s.default.post("/scale/storeapi/scale/page",{data:t})};var s=a(i("a3b5"))},be06:function(t,e,i){t.exports=i.p+"static/cashier/cart_empty.png"},c746:function(t,e,i){"use strict";i.r(e);var a=i("44e7"),s=i.n(a);for(var l in a)["default"].indexOf(l)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(l);e["default"]=s.a},e6c8:function(t,e,i){"use strict";var a=i("fedd"),s=i.n(a);s.a},ef05:function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-6288bf19]{display:none}\r\n/* 收银台相关 */uni-text[data-v-6288bf19],\r\nuni-view[data-v-6288bf19]{font-size:.14rem}body[data-v-6288bf19]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-6288bf19]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-6288bf19]::-webkit-scrollbar-button{display:none}body[data-v-6288bf19]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-6288bf19]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-6288bf19]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-6288bf19]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-6288bf19]{color:var(--primary-color)!important}.scalelist[data-v-6288bf19]{width:100%;height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.scalelist .scalelist-box[data-v-6288bf19]{width:100%;height:100%;background:#fff;display:flex}.scalelist .scalelist-box .scalelist-left[data-v-6288bf19]{width:5rem;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;display:flex;flex-direction:column}.scalelist .scalelist-box .scalelist-left .notYet[data-v-6288bf19]{color:#e6e6e6;font-size:.4rem;margin-top:3rem;text-align:center}.scalelist .scalelist-box .scalelist-left .add-printer[data-v-6288bf19]{padding:.24rem .2rem;background:#fff}.scalelist .scalelist-box .scalelist-left .add-printer uni-button[data-v-6288bf19]{height:.4rem;line-height:.4rem}.scalelist .scalelist-box .scalelist-left .scale-title[data-v-6288bf19]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.scalelist .scalelist-box .scalelist-left .scale-title .icongengduo1[data-v-6288bf19]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color)}.scalelist .scalelist-box .scalelist-left .scale-list-wrap[data-v-6288bf19]{flex:1;height:0}.scalelist .scalelist-box .scalelist-left .scale-list-scroll[data-v-6288bf19]{width:100%;height:100%}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .itemhover[data-v-6288bf19]{background:var(--primary-color-light-9)}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .item[data-v-6288bf19]{width:100%;display:flex;align-items:center;padding:.2rem;box-sizing:border-box;border-bottom:.01rem solid #e6e6e6}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .item uni-image[data-v-6288bf19]{width:.7rem;height:.7rem;margin-right:.1rem}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .item .item-right[data-v-6288bf19]{display:flex;flex-direction:column;justify-content:space-between;height:.6rem}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .item .item-right .scale-name[data-v-6288bf19]{font-size:.16rem}.scalelist .scalelist-box .scalelist-left .scale-list-scroll .item .item-right .scale-money[data-v-6288bf19]{font-size:.14rem}.scalelist .scalelist-box .scalelist-right[data-v-6288bf19]{width:0;flex:1;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.scalelist .scalelist-box .scalelist-right .scale-title[data-v-6288bf19]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.scalelist .scalelist-box .scalelist-right .scale-title .icongengduo1[data-v-6288bf19], .scalelist .scalelist-box .scalelist-right .scale-title .iconguanbi1[data-v-6288bf19]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color);cursor:pointer}.scalelist .scalelist-box .scalelist-right .scale-information[data-v-6288bf19]{width:100%;padding:.2rem .2rem .88rem .2rem;box-sizing:border-box;height:calc(100% - .6rem);overflow:auto;position:relative}.scalelist .scalelist-box .scalelist-right .scale-information .title[data-v-6288bf19]{font-size:.18rem;margin-bottom:.32rem}.scalelist .scalelist-box .scalelist-right .scale-information .title2[data-v-6288bf19]{margin-bottom:.35rem}.scalelist .scalelist-box .scalelist-right .scale-information .information-box[data-v-6288bf19]{display:flex;justify-content:space-between}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left[data-v-6288bf19]{width:5rem}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left .information[data-v-6288bf19]{width:100%;padding-left:.1rem;box-sizing:border-box;display:flex;align-items:center;margin-bottom:.15rem}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left .information uni-view[data-v-6288bf19]{color:#303133;font-size:.14rem}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left .information uni-view[data-v-6288bf19]:nth-child(1){width:1.3rem;margin-right:.16rem;text-align:right}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left .information uni-view[data-v-6288bf19]:nth-child(2){width:74%;margin-right:.23rem;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .box-left .information[data-v-6288bf19]:last-child{margin-bottom:.35rem}.scalelist .scalelist-box .scalelist-right .scale-information .information-box .scale-img[data-v-6288bf19]{width:1.5rem;height:1.5rem}.scalelist .scalelist-box .scalelist-right .scale-information .table[data-v-6288bf19]{width:100%;height:2.6rem;box-sizing:border-box}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-all[data-v-6288bf19]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:0 .38rem;box-sizing:border-box}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-all .table-td[data-v-6288bf19]{font-size:.14rem;text-align:left;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-th[data-v-6288bf19]{height:.56rem;background:#f7f8fa}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-tb[data-v-6288bf19]{width:100%;height:calc(100% - .56rem)}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-tb .table-tr[data-v-6288bf19]{height:.7rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-tb .table-tr .table-td[data-v-6288bf19]{text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.scalelist .scalelist-box .scalelist-right .scale-information .table .table-tb .table-tr .table-td uni-image[data-v-6288bf19]{width:.5rem;height:.5rem}uni-view[data-v-6288bf19]{color:#303133}[data-v-6288bf19] .uni-scroll-view::-webkit-scrollbar{width:.05rem;height:.3rem}[data-v-6288bf19] .uni-scroll-view::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.scale-information[data-v-6288bf19]::-webkit-scrollbar{width:.05rem;height:.3rem}.scale-information[data-v-6288bf19]::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.button-box[data-v-6288bf19]{position:absolute;width:100%;right:0;bottom:0;background-color:#fff;display:flex;align-items:center;justify-content:flex-end;padding:.24rem .2rem;box-sizing:border-box}.button-box uni-button[data-v-6288bf19]{min-width:.9rem;height:.4rem;line-height:.4rem;margin:0;margin-left:.1rem}.cart-empty[data-v-6288bf19]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.form-content[data-v-6288bf19]{margin-top:.2rem}.form-content .form-item[data-v-6288bf19]{margin-bottom:.1rem;display:flex}.form-content .form-item .form-label[data-v-6288bf19]{width:1.3rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.form-content .form-item .form-label .required[data-v-6288bf19]{color:red;margin-right:.03rem}.form-content .form-item .form-inline[data-v-6288bf19]{width:2.4rem;line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.form-content .form-item .form-inline .form-input[data-v-6288bf19]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6}.form-content .form-item .form-inline .form-textarea[data-v-6288bf19]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6;height:1rem}.form-content .form-item .form-inline uni-button[data-v-6288bf19]{width:calc(50% - .05rem);display:inline-block;margin-right:.1rem}.form-content .form-item .form-inline uni-button[data-v-6288bf19]:nth-child(2){margin-right:0}.order-type[data-v-6288bf19]{margin-right:.1rem}.status-icon[data-v-6288bf19]{width:.05rem;height:.05rem;border-radius:50%;margin-right:.05rem}.status-icon.success[data-v-6288bf19]{background:#32cd32}.status-icon.fail[data-v-6288bf19]{background:red}.scale-type-tag[data-v-6288bf19]{border:.01rem solid var(--primary-color);color:var(--primary-color);background-color:#fff;border-radius:.02rem;width:-webkit-fit-content;width:fit-content;padding:.01rem .05rem;margin-left:.1rem}',""]),t.exports=e},efc3:function(t,e,i){"use strict";i.r(e);var a=i("1beb"),s=i("c746");for(var l in s)["default"].indexOf(l)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(l);i("e6c8");var r=i("828b"),n=Object(r["a"])(s["default"],a["b"],a["c"],!1,null,"6288bf19",null,!1,a["a"],void 0);e["default"]=n.exports},fedd:function(t,e,i){var a=i("ef05");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=i("967d").default;s("0480419a",a,!0,{sourceMap:!1,shadowMode:!1})}}]);