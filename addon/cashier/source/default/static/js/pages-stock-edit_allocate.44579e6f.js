(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stock-edit_allocate"],{"1bf0":function(t,e,a){"use strict";var i=a("39fd"),o=a.n(i);o.a},3523:function(t,e,a){"use strict";a.r(e);var i=a("6e0b"),o=a("53f4");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("1bf0");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"3387bb78",null,!1,i["a"],void 0);e["default"]=r.exports},"39fd":function(t,e,a){var i=a("48fc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("2cfa7e01",i,!0,{sourceMap:!1,shadowMode:!1})},"48fc":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-3387bb78]{display:none}\r\n/* 收银台相关 */uni-text[data-v-3387bb78],\r\nuni-view[data-v-3387bb78]{font-size:.14rem}body[data-v-3387bb78]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-3387bb78]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-3387bb78]::-webkit-scrollbar-button{display:none}body[data-v-3387bb78]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-3387bb78]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-3387bb78]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-3387bb78]{color:var(--primary-color)!important}.uni-select-lay[data-v-3387bb78]{position:relative;z-index:999;box-sizing:border-box}.uni-select-lay .uni-select-input[data-v-3387bb78]{opacity:0;position:absolute;z-index:-111}.uni-select-lay .uni-select-lay-select[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:relative;z-index:3;height:.32rem;padding:0 .3rem 0 .1rem;box-sizing:border-box;border-radius:.02rem;border:.01rem solid #e5e5e5;display:flex;align-items:center;font-size:.14rem;color:#999}.uni-select-lay .uni-select-lay-select .uni-disabled[data-v-3387bb78]{position:absolute;left:0;width:100%;height:100%;z-index:19;cursor:no-drop;background:hsla(0,0%,100%,.5)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close[data-v-3387bb78]{position:absolute;right:.35rem;top:0;height:100%;width:.15rem;display:flex;align-items:center;justify-content:center;z-index:3;cursor:pointer}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]{position:relative;background:#fff;width:.13rem;height:.13rem;border-radius:50%;border:.01rem solid #bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::before, .uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{content:"";position:absolute;left:20%;top:50%;height:.01rem;width:60%;-webkit-transform:rotate(45deg);transform:rotate(45deg);background-color:#bbb}.uni-select-lay .uni-select-lay-select .uni-select-lay-input-close uni-text[data-v-3387bb78]::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.uni-select-lay .uni-select-lay-select .uni-select-lay-input[data-v-3387bb78]{font-size:.14rem;color:#999;display:block;width:98%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:.3rem;box-sizing:border-box}.uni-select-lay .uni-select-lay-select .uni-select-lay-input.active[data-v-3387bb78]{color:#333}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]{cursor:pointer;position:absolute;right:0;top:0;height:100%;width:.3rem;display:flex;align-items:center;justify-content:center}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon[data-v-3387bb78]::before{content:"";width:.01rem;height:100%;position:absolute;left:0;top:0;background-color:#e5e5e5}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon uni-text[data-v-3387bb78]{display:block;width:0;height:0;border-width:.07rem .07rem 0;border-style:solid;border-color:#bbb transparent transparent;transition:.3s}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled[data-v-3387bb78]{cursor:no-drop}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]{width:.2rem;height:.2rem;border:.02rem solid red;border-radius:50%;transition:.3s;position:relative;z-index:999}.uni-select-lay .uni-select-lay-select .uni-select-lay-icon.disabled uni-text[data-v-3387bb78]::after{content:"";position:absolute;top:50%;left:0;width:100%;height:.02rem;margin-top:-.01rem;background-color:red;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.uni-select-lay .uni-select-lay-select.active .uni-select-lay-icon uni-text[data-v-3387bb78]{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.uni-select-lay .uni-select-lay-options[data-v-3387bb78]{-webkit-user-select:none;user-select:none;position:absolute;top:calc(100% + .05rem);left:0;width:100%;max-height:2.5rem;border-radius:.02rem;border:1px solid #e5e5e5;background:#fff;padding:.05rem 0;box-sizing:border-box;z-index:9}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]{padding:0 .1rem;box-sizing:border-box;cursor:pointer;line-height:2.5;transition:.3s;font-size:.14rem}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.active[data-v-3387bb78]:hover{background:var(--primary-color);color:#fff}.uni-select-lay .uni-select-lay-options .uni-select-lay-item.disabled[data-v-3387bb78]{color:#999;cursor:not-allowed}.uni-select-lay .uni-select-lay-options .uni-select-lay-item[data-v-3387bb78]:hover{background-color:#f5f5f5}.uni-select-lay .uni-select-lay-options .nosearch[data-v-3387bb78]{font-size:.16rem;line-height:3;text-align:center;color:#666}.uni-date-mask[data-v-3387bb78]{position:fixed;bottom:0;top:0;left:0;right:0;background-color:transparent;transition-duration:.3s;z-index:8}',""]),t.exports=e},"53f4":function(t,e,a){"use strict";a.r(e);var i=a("6c5f"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},"610d":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-0bfe58bc]{display:none}\r\n/* 收银台相关 */uni-text[data-v-0bfe58bc],\r\nuni-view[data-v-0bfe58bc]{font-size:.14rem}body[data-v-0bfe58bc]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-0bfe58bc]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-0bfe58bc]::-webkit-scrollbar-button{display:none}body[data-v-0bfe58bc]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-0bfe58bc]::-webkit-scrollbar-track{background-color:initial}.stock-body .content-wrap[data-v-0bfe58bc], .stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]{height:100%;overflow:auto}.stock-body .content-wrap[data-v-0bfe58bc]::-webkit-scrollbar, .stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]::-webkit-scrollbar{width:.06rem;height:.06rem}.stock-body .content-wrap[data-v-0bfe58bc]::-webkit-scrollbar-button, .stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]::-webkit-scrollbar-button{display:none}.stock-body .content-wrap[data-v-0bfe58bc]::-webkit-scrollbar-thumb, .stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}.stock-body .content-wrap[data-v-0bfe58bc]::-webkit-scrollbar-track, .stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-0bfe58bc]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-0bfe58bc]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-0bfe58bc]{color:var(--primary-color)!important}.form-content[data-v-0bfe58bc]{display:flex;flex-wrap:wrap;margin-top:.2rem}.form-content .store-info .form-inline[data-v-0bfe58bc]{padding-left:.05rem}.form-content .form-item[data-v-0bfe58bc]{margin-bottom:.1rem;display:flex}.form-content .form-item .form-label[data-v-0bfe58bc]{width:1.3rem;text-align:right;padding-right:.1rem;box-sizing:border-box;height:.32rem;line-height:.32rem}.form-content .form-item .form-label .required[data-v-0bfe58bc]{color:red;margin-right:.03rem}.form-content .form-item .form-inline[data-v-0bfe58bc]{width:2.4rem;line-height:.32rem;margin-right:.1rem;box-sizing:border-box}.form-content .form-item .form-inline.input uni-input[data-v-0bfe58bc]{padding:0 .1rem}.form-content .form-item .form-inline .form-input[data-v-0bfe58bc]{border-width:.01rem;border-style:solid;background-color:#fff;color:rgba(0,0,0,.85);border-radius:.02rem;padding-left:.1rem;height:.32rem;line-height:.32rem;font-size:.14rem;border-color:#e6e6e6}.stock-body[data-v-0bfe58bc]{position:relative;height:100%}.stock-body .content-wrap[data-v-0bfe58bc]{padding:.15rem;background-color:#fff;box-sizing:border-box}.stock-body .content-wrap .title[data-v-0bfe58bc]{font-size:.18rem;margin-bottom:.2rem;text-align:center}.stock-body .content-wrap .table-wrap[data-v-0bfe58bc]{position:relative;margin-top:%?40?%;border:%?1?% solid #dcdfe6}.stock-body .content-wrap .table-wrap .table-head[data-v-0bfe58bc]{background-color:#f7f7f7}.stock-body .content-wrap .table-wrap .table-body[data-v-0bfe58bc]{max-height:6rem}.stock-body .content-wrap .table-wrap .table-body .table-tr[data-v-0bfe58bc]:nth-child(1){position:absolute;left:0;right:0;background:#fff;z-index:3}.stock-body .content-wrap .table-wrap .table-body .table-tr[data-v-0bfe58bc]:nth-child(2){margin-top:.49rem}.stock-body .content-wrap .table-wrap .table-body .table-tr:last-of-type .table-td[data-v-0bfe58bc]{border-bottom:0}.stock-body .content-wrap .table-wrap .table-tr[data-v-0bfe58bc]{display:flex}.stock-body .content-wrap .table-wrap .table-th[data-v-0bfe58bc],\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-0bfe58bc]{display:flex;align-items:center;justify-content:center;padding:.07rem .3rem;border-bottom:.01rem solid #dcdfe6;border-right:.01rem solid #dcdfe6;text-align:center}.stock-body .content-wrap .table-wrap .table-th[data-v-0bfe58bc]:last-of-type,\r\n.stock-body .content-wrap .table-wrap .table-td[data-v-0bfe58bc]:last-of-type{border-right:0;justify-content:flex-end}.stock-body .content-wrap .table-wrap .table-th.goods-name[data-v-0bfe58bc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name[data-v-0bfe58bc]{justify-content:flex-start}.stock-body .content-wrap .table-wrap .table-th.goods-name uni-image[data-v-0bfe58bc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name uni-image[data-v-0bfe58bc]{width:.45rem;height:.45rem;flex-shrink:0}.stock-body .content-wrap .table-wrap .table-th.goods-name .name[data-v-0bfe58bc],\r\n.stock-body .content-wrap .table-wrap .table-td.goods-name .name[data-v-0bfe58bc]{margin-left:.1rem}.stock-body .content-wrap .table-wrap .delete[data-v-0bfe58bc]{margin:0;font-size:.14rem;background-color:var(--primary-color);color:#fff;line-height:.32rem;height:.32rem}.stock-body .content-wrap .table-wrap .delete[data-v-0bfe58bc]::after{border-width:0}.stock-body .content-wrap .table-wrap .table-empty[data-v-0bfe58bc]{justify-content:center;padding:.3rem;color:#999}.stock-body .content-wrap .select-goods-input[data-v-0bfe58bc],\r\n.stock-body .content-wrap .goods-name[data-v-0bfe58bc]{position:relative}.stock-body .content-wrap .select-goods-input uni-input[data-v-0bfe58bc],\r\n.stock-body .content-wrap .goods-name uni-input[data-v-0bfe58bc]{flex:1;padding:0 .2rem}.stock-body .content-wrap .select-goods-input .icontuodong[data-v-0bfe58bc],\r\n.stock-body .content-wrap .goods-name .icontuodong[data-v-0bfe58bc]{font-size:.16rem;position:absolute;top:.17rem;right:.34rem;z-index:2;cursor:pointer}.stock-body .content-wrap uni-input[data-v-0bfe58bc]{font-size:.14rem!important;border:.01rem solid #e6e6e6!important;height:.32rem}.stock-body .action-wrap[data-v-0bfe58bc]{position:absolute;bottom:0;left:0;right:0;display:flex;justify-content:space-between;padding:.24rem .2rem;align-items:center;background-color:#fff;z-index:10}.stock-body .action-wrap .btn-wrap[data-v-0bfe58bc]{display:flex;align-items:center;justify-content:center}.stock-body .action-wrap .btn-wrap uni-button[data-v-0bfe58bc]{margin:0;min-width:2.75rem;height:.4rem;line-height:.4rem;font-size:.14rem}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-0bfe58bc]{margin-right:.15rem;background-color:var(--primary-color);color:#fff}.stock-body .action-wrap .btn-wrap uni-button.stockout-btn[data-v-0bfe58bc]::after{border-width:0}.stock-body .action-wrap .btn-wrap uni-button.remark[data-v-0bfe58bc]{margin-right:.15rem;min-width:1.2rem}.remark-wrap[data-v-0bfe58bc]{width:6rem;background-color:#fff;border-radius:.04rem;box-shadow:0 .01rem .12rem 0 rgba(0,0,0,.1)}.remark-wrap .header[data-v-0bfe58bc]{display:flex;justify-content:space-between;align-items:center;padding:0 .15rem;height:.45rem;line-height:.45rem;border-bottom:.01rem solid #e8eaec}.remark-wrap .header .iconfont[data-v-0bfe58bc]{font-size:.16rem}.remark-wrap .body[data-v-0bfe58bc]{padding:.15rem .15rem .1rem}.remark-wrap .body uni-textarea[data-v-0bfe58bc]{border:.01rem solid #e6e6e6;width:100%;padding:.1rem;box-sizing:border-box;font-size:.14rem}.remark-wrap .body .placeholder-class[data-v-0bfe58bc]{font-size:.14rem}.remark-wrap .footer[data-v-0bfe58bc]{height:.5rem;padding-bottom:.05rem;display:flex;align-items:center;justify-content:center}.remark-wrap .footer uni-button.default[data-v-0bfe58bc]{width:95%}',""]),t.exports=e},"6c5f":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("64aa"),a("bf0f"),a("2797"),a("8f71"),a("4626"),a("5ac7");var i={name:"select-lay",props:{disabled:{type:Boolean,default:!1},zindex:{type:Number,default:999},options:{type:Array,default:function(){return[]}},name:{type:String,default:""},value:{type:[String,Number],default:""},placeholder:{type:String,default:"请选择"},showplaceholder:{type:Boolean,default:!0},slabel:{type:String,default:"label"},svalue:{type:String,default:"value"}},data:function(){return{active:!1,isfocus:!1,isremove:!1,ismove:!1,changevalue:"",oldvalue:"",changes:!1,changesValue:"",vlist:[],settimer:null}},mounted:function(){this.itemcheck()},watch:{value:function(){this.itemcheck()},options:function(){this.itemcheck()}},methods:{itemcheck:function(){var t=this;""!=this.value?this.options.length>0&&this.options.forEach((function(e){t.value!=e[t.svalue]||(t.oldvalue=t.changevalue=e[t.slabel])})):this.oldvalue=this.changevalue=""},select:function(){this.disabled||(this.active=!this.active,this.active?this.changes=!1:this.changevalue=this.oldvalue)},unifocus:function(){this.disabled||(this.active=!0,this.changes=!1,this.isfocus=!0)},uniblur:function(){var t=this;this.isfocus=!1,setTimeout((function(){t.isremove||t.ismove?(t.isremove=!1,t.ismove=!1):(t.changevalue=t.oldvalue,t.isremove=!1,t.active=!1)}),153)},movetouch:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},selectmove:function(){var t=this;setTimeout((function(){t.isfocus?t.ismove=!1:t.ismove||(t.ismove=!0)}),100)},removevalue:function(){this.isremove=!0,this.changes=!1,this.changevalue=""},intchange:function(){var t=this;""!=this.changevalue?this.oldvalue!=this.changevalue&&(this.vlist=[],this.changes=!0,this.changesValue="正在搜索...",this.settimer&&clearTimeout(this.settimer),this.settimer=setTimeout((function(){t.vlist=t.options.filter((function(e){return e[t.slabel].includes(t.changevalue)})),0===t.vlist.length&&(t.changesValue="暂无匹配内容！")}),600)):this.changes=!1},selectitem:function(t,e){if(e&&e.disabled)return!1;this.changevalue=this.oldvalue,this.active=!1,this.$emit("selectitem",t,e)}}};e.default=i},"6e0b":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"uni-select-lay",style:{"z-index":t.zindex}},[a("v-uni-input",{staticClass:"uni-select-input",attrs:{type:"text",name:t.name,readonly:!0},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}}),a("v-uni-view",{staticClass:"uni-select-lay-select",class:{active:t.active}},[t.disabled?a("v-uni-view",{staticClass:"uni-disabled"}):t._e(),""!=t.changevalue&&this.active?a("v-uni-view",{staticClass:"uni-select-lay-input-close"},[a("v-uni-text",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.removevalue.apply(void 0,arguments)}}})],1):t._e(),a("v-uni-input",{staticClass:"uni-select-lay-input",class:{active:""!=t.changevalue&&t.changevalue!=t.placeholder},attrs:{type:"text",readonly:!0,disabled:"true",placeholder:t.placeholder},on:{focus:function(e){arguments[0]=e=t.$handleEvent(e),t.unifocus.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.intchange.apply(void 0,arguments)},blur:function(e){arguments[0]=e=t.$handleEvent(e),t.uniblur.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}},model:{value:t.changevalue,callback:function(e){t.changevalue=e},expression:"changevalue"}}),a("v-uni-view",{staticClass:"uni-select-lay-icon",class:{disabled:t.disabled},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}},[a("v-uni-text")],1)],1),a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-date-mask",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.select.apply(void 0,arguments)}}}),a("v-uni-scroll-view",{directives:[{name:"show",rawName:"v-show",value:t.active,expression:"active"}],staticClass:"uni-select-lay-options",attrs:{"scroll-y":!0},on:{scroll:function(e){arguments[0]=e=t.$handleEvent(e),t.selectmove.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.movetouch.apply(void 0,arguments)}}},[t.changes?[t.vlist.length>0?t._l(t.vlist,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue]},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(i,e)}}},[t._v(t._s(e[t.slabel]))])})):[a("v-uni-view",{staticClass:"nosearch"},[t._v(t._s(t.changesValue))])]]:[t.showplaceholder?a("v-uni-view",{staticClass:"uni-select-lay-item",class:{active:""==t.value},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.selectitem(-1,null)}}},[t._v(t._s(t.placeholder))]):t._e(),t._l(t.options,(function(e,i){return a("v-uni-view",{key:i,staticClass:"uni-select-lay-item",class:{active:t.value==e[t.svalue],disabled:e.disabled},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.selectitem(i,e)}}},[t._v(t._s(e[t.slabel]))])}))]],2)],1)},o=[]},"752e":function(t,e,a){var i=a("610d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("967d").default;o("571ac67e",i,!0,{sourceMap:!1,shadowMode:!1})},"8a7a":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d4b5"),a("aa9c"),a("e966"),a("bf0f"),a("2797"),a("4626"),a("5ac7"),a("5ef2"),a("e838"),a("dd2b"),a("c9b5"),a("ab80"),a("7a76");var i=a("f574"),o={data:function(){return{params:{search_text:"",temp_store_id:""},goodsList:[],goodsIdArr:[],goodsShow:!1,totalData:{kindsNum:0,price:0},isSubmit:!1,remark:"",type:"in",storeName:"出库门店",screen:{store_id:"",remark:"",allot_id:"",allot_no:"",storeList:[],startDate:"1998-01-30 00:00:00",birthday:"",allocateTypeList:[{label:"调拨入库",value:"in"},{label:"调拨出库",value:"out"}]},dialogVisible:!1,inputIndex:-1}},onLoad:function(t){this.screen.allot_id=t.allot_id||"",this.screen.allot_id?this.getEditData():this.getDocumentNo()},onShow:function(){this.screen.birthday=this.$util.timeFormat(Date.parse(new Date)/1e3),this.getStoreLists()},watch:{goodsIdArr:function(t){this.calcTotalData()}},methods:{getDocumentNo:function(){var t=this;(0,i.getAllotNo)().then((function(e){e.code>=0?t.screen.allot_no=e.data:t.$util.showToast({title:e.message})}))},getEditData:function(){var t=this;(0,i.getAllocateDetailInEdit)(this.screen.allot_id).then((function(e){if(e.code>=0&&e.data)for(var a in t.info=e.data,t.type=t.globalStoreInfo.store_id==t.info.input_store_id?"in":"out",t.screen.store_id="in"==t.type?t.info.output_store_id:t.info.input_store_id,t.screen.allot_no=t.info.allot_no,t.screen.birthday=t.$util.timeFormat(t.info.allot_time),t.remark=JSON.parse(JSON.stringify(t.info.remark)),t.screen.remark=t.info.remark,t.info.goods_list)t.info.goods_list[a].title=t.info.goods_list[a].sku_name,t.goodsIdArr.push(parseInt(a)),t.goodsList.push(t.info.goods_list[a])}))},selectAllocateType:function(t){this.type=-1==t?"":this.screen.allocateTypeList[t].value,this.storeName=-1==t||0==t?"出库门店":"入库门店",this.params.temp_store_id="in"==this.type?this.screen.store_id:"",this.goodsIdArr=[],this.goodsList=[]},selectStore:function(t){this.screen.store_id=-1==t?"":this.screen.storeList[t].value,"in"==this.type&&(this.goodsIdArr=[],this.goodsList=[])},changeTime:function(t){this.screen.birthday=t},getGoodsData:function(t,e){var a=this,o=t.detail;this.inputIndex=e;var n={search:o?o.value:""};"in"==this.type&&(n.temp_store_id=this.screen.store_id),o&&o.value?(0,i.getSkuListForStock)(n).then((function(t){t.code>=0&&1==t.data.length?a.selectGoods(t.data):t.code>=0?(a.params.search_text=o?o.value:"",a.dialogVisible=!0):a.$util.showToast({title:t.message})})):(this.params.search_text=o?o.value:"",this.dialogVisible=!0)},selectGoods:function(t){var e=this;t.forEach((function(t,a){if(t.goods_num=1,t.goods_price=0,t.title=t.sku_name+"",e.goodsIdArr.includes(t.sku_id)){var i=e.goodsIdArr.indexOf(t.sku_id);e.params.search_text&&(e.goodsList[i].goods_num=parseFloat(e.goodsList[i].goods_num)+1)}else console.log(111),e.goodsIdArr.push(t.sku_id),e.goodsList.push(t)})),this.goodsShow=!1,this.params.search_text="",this.$forceUpdate()},delGoods:function(t){this.goodsList.splice(this.goodsIdArr.indexOf(t),1),this.goodsIdArr.splice(this.goodsIdArr.indexOf(t),1)},getStoreLists:function(){var t=this;this.screen.storeList=[],(0,i.getStoreLists)().then((function(e){if(e.code>=0){for(var a=e.data,i=0;i<a.length;i++)t.globalStoreId!=a[i]["store_id"]&&t.screen.storeList.push({label:a[i]["store_name"],value:a[i]["store_id"].toString()});t.screen.storeList.length>0&&(t.screen.store_id=t.screen.storeList[0].value,t.params.temp_store_id=t.screen.store_id)}}))},stockOutFn:function(){var t=this;if(!this.screen.allot_no)return this.$util.showToast({title:"请输入调拨单号"}),!1;if(!this.type)return this.$util.showToast({title:"请选择调拨方式"}),!1;if(!this.screen.store_id)return this.$util.showToast({title:"请选择出库门店"}),!1;if(!this.screen.birthday)return this.$util.showToast({title:"请选择调拨时间"}),!1;if(!this.goodsIdArr.length)return this.$util.showToast({title:"请选择调拨数据"}),!1;var e=!1,a=[];try{this.goodsList.forEach((function(i,o){if(t.goodsIdArr.includes(i.sku_id)){if(!parseFloat(i.goods_num||0)){e=!0;var n="请输入"+i.sku_name+"的调拨数量";throw t.$util.showToast({title:n}),new Error("end")}var s={};s.goods_num=i.goods_num,s.goods_price=i.cost_price,s.goods_sku_id=i.sku_id,a.push(s)}}))}catch(n){if("end"!=n.message)throw n}if(e)return!1;if(this.isSubmit)return!1;this.isSubmit=!0;var o=this.screen.allot_id?i.editAllocate:i.addAllocate;o({allot_type:this.type,allot_id:this.screen.allot_id,temp_store_id:this.screen.store_id,allot_time:this.screen.birthday,remark:this.screen.remark,allot_no:this.screen.allot_no,goods_sku_list:JSON.stringify(a)}).then((function(e){t.isSubmit=!1,t.$util.showToast({title:e.message}),e.code>=0&&(setTimeout((function(){t.backFn()}),500),t.resetFn())}))},backFn:function(){this.$util.redirectTo("/pages/stock/allocate")},calcTotalData:function(){var t=this;this.totalData.kindsNum=0,this.totalData.price=0,this.goodsList.forEach((function(e,a){t.goodsIdArr.includes(e.sku_id)&&(t.totalData.price+=parseFloat(e.cost_price||0)*parseFloat(e.goods_num||1))})),this.totalData.kindsNum=this.goodsIdArr.length},resetFn:function(){this.goodsIdArr=[],this.goodsShow=!1,this.totalData.kindsNum=0,this.totalData.price=0},remarkConfirm:function(){this.screen.remark=JSON.parse(JSON.stringify(this.remark)),this.$refs.remarkPopup.close()}}};e.default=o},af57:function(t,e,a){"use strict";a.r(e);var i=a("c8dd"),o=a("db4e");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("fed2");var s=a("828b"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"0bfe58bc",null,!1,i["a"],void 0);e["default"]=r.exports},c8dd:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return i}));var i={selectLay:a("3523").default,uniDatetimePicker:a("ea9b").default,stockGoodsDialog:a("4e3a").default,uniPopup:a("2166").default},o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("base-page",[a("v-uni-view",{staticClass:"stock-body"},[a("v-uni-view",{staticClass:"content-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goodsShow=!1}}},[a("v-uni-view",{staticClass:"title"},[t._v(t._s(t.screen.allot_id?"编辑调拨单":"添加调拨单"))]),a("v-uni-view",{staticClass:"screen-warp form-content"},[a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("调拨单号")],1),a("v-uni-view",{staticClass:"form-inline input"},[a("v-uni-input",{attrs:{type:"text",disabled:""!=t.screen.allot_id,placeholder:"请输入调拨单号"},model:{value:t.screen.allot_no,callback:function(e){t.$set(t.screen,"allot_no",e)},expression:"screen.allot_no"}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("调拨方式")],1),a("v-uni-view",{staticClass:"form-inline"},[a("select-lay",{attrs:{zindex:10,value:t.type,name:"names",placeholder:"请选择调拨方式",options:t.screen.allocateTypeList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAllocateType.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"form-item store-info"},[a("v-uni-view",{staticClass:"form-label"},[t._v("当前门店：")]),a("v-uni-view",{staticClass:"form-inline"},[t._v(t._s(t.globalStoreInfo.store_name))])],1),a("v-uni-view",{staticClass:"form-item store-info"},[a("v-uni-view",{staticClass:"form-label"},[t._v("当前操作人：")]),a("v-uni-view",{staticClass:"form-inline"},[t._v(t._s(t.userInfo?t.userInfo.username:""))])],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v(t._s(t.storeName))],1),a("v-uni-view",{staticClass:"form-inline"},[a("select-lay",{attrs:{zindex:10,value:t.screen.store_id,name:"names",placeholder:"请选择"+t.storeName,options:t.screen.storeList},on:{selectitem:function(e){arguments[0]=e=t.$handleEvent(e),t.selectStore.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"form-item"},[a("v-uni-label",{staticClass:"form-label"},[a("v-uni-text",{staticClass:"required"},[t._v("*")]),t._v("调拨时间")],1),a("v-uni-view",{staticClass:"form-inline"},[a("uni-datetime-picker",{attrs:{start:t.screen.startDate,type:"timestamp",clearIcon:!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTime.apply(void 0,arguments)}},model:{value:t.screen.birthday,callback:function(e){t.$set(t.screen,"birthday",e)},expression:"screen.birthday"}})],1)],1)],1),a("v-uni-view",{staticClass:"table-wrap"},[a("v-uni-view",{staticClass:"table-head"},[a("v-uni-view",{staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"3"}},[t._v("产品名称/规格/编码")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("当前库存")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("单位")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"2"}},[t._v("成本价")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"2"}},[t._v("数量")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("总金额")]),a("v-uni-view",{staticClass:"table-th",staticStyle:{flex:"1"}},[t._v("操作")])],1)],1),a("v-uni-view",{staticClass:"table-body"},[a("v-uni-view",{staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-td select-goods-input",staticStyle:{flex:"3"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.goodsShow=!0}}},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入产品名称/规格/编码"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsData(e,-1)}},model:{value:t.params.search_text,callback:function(e){t.$set(t.params,"search_text",e)},expression:"params.search_text"}}),a("v-uni-text",{staticClass:"iconfont icontuodong",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getGoodsData({detail:null},-1)}}})],1),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}}),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}})],1),t._l(t.goodsList,(function(e,i){return[t.goodsIdArr.includes(e.sku_id)?a("v-uni-view",{key:i+"_0",staticClass:"table-tr"},[a("v-uni-view",{staticClass:"table-td goods-name",staticStyle:{flex:"3"}},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.real_stock||0))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s(e.unit||"件"))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}},[t._v(t._s(e.cost_price||0))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"2"}},[a("v-uni-input",{attrs:{type:"number",placeholder:"请输入数量"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.calcTotalData.apply(void 0,arguments)}},model:{value:e.goods_num,callback:function(a){t.$set(e,"goods_num",a)},expression:"item.goods_num"}})],1),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[t._v(t._s((e.goods_num*e.cost_price||0).toFixed(2)))]),a("v-uni-view",{staticClass:"table-td",staticStyle:{flex:"1"}},[a("v-uni-button",{staticClass:"delete",attrs:{type:"default"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.delGoods(e.sku_id)}}},[t._v("删除")])],1)],1):t._e()]})),t.goodsIdArr.length?t._e():a("v-uni-view",{staticClass:"table-tr table-empty"},[t._v("暂无数据，请选择商品数据")])],2)],1),a("stock-goods-dialog",{attrs:{params:t.params},on:{selectGoods:function(e){arguments[0]=e=t.$handleEvent(e),t.selectGoods.apply(void 0,arguments)}},model:{value:t.dialogVisible,callback:function(e){t.dialogVisible=e},expression:"dialogVisible"}})],1),a("v-uni-view",{staticClass:"action-wrap"},[a("v-uni-view",{staticClass:"table-total"},[t._v("合计：共"+t._s(t.totalData.kindsNum)+"种产品，合计金额"+t._s(t.totalData.price.toFixed(2)))]),a("v-uni-view",{staticClass:"btn-wrap"},[a("v-uni-button",{staticClass:"remark default",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.remarkPopup.open()}}},[t._v("备注")]),a("v-uni-button",{staticClass:"stockout-btn",attrs:{type:"default",loading:t.isSubmit},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.stockOutFn.apply(void 0,arguments)}}},[t._v("确认调拨")]),a("v-uni-button",{staticClass:"default",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backFn.apply(void 0,arguments)}}},[t._v("返回")])],1)],1)],1),a("uni-popup",{ref:"remarkPopup",attrs:{type:"center"}},[a("v-uni-view",{staticClass:"remark-wrap"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-text",{staticClass:"title"},[t._v("备注")]),a("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$refs.remarkPopup.close()}}})],1),a("v-uni-view",{staticClass:"body"},[a("v-uni-textarea",{attrs:{placeholder:"填写备注信息","placeholder-class":"placeholder-class"},model:{value:t.remark,callback:function(e){t.remark=e},expression:"remark"}})],1),a("v-uni-view",{staticClass:"footer"},[a("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.remarkConfirm.apply(void 0,arguments)}}},[t._v("确认")])],1)],1)],1)],1)},n=[]},db4e:function(t,e,a){"use strict";a.r(e);var i=a("ef46"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},ef46:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(a("8a7a")),n=i(a("4e3a")),s={components:{stockGoodsDialog:n.default},mixins:[o.default]};e.default=s},f574:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addAllocate=function(t){return o.default.post("/stock/storeapi/allocate/addallocate",{data:t})},e.addInventory=function(t){return o.default.post("/stock/storeapi/check/add",{data:t})},e.allocateAgree=function(t){return o.default.post("/stock/storeapi/allocate/agree",{data:{allot_id:t}})},e.allocateDelete=function(t){return o.default.post("/stock/storeapi/allocate/delete",{data:{allot_id:t}})},e.allocateRefuse=function(t){return o.default.post("/stock/storeapi/allocate/refuse",{data:t})},e.editAllocate=function(t){return o.default.post("/stock/storeapi/allocate/editAllocate",{data:t})},e.editInventory=function(t){return o.default.post("/stock/storeapi/check/edit",{data:t})},e.editStorage=function(t){return o.default.post("/stock/storeapi/storage/stockin",{data:t})},e.editWastage=function(t){return o.default.post("/stock/storeapi/wastage/stockout",{data:t})},e.getAllocateDetail=function(t){return o.default.post("/stock/storeapi/allocate/detail",{data:{allot_id:t}})},e.getAllocateDetailInEdit=function(t){return o.default.post("/stock/storeapi/allocate/editData",{data:{allot_id:t}})},e.getAllocateList=function(t){return o.default.post("/stock/storeapi/allocate/lists",{data:t})},e.getAllotNo=function(){return o.default.post("/stock/storeapi/allocate/getAllotNo")},e.getDocumentType=function(){return o.default.post("/stock/storeapi/manage/getDocumentType")},e.getInventoryDetail=function(t){return o.default.post("/stock/storeapi/check/detail",{data:{inventory_id:t}})},e.getInventoryDetailInEdit=function(t){return o.default.post("/stock/storeapi/check/editData",{data:{inventory_id:t}})},e.getInventoryList=function(t){return o.default.post("/stock/storeapi/check/lists",{data:t})},e.getInventoryNo=function(){return o.default.post("/stock/storeapi/Check/getInventoryNo")},e.getSkuListForStock=function(t){return o.default.post("/stock/storeapi/manage/getskulist",{data:t})},e.getStockGoodsList=function(t){return o.default.post("/stock/storeapi/manage/lists",{data:t})},e.getStockGoodsRecords=function(t){return o.default.post("/stock/storeapi/manage/records",{data:t})},e.getStorageDetail=function(t){return o.default.post("/stock/storeapi/storage/detail",{data:{document_id:t}})},e.getStorageDetailInEdit=function(t){return o.default.post("/stock/storeapi/storage/editData",{data:{document_id:t}})},e.getStorageDocumentNo=function(){return o.default.post("/stock/storeapi/storage/getDocumentNo")},e.getStorageLists=function(t){return o.default.post("/stock/storeapi/storage/lists",{data:t})},e.getStoreLists=function(){return o.default.post("/stock/storeapi/store/lists")},e.getWastageDetail=function(t){return o.default.post("/stock/storeapi/wastage/detail",{data:{document_id:t}})},e.getWastageDetailInEdit=function(t){return o.default.post("/stock/storeapi/wastage/editData",{data:{document_id:t}})},e.getWastageDocumentNo=function(){return o.default.post("/stock/storeapi/wastage/getDocumentNo")},e.getWastageLists=function(t){return o.default.post("/stock/storeapi/wastage/lists",{data:t})},e.inventoryAgree=function(t){return o.default.post("/stock/storeapi/check/agree",{data:{inventory_id:t}})},e.inventoryDelete=function(t){return o.default.post("stock/storeapi/check/delete",{data:{inventory_id:t}})},e.inventoryRefuse=function(t){return o.default.post("/stock/storeapi/check/refuse",{data:t})},e.storageAgree=function(t){return o.default.post("/stock/storeapi/storage/agree",{data:{document_id:t}})},e.storageDelete=function(t){return o.default.post("/stock/storeapi/storage/delete",{data:{document_id:t}})},e.storageRefuse=function(t){return o.default.post("/stock/storeapi/storage/refuse",{data:t})};var o=i(a("4e01"))},fed2:function(t,e,a){"use strict";var i=a("752e"),o=a.n(i);o.a}}]);