(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stock-edit_allocate~pages-stock-edit_check~pages-stock-stockin~pages-stock-stockout"],{"084b":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.editGoods=function(t){return o.default.post("/cashier/storeapi/goods/editgoods",{data:t})},e.exportPrintPriceTagData=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o.default.post("/cashier/storeapi/goods/exportPrintPriceTagData",{data:t})},e.getElectronicScaleInformation=function(){return o.default.post("/scale/storeapi/scale/cashierscale")},e.getGoodsCategory=function(t){return o.default.post("/cashier/storeapi/goods/category",{data:t})},e.getGoodsDetail=function(t){return o.default.post("/cashier/storeapi/goods/detail",{data:{goods_id:t}})},e.getGoodsInfoByCode=function(t){return o.default.post("/cashier/storeapi/goods/skuinfo",{data:{sku_no:t}})},e.getGoodsList=function(t){return o.default.post("/cashier/storeapi/goods/page",{data:t})},e.getGoodsSceen=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o.default.post("/cashier/storeapi/goods/screen",{data:t})},e.getGoodsSkuList=function(t){return o.default.post("/cashier/storeapi/goods/skulist",{data:{goods_id:t}})},e.getManageGoodsCategory=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o.default.post("/stock/storeapi/manage/getGoodsCategory",{data:t})},e.getServiceCategory=function(t){return o.default.post("/cashier/storeapi/service/category",{data:t})},e.getServiceList=function(t){return o.default.post("/cashier/storeapi/service/page",{data:t})},e.getSkuListBySelect=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return o.default.post("/cashier/storeapi/goods/getSkuListBySelect",{data:t})},e.setGoodsLocalRestrictions=function(t){return o.default.post("/cashier/storeapi/goods/setGoodsLocalRestrictions",{data:t})},e.setGoodsStatus=function(t){return o.default.post("/cashier/storeapi/goods/setstatus",{data:t})};var o=a(i("a3b5"))},"3bfb":function(t,e,i){"use strict";i("6a54");var a=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("c223"),i("dc8a"),i("5ef2"),i("aa9c"),i("dd2b"),i("22b6"),i("bf0f"),i("2797");var o=a(i("cea0")),s=a(i("01ce")),n=i("084b"),r={name:"stockDialog",components:{unipopup:o.default,uniDataTable:s.default},model:{prop:"value",event:"change"},props:{value:{type:Boolean,default:!1},params:{type:Object,default:function(){return{}}},apiType:{type:String,default:"sku"}},data:function(){var t=this;return{goodsCategoryList:{},activeList:[],option:{category_id:"",search_text:"",is_weigh:0,page_size:8},cols:[{width:6,align:"center",checkbox:!0},{field:"account_data",width:50,title:"商品信息",align:"left",templet:function(e){var i=t.$util.img(e.sku_image),a='\n\t\t\t\t\t\t\t<view class="goods-content">\n\t\t\t\t\t\t\t\t<image class="goods-img" src="'.concat(i,'" mode="aspectFit"/>\n\t\t\t\t\t\t\t\t<text class="goods-name multi-hidden"  title="').concat(e.sku_name,'">').concat(e.sku_name,"</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t");return a}},{field:"real_stock",width:22,title:"库存",align:"center",templet:function(t){return t.real_stock||0}},{width:22,title:"单位",templet:function(t){return t.unit||"件"}}],checkList:{},url:"/stock/storeapi/manage/getStoreGoods"}},watch:{value:{handler:function(t){var e=this;t?this.$nextTick((function(){e.option=Object.assign(e.option,e.params),e.params.temp_store_id&&""==e.params.temp_store_id&&delete e.option.temp_store_id,e.$refs.dialogRef.open()})):this.$nextTick((function(){e.option=Object(e.option,{category_id:"",search_text:"",is_weigh:0,page:1,page_size:8}),e.checkList={},e.$refs.dialogRef.close()}))},immediate:!0},apiType:{handler:function(t){var e=this;"sku"==t?(this.cols=[{width:6,align:"center",checkbox:!0},{field:"account_data",width:50,title:"商品信息",align:"left",templet:function(t){var i=e.$util.img(t.sku_image),a='\n\t\t\t\t\t\t\t\t\t<view class="goods-content">\n\t\t\t\t\t\t\t\t\t\t<image class="goods-img" src="'.concat(i,'" mode="aspectFit"/>\n\t\t\t\t\t\t\t\t\t\t<text class="goods-name multi-hidden"  title="').concat(t.sku_name,'">').concat(t.sku_name,"</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t");return a}},{field:"real_stock",width:22,title:"库存",align:"center",templet:function(t){return t.real_stock||0}},{width:22,title:"单位",templet:function(t){return t.unit||"件"}}],this.url="/stock/storeapi/manage/getStoreGoods"):"spu"==t&&(this.cols=[{width:6,align:"center",checkbox:!0},{field:"account_data",width:50,title:"商品信息",align:"left",templet:function(t){var i=e.$util.img(t.goods_image),a='\n\t\t\t\t\t\t\t\t\t<view class="goods-content">\n\t\t\t\t\t\t\t\t\t\t<image class="goods-img" src="'.concat(i,'" mode="aspectFit"/>\n\t\t\t\t\t\t\t\t\t\t<text class="goods-name multi-hidden"  title="').concat(t.goods_name,'">').concat(t.goods_name,"</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t");return a}},{field:"goods_stock",width:22,title:"库存",align:"center",templet:function(t){return t.goods_stock||0}},{width:22,title:"商品类型",templet:function(t){return t.goods_class_name||"--"}}],this.url="/cashier/storeapi/goods/getGoodsListBySelect")},immediate:!0}},mounted:function(){this.getGoodsCategory()},methods:{getGoodsCategory:function(){var t=this;(0,n.getManageGoodsCategory)().then((function(e){uni.hideLoading(),e.data&&Object.keys(e.data)?t.goodsCategoryList=e.data:t.$util.showToast({title:e.message})}))},itemClick:function(t){this.option.category_id=t.category_id;var e=this.activeList.indexOf(t.category_id);t.child_num&&-1===e?this.activeList.push(t.category_id):t.child_num&&-1!=e&&this.activeList.splice(e,1),this.$forceUpdate(),this.getStoreGoods()},getStoreGoods:function(){this.$refs.goodsListTable.load({page:1})},checkBox:function(t,e){this.checkList[this.$refs.goodsListTable.page]={},this.checkList[this.$refs.goodsListTable.page].data=t,this.checkList[this.$refs.goodsListTable.page].index=e},tableDataChange:function(){this.checkList[this.$refs.goodsListTable.page]&&this.$refs.goodsListTable.defaultSelectData(this.checkList[this.$refs.goodsListTable.page].data,this.checkList[this.$refs.goodsListTable.page].index)},submit:function(t){if(!Object.values(this.checkList).length)return this.$util.showToast({title:"请选择商品"}),!1;var e=[];Object.values(this.checkList).forEach((function(t,i){e=e.concat(t.data)})),this.$emit("selectGoods",e),"submit"!=t?this.$emit("change",!1):this.$refs.goodsListTable.clearCheck(),this.checkList=[]}}};e.default=r},"4e43":function(t,e,i){var a=i("c86c");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-50b820f6]{display:none}\r\n/* 收银台相关 */uni-text[data-v-50b820f6],\r\nuni-view[data-v-50b820f6]{font-size:.14rem}body[data-v-50b820f6]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-50b820f6]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-50b820f6]::-webkit-scrollbar-button{display:none}body[data-v-50b820f6]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-50b820f6]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-50b820f6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-50b820f6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-50b820f6]{color:var(--primary-color)!important}.stock-dialog-wrap[data-v-50b820f6]{background-color:#fff;border-radius:.05rem;width:9rem}.stock-dialog-wrap .stock-dialog-head[data-v-50b820f6]{padding:0 .15rem;display:flex;align-items:center;justify-content:space-between;font-size:.15rem;height:.45rem;border-bottom:.01rem solid #e8eaec}.stock-dialog-wrap .stock-dialog-head .iconguanbi1[data-v-50b820f6]{font-size:.16rem}.stock-dialog-wrap .stock-dialog-body[data-v-50b820f6]{width:100%;height:7.3rem;padding:.1rem .2rem 0 .2rem;box-sizing:border-box;display:flex}.stock-dialog-wrap .stock-dialog-body .tree[data-v-50b820f6]{width:1.8rem;height:7.1rem;overflow-y:auto;border-right:.01rem solid #e8eaec;flex-shrink:0;flex-basis:auto;flex-grow:0;box-sizing:border-box}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap[data-v-50b820f6]{width:100%;height:100%}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap > uni-view[data-v-50b820f6]{box-sizing:border-box;width:100%}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item[data-v-50b820f6]{display:flex;align-items:center;width:100%;box-sizing:border-box;line-height:.3rem;min-height:.3rem;font-weight:500}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active[data-v-50b820f6]{background-color:#f7f7f7}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active .icon[data-v-50b820f6],\r\n.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item.active uni-view[data-v-50b820f6]{color:var(--primary-color)!important}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item[data-v-50b820f6]:hover{background-color:#f7f7f7}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item .icon[data-v-50b820f6]{width:.2rem;height:.3rem;display:flex;align-items:center;justify-content:center;-webkit-transform:rotate(-90deg);transform:rotate(-90deg);transition:all ease .5s}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap uni-view.item .icon.active[data-v-50b820f6]{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level[data-v-50b820f6]{width:100%;box-sizing:border-box}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level .item[data-v-50b820f6]{padding-left:.2rem}.stock-dialog-wrap .stock-dialog-body .tree .list-wrap .level .item2[data-v-50b820f6]{padding-left:.4rem}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table[data-v-50b820f6]{width:6.6rem;margin-left:.2rem}.stock-dialog-wrap .stock-dialog-body .stock-dialog-table .search[data-v-50b820f6]{display:flex;justify-content:flex-end}.stock-dialog-wrap .btn[data-v-50b820f6]{display:flex;justify-content:flex-end;border-top:.01rem solid #e8eaec;padding:.1rem .2rem .1rem .2rem;height:.38rem}.stock-dialog-wrap .btn .default-btn[data-v-50b820f6],\r\n.stock-dialog-wrap .btn .primary-btn[data-v-50b820f6]{margin:0}.stock-dialog-wrap .btn .default-btn[data-v-50b820f6]{border:.01rem solid #e8eaec!important}.stock-dialog-wrap .btn .submit[data-v-50b820f6]{margin-right:.15rem}.stock-dialog-wrap .btn .default-btn[data-v-50b820f6]::after{display:none}.stock-dialog-wrap .common-form .common-btn-wrap[data-v-50b820f6]{margin-left:0}.stock-dialog-wrap .common-form .common-btn-wrap .screen-btn[data-v-50b820f6]{margin-right:0}.stock-dialog-wrap .common-form .common-form-item[data-v-50b820f6]{margin-bottom:.1rem}.stock-dialog-wrap[data-v-50b820f6] .goods-content{display:flex}.stock-dialog-wrap[data-v-50b820f6] .goods-content .goods-img{margin-right:.1rem;width:.5rem;height:.5rem;flex-shrink:0;flex-basis:auto;flex-grow:0}',""]),t.exports=e},5908:function(t,e,i){var a=i("4e43");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=i("967d").default;o("7102a05c",a,!0,{sourceMap:!1,shadowMode:!1})},"818d":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("unipopup",{ref:"dialogRef",attrs:{type:"center",maskClick:!1}},[i("v-uni-view",{staticClass:"stock-dialog-wrap"},[i("v-uni-view",{staticClass:"stock-dialog-head"},[i("v-uni-text",[t._v("商品选择")]),i("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("change",!1)}}})],1),i("v-uni-view",{staticClass:"stock-dialog-body"},[i("v-uni-view",{staticClass:"tree"},[i("v-uni-scroll-view",{staticClass:"list-wrap",attrs:{"scroll-y":"true"}},[i("v-uni-view",{staticClass:"item",class:{active:""===t.option.category_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.itemClick({category_id:"",child_num:0})}}},[i("v-uni-view",{staticClass:"icon"}),i("v-uni-view",[t._v("全部分类")])],1),t._l(t.goodsCategoryList,(function(e,a){return i("v-uni-view",{key:a},[i("v-uni-view",{staticClass:"item",class:{active:t.option.category_id===e.category_id},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.itemClick(e)}}},[i("v-uni-view",{staticClass:"icon",class:{active:-1!=t.activeList.indexOf(e.category_id)}},[e.child_num?i("v-uni-text",{staticClass:"iconfont iconsanjiao_xia"}):t._e()],1),i("v-uni-view",[t._v(t._s(e.title))])],1),e.child_num?t._l(e.children,(function(a,o){return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:-1!=t.activeList.indexOf(e.category_id),expression:"activeList.indexOf(item.category_id) != -1"}],key:o,staticClass:"level"},[i("v-uni-view",{staticClass:"item",class:{active:t.option.category_id===a.category_id},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.itemClick(a)}}},[i("v-uni-view",{staticClass:"icon",class:{active:-1!=t.activeList.indexOf(a.category_id)}},[a.child_num?i("v-uni-text",{staticClass:"iconfont iconsanjiao_xia"}):t._e()],1),i("v-uni-view",[t._v(t._s(a.title))])],1),t._l(a.children,(function(e,o){return i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:-1!=t.activeList.indexOf(a.category_id),expression:"activeList.indexOf(item2.category_id) != -1"}],key:o,staticClass:"level"},[i("v-uni-view",{staticClass:"item item2",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.itemClick(e)}}},[i("v-uni-view",{staticClass:"icon"}),i("v-uni-view",[t._v(t._s(e.title))])],1)],1)}))],2)})):t._e()],2)}))],2)],1),i("v-uni-view",{staticClass:"stock-dialog-table"},[i("v-uni-view",{staticClass:"search  common-form"},[i("v-uni-view",{staticClass:"common-form-item"},[i("v-uni-view",{staticClass:"form-inline"},[i("v-uni-view",{staticClass:"form-input-inline"},[i("v-uni-input",{staticClass:"form-input",attrs:{type:"text",placeholder:"请输入产品名称/规格/编码"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.getStoreGoods.apply(void 0,arguments)}},model:{value:t.option.search_text,callback:function(e){t.$set(t.option,"search_text",e)},expression:"option.search_text"}})],1)],1),i("v-uni-view",{staticClass:"form-inline common-btn-wrap"},[i("v-uni-button",{staticClass:"screen-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getStoreGoods.apply(void 0,arguments)}}},[t._v("筛选")])],1)],1)],1),i("uniDataTable",{ref:"goodsListTable",attrs:{url:t.url,option:t.option,cols:t.cols,pagesize:8},on:{checkBox:function(e){arguments[0]=e=t.$handleEvent(e),t.checkBox.apply(void 0,arguments)},tableData:function(e){arguments[0]=e=t.$handleEvent(e),t.tableDataChange.apply(void 0,arguments)}}})],1)],1),i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{staticClass:"default-btn submit",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit("close")}}},[t._v("选中")]),i("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("change",!1)}}},[t._v("取消")])],1)],1)],1)},o=[]},cbbf:function(t,e,i){"use strict";var a=i("5908"),o=i.n(a);o.a},d23f:function(t,e,i){"use strict";i.r(e);var a=i("3bfb"),o=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=o.a},e420:function(t,e,i){"use strict";i.r(e);var a=i("818d"),o=i("d23f");for(var s in o)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(s);i("cbbf");var n=i("828b"),r=Object(n["a"])(o["default"],a["b"],a["c"],!1,null,"50b820f6",null,!1,a["a"],void 0);e["default"]=r.exports}}]);