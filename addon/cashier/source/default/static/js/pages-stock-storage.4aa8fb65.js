(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-stock-storage"],{"1bda":function(t,e,o){var i=o("bef0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=o("967d").default;a("44cd9177",i,!0,{sourceMap:!1,shadowMode:!1})},"2bba":function(t,e,o){"use strict";var i=o("1bda"),a=o.n(i);a.a},"390f":function(t,e,o){t.exports=o.p+"static/goods/goods_empty.png"},4712:function(t,e,o){"use strict";o.r(e);var i=o("99bf"),a=o("7e40");for(var s in a)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(s);o("2bba");var n=o("828b"),l=Object(n["a"])(a["default"],i["b"],i["c"],!1,null,"9ca1bb4e",null,!1,i["a"],void 0);e["default"]=l.exports},"7e40":function(t,e,o){"use strict";o.r(e);var i=o("e08f"),a=o.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(s);e["default"]=a.a},"99bf":function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("base-page",[i("v-uni-view",{staticClass:"goodslist"},[i("v-uni-view",{staticClass:"goodslist-box"},[i("v-uni-view",{staticClass:"goodslist-left"},[i("v-uni-view",{staticClass:"goods-title"},[t._v("入库单查询"),i("v-uni-text",{staticClass:"iconfont icongengduo1"})],1),i("v-uni-view",{staticClass:"goods-search"},[i("v-uni-view",{staticClass:"search"},[i("v-uni-text",{staticClass:"iconfont icon31sousuo"}),i("v-uni-input",{attrs:{type:"text",placeholder:"搜索入库单号"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}},model:{value:t.search_text,callback:function(e){t.search_text=e},expression:"search_text"}})],1)],1),i("v-uni-scroll-view",{staticClass:"goods-list-scroll",attrs:{"scroll-y":"true","show-scrollbar":!1},on:{scrolltolower:function(e){arguments[0]=e=t.$handleEvent(e),t.getListData.apply(void 0,arguments)}}},[t._l(t.list,(function(e,o){return i("v-uni-view",{key:o,staticClass:"item",class:{itemhover:t.selectGoodsKeys==o},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.getDetailData(e.document_id,o)}}},[i("v-uni-view",{staticClass:"title"},[i("v-uni-view",[t._v(t._s(e.document_no))]),i("v-uni-view",[t._v(t._s(e.type_name))])],1),i("v-uni-view",{staticClass:"other-info"},[i("v-uni-view",[t._v("￥"+t._s(e.document_money))]),i("v-uni-view",[t._v(t._s(e.operater_name))]),i("v-uni-view",[t._v(t._s(e.status_name))]),i("v-uni-view",[t._v(t._s(t.$util.timeFormat(e.create_time)))])],1)],1)})),t.one_judge||t.list.length?t._e():i("v-uni-view",{staticClass:"notYet"},[t._v("暂无数据")])],2),i("v-uni-view",{staticClass:"add-wastage"},["store"==t.globalStoreInfo.stock_type?i("v-uni-button",{staticClass:"primary-btn",attrs:{type:"default"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.add()}}},[t._v("添加入库单")]):t._e()],1)],1),i("v-uni-view",{staticClass:"goodslist-right"},[i("v-uni-view",{staticClass:"goods-title"},[t._v("入库单详情")]),Object.keys(t.detail).length?i("v-uni-view",{staticClass:"order-information"},[i("v-uni-view",{staticClass:"order-status"},[t._v("基本信息")]),i("v-uni-view",{staticClass:"order-types"},[i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("入库单号：")]),i("v-uni-view",[t._v(t._s(t.detail.document_no))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("制单人：")]),i("v-uni-view",[t._v(t._s(t.detail.operater_name||"--"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("制单时间：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.create_time))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("入库时间：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.time))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("单据类型：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.type_name))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("状态：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.status_data.name))])],1),t.detail.verifier_name?i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("审核人：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.verifier_name))])],1):t._e(),t.detail.audit_time?i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("审核时间：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.audit_time))])],1):t._e(),-1==t.detail.status?i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("拒绝理由：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.refuse_reason))])],1):t._e(),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[t._v("备注：")]),i("v-uni-view",{staticClass:"message"},[t._v(t._s(t.detail.remark))])],1)],1),i("v-uni-view",{staticClass:"goods-info"},[i("v-uni-view",{staticClass:"title"},[t._v("商品明细")]),i("v-uni-view",{staticClass:"table"},[i("v-uni-view",{staticClass:"table-th table-all"},[i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"45%","justify-content":"flex-start"}},[t._v("商品名称/规格/条形码")]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[t._v("单位")]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[t._v("数量")]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[t._v("成本价(元)")]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%","justify-content":"flex-end"}},[t._v("金额(元)")])],1),t._l(t.detail.goods_sku_list_array,(function(e,o){return i("v-uni-view",{key:o,staticClass:"table-tr table-all"},[i("v-uni-view",{staticClass:"table-td table-goods-name",staticStyle:{width:"45%","justify-content":"flex-start"}},[i("v-uni-image",{attrs:{src:t.$util.img(e.goods_sku_img),mode:"aspectFill"}}),i("v-uni-text",{staticClass:"multi-hidden"},[t._v(t._s(e.goods_sku_name))])],1),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[t._v(t._s(e.goods_unit||"件"))]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"10%"}},[t._v(t._s(e.goods_num))]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%"}},[t._v(t._s(e.goods_price))]),i("v-uni-view",{staticClass:"table-td",staticStyle:{width:"15%","justify-content":"flex-end"}},[t._v(t._s(parseFloat(e.goods_sum).toFixed(2)))])],1)}))],2),i("v-uni-view",{staticClass:"total-money-num"},[i("v-uni-view",{staticClass:"box"},[i("v-uni-view",[t._v("商品种类")]),i("v-uni-view",{staticClass:"money"},[t._v(t._s(t.detail.goods_count)+"种")])],1),i("v-uni-view",{staticClass:"box"},[i("v-uni-view",[t._v("商品数量")]),i("v-uni-view",{staticClass:"money"},[t._v(t._s(t.detail.goods_price)+t._s(t.detail.goods_unit))])],1),i("v-uni-view",{staticClass:"box total"},[i("v-uni-view",[t._v("合计金额")]),i("v-uni-view",{staticClass:"money"},[t._v("￥"+t._s(parseFloat(t.detail.goods_total_price).toFixed(2)))])],1)],1),i("v-uni-view",{staticClass:"action-box"},[1!=t.detail.status&&-1!=t.detail.status||t.detail.operater!=t.detail.uid?t._e():[i("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.open("deleteWastagePop")}}},[t._v("删除")]),i("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.edit.apply(void 0,arguments)}}},[t._v("编辑")])],1==t.detail.status&&0==t.detail.is_audit?[i("v-uni-button",{staticClass:"default-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.open("refuseWastagePop")}}},[t._v("审核拒绝")]),i("v-uni-button",{staticClass:"primary-btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.open("agreeWastagePop")}}},[t._v("审核通过")])]:t._e()],2)],1)],1):t.one_judge||Object.keys(t.detail).length?t._e():[i("v-uni-image",{staticClass:"cart-empty",attrs:{src:o("390f"),mode:"widthFix"}})]],2)],1)],1),i("unipopup",{ref:"agreeWastagePop",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"confirm-pop"},[i("v-uni-view",{staticClass:"title"},[t._v("确定要通过该单据吗？")]),i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close("agreeWastagePop")}}},[t._v("取消")]),i("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.agree.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1),i("unipopup",{ref:"refuseWastagePop",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"confirm-pop message"},[i("v-uni-view",{staticClass:"title"},[t._v("拒绝理由"),i("v-uni-text",{staticClass:"iconfont iconguanbi1",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close("refuseWastagePop")}}})],1),i("v-uni-view",{staticClass:"textarea-box"},[i("v-uni-textarea",{staticClass:"textarea",attrs:{maxlength:"200",placeholder:"输入请不多于200字"},model:{value:t.refuseReason,callback:function(e){t.refuseReason=e},expression:"refuseReason"}})],1),i("v-uni-button",{staticClass:"primary-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.refuse.apply(void 0,arguments)}}},[t._v("保存")])],1)],1),i("unipopup",{ref:"deleteWastagePop",attrs:{type:"center"}},[i("v-uni-view",{staticClass:"confirm-pop"},[i("v-uni-view",{staticClass:"title"},[t._v("确定要删除该单据吗？")]),i("v-uni-view",{staticClass:"btn"},[i("v-uni-button",{staticClass:"default-btn btn save",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close("deleteWastagePop")}}},[t._v("取消")]),i("v-uni-button",{staticClass:"primary-btn btn",attrs:{type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteDocument.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1)],1)},a=[]},bef0:function(t,e,o){var i=o("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-9ca1bb4e]{display:none}\r\n/* 收银台相关 */uni-text[data-v-9ca1bb4e],\r\nuni-view[data-v-9ca1bb4e]{font-size:.14rem}body[data-v-9ca1bb4e]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-9ca1bb4e]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-9ca1bb4e]::-webkit-scrollbar-button{display:none}body[data-v-9ca1bb4e]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-9ca1bb4e]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-9ca1bb4e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-9ca1bb4e]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-9ca1bb4e]{color:var(--primary-color)!important}.goodslist[data-v-9ca1bb4e]{width:100%;height:100%;display:flex;align-items:center;justify-content:space-between;box-sizing:border-box}.goodslist .goodslist-box[data-v-9ca1bb4e]{width:100%;height:100%;background:#fff;display:flex}.goodslist .goodslist-box .goodslist-left[data-v-9ca1bb4e]{width:5rem;height:100%;border-right:.01rem solid #e6e6e6;box-sizing:border-box;overflow:hidden;position:relative}.goodslist .goodslist-box .goodslist-left .notYet[data-v-9ca1bb4e]{color:#e6e6e6;font-size:.4rem;padding-top:3rem;text-align:center}.goodslist .goodslist-box .goodslist-left .goods-title[data-v-9ca1bb4e]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.goodslist .goodslist-box .goodslist-left .goods-title .icongengduo1[data-v-9ca1bb4e]{position:absolute;top:50%;right:.2rem;-webkit-transform:translateY(-50%);transform:translateY(-50%);font-size:.3rem;color:var(--primary-color)}.goodslist .goodslist-box .goodslist-left .goods-search[data-v-9ca1bb4e]{width:100%;height:.6rem;border-bottom:.01rem solid #e6e6e6;display:flex;align-items:center;justify-content:center;padding:0 .2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-left .goods-search .search[data-v-9ca1bb4e]{width:5.6rem;height:.4rem;border-radius:.04rem;background:#f5f5f5;display:flex;align-items:center;padding:0 .2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-left .goods-search .search .iconfont[data-v-9ca1bb4e]{font-size:.16rem;color:#909399;margin-right:.11rem}.goodslist .goodslist-box .goodslist-left .goods-search .search uni-input[data-v-9ca1bb4e]{width:80%;height:60%;border:none;font-size:.14rem}.goodslist .goodslist-box .goodslist-left .goods-list-scroll[data-v-9ca1bb4e]{width:100%;height:calc(100% - 2.08rem)}.goodslist .goodslist-box .goodslist-left .goods-list-scroll .itemhover[data-v-9ca1bb4e]{background:var(--primary-color-light-9)}.goodslist .goodslist-box .goodslist-left .goods-list-scroll .item[data-v-9ca1bb4e]{padding:.2rem}.goodslist .goodslist-box .goodslist-left .goods-list-scroll .item .title[data-v-9ca1bb4e]{display:flex;align-items:center;justify-content:space-between;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-left .goods-list-scroll .item .title uni-view[data-v-9ca1bb4e]{font-size:.16rem}.goodslist .goodslist-box .goodslist-left .goods-list-scroll .item .title uni-view[data-v-9ca1bb4e]:nth-child(2){color:var(--primary-color)}.goodslist .goodslist-box .goodslist-left .add-wastage[data-v-9ca1bb4e]{padding:.24rem .2rem}.goodslist .goodslist-box .goodslist-left .add-wastage uni-button[data-v-9ca1bb4e]{width:100%;line-height:.4rem;height:.4rem}.goodslist .goodslist-box .goodslist-right[data-v-9ca1bb4e]{flex:1;width:0;height:100%;box-sizing:border-box;position:relative;padding-bottom:.88rem;overflow:hidden}.goodslist .goodslist-box .goodslist-right .goods-title[data-v-9ca1bb4e]{text-align:center;line-height:.6rem;font-size:.18rem;font-weight:500;height:.6rem;border-bottom:.01rem solid #e6e6e6;box-sizing:border-box;position:relative}.goodslist .goodslist-box .goodslist-right .cart-empty[data-v-9ca1bb4e]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:2.1rem}.goodslist .goodslist-box .goodslist-right .order-information[data-v-9ca1bb4e]{width:100%;height:calc(100% - .6rem);padding:.2rem;box-sizing:border-box;overflow-y:auto}.goodslist .goodslist-box .goodslist-right .order-information .order-status[data-v-9ca1bb4e]{font-size:.24rem;font-weight:700;margin-bottom:.24rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types[data-v-9ca1bb4e]{width:100%;min-height:1rem;padding:.2rem .3rem;display:flex;flex-direction:column;justify-content:space-between;margin-bottom:.2rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type[data-v-9ca1bb4e]{padding-left:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view[data-v-9ca1bb4e]{font-size:.14rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view .look[data-v-9ca1bb4e]{color:var(--primary-color);margin-left:.24rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type uni-view[data-v-9ca1bb4e]:nth-child(1){width:.9rem;text-align:right;margin-right:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type .message[data-v-9ca1bb4e]{max-width:10.7rem;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis;white-space:nowrap}.goodslist .goodslist-box .goodslist-right .order-information .order-types .type1[data-v-9ca1bb4e]{display:flex;align-items:center;height:.34rem}.goodslist .goodslist-box .goodslist-right .order-information .goods-info[data-v-9ca1bb4e]{min-height:2.7rem;background:#fff;padding:.2rem 0;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .title[data-v-9ca1bb4e]{font-size:.18rem;font-weight:550;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table[data-v-9ca1bb4e]{width:100%;box-sizing:border-box;margin-bottom:.2rem}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-all[data-v-9ca1bb4e]{width:100%;display:flex;align-items:center;justify-content:space-between;padding:0 .38rem;box-sizing:border-box}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-all .table-td[data-v-9ca1bb4e]{font-size:.14rem;text-align:left;display:flex;align-items:center;justify-content:center}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-all .table-td uni-image[data-v-9ca1bb4e]{margin-right:.1rem}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-all .table-goods-name uni-image[data-v-9ca1bb4e]{width:.6rem;height:.6rem;margin-right:.1rem;flex-shrink:0}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-th[data-v-9ca1bb4e]{height:.56rem;background:#f7f8fa}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-tr[data-v-9ca1bb4e]{height:.7rem;border-bottom:.01rem solid #e6e6e6}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-tr .table-td uni-image[data-v-9ca1bb4e]{width:.5rem;height:.5rem}.goodslist .goodslist-box .goodslist-right .order-information .goods-info .table .table-tr .table-td .content-text[data-v-9ca1bb4e]{width:80%;height:.4rem;text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical}.goodslist .goodslist-box .goodslist-right .total-money-num[data-v-9ca1bb4e]{display:flex;flex-direction:column}.goodslist .goodslist-box .goodslist-right .total-money-num .box[data-v-9ca1bb4e]{justify-content:flex-end;padding:.1rem 0 0 0;color:#333}.goodslist .goodslist-box .goodslist-right .total-money-num .money[data-v-9ca1bb4e]{text-align:right;width:1.2rem;color:#333;font-size:.14rem}.goodslist .goodslist-box .goodslist-right .total-money-num .total[data-v-9ca1bb4e]{border-top:.01rem solid #e6e6e6;margin-top:.1rem}.goodslist .goodslist-box .goodslist-right .total-money-num .total .money[data-v-9ca1bb4e]{color:#fe2278;font-size:.18rem}.other-info[data-v-9ca1bb4e]{display:flex;justify-content:space-between;align-items:center}.total-money-num .member-info[data-v-9ca1bb4e]{display:flex;align-items:center;float:left;-ms-flex-negative:0;-webkit-flex-shrink:0;flex-shrink:0}.total-money-num .box[data-v-9ca1bb4e]{display:flex;align-items:center;float:right}.total-money-num .box uni-view[data-v-9ca1bb4e]{font-size:.14rem}.total-money-num .box uni-view[data-v-9ca1bb4e]:nth-child(2){color:#fe2278;font-size:.18rem}.total-money-num[data-v-9ca1bb4e]:after{overflow:hidden;content:"";height:0;display:block;clear:both}[data-v-9ca1bb4e] .uni-scroll-view::-webkit-scrollbar{width:.05rem;height:.3rem}[data-v-9ca1bb4e] .uni-scroll-view::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.order-information[data-v-9ca1bb4e]::-webkit-scrollbar{width:.05rem;height:.3rem}.order-information[data-v-9ca1bb4e]::-webkit-scrollbar-thumb{border-radius:.1rem;box-shadow:inset 0 0 .05rem rgba(0,0,0,.2);background:#c1c1c1}.action-box[data-v-9ca1bb4e]{width:100%;position:absolute;bottom:0;right:0;background:#fff;padding:.24rem .2rem;box-sizing:border-box}.action-box uni-button[data-v-9ca1bb4e]{min-width:.9rem;height:.4rem;font-size:.18rem;text-align:center;line-height:.4rem;float:right;margin-left:.1rem}',""]),t.exports=e},e08f:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,o("c223"),o("5c47"),o("af8f"),o("dd2b");var a=o("f574"),s=i(o("2166")),n={components:{unipopup:s.default},data:function(){return{selectGoodsKeys:0,page:1,page_size:9,search_text:"",one_judge:!0,list:[],detail:{},repeatFlag:!1,refuseReason:""}},onLoad:function(t){t.id&&(this.search_text=t.id),this.getListData()},methods:{search:function(){this.page=1,this.list=[],this.one_judge=!0,this.getListData()},getListData:function(){var t=this;(0,a.getStorageLists)({page:this.page,page_size:this.page_size,search_text:this.search_text}).then((function(e){0==e.data.list.length&&t.one_judge&&(t.detail={},t.one_judge=!1),e.code>=0&&0!=e.data.list.length&&(t.page+=1,0==t.list.length?t.list=e.data.list:t.list=t.list.concat(e.data.list),t.one_judge&&t.getDetailData(t.list[0].document_id))}))},getDetailData:function(t){var e=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.selectGoodsKeys=o,this.type="detail",(0,a.getStorageDetail)(t).then((function(t){t.code>=0&&(e.detail=t.data,e.$forceUpdate(),e.one_judge=!1)}))},add:function(){this.$util.redirectTo("/pages/stock/stockin")},edit:function(){this.$util.redirectTo("/pages/stock/stockin",{document_id:this.detail.document_id})},open:function(t){this.$refs[t].open()},close:function(t){this.$refs[t].close()},agree:function(){var t=this;this.repeatFlag||(this.repeatFlag=!0,(0,a.storageAgree)(this.detail.document_id).then((function(e){t.$util.showToast({title:e.message}),e.code>=0&&(t.search(),t.getDetailData(t.detail.document_id),t.close("agreeWastagePop")),t.repeatFlag=!1})))},refuse:function(){var t=this;this.refuseReason?this.repeatFlag||(this.repeatFlag=!0,(0,a.storageRefuse)({document_id:this.detail.document_id,refuse_reason:this.refuseReason}).then((function(e){t.$util.showToast({title:e.message}),e.code>=0&&(t.search(),t.getDetailData(t.detail.document_id),t.close("refuseWastagePop")),t.repeatFlag=!1}))):this.$util.showToast({title:"请输入拒绝理由"})},deleteDocument:function(){var t=this;this.repeatFlag||(this.repeatFlag=!0,(0,a.storageDelete)(this.detail.document_id).then((function(e){t.$util.showToast({title:e.message}),e.code>=0&&(t.list.splice(t.selectGoodsKeys,1),0==t.selectGoodsKeys?t.selectGoodsKeys=0:t.selectGoodsKeys-=1,t.getDetailData(t.list[t.selectGoodsKeys].document_id,t.selectGoodsKeys),t.close("deleteWastagePop")),t.repeatFlag=!1})))}}};e.default=n},f574:function(t,e,o){"use strict";o("6a54");var i=o("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addAllocate=function(t){return a.default.post("/stock/storeapi/allocate/addallocate",{data:t})},e.addInventory=function(t){return a.default.post("/stock/storeapi/check/add",{data:t})},e.allocateAgree=function(t){return a.default.post("/stock/storeapi/allocate/agree",{data:{allot_id:t}})},e.allocateDelete=function(t){return a.default.post("/stock/storeapi/allocate/delete",{data:{allot_id:t}})},e.allocateRefuse=function(t){return a.default.post("/stock/storeapi/allocate/refuse",{data:t})},e.editAllocate=function(t){return a.default.post("/stock/storeapi/allocate/editAllocate",{data:t})},e.editInventory=function(t){return a.default.post("/stock/storeapi/check/edit",{data:t})},e.editStorage=function(t){return a.default.post("/stock/storeapi/storage/stockin",{data:t})},e.editWastage=function(t){return a.default.post("/stock/storeapi/wastage/stockout",{data:t})},e.getAllocateDetail=function(t){return a.default.post("/stock/storeapi/allocate/detail",{data:{allot_id:t}})},e.getAllocateDetailInEdit=function(t){return a.default.post("/stock/storeapi/allocate/editData",{data:{allot_id:t}})},e.getAllocateList=function(t){return a.default.post("/stock/storeapi/allocate/lists",{data:t})},e.getAllotNo=function(){return a.default.post("/stock/storeapi/allocate/getAllotNo")},e.getDocumentType=function(){return a.default.post("/stock/storeapi/manage/getDocumentType")},e.getInventoryDetail=function(t){return a.default.post("/stock/storeapi/check/detail",{data:{inventory_id:t}})},e.getInventoryDetailInEdit=function(t){return a.default.post("/stock/storeapi/check/editData",{data:{inventory_id:t}})},e.getInventoryList=function(t){return a.default.post("/stock/storeapi/check/lists",{data:t})},e.getInventoryNo=function(){return a.default.post("/stock/storeapi/Check/getInventoryNo")},e.getSkuListForStock=function(t){return a.default.post("/stock/storeapi/manage/getskulist",{data:t})},e.getStockGoodsList=function(t){return a.default.post("/stock/storeapi/manage/lists",{data:t})},e.getStockGoodsRecords=function(t){return a.default.post("/stock/storeapi/manage/records",{data:t})},e.getStorageDetail=function(t){return a.default.post("/stock/storeapi/storage/detail",{data:{document_id:t}})},e.getStorageDetailInEdit=function(t){return a.default.post("/stock/storeapi/storage/editData",{data:{document_id:t}})},e.getStorageDocumentNo=function(){return a.default.post("/stock/storeapi/storage/getDocumentNo")},e.getStorageLists=function(t){return a.default.post("/stock/storeapi/storage/lists",{data:t})},e.getStoreLists=function(){return a.default.post("/stock/storeapi/store/lists")},e.getWastageDetail=function(t){return a.default.post("/stock/storeapi/wastage/detail",{data:{document_id:t}})},e.getWastageDetailInEdit=function(t){return a.default.post("/stock/storeapi/wastage/editData",{data:{document_id:t}})},e.getWastageDocumentNo=function(){return a.default.post("/stock/storeapi/wastage/getDocumentNo")},e.getWastageLists=function(t){return a.default.post("/stock/storeapi/wastage/lists",{data:t})},e.inventoryAgree=function(t){return a.default.post("/stock/storeapi/check/agree",{data:{inventory_id:t}})},e.inventoryDelete=function(t){return a.default.post("stock/storeapi/check/delete",{data:{inventory_id:t}})},e.inventoryRefuse=function(t){return a.default.post("/stock/storeapi/check/refuse",{data:t})},e.storageAgree=function(t){return a.default.post("/stock/storeapi/storage/agree",{data:{document_id:t}})},e.storageDelete=function(t){return a.default.post("/stock/storeapi/storage/delete",{data:{document_id:t}})},e.storageRefuse=function(t){return a.default.post("/stock/storeapi/storage/refuse",{data:t})};var a=i(o("4e01"))}}]);