(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-store-index"],{"531f":function(e,t,i){"use strict";i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("base-page",[i("v-uni-view",{},[e.addon.includes("store")?i("v-uni-view",{staticClass:"store-information"},[i("v-uni-view",{staticClass:"store-status"},[e._v("门店信息")]),i("v-uni-view",{staticClass:"store-types"},[i("v-uni-view",{staticClass:"info-left"},[i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("门店名称：")]),i("v-uni-view",[e._v(e._s(e.storeData.store_name))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("门店电话：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.telphone))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("门店类型：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s("directsale"==e.storeData.store_type?"直营店":"加盟店"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("门店地址：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.full_address)+e._s(e.storeData.address))])],1)],1),i("v-uni-view",{staticClass:"info-img"},[i("v-uni-image",{attrs:{src:e.$util.img(e.storeData.store_image),mode:"aspectFit"},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.img(e.defaultImg.store)}}})],1),i("v-uni-view",{staticClass:"btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages/store/config")}}},[e._v("设置")])],1)],1):e._e(),i("v-uni-view",{staticClass:"store-information"},[i("v-uni-view",{staticClass:"store-status"},[e._v("运营信息")]),i("v-uni-view",{staticClass:"store-types"},[i("v-uni-view",{staticClass:"info-left"},[e.addon.includes("store")?[i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("营业状态：")]),1==e.storeData.is_frozen?i("v-uni-view",{staticClass:"message"},[e._v("已停业")]):i("v-uni-view",{staticClass:"message"},[e._v(e._s(1==e.storeData.status?"营业中":"休息"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("营业时间：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.open_date))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("物流配送：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.is_express?"开启":"关闭"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("同城配送：")]),i("v-uni-view",[e._v(e._s(e.storeData.is_o2o?"开启":"关闭"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("门店自提：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.is_pickup?"开启":"关闭"))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("自提日期：")]),1==e.storeData.time_type?i("v-uni-view",{staticClass:"message"},[e.storeData.time_week.includes("1")||e.storeData.time_week.includes(1)?i("v-uni-text",{staticClass:"week"},[e._v("周一")]):e._e(),e.storeData.time_week.includes("2")||e.storeData.time_week.includes(2)?i("v-uni-text",{staticClass:"week"},[e._v("周二")]):e._e(),e.storeData.time_week.includes("3")||e.storeData.time_week.includes(3)?i("v-uni-text",{staticClass:"week"},[e._v("周三")]):e._e(),e.storeData.time_week.includes("4")||e.storeData.time_week.includes(4)?i("v-uni-text",{staticClass:"week"},[e._v("周四")]):e._e(),e.storeData.time_week.includes("5")||e.storeData.time_week.includes(5)?i("v-uni-text",{staticClass:"week"},[e._v("周五")]):e._e(),e.storeData.time_week.includes("6")||e.storeData.time_week.includes(6)?i("v-uni-text",{staticClass:"week"},[e._v("周六")]):e._e(),e.storeData.time_week.includes("0")||e.storeData.time_week.includes(0)?i("v-uni-text",{staticClass:"week"},[e._v("周日")]):e._e()],1):e._e(),0==e.storeData.time_type?i("v-uni-view",{staticClass:"message"},[e._v("每天")]):e._e()],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("自提时间：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s(e.storeData.start_time)+"-"+e._s(e.storeData.end_time))])],1),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("库存设置：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s("all"==e.storeData.stock_type?"总部统一库存":"门店独立库存"))])],1)]:e._e(),i("v-uni-view",{staticClass:"type type1"},[i("v-uni-view",[e._v("会员搜索方式：")]),i("v-uni-view",{staticClass:"message"},[e._v(e._s("exact"==e.memberSearchWayConfig.way?"精确搜索":"列表搜索"))])],1)],2),i("v-uni-view",{staticClass:"btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$util.redirectTo("/pages/store/operate")}}},[e._v("设置")])],1)],1)],1)],1)},a=[]},6687:function(e,t,i){"use strict";var s=i("8ca8"),a=i.n(s);a.a},"7ee5":function(e,t,i){"use strict";i("6a54");var s=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("e966");var a=s(i("9b1b")),n=i("8f59"),r={data:function(){return{storeData:{store_name:"",store_image:"",status:0,telphone:"",open_date:"",is_o2o:0,is_pickup:0,time_type:0,start_time:"00:00",end_time:"23:59",stock_type:"all",time_week:"",latitude:39.909,longitude:116.39742,province_id:11e4,city_id:110100,district_id:110101,address:"",full_address:"",store_type:"directsale"}}},onLoad:function(){},onShow:function(){this.getData()},computed:(0,a.default)({},(0,n.mapGetters)(["memberSearchWayConfig"])),methods:{getData:function(){this.storeData=this.$util.deepClone(this.globalStoreInfo),this.storeData.start_time=this.timeFormat(this.storeData.start_time),this.storeData.end_time=this.timeFormat(this.storeData.end_time)},timeFormat:function(e){var t=parseInt(e/3600),i=parseInt(e%3600/60);return t=t<10?"0"+t:t,i=i<10?"0"+i:i,t+":"+i}}};t.default=r},"8ca8":function(e,t,i){var s=i("f306");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[e.i,s,""]]),s.locals&&(e.exports=s.locals);var a=i("967d").default;a("145be9be",s,!0,{sourceMap:!1,shadowMode:!1})},c3da:function(e,t,i){"use strict";i.r(t);var s=i("7ee5"),a=i.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(n);t["default"]=a.a},d075:function(e,t,i){"use strict";i.r(t);var s=i("531f"),a=i("c3da");for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);i("6687");var r=i("828b"),o=Object(r["a"])(a["default"],s["b"],s["c"],!1,null,"449021e6",null,!1,s["a"],void 0);t["default"]=o.exports},f306:function(e,t,i){var s=i("c86c");t=s(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.uni-app--showleftwindow + .uni-tabbar-bottom[data-v-449021e6]{display:none}\r\n/* 收银台相关 */uni-text[data-v-449021e6],\r\nuni-view[data-v-449021e6]{font-size:.14rem}body[data-v-449021e6]{min-width:1200px;overflow-x:unset!important;overflow-y:hidden;background:#f8f8f8}body[data-v-449021e6]::-webkit-scrollbar{width:.06rem;height:.06rem}body[data-v-449021e6]::-webkit-scrollbar-button{display:none}body[data-v-449021e6]::-webkit-scrollbar-thumb{border-radius:.06rem;box-shadow:inset 0 0 .06rem rgba(45,43,43,.45);background-color:#ddd}body[data-v-449021e6]::-webkit-scrollbar-track{background-color:initial}uni-radio[data-v-449021e6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-checkbox[data-v-449021e6]{-webkit-transform:scale(.8);transform:scale(.8)}uni-modal .uni-modal__btn_primary[data-v-449021e6]{color:var(--primary-color)!important}.store-information[data-v-449021e6]{width:100%;box-sizing:border-box;padding-bottom:.1rem;margin-bottom:.2rem}.store-information .store-status[data-v-449021e6]{font-size:.24rem;font-weight:700;height:.6rem;line-height:.6rem;padding-left:.2rem}.store-information .store-types[data-v-449021e6]{width:100%;background:#fff;padding:.2rem .3rem;display:flex;flex-direction:row;justify-content:space-between;margin-bottom:.2rem;box-sizing:border-box;position:relative}.store-information .store-types .info-left[data-v-449021e6]{display:flex;flex-direction:column;justify-content:space-between}.store-information .store-types .btn[data-v-449021e6]{position:absolute;top:.2rem;right:.2rem;color:var(--primary-color);cursor:pointer}.store-information .store-types .info-img[data-v-449021e6]{margin-top:.4rem}.store-information .store-types .info-img uni-image[data-v-449021e6]{max-width:1.5rem;height:1rem}.store-information .store-types .type[data-v-449021e6]{padding-left:.1rem}.store-information .store-types .type uni-view[data-v-449021e6]{font-size:.14rem}.store-information .store-types .type uni-view .look[data-v-449021e6]{color:var(--primary-color);margin-left:.24rem}.store-information .store-types .type uni-view[data-v-449021e6]:nth-child(1){width:1rem;text-align:right;margin-right:.1rem}.store-information .store-types .type1[data-v-449021e6]{display:flex;align-items:center;height:.34rem}.week[data-v-449021e6]{margin-right:.1rem}',""]),e.exports=t}}]);