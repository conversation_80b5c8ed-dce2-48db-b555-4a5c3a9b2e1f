.container {
  height: 100%;
}

.header-action {
  padding: 0.22rem 0.24rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 0.04rem;

  .header-action-left {
    display: flex;
	align-items: center;
	height: 0.44rem;
	background-color: var(--primary-color-light-9);
	border-radius: 0.22rem;

    view {
      min-width: 1.02rem;
      height: 0.44rem;
      line-height: 0.44rem;
      text-align: center;
      font-size: 0.14rem;
      border-left-width: 0;
      transition: all 0.3s;
      cursor: pointer;
      border-radius: 0.22rem;
      color: $primary-color;

      &.active {
        color: #fff;
        background-color: $primary-color;
      }

      &:first-child {
        border-left-width: 0.01rem;
        box-shadow: none;
      }
    }
  }

}

.content {
  margin-top: .2rem;
  box-sizing: border-box;
  height: calc(100% - 1.08rem);
  transform: rotate(0);
  display: flex;
  flex-direction: column;
}

.list-wrap {
  flex: 1;
  height: 0;
}

.table-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .table-item {
    border: 0.01rem solid #fff;
    box-sizing: border-box;
    padding: 0.1rem 0.18rem 0.1rem 0.1rem;
    background-color: #fff;
    margin-bottom: 0.12rem;
    margin-right: 0.12rem;
    cursor: pointer;
    transition: border-color, background-color 0.3s;
    position: relative;
    border-radius: 0.04rem;
    &.item-mum-2{
      width: calc((100% - 0.25rem) / 2);
    }
    &.item-mum-3{
      width: calc((100% - 0.37rem) / 3);
    }
    &.item-mum-4{
      width: calc((100% - 0.49rem) / 4);
    }
    .item-other {
      display: flex;
      flex-wrap: wrap;
      margin-left: 0.1rem;
    }

    .item-img {
      width: 0.9rem;
      height: 0.9rem;
      display: flex;
      align-items: center;
      overflow: hidden;
      -ms-flex-negative: 0;
      -webkit-flex-shrink: 0;
      flex-shrink: 0;

      image {
        width: 100%;
        border-radius: 0.03rem;
      }
    }

    .item-name {
      height: 0.4rem;
      line-height: 0.2rem;
      max-width: 1.58rem;
      margin-bottom: 0.05rem;
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
	.no-stock {
		position: absolute;
		z-index: 1;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(0, 0, 0, 0.5);
		cursor: not-allowed;
		image{
			height: 60%;
		}
	}

    .item-info {
      cursor: pointer;
      flex: 1;
      display: flex;

      .item-time {
        font-size: 0.12rem;
        color: #909399;
      }

      .item-money {
        font-size: 0.14rem;
        color: $primary-color;
        height: 0.19rem;
        line-height: 0.19rem;
      }

      .item-stock {
        height: 0.17rem;
        font-size: 0.12rem;
        color: #808695;
        line-height: 0.17rem;
      }
    }

  }
  .table-item.yes-stock {
    &:hover {
      background-color: var(--primary-color-light-9);
      border-color: $primary-color;
    }

    &.focus,
    &:focus {
      background-color: var(--primary-color-light-9);
      border-color: $primary-color;
      outline: none;
    }

    &.active {
      border-color: $primary-color;
      background-color: $primary-color;
      color: #fff;

      .item-time,
      .item-money,
      .item-stock {
        color: #fff;
      }
    }
  }
}

.empty {
  text-align: center;
  padding-top: 1.2rem;

  image {
    width: 2rem;
  }

  .tips {
    color: #999;
    margin-top: 0.15rem;
  }
}

/deep/ .uni-scroll-view {
  &::-webkit-scrollbar {
    width: 0.06rem;
    height: 0.06rem;
    background-color: rgba($color: #000000, $alpha: 0);
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 0.06rem;
    box-shadow: inset 0 0 0.06rem rgba(45, 43, 43, 0.45);
    background-color: #ddd;
    display: none;
  }

  &:hover::-webkit-scrollbar-thumb {
    display: block;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}