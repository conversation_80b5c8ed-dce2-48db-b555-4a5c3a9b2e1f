.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .pend-order-scroll-view-wrap {
    height: calc(100% - 0.67rem) !important;
    box-sizing: border-box;
    padding-top: 0.15rem;
    padding-bottom: 0;
  }
}

.header {
  height: 0.66rem;
  line-height: 0.66rem;
  text-align: left;
  border-bottom: 0.01rem solid #e6e6e6;
  color: #303133;
  font-size: 0.14rem;
}

.table-list {
  .table-item {
    border: 0.01rem solid #e6e6e6;
    padding: 0.15rem;
    position: relative;
    margin-bottom: 0.2rem;

    .table-header-info {
      text {
        font-size: 0.16rem;
      }

      .color {
        font-size: 0.18rem;
        color: #fe2278;
      }
    }

    .table-header-time {
      color: #909399;
      font-size: 0.14rem;
      margin-top: 0.1rem;

      .line {
        margin: 0 0.15rem;
      }
    }

    .table-header-btn {
      position: absolute;
      right: 0.3rem;
      top: 0.3rem;
      color: $primary-color;
      cursor: pointer;
    }

    .table-content {
      margin-top: 0.1rem;

      .table-content-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 0.01rem solid #e6e6e6;
        padding: 0.1rem 0;

        .content-item-left {
          display: flex;
          align-items: center;
          flex: 1;
          width: 0;

          .content-item-info {
            padding-right: 0.15rem;
            flex: 1;
            width: 0;

            view {
              font-size: 0.14rem;
            }

            .content-item-name {
              margin-bottom: 0.05rem;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              white-space: normal;
              word-break: break-all;
            }
          }
        }

        .content-item-img {
          width: 0.5rem;
          height: 0.5rem;
          margin-right: 0.1rem;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .content-item-number {
          font-size: 0.16rem;
        }

        .content-item-price {
          font-size: 0.14rem;
          width: 40%;
          text-align: right;
        }
      }
    }

    .remark-info {
      padding: 0.1rem;
      background-color: var(--primary-color-light-9);
      color: $primary-color;
      margin-top: 0.1rem;
      font-size: 0.12rem;
    }

    .table-bottom {
      margin-top: 0.2rem;
      display: flex;
      justify-content: end;

      button {
        width: 1rem;
        margin: 0;
        margin-right: 0.1rem;
      }
    }
  }
}

/deep/ .uni-scroll-view {
  &::-webkit-scrollbar {
    width: 0.06rem;
    height: 0.06rem;
    background-color: rgba($color: #000000, $alpha: 0);
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 0.06rem;
    box-shadow: inset 0 0 0.06rem rgba(45, 43, 43, 0.45);
    background-color: #ddd;
    display: none;
  }

  &:hover::-webkit-scrollbar-thumb {
    display: block;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

.remark-wrap {
  width: 6rem;
  background-color: #fff;
  border-radius: 0.04rem;
  box-shadow: 0 0.01rem 0.12rem 0 rgba(0, 0, 0, 0.1);

  .header,
  .footer {
    height: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 0.01rem solid #e6e6e6;
    position: relative;

    .title {
      font-size: 0.16rem;
      font-weight: bold;
    }

    .iconfont {
      line-height: 1;
      position: absolute;
      right: 0.15rem;
      font-size: 0.2rem;
      cursor: pointer;
    }
  }

  .body {
    padding: 0.15rem;

    textarea {
      border: 0.01rem solid #e6e6e6;
      width: 100%;
      padding: 0.1rem;
      box-sizing: border-box;
      font-size: 0.14rem;
    }

    .placeholder-class {
      font-size: 0.14rem;
    }
  }

  .footer {
    border-top: 0.01rem solid #e6e6e6;
    border-bottom: unset;

    button {
      width: 1rem;
    }
  }
}

.empty {
  text-align: center;
  padding-top: 1.2rem;

  image {
    width: 2rem;
  }

  .tips {
    color: #999;
    margin-top: 0.15rem;
  }
}

// pop弹框
.pop-box {
  background: #ffffff;
  width: 8rem;
  height: 7rem;

  .pop-header {
    padding: 0 0.15rem 0 0.2rem;
    height: 0.5rem;
    line-height: 0.5rem;
    border-bottom: 0.01rem solid #f0f0f0;
    font-size: 0.14rem;
    color: #333;
    overflow: hidden;
    border-radius: 0.02rem 0.2rem 0 0;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .pop-header-text {}

    .pop-header-close {
      cursor: pointer;

      text {
        font-size: 0.18rem;
      }
    }
  }

}