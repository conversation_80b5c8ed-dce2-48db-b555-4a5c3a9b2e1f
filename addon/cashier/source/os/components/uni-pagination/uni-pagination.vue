<template>
	<view class="uni-pagination">
		<!-- #ifndef APP-NVUE -->
		<view class="uni-pagination__total is-phone-hide">共 {{ total }} 条</view>
		<!-- #endif -->
		<view
			class="uni-pagination__btn"
			:class="currentIndex === 1 ? 'uni-pagination--disabled' : 'uni-pagination--enabled'"
			:hover-class="currentIndex === 1 ? '' : 'uni-pagination--hover'"
			:hover-start-time="20"
			:hover-stay-time="70"
			@click="clickLeft"
		>
			<template v-if="showIcon === true || showIcon === 'true'">
				<text class="iconfont iconqianhou1"></text>
			</template>
			<template v-else>
				<text class="uni-pagination__child-btn">{{ prevPageText }}</text>
			</template>
		</view>
		<view class="uni-pagination__num uni-pagination__num-flex-none">
			<view class="uni-pagination__num-current">
				<text class="uni-pagination__num-current-text is-pc-hide text-color">{{ currentIndex }}</text>
				<text class="uni-pagination__num-current-text is-pc-hide">/{{ maxPage || 0 }}</text>
				<!-- #ifndef APP-NVUE -->
				<view
					v-for="(item, index) in paper" :key="index"
					:class="{ 'page--active': item === currentIndex }"
					class="uni-pagination__num-tag tag--active is-phone-hide"
					@click.top="selectPage(item, index)"
				>
					<text>{{ item }}</text>
				</view>
				<!-- #endif -->
			</view>
		</view>
		<view
			class="uni-pagination__btn"
			:class="currentIndex >= maxPage ? 'uni-pagination--disabled' : 'uni-pagination--enabled'"
			:hover-class="currentIndex === maxPage ? '' : 'uni-pagination--hover'"
			:hover-start-time="20"
			:hover-stay-time="70"
			@click="clickRight"
		>
			<template v-if="showIcon === true || showIcon === 'true'">
				<text class="iconfont iconqianhou2"></text>
			</template>
			<template v-else>
				<text class="uni-pagination__child-btn">{{ nextPageText }}</text>
			</template>
		</view>
	</view>
</template>

<script>
/**
 * Pagination 分页器
 * @description 分页器组件，用于展示页码、请求数据等
 * @tutorial https://ext.dcloud.net.cn/plugin?id=32
 * @property {String} prevText 左侧按钮文字
 * @property {String} nextText 右侧按钮文字
 * @property {Number} current 当前页
 * @property {Number} total 数据总量
 * @property {Number} pageSize 每页数据量
 * @property {Number} showIcon = [true|false] 是否以 icon 形式展示按钮
 * @event {Function} change 点击页码按钮时触发 ,e={type,current} current为当前页，type值为：next/prev，表示点击的是上一页还是下一个
 */
export default {
	name: 'UniPagination',
	emits: ['update:modelValue', 'input', 'change'],
	props: {
		value: {
			type: [Number, String],
			default: 1
		},
		modelValue: {
			type: [Number, String],
			default: 1
		},
		prevText: {
			type: String
		},
		nextText: {
			type: String
		},
		current: {
			type: [Number, String],
			default: 1
		},
		total: {
			// 数据总量
			type: [Number, String],
			default: 0
		},
		pageSize: {
			// 每页数据量
			type: [Number, String],
			default: 10
		},
		showIcon: {
			// 是否以 icon 形式展示按钮
			type: [Boolean, String],
			default: false
		},
		pagerCount: {
			type: Number,
			default: 7
		}
	},
	data() {
		return {
			currentIndex: 1,
			paperData: []
		};
	},
	computed: {
		prevPageText() {
			return this.prevText || '上一页';
		},
		nextPageText() {
			return this.nextText || '下一页';
		},
		maxPage() {
			let maxPage = 1;
			let total = Number(this.total);
			let pageSize = Number(this.pageSize);
			if (total && pageSize) {
				maxPage = Math.ceil(total / pageSize);
			}
			return maxPage;
		},
		paper() {
			const num = this.currentIndex;
			// TODO 最大页数
			const pagerCount = this.pagerCount;
			// const total = 181
			const total = this.total;
			const pageSize = this.pageSize;
			let totalArr = [];
			let showPagerArr = [];
			let pagerNum = Math.ceil(total / pageSize);
			for (let i = 0; i < pagerNum; i++) {
				totalArr.push(i + 1);
			}
			showPagerArr.push(1);
			const totalNum = totalArr[totalArr.length - (pagerCount + 1) / 2];
			totalArr.forEach((item, index) => {
				if ((pagerCount + 1) / 2 >= num) {
					if (item < pagerCount + 1 && item > 1) {
						showPagerArr.push(item);
					}
				} else if (num + 2 <= totalNum) {
					if (item > num - (pagerCount + 1) / 2 && item < num + (pagerCount + 1) / 2) {
						showPagerArr.push(item);
					}
				} else {
					if ((item > num - (pagerCount + 1) / 2 || pagerNum - pagerCount < item) && item < totalArr[totalArr.length - 1]) {
						showPagerArr.push(item);
					}
				}
			});
			if (pagerNum > pagerCount) {
				if ((pagerCount + 1) / 2 >= num) {
					showPagerArr[showPagerArr.length - 1] = '...';
				} else if (num + 2 <= totalNum) {
					showPagerArr[1] = '...';
					showPagerArr[showPagerArr.length - 1] = '...';
				} else {
					showPagerArr[1] = '...';
				}
				showPagerArr.push(totalArr[totalArr.length - 1]);
			} else {
				if ((pagerCount + 1) / 2 >= num) {
				} else if (num + 2 <= totalNum) {
				} else {
					showPagerArr.shift();
					showPagerArr.push(totalArr[totalArr.length - 1]);
				}
			}

			return showPagerArr;
		}
	},
	watch: {
		current: {
			immediate: true,
			handler(val, old) {
				if (val < 1) {
					this.currentIndex = 1;
				} else {
					this.currentIndex = val;
				}
			}
		},
		value: {
			immediate: true,
			handler(val) {
				if (Number(this.current) !== 1) return;
				if (val < 1) {
					this.currentIndex = 1;
				} else {
					this.currentIndex = val;
				}
			}
		}
	},
	methods: {
		// 选择标签
		selectPage(e, index) {
			if (parseInt(e)) {
				this.currentIndex = e;
				this.change('current');
			} else {
				let pagerNum = Math.ceil(this.total / this.pageSize);
				// let pagerNum = Math.ceil(181 / this.pageSize)
				// 上一页
				if (index <= 1) {
					if (this.currentIndex - 5 > 1) {
						this.currentIndex -= 5;
					} else {
						this.currentIndex = 1;
					}
					return;
				}
				// 下一页
				if (index >= 6) {
					if (this.currentIndex + 5 > pagerNum) {
						this.currentIndex = pagerNum;
					} else {
						this.currentIndex += 5;
					}
				}
			}
		},
		clickLeft() {
			if (Number(this.currentIndex) === 1) {
				return;
			}
			this.currentIndex -= 1;
			this.change('prev');
		},
		clickRight() {
			if (Number(this.currentIndex) >= this.maxPage) {
				return;
			}
			this.currentIndex += 1;
			this.change('next');
		},
		change(e) {
			this.$emit('input', this.currentIndex);
			this.$emit('update:modelValue', this.currentIndex);
			this.$emit('change', {
				type: e,
				current: this.currentIndex
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.uni-pagination {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	position: relative;
	overflow: hidden;
	flex-direction: row;
	justify-content: center;
	align-items: center;
}

.uni-pagination__total {
	font-size: 0.14rem;
	color: #999;
	margin-right: 0.15rem;
}

.uni-pagination__btn {
	/* #ifndef APP-NVUE */
	display: flex;
	cursor: pointer;
	/* #endif */
	padding: 0 0.08rem;
	line-height: 0.3rem;
	font-size: $uni-font-size-base;
	position: relative;
	background-color: #f0f0f0;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	text-align: center;
	border-radius: 0.05rem;
	// border-width: .01rem;
	// border-style: solid;
	// border-color: $uni-border-color;
}

.uni-pagination__child-btn {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	position: relative;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	text-align: center;
	color: #0f1214;
	font-size: 0.12rem;
}

.uni-pagination__num {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex: 1;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	height: 0.3rem;
	line-height: 0.3rem;
	font-size: $uni-font-size-base;
	color: $uni-text-color;
	margin: 0 0.05rem;
}

.uni-pagination__num-tag {
	cursor: pointer;
	min-width: 0.3rem;
	margin: 0 0.05rem;
	height: 0.3rem;
	text-align: center;
	line-height: 0.3rem;
	// border: .01rem red solid;
	color: #666;
	border-radius: 0.04rem;
	// border-width: .01rem;
	// border-style: solid;
	// border-color: $uni-border-color;
}

.uni-pagination__num-current {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
}

.uni-pagination__num-current-text {
	font-size: 0.15rem;
}

.uni-pagination--enabled {
	color: #333333;
	opacity: 1;
}

.uni-pagination--disabled {
	opacity: 0.5;
	/* #ifdef H5 */
	cursor: default;
	/* #endif */
}

.uni-pagination--hover {
	color: rgba(0, 0, 0, 0.6);
	background-color: $uni-bg-color-hover;
}

.tag--active:hover {
	color: $primary-color;
}

.page--active {
	color: #fff;
	background-color: $primary-color;
}

.page--active:hover {
	color: #fff;
}

/* #ifndef APP-NVUE */
.is-pc-hide {
	display: block;
}

.is-phone-hide {
	display: none;
}

@media screen and (min-width: 450px) {
	.is-pc-hide {
		display: none;
	}

	.is-phone-hide {
		display: block;
	}

	.uni-pagination__num-flex-none {
		flex: none;
	}
}

/* #endif */
</style>
