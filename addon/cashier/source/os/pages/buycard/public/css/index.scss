.common-wrap.right-wrap{
	background-color: transparent;
}
.left-wrap {
  position: relative;
  width: 4rem;
  display: flex;
  flex-direction: column;
  margin-right: 0.2rem;
  border-radius: 0.04rem;

  .pay-shade {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba($color: #fff, $alpha: 0.6);
    z-index: 10;
  }
  .content {
    color: #303133;
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;

    .title {
      font-size: 0.14rem;
      padding: 0.1rem 0.25rem;
      justify-content: space-between;
      display: flex;
    }

    .clear {
      display: flex;
      align-items: center;

      text {
        &:nth-child(1) {
          font-size: 0.18rem;
        }

        &:nth-child(2) {
          margin-left: 0.03rem;
          font-size: 0.14rem;
        }
      }
    }

    .content-list {
		margin-top: 0.1rem;
		flex: 1;
		height: 0;
		overflow-y: scroll;
		padding: 0 0.2rem;

		.content-item {
			position: relative;
			display: flex;
			align-items: start;
			flex-wrap: wrap;
			justify-content: space-between;
			border-bottom: 0.01rem solid #e6e6e6;
			padding-top: 0.08rem;
			padding-bottom: 0.08rem;

			&.focus,
			&:focus {
				outline: none;
			}

			.flex {
				display: flex;
				align-items: center;
			}

			.info-wrap {
				margin-left: 0.1rem;
				flex-wrap: wrap;
				min-height: 0.6rem;
			}

			.item-img {
				width: 0.6rem;
				height: 0.6rem;
				display: flex;
				align-items: center;

				image {
					width: 100%;
				}
			}

			.item-name {
				font-size: 0.14rem;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				width: 2.04rem;
				height: 0.20rem;
				font-weight: 500;
				color: #222222;
				line-height: 0.20rem;
			}

			.item-del {
				cursor: pointer;
				font-size: 0.14rem;
				display: flex;
				align-items: center;

				&:hover {
					color: $primary-color;
				}

				.iconfont.iconshanchu {
					font-size: 0.2rem;
				}
			}

			.item-spe {
				font-size: 0.12rem;
				margin-top: 0.04rem;
				width: 2.04rem;
				height: 0.17rem;
				font-weight: 500;
				color: #808695;
				line-height: 0.17rem;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.info-top {
				width: 100%;
			}

			.info-bottom {
				width: 100%;
				align-self: flex-end;
			}

			.item-price {
				flex: 1;
			}

			.item-subtotal {
				flex: 1;
				display: flex;
				align-items: end;

				.unit {
					font-size: 0.12rem;
				}
			}

			.item-num {
				display: flex;
				flex: 1;
				justify-content: flex-end;
				align-items: center;
				margin-left: 0.1rem;

				.num {
					// min-width: 0.82rem;
					// text-align: right;
				}

				.num-dec {
					width: 0.25rem;
					height: 0.25rem;
					background: #e6e6e6;
					border: 0.01rem solid #e6e6e6;
					border-radius: 30%;
					text-align: center;
					line-height: 0.23rem;
					font-size: 0.25rem;
					margin-right: 0.1rem;
					cursor: pointer;
					transition: 0.3s;
				}

				.num-inc {
					width: 0.25rem;
					height: 0.25rem;
					background: $primary-color;
					border: 0.01rem solid #e6e6e6;
					border-radius: 30%;
					text-align: center;
					line-height: 0.23rem;
					font-size: 0.25rem;
					margin-left: 0.1rem;
					cursor: pointer;
					transition: 0.3s;
					color: #fff;
				}
			}

			.weight {
				flex: 1;
				justify-content: end;
			}

			.item-total-price {
				font-size: 0.14rem;
				margin-left: 0.1rem;
				color: #fe2278;
			}

			.card-deduction {
				width: 100%;
				font-size: 0.12rem;
				margin-top: 0.05rem;
				color: #999;
			}
		}
	}

    .empty {
      text-align: center;

      image {
        width: 60%;
        margin-top: 0.4rem;
      }

      .tips {
        color: #999;
        margin-top: 0.15rem;
      }
    }
  }

  .bottom {
	width: 100%;
	padding: 0.2rem 0.2rem 0.24rem 0.2rem;
	box-sizing: border-box;
	background-color: #ffffff;

    .bottom-info {
	  display: flex;
	  align-items: center;
	  justify-content: space-between;
	  color: #303133;
	  font-weight: 500;
	  height: 0.27rem;
	  line-height: 0.27rem;
	  margin-bottom: 0.12rem;

      .bottom-left {
        font-size: 0.14rem;
        color: #303133;
		text{
			display: inline-block;
			margin: 0 .05rem;
		}

        .money {
          color: #fe2278;
        }
      }
	  .pay-money {
	    font-size: 0.27rem;
		height: 0.27rem;
		font-weight: 600;
		font-family: AlibabaPuHuiTiM;
		color: $primary-color;
		line-height: 0.22rem;
	  }

    }

    .bottom-btn {
      display: flex;
      align-items: center;
      margin-top: 0.2rem;
	  justify-content: flex-end;

      .btn-right {
        width: 1.4rem;
		height: 0.4rem;
		line-height: 0.4rem;
		border: 0 !important;
		margin: 0;
      }
    }
  }
}

.list-wrap {
  border-radius: 0.02rem;
  height: 100%;
  border-left: 0;
  box-sizing: border-box;
  flex:1;

  .content {
    height: 100%;
  }

  .comp-btn {
    width: 80%;
    margin-top: 0.2rem;
  }

  .header {
    height: 0.66rem;
    line-height: 0.66rem;
    text-align: left;
    color: #303133;
    font-size: 0.14rem;
  }

  .body {
    padding: 0.3rem;
  }
}

.page-height {
  height: 100%;
}

.common-wrap {
  height: 100%;
}