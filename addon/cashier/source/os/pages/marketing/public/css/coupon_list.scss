view {
  color: #303133;
}

/deep/ .uni-scroll-view::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.3rem;
}

/deep/ .uni-scroll-view::-webkit-scrollbar-thumb {
  border-radius: 0.1rem;
  box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
  background: rgba(193, 193, 193, 1);
}

.cart-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.1rem;
}

.record-body {
  width: 10rem;
  min-height: 7rem;
}

.add-coupons{
  margin-bottom: 0.1rem;
  
  button{
    background-color: $primary-color;
    color: #fff;
    display: inline-block;
    padding: 0 0.2rem;
    height: 0.36rem;
    line-height: .36rem;
    font-size: 0.14rem;
    &::after{
      border-width: 0;
    }
  }
}
// 筛选面板
.screen-warp {
  padding: 0.15rem 0.15rem 0;
  background-color: #f2f3f5;
  margin-bottom: 0.15rem;

  .common-form-item .form-label {
    width: 1.2rem;
  }

  .common-btn-wrap {
    margin-left: 1.2rem;
  }

  .coupons-category .form-input-inline {
    width: 2.8rem;
  }

  .form-inline {
    margin-bottom: 0.15rem;
  }

  .common-form-item {
    margin-bottom: 0;
  }

  .input-append {
    position: relative;

    .form-input {
      padding-right: 0.3rem;
    }

    .unit {
      position: absolute;
      top: 0;
      right: 0.1rem;
      height: 0.35rem;
      line-height: 0.35rem;
    }
  }

  .form-input-inline.split-wrap {
    width: initial;
    background: none;
    border: none;
  }
}

.coupons-list {
  display: block;
  width: 100%;
  @extend %body-overhide;
  padding: 0.15rem 0.15rem 0;
  background-color: #fff;

  /deep/ .coupons-content {
    display: flex;
  }

  .action-btn-wrap {
    .action-item {
      margin-left: 0.1rem;
      color: $primary-color;

      &:first-of-type {
        margin-left: 0;
      }
    }
  }

  /deep/ .batch-action {
    .batch-item {
      margin-right: 0.15rem;
      border: 0.01rem solid rgba(0, 0, 0, 0.2);
      padding: 0.05rem;
      border-radius: 0.03rem;
    }
  }
}