.goodslist {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  .goodslist-box {
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;

    .goodslist-left {
      width: 5rem;
      height: 100%;
      border-right: 0.01rem solid #e6e6e6;
      box-sizing: border-box;
      overflow: hidden;
      position: relative;

      .notYet {
        color: #e6e6e6;
        font-size: 0.4rem;
        margin-top: 3rem;
        text-align: center;
      }

      .goods-title {
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.18rem;
        font-weight: 500;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        box-sizing: border-box;
        position: relative;

        .icongengduo1 {
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
          font-size: 0.3rem;
          color: $primary-color;
        }
      }

      .goods-search {
        width: 100%;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0.2rem;
        box-sizing: border-box;

        .search {
          width: 5.6rem;
          height: 0.4rem;
          border-radius: 0.04rem;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          padding: 0 0.2rem;
          box-sizing: border-box;

          .iconfont {
            font-size: 0.16rem;
            color: #909399;
            margin-right: 0.11rem;
          }

          input {
            width: 80%;
            height: 60%;
            border: none;
            font-size: 0.14rem;
          }
        }
      }

      .goods-list-scroll {
        width: 100%;
        height: calc(100% - 1.2rem);

        .itemhover {
          background: var(--primary-color-light-9);
        }

        .item {
          padding: 0.2rem;

          .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.2rem;

            view {
              font-size: 0.16rem;
            }

            view:nth-child(2) {
              color: $primary-color;
            }
          }

        }
      }
    }

    .goodslist-right {
      flex: 1;
      width: 0;
      height: 100%;
      border-right: 0.01rem solid #e6e6e6;
      box-sizing: border-box;
      position: relative;
      padding-bottom: 0.8rem;
      overflow: hidden;

      .goods-title {
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.18rem;
        font-weight: 500;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        box-sizing: border-box;
        position: relative;
      }

      .cart-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 2.1rem;
      }

      .order-information {
        width: 100%;
        height: calc(100% - 0.6rem);
        background: #f8f8f8;
        padding: 0.2rem;
        box-sizing: border-box;
        overflow: scroll;
        // position: relative;

        .order-status {
          font-size: 0.24rem;
          font-weight: bold;
          margin-bottom: 0.24rem;
        }

        .goods-info {
          background: #ffffff;
          padding: 0.2rem;
          box-sizing: border-box;

          .goods-item {
            padding: 0.15rem 0;
            border-bottom: 0.01rem solid #e6e6e6;
            display: flex;

            &:last-child {
              border-bottom: 0;
            }

            .iconfont {
              font-size: 0.2rem;
              cursor: pointer;
              display: flex;
              align-items: center;
            }

            .iconyuan_checked {
              color: $primary-color;
            }

            .image {
              width: 0.8rem;
              height: 0.8rem;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 0.15rem;

              image {
                width: inherit;
              }
            }

            .info {
              flex: 1;
              margin-right: 0.15rem;
            }

            .price {
              text-align: right;
              font-weight: bolder;

              .title {
                font-weight: normal;
                font-size: .12rem;
                color: #999;
              }
            }

            .num {
              text-align: right;
            }
          }
        }

        .refund-info {
          margin-top: .15rem;

          .info-item {
            display: flex;
            line-height: .3rem;

            .content {
              flex: 1;
              text-align: right;
              margin-left: .1rem;
              width: 0;
            }
          }
        }
      }

    }
  }
}

.total-money-num {
  .member-info {
    display: flex;
    align-items: center;
    float: left;
    -ms-flex-negative: 0;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
  }

  .box {
    display: flex;
    align-items: center;
    float: right;

    view {
      font-size: 0.14rem;
    }

    view:nth-child(1) {
      // transform: translateY(-.01rem);
    }

    view:nth-child(2) {
      color: #fe2278;
      font-size: 0.18rem;
    }
  }
}

.total-money-num:after {
  overflow: hidden;
  content: '';
  height: 0;
  display: block;
  clear: both;
}

view {
  color: #303133;
}

/deep/ .uni-scroll-view::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.3rem;
}

/deep/ .uni-scroll-view::-webkit-scrollbar-thumb {
  border-radius: 0.1rem;
  box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
  background: rgba(193, 193, 193, 1);
}

.order-information::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.3rem;
}

.order-information::-webkit-scrollbar-thumb {
  border-radius: 0.1rem;
  box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
  background: rgba(193, 193, 193, 1);
}
