.goodslist {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	.goodslist-box {
		width: 100%;
		height: 100%;
		background: #fff;
		display: flex;
		.goodslist-left {
			width: 5rem;
			height: 100%;
			border-right: 0.01rem solid #e6e6e6;
			box-sizing: border-box;
			overflow: hidden;
			position: relative;
			.notYet {
				color: #e6e6e6;
				font-size: 0.4rem;
				padding-top: 3rem;
				text-align: center;
			}
			.goods-title {
				text-align: center;
				line-height: 0.6rem;
				font-size: 0.18rem;
				font-weight: 500;
				height: 0.6rem;
				border-bottom: 0.01rem solid #e6e6e6;
				box-sizing: border-box;
				position: relative;
				.icongengduo1 {
					position: absolute;
					top: 50%;
					right: 0.2rem;
					transform: translateY(-50%);
					font-size: 0.3rem;
					color: $primary-color;
				}
			}
			.goods-search {
				width: 100%;
				height: 0.6rem;
				border-bottom: 0.01rem solid #e6e6e6;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 0.2rem;
				box-sizing: border-box;
				.search {
					width: 5.6rem;
					height: 0.4rem;
					border-radius: 0.04rem;
					background: #f5f5f5;
					display: flex;
					align-items: center;
					padding: 0 0.2rem;
					box-sizing: border-box;
					.iconfont {
						font-size: 0.16rem;
						color: #909399;
						margin-right: 0.11rem;
					}
					input {
						width: 80%;
						height: 60%;
						border: none;
						font-size: 0.14rem;
					}
				}
			}
			.goods-list-scroll {
				width: 100%;
				height: calc(100% - 2.08rem);
				.itemhover {
					background: var(--primary-color-light-9);
				}
				.item {
					padding: 0.2rem;
					.title {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 0.2rem;
						view {
							font-size: 0.16rem;
						}
						view:nth-child(2) {
							color: $primary-color;
						}
					}
				}
			}
			.add-wastage {
				padding: 0.24rem 0.2rem;
				button{
					width: 100%;
					line-height: 0.4rem;
					height: 0.4rem;
				}
				
			}
		}
		.goodslist-right {
			flex: 1;
			width: 0;
			height: 100%;
			box-sizing: border-box;
			position: relative;
			padding-bottom: 0.88rem;
			overflow: hidden;

			.goods-title {
				text-align: center;
				line-height: 0.6rem;
				font-size: 0.18rem;
				font-weight: 500;
				height: 0.6rem;
				border-bottom: 0.01rem solid #e6e6e6;
				box-sizing: border-box;
				position: relative;
			}
			.cart-empty {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 2.1rem;
			}
			.order-information {
				width: 100%;
				height: calc(100% - 0.6rem);
				padding: 0.2rem;
				box-sizing: border-box;
				overflow-y: auto;
				// position: relative;

				.order-status {
					font-size: 0.24rem;
					font-weight: bold;
					margin-bottom: 0.24rem;
				}
				.order-types {
					width: 100%;
					min-height: 1rem;
					padding: 0.2rem 0.3rem;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					margin-bottom: 0.2rem;
					box-sizing: border-box;
					.type {
						padding-left: 0.1rem;
						view {
							font-size: 0.14rem;
							.look {
								color: $primary-color;
								margin-left: 0.24rem;
							}
						}
						view:nth-child(1) {
							width: 0.9rem;
							text-align: right;
							margin-right: 0.1rem;
						}
						.message{
							max-width: 10.7rem;
							overflow: hidden;
							-o-text-overflow: ellipsis;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}
					.type1 {
						display: flex;
						align-items: center;
						height: 0.34rem;
					}
				}
				.goods-info {
					min-height: 2.7rem;
					background: #ffffff;
					padding: 0.2rem 0;
					box-sizing: border-box;
					.title {
						font-size: 0.18rem;
						font-weight: 550;
						margin-bottom: 0.2rem;
					}
					.table {
						width: 100%;
						box-sizing: border-box;
						margin-bottom: 0.2rem;
						.table-all {
							width: 100%;
							display: flex;
							align-items: center;
							justify-content: space-between;
							padding: 0 0.38rem;
							box-sizing: border-box;
							.table-td {
								font-size: 0.14rem;
								text-align: left;
								display: flex;
								align-items: center;
								justify-content: center;
								image {
									margin-right: 0.1rem;
								}
							}
							.table-goods-name {
								image {
									width: 0.6rem;
									height: 0.6rem;
									margin-right: 0.1rem;
									flex-shrink: 0;
								}
							}
						}
						.table-th {
							height: 0.56rem;
							background: #f7f8fa;
						}
						.table-tr {
							height: 0.7rem;
							border-bottom: 0.01rem solid #e6e6e6;
							.table-td {
								image {
									width: 0.5rem;
									height: 0.5rem;
								}
								.content-text {
									width: 80%;
									height: 0.4rem;
									text-overflow: -o-ellipsis-lastline;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									line-clamp: 2;
									-webkit-box-orient: vertical;
								}
							}
						}
					}
				}
			}
			.total-money-num {
				display: flex;
				flex-direction: column;

				.box {
					justify-content: flex-end;
					padding: 0.1rem 0 0 0;
					color: #333;
				}
				.money {
					text-align: right;
					width: 1.2rem;
					color: #333;
					font-size: 0.14rem;
				}
				.total {
					border-top: 0.01rem solid #e6e6e6;
					margin-top: 0.1rem;

					.money {
						color: #fe2278;
						font-size: 0.18rem;
					}
				}
			}
		}
	}
}

.other-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.total-money-num {
	.member-info {
		display: flex;
		align-items: center;
		float: left;
		-ms-flex-negative: 0;
		-webkit-flex-shrink: 0;
		flex-shrink: 0;
	}
	.box {
		display: flex;
		align-items: center;
		float: right;
		view {
			font-size: 0.14rem;
		}
		view:nth-child(1) {
			// transform: translateY(-.01rem);
		}
		view:nth-child(2) {
			color: #fe2278;
			font-size: 0.18rem;
		}
	}
}
.total-money-num:after {
	overflow: hidden;
	content: '';
	height: 0;
	display: block;
	clear: both;
}

/deep/ .uni-scroll-view::-webkit-scrollbar {
	width: 0.05rem;
	height: 0.3rem;
}
/deep/ .uni-scroll-view::-webkit-scrollbar-thumb {
	border-radius: 0.1rem;
	box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
	background: rgba(193, 193, 193, 1);
}

.order-information::-webkit-scrollbar {
	width: 0.05rem;
	height: 0.3rem;
}
.order-information::-webkit-scrollbar-thumb {
	border-radius: 0.1rem;
	box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
	background: rgba(193, 193, 193, 1);
}

.action-box {
	width: 100%;
	position: absolute;
	bottom: 0;
	right: 0;
	background: #ffffff;
	padding: 0.24rem 0.2rem;
	box-sizing: border-box;
	button {
		min-width: 0.9rem;
		height: 0.4rem;
		font-size: 0.18rem;
		text-align: center;
		line-height: 0.4rem;
		float: right;
		margin-left: 0.1rem;
	}
}
