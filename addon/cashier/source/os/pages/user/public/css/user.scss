.userlist {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  .userlist-box {
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;

    .userlist-left {
      width: 5rem;
      height: 100%;
      border-right: 0.01rem solid #e6e6e6;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .notYet {
        color: #e6e6e6;
        font-size: 0.4rem;
        margin-top: 3rem;
        text-align: center;
      }

      .add-user {
        padding: 0.24rem 0.2rem;
        background: #fff;

        button {
          height: .4rem;
          line-height: .4rem;
        }
      }

      .user-title {
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.18rem;
        font-weight: 500;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        box-sizing: border-box;
        position: relative;

        .icongengduo1 {
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
          font-size: 0.3rem;
          color: $primary-color;
        }
      }

      .user-search {
        width: 100%;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 0.2rem;
        box-sizing: border-box;

        .search {
          width: 5.6rem;
          height: 0.4rem;
          border-radius: 0.04rem;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          padding: 0 0.2rem;
          box-sizing: border-box;

          .iconfont {
            font-size: 0.16rem;
            color: #909399;
            margin-right: 0.11rem;
          }

          input {
            width: 80%;
            height: 60%;
            border: none;
            font-size: 0.14rem;
          }
        }
      }

      .user-list-wrap {
        flex: 1;
        height: 0;
      }

      .user-list-scroll {
        width: 100%;
        height: 100%;

        .itemhover {
          background: var(--primary-color-light-9);
        }

        .item {
          width: 100%;
          display: flex;
          align-items: center;
          padding: 0.2rem;
          box-sizing: border-box;
          border-bottom: 0.01rem solid #e6e6e6;

          image {
            width: 0.7rem;
            height: 0.7rem;
            margin-right: 0.1rem;
          }

          .item-right {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            height: 0.6rem;
            width: 100%;

            .user-name {
              font-size: 0.16rem;
            }

            .user-money {
              font-size: 0.14rem;
              margin-top: 0.1rem;
            }

            .login-time {
              margin-top: 0.1rem;
            }

            .user-status {
              text-align: right;
              color: $primary-color;
              font-size: 0.16rem;
            }
          }
        }
      }
    }

    .userlist-right {
      width: 0;
      flex: 1;
      height: 100%;
      border-right: 0.01rem solid #e6e6e6;
      box-sizing: border-box;
      position: relative;

      .user-title {
        text-align: center;
        line-height: 0.6rem;
        font-size: 0.18rem;
        font-weight: 500;
        height: 0.6rem;
        border-bottom: 0.01rem solid #e6e6e6;
        box-sizing: border-box;
        position: relative;

        .icongengduo1, .iconguanbi1 {
          position: absolute;
          top: 50%;
          right: 0.2rem;
          transform: translateY(-50%);
          font-size: 0.3rem;
          color: $primary-color;
          cursor: pointer;
        }
      }

      .user-information {
        width: 100%;
        padding: 0.2rem 0.2rem 0.2rem 0.2rem;
        box-sizing: border-box;
        height: calc(100% - 1.38rem);
        overflow-y: auto;
        position: relative;

        .title {
          font-size: 0.18rem;
          margin-bottom: 0.32rem;
        }

        .title2 {
          margin-bottom: 0.35rem;
        }

        .information-box {
          display: flex;
          justify-content: space-between;

          .box-left {
            width: 5rem;

            .information {
              width: 100%;
              padding-left: 0.1rem;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              margin-bottom: 0.15rem;

              view {
                color: #303133;
                font-size: 0.14rem;
              }

              view:nth-child(1) {
                width: 1.3rem;
                margin-right: 0.16rem;
                text-align: right;
              }

              view:nth-child(2) {
                width: 74%;
                margin-right: 0.23rem;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }

            .information:last-child {
              margin-bottom: 0.35rem;
            }
          }

          .user-img {
            width: 1.5rem;
            height: 1.5rem;
          }
        }

        .table {
          width: 100%;
          height: 2.6rem;
          box-sizing: border-box;

          .table-all {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 0.38rem;
            box-sizing: border-box;

            .table-td {
              font-size: 0.14rem;
              text-align: left;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }

          .table-th {
            height: 0.56rem;
            background: #f7f8fa;
          }

          .table-tb {
            width: 100%;
            height: calc(100% - 0.56rem);

            .table-tr {
              height: 0.7rem;
              border-bottom: 0.01rem solid #e6e6e6;
              box-sizing: border-box;

              .table-td {
                image {
                  width: 0.5rem;
                  height: 0.5rem;
                }

                text-overflow: -o-ellipsis-lastline;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .button-box {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
        padding: 0.24rem 0.2rem;
        button {
          width: 1rem;
          height: 0.4rem;
          line-height: 0.4rem;
          margin-left: 0.1rem;
          margin-right: 0;
        }
      }
    }
  }
}

view {
  color: #303133;
}

/deep/ .uni-scroll-view::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.3rem;
}

/deep/ .uni-scroll-view::-webkit-scrollbar-thumb {
  border-radius: 0.1rem;
  box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
  background: rgba(193, 193, 193, 1);
}

.user-information::-webkit-scrollbar {
  width: 0.05rem;
  height: 0.3rem;
}

.user-information::-webkit-scrollbar-thumb {
  border-radius: 0.1rem;
  box-shadow: inset 0 0 0.05rem rgba(0, 0, 0, 0.2);
  background: rgba(193, 193, 193, 1);
}

.cart-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2.1rem;
}

.form-content {
  margin-top: 0.2rem;

  .form-item {
    margin-bottom: 0.1rem;
    display: flex;

    .form-label {
      width: 1.3rem;
      text-align: right;
      padding-right: 0.1rem;
      box-sizing: border-box;
      height: 0.32rem;
      line-height: 0.32rem;

      .required {
        color: red;
        margin-right: 0.03rem;
      }
    }

    .form-inline {
      width: 2.4rem;
      line-height: 0.32rem;
      margin-right: 0.1rem;
      box-sizing: border-box;

      .form-input {
        border-width: 0.01rem;
        border-style: solid;
        background-color: #fff;
        color: rgba(0, 0, 0, 0.85);
        border-radius: 0.02rem;
        padding-left: 0.1rem;
        height: 0.32rem;
        line-height: 0.32rem;
        font-size: 0.14rem;
        border-color: #e6e6e6;
      }

      button {
        width: calc(50% - 0.05rem);
        display: inline-block;
        margin-right: 0.1rem;

        &:nth-child(2) {
          margin-right: 0;
        }
      }
    }

    .search-wrap {
      position: relative;
    }
  }
}

.form-radio-group {
  display: flex;
  align-items: center;
}

.form-checkbox-item, .form-radio-item {
  margin-right: 26rpx;
  display: flex;
  align-items: center;
}

/deep/ .uni-radio-input, .uni-checkbox-input {
  width: .18rem;
  height: .18rem;
}

// pop弹框
.pop-box {
  background: #ffffff;
  width: 4.2rem;
  height: 3.38rem;

  .pop-header {
    padding: 0 0.15rem 0 0.2rem;
    height: 0.5rem;
    line-height: 0.5rem;
    border-bottom: 0.01rem solid #f0f0f0;
    font-size: 0.14rem;
    color: #333;
    overflow: hidden;
    border-radius: 0.02rem 0.2rem 0 0;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .pop-header-text {
    }

    .pop-header-close {
      cursor: pointer;

      text {
        font-size: 0.18rem;
      }
    }
  }

  .pop-content {
    height: calc(100% - 1.05rem);
    padding: 0.2rem;
    box-sizing: border-box;
  }

  .form-content {
    margin-top: 0;
    padding-top: 0.2rem;
    display: flex;
    flex-direction: column;
    align-items: center;

    .form-label {
      width: .9rem;
    }
  }

  .pop-bottom {
    padding: 0.1rem;
    border-top: 0.01rem solid #eee;

    button {
      width: 95%;
    }
  }
}