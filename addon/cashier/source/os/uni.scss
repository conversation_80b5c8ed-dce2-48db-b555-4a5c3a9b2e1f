/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color: #333; //基本色
$uni-text-color-inverse: #fff; //反色
$uni-text-color-grey: #999; //辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #c0c0c0;

/* 背景颜色 */
$uni-bg-color: #ffffff;
$uni-bg-color-grey: #f8f8f8;
$uni-bg-color-hover: #f1f1f1; //点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$uni-border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 0.12rem;
$uni-font-size-base: 0.14rem;
$uni-font-size-lg: 0.16rem;

/* 图片尺寸 */
$uni-img-size-sm: 0.2rem;
$uni-img-size-base: 0.26rem;
$uni-img-size-lg: 0.4rem;

/* Border Radius */
$uni-border-radius-sm: 0.02rem;
$uni-border-radius-base: 0.03rem;
$uni-border-radius-lg: 0.06rem;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 0.5rem;
$uni-spacing-row-base: 0.1rem;
$uni-spacing-row-lg: 0.15rem;

/* 垂直间距 */
$uni-spacing-col-sm: 0.04rem;
$uni-spacing-col-base: 0.08rem;
$uni-spacing-col-lg: 0.12rem;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 0.2rem;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle: 0.26rem;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 0.15rem;

.uni-app--showleftwindow + .uni-tabbar-bottom {
  display: none;
}

/* 收银台相关 */
$primary-color: var(--primary-color); // 主色调
$header-height: 0.56rem; // 头部高度
$aside-width: 0.88rem; // 侧边栏宽度
$body-bg: #F8F8F8; // 整体背景色
$statusbar-height: 0rem; // app状态栏高度
/* #ifdef APP-PLUS */
$statusbar-height: var(--status-bar-height);
/* #endif */

text,
view {
  font-size: 0.14rem;
}

/* #ifdef H5*/
body {
  min-width: 1200px;
  overflow-x: unset !important;
  overflow-y: hidden;
  background: $body-bg;

  &::-webkit-scrollbar {
    width: 0.06rem;
    height: 0.06rem;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 0.06rem;
    box-shadow: inset 0 0 0.06rem rgba(45, 43, 43, 0.45);
    background-color: #ddd;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

%body-overhide {
  height: 100%;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0.06rem;
    height: 0.06rem;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 0.06rem;
    box-shadow: inset 0 0 0.06rem rgba(45, 43, 43, 0.45);
    background-color: #ddd;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

/* #endif */

radio {
  transform: scale(0.8);
}

checkbox {
  transform: scale(0.8);
}

uni-modal .uni-modal__btn_primary {
  color: $primary-color !important;
}