<style>
	.forbidden{cursor:not-allowed;background-color: #eee;}
	.layui-table-body{max-height: 480px !important;}
	.layui-form-item .layui-input-inline.end-time{float: none;}
	.goods-title{display: flex;align-items: center;}
	.goods-title .goods-img{display: flex;align-items: center;justify-content: center;width: 55px;height: 55px;margin-right: 5px;}
	.goods-title .goods-img img{max-height: 100%;max-width: 100%;}
	.goods-title .goods-name{flex: 1;line-height: 1.6;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动名称：</label>
		<div class="layui-input-block">
			<input type="text" name="discount_name" value="{$discount_info.discount_name}" autocomplete="off" class="layui-input len-long" maxlength="40">
		</div>
		<div class="word-aux">
			<p>活动名称将显示在限时折扣活动列表中，方便商家管理使用</p>
		</div>
	</div>

	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea len-long" maxlength="150">{$discount_info.remark}</textarea>
		</div>
		<div class="word-aux">
			<p>备注是商家对限时折扣活动的补充说明文字，在商品详情页-优惠信息位置显示；非必填选项</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>活动时间：</label>
		<div class="layui-inline">
			<div class="layui-input-inline len-mid">
				<input type="text" id="start_time" name="start_time" value="{:date('Y-m-d H:i:s', $discount_info.start_time)}" lay-verify="required" autocomplete="off" class="layui-input">
				<i class=" iconrili iconfont calendar"></i>
			</div>
			<span class="layui-form-mid">-</span>
			<div class="layui-input-inline len-mid end-time">
				<input  type="text" id="end_time" lay-verify="required|time" name="end_time" value="{:date('Y-m-d H:i:s', $discount_info.end_time)}" autocomplete="off" class="layui-input">
				<i class=" iconrili iconfont calendar"></i>
			</div>
		</div>
	</div>

	<div class="layui-form-item goods_list">
		<label class="layui-form-label"><span class="required">*</span>商品选择：</label>
		<div class="layui-input-block">
			<table id="selected_goods_list" lay-filter="selected_goods_list"></table>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backDiscountList()">返回</button>
	</div>

	<!-- 操作 -->
	<script type="text/html" id="operation">
		<div class="table-btn">
			{{# if (d.is_select == 1){ }}
			<a class="layui-btn no-participation">不参与</a>
			{{# }else{ }}
			<a class="layui-btn participation">参与</a>
			{{# } }}
		</div>
	</script>

	<input type="hidden" name="discount_id" value="{$discount_info.discount_id}" />
	<input type="hidden" name="goods_id" value="{$discount_info.goods_id}" />
</div>

<script type="text/html" id="discountPrice">
	{{# if (d.is_select == 1){ }}
	<input type="number" class="layui-input len-input discount_price" onchange="setGoodsSku('discount_price', {{d.sku_id}}, this)" value="{{d.discount_price}}" lay-verify="discount_price" min="0.00"/>
	{{# }else{ }}
	<input type="number" class="layui-input len-input forbidden discount_price" readonly value="{{d.discount_price}}" lay-verify="discount_price" min="0.00"/>
	{{# } }}
</script>

<script type="text/html" id="toolbarOperation">
	<button class="layui-btn layui-btn-primary" lay-event="discount-price">折扣价</button>
</script>
<script>
	var goods_id = [], selectedGoodsId = [], sku_list = {:json_encode($discount_info.goods_sku, JSON_UNESCAPED_UNICODE)}
	,info = {:json_encode($discount_info, JSON_UNESCAPED_UNICODE)};

	layui.use(['form', 'laydate'], function() {

		var form = layui.form,
			laydate = layui.laydate,
			repeat_flag = false; //防重复标识

		form.render();

		renderTable(sku_list);

		// 时间模块
		if (info.status == 0){
			laydate.render({
				elem: '#start_time', //指定元素
				type: 'datetime'
			});
		}

		laydate.render({
			elem: '#end_time', //指定元素
			type: 'datetime'
		});

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {

			var goods_list = [];
			var cancel_goods_list = [];
			for (var i in sku_list){
				if(sku_list[i]['is_select'] == 1){
					goods_list.push(sku_list[i]);
				}else{
					cancel_goods_list.push(sku_list[i]);
				}
			}

			//活动开始中时间限制
			if (info.status == 1){

				if ((Date.parse(data.field.start_time)/1000) != info.start_time){
					layer.msg('活动进行中禁止修改开始时间', {icon: 5, anim: 6});
					return false;
				}

				if ((Date.parse(data.field.end_time)/1000) < info.end_time){
					layer.msg('活动进行中只能延长结束时间，不能缩短时间', {icon: 5, anim: 6});
					return false;
				}
			}

			if(goods_list.length == 0){
				layer.msg('请选择商品', {icon: 5, anim: 6});
				return false;
			}

			data.field.sku_list = goods_list;
			data.field.cancel_sku_list = cancel_goods_list;
			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				url: ns.url("discount://shop/discount/edit"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title: '操作提示',
							btn: ['返回列表', '继续编辑'],
							yes: function(index, layero) {
								location.hash = ns.hash("discount://shop/discount/lists")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			});
		});

		/**
		 * 表单验证
		 */
		form.verify({
			time: function(value) {
				var now_time = (new Date()).getTime();
				var start_time = (new Date($("#start_time").val())).getTime();
				var end_time = (new Date(value)).getTime();
				if (now_time > end_time) {
					return '结束时间不能小于当前时间!'
				}
				if (start_time > end_time) {
					return '结束时间不能小于开始时间!';
				}
			},

		});

	});

	function setSelect(status,id) {
		for (var i = 0; i < sku_list.length; i++){
			if (sku_list[i].sku_id == parseInt(id)){
				sku_list[i]['is_select'] = status;
			}
		}
		renderTable(sku_list);
		return false;
	}

	// 表格渲染
	function renderTable(sku_list) {
		//展示已知数据
		table = new Table({
			elem: '#selected_goods_list',
			page: false,
			limit: Number.MAX_VALUE,
			cols: [
				[{
					width: "3%",
					type: 'checkbox',
					unresize: 'false'
				},{
					field: 'sku_name',
					title: '商品名称',
					width: '23%',
					unresize: 'false',
					templet: function(data) {
						var html = '';
						html += `
							<div class="goods-title">
								<div class="goods-img">
									<img layer-src src="${data.sku_image ? ns.img(data.sku_image) : ''}" alt="">
								</div>
								<p class="multi-line-hiding goods-name" data-goods_id="${data.goods_id}" data-sku_id="${data.sku_id}" title="${data.sku_name}">${data.sku_name}</p>
							</div>
						`;
						return html;
					}
				}, {
					field: 'price',
					title: '商品价格',
					unresize: 'false',
					align: 'left',
					width: '15%',
					templet: function(data) {
						return '<p class="line-hiding" title="'+ data.price +'">￥<span>' + data.price +'</span></p>';
					}
				}, {
					field: 'stock',
					title: '库存',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return '<p class="stock">' + data.stock +'</p>';
					}
				}, {
					title: '<span title="折扣价">折扣价</span>',
					unresize: 'false',
					width: '13%',
					templet: '#discountPrice'

				}, {
					title: '是否参与活动',
					toolbar: '#operation',
					width: '10%',
					unresize: 'false'
				}]
			],
			data: sku_list,
			toolbar: '#toolbarOperation'
		});
		/**
		 * 批量操作
		 */
		table.toolbar(function(obj) {

			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			switch (obj.event) {
				case "discount-price":
					editInput(0,obj);
					break;
			}
		});
		return false;
	}

	function editInput(textIndex=0,data) {
		var text = [{
			name: '折扣价',
			value: 'discount_price'
		}];

		layer.open({
			type: 1,
			title:"修改"+text[textIndex].name,
			area:['600px'],
			btn:["保存","返回"],
			content: `
			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>${text[textIndex].name}：</label>
				<div class="layui-input-block">
					<input type="text" name="bargain_edit_input" lay-verify="required" autocomplete="off" class="layui-input len-mid" placeholder="请输入${text[textIndex].name}">
				</div>
			</div>
		`,
			yes: function(index, layero){
				var val = $("input[name='bargain_edit_input']").val();
				if (!val){
					layer.msg("请输入" + text[textIndex].name);
					return false;
				}
				data.data.forEach(function (item,index) {
					sku_list.forEach(function (skuItem,skuIndex) {
						if (item.sku_id == skuItem.sku_id){
							sku_list[skuIndex][text[textIndex].value] = val;
						}
					})
				});
				renderTable(sku_list);
				layer.closeAll();
			}
		});
	}

	function setGoodsSku(type, sku_id, obj){
		$.each(sku_list, function (i, e) {
			if(sku_id == e.sku_id){
				if(Number(sku_list[i]['price'])< Number( $(obj).val())){
					$(obj).val(sku_list[i]['price']);
					return layer.msg('折扣价格不能大于商品价格');
				}else if($(obj).val()<0){
					$(obj).val(sku_list[i]['price']);
					return layer.msg('折扣价格不能小于0');
				}else{
					sku_list[i][type] = $(obj).val();
				}
			}
		})
	}

	$("body").off("click",".no-participation").on("click",".no-participation",function(){
		$(this).text("参与");
		$(this).parents("tr").find("input").each(function (index,item) {
			$(item).attr("readonly",true);
			$(item).attr("disabled",true);
			$(item).addClass("forbidden");
			$(item).attr("lay-verify","");
		});

		$(this).addClass("participation").removeClass("no-participation");
		sku_list[$(this).parents("tr").attr("data-index")].is_select = 0;
	});

	$("body").off("click",".participation").on("click",".participation",function(){
		$(this).text("不参与");
		$(this).parents("tr").find("input").each(function (index,item) {
			$(item).attr("readonly",false);
			$(item).attr("disabled",false);
			$(item).removeClass("forbidden");
		});

		$(this).removeClass("participation").addClass("no-participation");
		sku_list[$(this).parents("tr").attr("data-index")].is_select = 1;
	});

	function backDiscountList() {
		location.hash = ns.hash("discount://shop/discount/lists");
	}
</script>
