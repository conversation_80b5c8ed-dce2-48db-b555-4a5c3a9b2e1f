<link rel="stylesheet" href="STATIC_CSS/promotion_detail.css">

<div class="layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">基本信息</span>
    </div>
    <div class="layui-card-body">
        <div class="promotion-view">
            <div class="promotion-view-item">
                <label>活动名称：</label>
                <span>{$info.name}</span>
            </div>
            <div class="promotion-view-item">
                <label>活动状态：</label>
                <span>{$info.status_name}</span>
            </div>
            <div class="promotion-view-item">
                <label>开始时间：</label>
                <span>{:date('Y-m-d H:i:s',$info.start_time)}</span>
            </div>
            <div class="promotion-view-item">
                <label>结束时间：</label>
                <span>{:date('Y-m-d H:i:s',$info.end_time)}</span>
            </div>
            <div class="promotion-view-item">
                <label>使用门槛：</label>
                <span>满{$info.at_least}元</span>
            </div>
            <div class="promotion-view-item">
                <label>活动内容：</label>
                <span>{$info.divide_num}名好友，瓜分{$info.money}元</span>
            </div>
            <div class="promotion-view-item">
                <label>瓜分券总量：</label>
                <span>{$info.inventory}张</span>
            </div>
            <div class="promotion-view-item">
                <label>瓜分有效期：</label>
                <span>{$info.divide_time}小时</span>
            </div>

            <div class="promotion-view-item">
                <label>是否模拟好友：</label>
                <span>{if $info.is_simulation==1}是{else/}否{/if}</span>
            </div>
            <div class="promotion-view-item">
                <label>仅新人参与限制：</label>
                <span>{if $info.is_new==1}是{else/}否{/if}</span>
            </div>
            <div class="promotion-view-item">
                <label>瓜分方式：</label>
                <span>{if $info.divide_type == 0}固定金额{else/}随机金额{/if}</span>
            </div>
            {if $info.goods_type == 1}
            <div class="promotion-view-item">
                <label>活动商品：</label>
                <span>全部商品参与</span>
            </div>
            {/if}
        </div>
        <div class="promotion-view-item-line">
            <label class="promotion-view-item-custom-label">横幅图片：</label>
            <div class="promotion-view-item-custom-box img-upload">
                <div class="upload-img-block icon">
                    <div class="upload-img-box">
                        {if condition="$info.image"}
                        <img layer-src  src="{:img($info.image)}" >
                        {else/}
                        <img layer-src src="__STATIC__/img/shape.png" />
                        {/if}
                    </div>
                </div>
            </div>
        </div>
        {if !empty($info.remark)}
        <div class="promotion-view">
            <div class="promotion-view-item-line">
                <label class="promotion-view-item-custom-label">活动说明：</label>
                <div class="promotion-view-item-custom-box">{$info.remark}</div>
            </div>
        </div>
        {/if}
    </div>
</div>
{if $info.goods_type != 1}
<div class="layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">活动商品</span>
    </div>
    <div class="layui-card-body">
        <div class='promotion-view-list'>
            <table id="promotion_list"></table>
        </div>
    </div>
</div>
{/if}

<script type='text/html' id="promotion_list_item_box_html">
    <div class="promotion-list-item-title">
        <div class="promotion-list-item-title-icon">
            <img src="{{ ns.img(d.goods_image) }}" alt="">
        </div>
        <p class="promotion-list-item-title-name multi-line-hiding">{{ d.goods_name }}</p>
    </div>
</script>
<script>
    var promotion_list = {:json_encode($info.goods_list, JSON_UNESCAPED_UNICODE)};
    layui.use('table', function() {
        new Table({
            elem: '#promotion_list',
            cols: [
                [{
                    field: 'goods_name',
                    title: '商品名称',
                    unresize: 'false',
                    width: '60%',
                    templet: '#promotion_list_item_box_html'
                }, {
                    field: 'price',
                    title: '商品价格(元)',
                    unresize: 'false',
                    align: 'right',
                    width: '20%',
                    templet: function(data) {
                        return '￥' + data.price;
                    }
                }, {
                    field: 'goods_stock',
                    title: '库存',
                    unresize: 'false',
                    align: 'center',
                    width: '20%'
                }],
            ],
            data: promotion_list
        });
    });
</script>
