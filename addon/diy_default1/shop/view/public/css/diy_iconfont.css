@font-face {
  font-family: "icondiy-my-template";
  src: url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.eot?t=20d1e70f3aeb7dcbee56c813164786bc'); /* IE9 */
  src: url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.eot?t=20d1e70f3aeb7dcbee56c813164786bc#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.woff?t=20d1e70f3aeb7dcbee56c813164786bc') format('woff2'),
  url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.woff?t=20d1e70f3aeb7dcbee56c813164786bc') format('woff'), /* chrome、firefox */
  url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.ttf?t=20d1e70f3aeb7dcbee56c813164786bc') format('truetype'), /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
  url('https://cdn3.codesign.qq.com/icons/r2nL6jg1z80pJXV/latest/iconfont.svg?t=20d1e70f3aeb7dcbee56c813164786bc#icondiy-my-template') format('svg'); /* iOS 4.1- */
}

.icondiy-my-template {
  font-family: "icondiy-my-template" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-furniture-taideng:before {
  content: "\e001";
}
.icon-furniture-deng:before {
  content: "\e002";
}
.icon-furniture-szt:before {
  content: "\e003";
}
.icon-furniture-safa:before {
  content: "\e004";
}
.icon-furniture-bed:before {
  content: "\e005";
}
.icon-furniture-yaoyi:before {
  content: "\e006";
}
.icon-furniture-guizi:before {
  content: "\e007";
}
.icon-furniture-lunyi:before {
  content: "\e008";
}
.icon-furniture-fj:before {
  content: "\e009";
}
.icon-furniture-xicaichi:before {
  content: "\e00a";
}
.icon-building-glou:before {
  content: "\e00b";
}
.icon-building-gaolou:before {
  content: "\e00c";
}
.icon-building-glt:before {
  content: "\e00d";
}
.icon-animal-hippo:before {
  content: "\e00e";
}
.icon-animal-pig:before {
  content: "\e00f";
}
.icon-animal-dolphin:before {
  content: "\e010";
}
.icon-animal-duck:before {
  content: "\e011";
}