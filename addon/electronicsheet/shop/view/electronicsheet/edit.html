<div class="layui-collapse tips-wrap">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>管理员可以在此页编辑电子面单模板</li>
		</ul>
	</div>
</div>

<div class="layui-form form-wrap">

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">基础设置</span>
		</div>

		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>电子面单名称：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.template_name}" name="template_name" lay-verify="required" autocomplete="off" class="layui-input len-long">
				</div>
			</div>

			<div class="layui-form-item express_company">
				<label class="layui-form-label"><span class="required">*</span>快递公司：</label>
				<div class="layui-input-block len-short">
					<select name="company_id" lay-verify="required" lay-filter="company_id">
						<option value="">请选择</option>
						{foreach $company_list as $v}
						<option value="{$v.company_id}" class="company_id_{$v.company_id}" data-value="{$v.print_style}" {if $electronicsheet_info.company_id == $v.company_id} selected {/if}>{$v.company_name}</option>
						{/foreach}
					</select>
				</div>
			</div>

			<div class="layui-form-item print_style"></div>

		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">其他设置</span>
		</div>

		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label">CustomerName：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.customer_name}" name="customer_name" autocomplete="off" class="layui-input len-long">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">CustomerPwd：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.customer_pwd}" name="customer_pwd" autocomplete="off" class="layui-input len-long">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">SendSite：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.send_site}" name="send_site" autocomplete="off" class="layui-input len-long">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">SendStaff：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.send_staff}" name="send_staff" autocomplete="off" class="layui-input len-long">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">MonthCode：</label>
				<div class="layui-input-block">
					<input type="text" value="{$electronicsheet_info.month_code}" name="month_code" autocomplete="off" class="layui-input len-long">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">邮费支付方式：</label>
				<div class="layui-input-block len-short">
					<select name="postage_payment_method">
						<option value="">请选择</option>
						<option value="1" {if $electronicsheet_info.postage_payment_method == 1} selected {/if}>现付</option>
						<option value="2" {if $electronicsheet_info.postage_payment_method == 2} selected {/if}>到付</option>
						<option value="3" {if $electronicsheet_info.postage_payment_method == 3} selected {/if}>月结</option>
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">是否上门揽件：</label>
				<div class="layui-input-inline">
					<input type="radio" name="is_notice" value="1" title="是"  {if $electronicsheet_info.is_notice == 1} checked {/if}>
					<input type="radio" name="is_notice" value="0" title="否" {if $electronicsheet_info.is_notice == 0} checked {/if}>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">是否默认：</label>
				<div class="layui-input-inline">
					<input type="radio" name="is_default" value="1" title="是" {if $electronicsheet_info.is_default == 1} checked {/if}>
					<input type="radio" name="is_default" value="0" title="否" {if $electronicsheet_info.is_default == 0} checked {/if}>
				</div>
			</div>

		</div>
	</div>
	<input type="hidden" name="id" value="{$electronicsheet_info.id}">

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backElectronicSheetList()">返回</button>
	</div>
</div>

<script>
    layui.use(['form'], function() {
        var form = layui.form,
            repeat_flag = false;

        var print_style = '{$electronicsheet_info.print_style}';
        var company_id = '{$electronicsheet_info.company_id}';
		var print_style_data =  $('.company_id_'+company_id).attr('data-value') || '{}';
        print_style_data = JSON.parse(print_style_data);

        var html = '<div class="layui-form-item">' +
            '<label class="layui-form-label"><span class="required">*</span>模板样式：</label>' +
            '<div class="layui-input-block len-short">' +
            '<select name="print_style" lay-verify="required">' +
            '<option value="">请选择</option>';

        for(i = 0; i < print_style_data.length; i ++){
            if(i == print_style){
                html += '<option value="'+ i +'" selected>'+ print_style_data[i]['template_name'] +'</option>';
			}else{
                html += '<option value="'+ i +'">'+ print_style_data[i]['template_name'] +'</option>';
			}
        }

        html += '</select>' +
            '</div>' +
            '</div>';

        $('.print_style').html(html);

        form.render();

        /**
         * 表单验证
         */
        form.verify({
            time: function(value) {
                var now_time = (new Date()).getTime();
                var start_time = (new Date($("#start_time").val())).getTime();
                var end_time = (new Date(value)).getTime();
                if (now_time > end_time) {
                    return '结束时间不能小于当前时间!'
                }
                if (start_time > end_time) {
                    return '结束时间不能小于开始时间!';
                }
            },
            flnum: function(value) {
                var arrMen = value.split(".");
                var val = 0;
                if (arrMen.length == 2) {
                    val = arrMen[1];
                }
                if (val.length > 2) {
                    return '保留小数点后两位！'
                }
            },
            int: function(value) {
                if (value <= 1 || value % 1 != 0) {
                    return '请输入大于1的正整数！'
                }
            }
        });

        form.on('select(company_id)', function(data){
            var print_style = $(".company_id_"+data.value).attr("data-value");

            print_style = JSON.parse(print_style);

            var html = '<div class="layui-form-item">' +
                '<label class="layui-form-label"><span class="required">*</span>模板样式：</label>' +
                '<div class="layui-input-block len-short">' +
                '<select name="print_style" lay-verify="required">' +
                '<option value="">请选择</option>';

            for(i = 0; i < print_style.length; i ++){
                html += '<option value="'+ i +'">'+ print_style[i]['template_name'] +'</option>';
            }

            html += '</select>' +
                '</div>' +
                '</div>';

            $('.print_style').html(html);
            form.render();
        });

        /**
         * 表单提交
         */
        form.on('submit(save)', function(data){
            if(repeat_flag) return;
            repeat_flag = true;
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("electronicsheet://shop/electronicsheet/edit"),
                data: data.field,
                async: false,
                success: function(res){
                    repeat_flag = false;

                    if (res.code == 0) {
                        layer.confirm('编辑成功', {
                            title:'操作提示',
                            btn: ['返回列表', '继续编辑'],
                            yes: function(index, layero) {
                                location.hash = ns.hash("electronicsheet://shop/electronicsheet/lists");
								layer.close(index);
                            },
                            btn2: function(index, layero) {
								layer.close(index);
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            })
        });
    });

    function backElectronicSheetList() {
        location.hash = ns.hash("electronicsheet://shop/electronicsheet/lists");
    }
</script>
