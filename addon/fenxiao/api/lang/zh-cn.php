<?php

return [
	'REQUEST_FENXIAO_NAME' => '缺少参数fenxiao_name',
	'REQUEST_QRCODE_PARAM' => '缺少参数qrcode_param',
	'REQUEST_MONEY' => '缺少参数money',
	'REQUEST_FENXIAO_ORDER_ID' => '缺少参数fenxiao_order_id',
	'REQUEST_SOURCE_MEMBER' => '缺少参数source_member',
	'REQUEST_LEVEL_ID' => '缺少参数level_id',
	'MEMBER_NOT_IS_FENXIAO' => '该会员还不是分销商',
	'FENXIAO_NOT_EXIST' => '分销商不存在',
	'REQUEST_FENXIAO_ID' => '缺少参数fenxiao_id',
	'REQUEST_COLLECT_ID' => '缺少参数collect_id',
	'GOODS_COLLECT_IS_EXIST' => '已关注该分销商品',
	'APPLYING' => '审核中',
	'ALREADY_DISTRIBUTOR' => '已是分销商',
    'NOT_EXIST_FENXIAO_RELATION' => '查询用户与您不存在分销关系'
];