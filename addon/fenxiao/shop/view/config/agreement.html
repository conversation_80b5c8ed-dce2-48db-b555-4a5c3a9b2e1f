<div class="layui-form form-wrap">
    <!-- 基础上传 -->
    <div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">分销商申请设置</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item apply">
                <label class="layui-form-label">显示申请协议：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        <input type="radio" name="is_agreement" value="1" lay-filter="level" title="显示" {if $agreement_info.is_agreement == 1} checked {/if}/>
                        <input type="radio" name="is_agreement" value="0" lay-filter="level" title="隐藏" {if $agreement_info.is_agreement != 1} checked {/if}/>
                    </div>
                </div>
            </div>

            <div class="layui-form-item apply_img">
                <label class="layui-form-label">申请页面顶部图片：</label>
                <div class="layui-input-block">
                    <div class="upload-img-block">
                        <div class="upload-img-box {notempty name="$agreement_info['img']"}hover{/notempty}">
							<div class="upload-default" id="applyImg">
                            {if condition="$agreement_info.img"}
								<div id="preview_imgUpload" class="preview_img">
									<img layer-src src="{:img($agreement_info.img)}"  class="img_prev"/>
								</div>
                            {else/}
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
                            {/if}
							</div>
	                        <div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							 <input type="hidden" name="img" value="{$agreement_info.img}">
                        </div>
                       <!-- <p id="applyImg" class=" {if condition='$agreement_info.img'} replace {else/} no-replace{/if}">替换</p>
                        <input type="hidden" name="img" value="{$agreement_info.img}">
                        <i class="del {if condition="$agreement_info.img"}show{/if}">x</i> -->
                    </div>
                </div>

                <div class="word-aux">
                    <p>建议尺寸：750*337像素</p>
                </div>
            </div>

        </div>

    </div>
    <div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">协议设置</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">协议名称：</label>
                <div class="layui-input-block">
                    <input type="text" name="agreement_title" value="{$document.title}" lay-verify="required" class="layui-input len-mid new_pass" maxlength="18">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">协议内容：</label>
                <div class="layui-input-block">
                    <script id="editor" type="text/plain" class="special-length" style="height:600px;"></script>
                    <input type="hidden" name="agreement_content" id="agreement_content" value="{$document.content}" />
                </div>
            </div>
        </div>
    </div>

	<div class="form-row" style="margin-top:0;padding-left:20px;">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>

</div>

<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script>
    //实例化富文本
    var ue = UE.getEditor('editor');
    if($("#agreement_content").val()){
        ue.ready(function() {
            ue.setContent($("#agreement_content").val());
        });
    }

    layui.use(['form'], function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
        form.render();

        var upload = new Upload({
            elem: '#applyImg'
        });

        form.on('submit(save)', function(data) {
            if (repeat_flag) return;
            repeat_flag = true;

            var field = data.field;

            // 删除图片
            if(!field.img) upload.delete();

            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("fenxiao://shop/config/agreement"),
                data: {
                    'is_agreement' : field.is_agreement,
                    'agreement_title'  : field.agreement_title,
                    'agreement_content' : ue.getContent(),
                    'img' : field.img,
                },
                success: function(res) {
                    layer.msg(res.message);
					repeat_flag = false;
                }
            });
        });

    });

</script>
