<link rel="stylesheet" type="text/css" href="REPLACEBUY_CSS/index.css"/>
<style>
	.layui-form {
		position: relative;
	}
	.layui-form-label {
		text-align: right;
		margin-left: 60px;
	}
	.layui-form-item {
		margin-bottom: 22px;
	}
	.fenxiao-select {
		    position: absolute;
		    left: 400px;
		    top: 135px;
	}
	.member-select {
		    position: absolute;
		    left: 400px;
		    top: 175px;
	}
	.member-select-add{
		border:1px solid;
		padding: 5px 10px;
	}
	.layui-unselect.layui-form-radio.layui-form-radioed i:after {
		background-color: #fff;
	}
	.layui-table-body {
		max-height: 530px;
	}
	.inline{width: 205px;}
	.layui-input{width: 205px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分销商名称：</label>
		<div class="layui-input-block">
			<input name="fenxiao_name" type="text" lay-verify="required" class="layui-input len-mid">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分销商等级：</label>
		<div class="layui-input-inline len-mid">
			<select name="level_id" lay-verify="required"> 
				<option value="">请选择</option>
				{volist name="$level_list" id="level"}
				<option value="{$level.level_id}">{$level.level_name}</option>
				{/volist}
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">上级分销商：</label>
		<!-- <div class="layui-input-inline len-mid">
			<select name="fenxiao_id" lay-filter="fenxiao_id">
				<option value="">请选择</option>
				{volist name="$fenxiao_list" id="fenxiao"}
				<option value="{$fenxiao.fenxiao_id}">{$fenxiao.fenxiao_name}</option>
				{/volist}
			</select>
		</div> -->
		<button class="layui-btn layui-btn1" onclick="addFenxiao()">选择分销商</button>
	</div>
	
	<div class="fenxiao-select"></div>
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>关联会员：</label>
		<!-- <div class="layui-input-inline len-mid">
			<select name="member_id" lay-verify="required" lay-filter="member_id">
				<option value="">请选择</option>
				{volist name="$member_list" id="member"}
				<option value="{$member.member_id}">{$member.nickname}</option>
				{/volist}
			</select>
		</div> -->
		<button class="layui-btn layui-btn2"  onclick="addMember()">选择会员</button>
	</div>
	
	<div class="member-select"></div>
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" type="reset" onclick="backFenxiaoList()">返回</button>
	</div>
</div>

<script type="text/html" id="addFenxiao">
	<div class="layui-form">
		<div class="layui-input-inline inline">
			<input type="text"  name="fenxiao_search" placeholder="请输入分销商名称" autocomplete="off" class="layui-input ">
			<button type="button" class="layui-btn layui-btn-primary fenxiao-search"  lay-filter="search" lay-submit>
				<i class="layui-icon"></i>
			</button>
		</div>
		<table id="fenxiao_list" lay-filter="fenxiao_list"></table>
	</div>
</script>

<script type="text/html" id="addMember">
	<div class="layui-form">
		<div class="layui-input-inline inline">
			<input type="text"  name="member_search" placeholder="请输入会员名称" autocomplete="off" class="layui-input ">
			<button type="button" class="layui-btn layui-btn-primary member-search"  lay-filter="search" lay-submit>
				<i class="layui-icon"></i>
			</button>
		</div>
		<table id="member_list" lay-filter="member_list"></table>
	</div>
</script>
<!-- 会员详情 -->
<script type="text/html" id="member_detail">
	<div class="member-detail">
		<div class="table-title checked-peo">
			<div class="title-pic">
				<img src="{{ns.img(d.headimg)}}" onerror=src="{:img('public/static/img/default_img/head.png')}" />
			</div>
			<div class="title-content">
				<div class="member-con-first">
					<h3 class="nickname" title="{{d.nickname}}">{{d.nickname}}</h3>
				</div>
				<div class="member-con-second">
					<p class="text-color-gray"><span class="member-mobile">{{d.mobile == "" ? '--' : d.mobile}}</span></p>
				</div>
			</div>
		</div>
		<button class="layui-btn check-btn" lay-submit lay-filter="logout_member">重新选择</button>
	</div>
</script>

<script type="text/javascript">
	var new_fenxiao_id = 0;var new_member_id = 0,repeat_flag = false,table;
	layui.use(['form', 'laytpl'], function() {
		laytpl = layui.laytpl;
		form = layui.form;

		form.render();
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			data.field.fenxiao_id  = new_fenxiao_id;
			data.field.member_id  = new_member_id;
			$.ajax({
				url: ns.url("fenxiao://shop/fenxiao/add"),
				data: data.field,
				dataType: 'JSON', //服务器返回json格式数据
				type: 'POST', //HTTP请求类型
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.msg(res.message);
						location.hash = ns.hash("fenxiao://shop/fenxiao/lists")
					} else {
						layer.msg(res.message);
					}
				}
			});
		});
	})
	
	function backFenxiaoList() {
		location.hash = ns.hash("fenxiao://shop/fenxiao/lists");
	}

	// 会员详情页面渲染
	function memberDetail(data) {
		laytpl($("#member_detail").html()).render(data, function(html) {
			$(".member").html(html);
			$("#member_id").val(data.member_id);
		})
	}

	var add_attr_radioState = 0;
	var add_attr_radioState2 = 0;
	function addFenxiao() {
		var add_attr = $("#addFenxiao").html();
		var _this = this;
		form.on('radio(layfenxiaoid)', function(obj){
			new_fenxiao_id = obj.value;
			_this.add_attr_radioState = obj.value;
			var title_fenxiao = ($(this).data('index'));
			$('.layui-btn1').parents('.layui-form-item').next('.fenxiao-select').html(title_fenxiao)
		});
		
		laytpl(add_attr).render({}, function(html) {

			add_attr_index = layer.open({
				title: '选择分销商',
				skin: 'layer-tips-class',
				type: 1,
				area: ['1000px', '800px'],
				content: html,
				btn: ["保存", "返回"],
				yes: function () {
					layer.close(add_attr_index);
				}
			});
		});
			
			//展示已知数据
			table = new Table({
			    elem: '#fenxiao_list',
				url: ns.url("fenxiao://shop/fenxiao/getfenxiaolist"),
			    cols: [
			        [{
						width: "20%",
						title: '分销商选择',
						unresize: 'false',
						templet: function(data) {
							var html = '';
							if(data.fenxiao_id == Number(add_attr_radioState)){
								html += `
								<div>
								<input type="radio" name="layTableRadioc" checked = true  value="${data.fenxiao_id}"  data-index="${data.fenxiao_name}" lay-type="layTableRadio" lay-filter="layfenxiaoid">
								</div>
							`;
							}else{
								html += `
								<div>
								<input type="radio" name="layTableRadioc" value="${data.fenxiao_id}"  data-index="${data.fenxiao_name}" lay-type="layTableRadio" lay-filter="layfenxiaoid">
								</div>
							`;
							}
							return html;
						}
					},{
		                title: '分销商名称',
						width: '50%',
		                unresize: 'false',
						 field: 'fenxiao_name',
		            }, {
		                field: 'account',
		                title: '佣金',
		                unresize: 'false',
						width: '30%',
		            },
					]
			    ],
			});
			
			/**
			 * 搜索功能
			 */
			form.on('submit(search)', function (data) {
				table.reload({
					page: {
						curr: 1
					},
					where: data.field
				});
			});
		
			$(document).keydown(function (event) {
				if (event.keyCode == 13) {
					$(".fenxiao-search").trigger("click");
				}
			});
			
		}
		
		function addMember() {
			var _this = this;
			var add_attr2 = $("#addMember").html();
			form.on('radio(laymemberid)', function(obj){
				new_member_id = obj.value;
				_this.add_attr_radioState2 = obj.value;
			});
			
			laytpl(add_attr2).render({}, function(html) {
				var add_attr_index2 = layer.open({
						title: '选择会员',
						skin: 'layer-tips-class',
						type: 1,
						area: ['1000px', '800px'],
						content: html,
						btn:["保存","返回"],
						yes: function(){
							$.ajax({
								url: ns.url("fenxiao://shop/fenxiao/memberInfo"),
								data:{member_id: new_member_id},
								dataType: 'JSON',
								type: 'POST',
								success: function(res) {
									if (res.code == 0) {
										var html = '';
										html += '<div class="member-detail">'
												+'<div class="table-title checked-peo">'
												+'<div class="title-pic">'
												+'<img src="'+ns.img(res.data.headimg)+'" onerror=this.src="{:img('public/static/img/default_img/head.png')}" />'
												+'</div>'
												+'<div class="title-content">'
												+'<div class="member-con-first">'
												+'<h3 class="nickname" title="'+res.data.nickname+'">'+res.data.nickname+'</h3>'
												+'</div>'
												+'<div class="member-con-second">';
										if(res.data.mobile){
											html += '<p class="text-color-gray"><span class="member-mobile">'+res.data.mobile+'</span></p>'
										}else{
											html += '<p class="text-color-gray"><span class="member-mobile">'+'--'+'</span></p>'
										}

												html+= '</div>'
												+'</div>'
												+'</div>'
												+'</div>';
										// memberDetail(res.data);
										$('.layui-btn2').parents('.layui-form-item').next('.member-select').html(html);
										$('.layui-btn2').parents('.layui-form-item').next('.member-select').addClass('member-select-add border-color');
										layer.close(add_attr_index2);
									} else {
										layer.msg(res.message);
									}
								}
							});
							// memberDetail(res.data);
							// layer.close(add_attr_index2);
						}
					});
				});
				
				//展示已知数据
				table = new Table({
					elem: '#member_list',
					url: ns.url("fenxiao://shop/fenxiao/getmemberlist"),
					cols: [
						[{
							width: "12%",
							title: '会员选择',
							unresize: 'false',
							templet: function(data) {
								var html = '';
								if(data.member_id == Number(add_attr_radioState2)){
									html += `
									<div>
									<input type="radio" name="layTableRadioc" checked = true  value="${data.member_id}" data-index="${data.nickname}" lay-type="layTableRadio" lay-filter="laymemberid">
									</div>
								`;
								}else{
									html += `
									<div>
									<input type="radio" name="layTableRadioc" value="${data.member_id}" data-index="${data.nickname}" lay-type="layTableRadio" lay-filter="laymemberid">
									</div>
								`;
								}
								return html;
							}
						},{
							title: '分销商名称',
							width: '50%',
							unresize: 'false',
							templet: function(data) {
								var html = '';
								html += `
									<div>
									<img style="width:40px; height:40px; margin-right:20px;" layer-src src="${ns.img(data.headimg)}" onerror="this.src = '{:img('public/static/img/default_img/head.png')}' "><span>${data.nickname}</span>
									</div>
								`;
								return html;
							}
						}, {
							field: 'balance',
							title: '余额',
							unresize: 'false',
							width: '15%',
						},{
							field: 'point',
							title: '积分',
							unresize: 'false',
							width: '15%',
						},
						]
					],
				});
				
				/**
				* 搜索功能
				*/
				form.on('submit(search)', function (data) {
					table.reload({
						page: {
							curr: 1
						},
						where: data.field
					});
				});
			
				$(document).keydown(function (event) {
					if (event.keyCode == 13) {
						$(".member-search").trigger("click");
					}
				});
			
		}
</script>