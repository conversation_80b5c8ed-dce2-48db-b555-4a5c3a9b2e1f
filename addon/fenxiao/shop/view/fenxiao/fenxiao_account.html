<style>
    .layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
    .calendar{line-height: 34px;text-align: center;}
</style>
<!-- 筛选面板 -->
<div class="single-filter-box" style="margin-top: 15px !important;">
    <div class="layui-form">
        <div class="layui-inline">
            <div class="layui-input-inline">
				<input type="text" class="layui-input" name="start_time" id="start_time_account" autocomplete="off" placeholder="开始时间" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
            <div class="layui-input-inline end-time">
                <input type="text" class="layui-input" name="end_time" id="end_time_account" placeholder="结束时间" autocomplete="off" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
			<input id="fenxiao_id" value="{$fenxiao_id}" type="hidden" />
            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="search_account">搜索</button>
        </div>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="status">
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="">全部</li>
        <li lay-id="1">收入</li>
        <li lay-id="2">支出</li>
    </ul>

    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="renewal_list" lay-filter="renewal_list"></table>
    </div>
</div>
<script>
    var fx_account_table, form, laytpl, laydate, element, fenxiao_id = $("#fenxiao_id").val(), start_time, end_time;
    layui.use(['form', 'laytpl', 'laydate'], function() {
        form = layui.form;
        laytpl = layui.laytpl;
        laydate = layui.laydate;
        element = layui.element;
        form.render();

        //监听Tab切换
        element.on('tab(status)', function(data) {
            var status = $(this).attr("lay-id");
            fx_account_table.reload( {
                page: {
                    curr: 1
                },
                where: {
                    'status': status
                }
            });
        });

        //开始时间
        laydate.render({
            elem: '#start_time_account', //指定元素
        });
        //结束时间
        laydate.render({
            elem: '#end_time_account', //指定元素
        });

        /**
         * 搜索功能
         */
		form.on('submit(search_account)', function (data) {
			if (start_time) {
				data.field.start_time = start_time;
			}
			if (end_time) {
				data.field.end_time = end_time;
			}
			
			fx_account_table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

        /**
         * 加载表格
         */
        fx_account_table = new Table({
            elem: '#renewal_list',
            url: ns.url("fenxiao://shop/fenxiao/account"),
            where: {'fenxiao_id': fenxiao_id},
            cols: [
                [{
                    field: 'account_no',
                    title: '账单编号',
                    unresize: 'false',
                    width:'25%',
                }, {
                    field: 'money',
                    title: '金额（元）',
                    unresize: 'false',
                    width:'25%',
                }, {
                    field: 'type_name',
                    title: '金额类型',
                    unresize: 'false',
                    width:'25%',
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    unresize: 'false',
                    width:'25%',
                    templet: function(data) {
                        if(data.create_time == 0){
                            return '--';
                        }else{
                            return ns.time_to_date(data.create_time)
                        }
                    },
                }]
            ]
        });

    });

</script>