<link rel="stylesheet" href="FENXIAO_CSS/order_list.css">
<style>
    .screen .layui-colla-content {border: none;background-color: #F2F3F5;}
    .screen .layui-colla-content .layui-input{background-color: #fff !important;}
</style>
<div class="screen layui-collapse" lay-filter="selection_panel" style="margin-top: 15px !important;">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">

			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">搜索方式：</label>
					<div class="layui-input-inline">
						<select name="search_text_type">
							<option value="order_no">订单编号</option>
						</select>
					</div>
					<div class="layui-input-inline">
						<input type="text" name="search_text" autocomplete="off" class="layui-input" placeholder="" />
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label">结算状态：</label>
					<div class="layui-input-inline">
						<select name="status">
							<option value="">全部</option>
							<option value="1">未结算</option>
							<option value="2">已结算</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">下单时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time_order" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time_order" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(7, this);return false;">近7天</button>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(30, this);return false;">近30天</button>
				</div>

			</div>
			<input id="fenxiao_id" name="fenxiao_id" value="{$fenxiao_id}" type="hidden" />
			<div class="form-row">
				<button class="layui-btn" lay-submit id="" lay-filter="search_order">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab">
    <div class="layui-tab-content">
        <div id="order_list"></div>
    </div>
</div>
<div id="order_page"></div>
<script src="FENXIAO_JS/order_list.js?time=20240827"></script>
<script>
    var laypage,element, form;
    // 通过hash获取页数
    function getHashPage(){
        var page = 1;
		var hash_arr = getHashArr();
        $.each(hash_arr,function(index, itemobj){
            var item_arr = itemobj.split("=");
            if(item_arr.length == 2){
                if(item_arr[0].indexOf("page") != "-1"){
                    page = item_arr[1];
                }
            }
        });
        return page;
    }

    //从hash中获取数据
    function getHashData(){
		var hash_arr = getHashArr();
        var form_json = {
        	"fenxiao_id":"",
            "end_time" : "",
            "search" : "",
            "start_time" : "",
            "page" : ""
        };
        if(hash_arr.length > 0){
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    $.each(form_json,function(key, form_val){
                        if(item_arr[0].indexOf(key) != "-1"){
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }
        form.val("order_list", form_json);
        return form_json;
    }

    layui.use(['laypage','laydate','form', 'element'], function(){
        form = layui.form;
        laypage = layui.laypage;
        element = layui.element;
        var laydate = layui.laydate;
		form.render();
		
        //渲染时间
        laydate.render({
            elem: '#start_time_order'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#end_time_order'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        //监听筛选事件
        form.on('submit(search_order)', function(data){
            data.field.page = 1;
			setHashOrderList(data.field);
            return false;
        });

        getOrderList();//筛选
    });
    
    var order = new Order();
    function getOrderList(param){
        var url = ns.url("fenxiao://shop/fenxiao/order");

        var data = {
        	fenxiao_id: '{$fenxiao_id}'
        };
        if (param != undefined) Object.assign(data, param);

        $.ajax({
            type : 'post',
            dataType: 'json',
            url :url,
            data: data,
            success : function(res){
                if(res.code == 0){
                    order.setData(res.data);
                    $("#order_list").html(order.fetch());
                    laypage.render({
                        elem: 'order_page',
                        count: res.data.count,
                        curr: getHashPage(),
                        layout: ['count', 'prev', 'page', 'next'],
                        jump: function(obj, first){
                            //首次不执行
                            if(!first){
                                var hash_data = getHashData();
                                hash_data.page = obj.curr;
								setHashOrderList(hash_data);
                            }

                        }
                    });
                }else{
                    layer.msg(res.message);
                }
            }
        });
    }

	function setHashOrderList(data){
		localStorage.setItem('formSubmit','search'); // 表单搜索标识，防止页面重新加载
		var hash = ['url=fenxiao://shop/fenxiao/detail','tab=fenxiao_order'];
		for (var key in data) {
			if (data[key] != '' && data[key] != 'all') {
				hash.push(`${key}=${data[key]}`)
			}
		}
		location.hash = hash.join('&');
		getOrderList(data);
	}

    /**
     * 七天时间
     */
    function datePick(date_num,event_obj){
        $(".date-picker-btn").removeClass("selected");
        $(event_obj).addClass('selected');
        Date.prototype.Format = function (fmt,date_num) { //author: meizz
            this.setDate(this.getDate()-date_num);
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };
        var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
        var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
        $("#start_time_order").val(before_time,0);
        $("#end_time_order").val(now_time,date_num-1);
    }
</script>