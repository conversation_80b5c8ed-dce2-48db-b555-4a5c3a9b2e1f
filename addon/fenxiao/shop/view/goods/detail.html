<style>
	.align-right {text-align: right;}
</style>

<div class="layui-form">
    <div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">商品信息</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item goods-image-wrap">
                <label class="layui-form-label">商品图片：</label>
                <div class="layui-input-block">
                    <div class="js-goods-image"><img layer-src src="{:img($goods_info.goods_image[0],'small')}" width = "50px"/></div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">商品名称：</label>
                <div class="layui-input-inline good-name">{$goods_info.goods_name}</div>
            </div>
        </div>
    </div>

    <div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">佣金设置</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">佣金规则：</label>
                <div class="layui-input-inline good-name">
                    {if $goods_info['fenxiao_type'] == 1 }默认规则{/if}
                    {if $goods_info['fenxiao_type'] == 2 }单独设置{/if}
                </div>
            </div>

            <div class="layui-form-item" id="default_rule" {if condition="$goods_info['fenxiao_type'] == 2"} style="display:none" {/if}>
                <label class="layui-form-label"></label>
				<div class="layui-input-block">
					<table class="layui-table" id="default_rule_list" lay-skin="line" lay-size="lg">
						<colgroup>
							<col width="25%">
							<col width="25%">
							{if $fenxiao_config.level >= 2}
							<col width="25%">
							{/if}
							{if $fenxiao_config.level >= 3}
							<col width="25%">
							{/if}
						</colgroup>
						<thead>
							<tr>
								<th>默认规则</th>
								<th>一级佣金比例</th>
								{if $fenxiao_config.level >= 2}
								<th>二级佣金比例</th>
								{/if}
								{if $fenxiao_config.level >= 3}
								<th>三级佣金比例</th>
								{/if}
							</tr>
						</thead>
						<tbody>
							{foreach $fenxiao_level as $level}
							<tr>
								<td>{$level.level_name}</td>
								<td>{$level.one_rate}%</td>
								{if $fenxiao_config.level >= 2}
								<td>{$level.two_rate}%</td>
								{/if}
								{if $fenxiao_config.level >= 3}
								<td>{$level.three_rate}%</td>
								{/if}
							</tr>
							{/foreach}
						</tbody>
					</table>
				</div>
			</div>
				
			<div class="layui-form-item" id="personal_rule" {if condition="$goods_info['fenxiao_type'] == 1"} style="display:none" {/if}>
			    <label class="layui-form-label"></label>
				<div class="layui-input-block">
					<table class="layui-table" id="rule_list" lay-skin="line" lay-size="lg">
						<colgroup>
							<col width="15%">
							<col width="10%">
							<col width="15%">
							<col width="20%">
							{if $fenxiao_config.level >= 2}
							<col width="20%">
							{/if}
							{if $fenxiao_config.level >= 3}
							<col width="20%">
							{/if}
						</colgroup>
						<thead>
							<tr>
								<th>商品规格</th>
								<th><p class="align-right">价格</p></th>
								<th><p class="line-hiding" title="分销商等级名称">分销商等级名称</p></th>
								<th>一级佣金比例</th>
								{if $fenxiao_config.level >= 2}
								<th>二级佣金比例</th>
								{/if}
								{if $fenxiao_config.level >= 3}
								<th>三级佣金比例</th>
								{/if}
							</tr>
						</thead>
						<tbody>
							{foreach $fenxiao_level as $level}
							<tr>
								{foreach $goods_info['sku_data'] as $sku}
								{/foreach}
								<td>
									{foreach $goods_info['sku_data'] as $sku}
									<p class="line-hiding line-height" title="{$sku.sku_name}">{$sku.sku_name}</p>
									{/foreach}
								</td>
								<td>
									{foreach $goods_info['sku_data'] as $sku}
									<p class="align-right line-height" title="￥{$sku.price}">￥{$sku.price}</p>
									{/foreach}
								</td>
								<td>{$level.level_name}</td>
								<td>
									{foreach $goods_info['sku_data'] as $sku}
									<p>
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_rate']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_rate'] > 0}
                                        {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_rate']}%
                                        {/if}
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_money']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_money'] > 0}
                                            {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['one_money']}元
                                        {/if}
									</p>
									{/foreach}
								</td>
								{if $fenxiao_config.level >= 2}
								<td>
									{foreach $goods_info['sku_data'] as $sku}
									<p>
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_rate']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_rate'] > 0}
                                        {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_rate']}%
                                        {/if}
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_money']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_money'] > 0}
                                        {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['two_money']}元
                                        {/if}
									</p>
									{/foreach}
								</td>
								{/if}
								{if $fenxiao_config.level >= 3}
								<td>
									{foreach $goods_info['sku_data'] as $sku}
									<p>
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_rate']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_rate'] > 0}
                                        {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_rate']}%
                                        {/if}
                                        {if !empty($goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_money']) && $goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_money'] > 0}
                                        {$goods_info['fenxiao_skus'][$level.level_id . '_' . $sku.sku_id]['three_money']}元
                                        {/if}
									</p>
									{/foreach}
								</td>
								{/if}
							</tr>
							{/foreach}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
	
	<div class="layui-form form-wrap">
		<div class="form-row">
			<button class="layui-btn layui-btn-primary" onclick="backFenxiaoGoodsList()">返回</button>
		</div>
	</div>
</div>

<script>
    function backFenxiaoGoodsList() {
        location.hash = ns.hash("fenxiao://shop/goods/lists");
    }
</script>