<style>.table-tab{margin-top: 0;}</style>
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="search_text" autocomplete="off" class="layui-input" placeholder="输入商品名称" />
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">商品类型：</label>
					<div class="layui-input-inline">
						<select name="goods_class" lay-filter="goods_class">
							<option value="">全部</option>
							<option value="1">实物商品</option>
							<option value="2">虚拟商品</option>
						</select>
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">是否参与：</label>
					<div class="layui-input-inline">
						<select name="is_fenxiao" lay-filter="is_fenxiao">
							<option value="">全部</option>
							<option value="1">已参与</option>
							<option value="0">未参与</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">商品分类：</label>
					<div class="layui-input-inline">
						{include file="goods/category_select" /}
					</div>
				</div>
			</div>
			
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">销量：</label>
					<div class="layui-input-inline">
						<input type="number" name="start_sale" id="start_sale" lay-verify="int" placeholder="最低销量" class="layui-input" autocomplete="off">
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="number" name="end_sale" id="end_sale" lay-verify="int" placeholder="最高销量" class="layui-input" autocomplete="off">
					</div>
				</div>
			</div>
			
			<input type="hidden" name="goods_state" />
			
			<div class="form-row">
				<button class="layui-btn" lay-submit id="" lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>
<div class="layui-tab table-tab" lay-filter="fenxiao_goods_tab">
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="fenxiao_list" lay-filter="fenxiao_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods_info">
	<div class="table-title">
		<div class="title-pic">
			<img layer-src src="{{ns.img(d.goods_image.split(',')[0], 'small')}}"/>
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="status">
	{{# if(d.status == 0){ }}
	<span style="color: red;">待审核</span>
	{{# }else if(d.status == 1){ }}
	<span style="color: green;">审核通过</span>
	{{# }else if(d.status == 2){ }}
	<span style="color: gray;">审核拒绝</span>
	{{# }else if(d.status == -1){ }}
	<span style="color: gray;">冻结</span>
	{{# } }}
</script>

<!-- 工具栏操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-goods-id="{{d.goods_id}}">
		<div class="table-btn">
			<a class="layui-btn" lay-event="detail">详情</a>
			{{# if(d.is_fenxiao == 1){ }}
			<a class="layui-btn" lay-event="config">佣金设置</a>
			{{# }else{ }}
			{{# } }}
		</div>
	</div>
</script>

<!-- 批量操作 -->
<script type="text/html" id="toolbarOperation">
	<button class="layui-btn layui-btn-primary" lay-event="join">参与</button>
	<button class="layui-btn layui-btn-primary" lay-event="cancel">不参与</button>
</script>

<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
	<button class="layui-btn layui-btn-primary" lay-event="join">参与</button>
	<button class="layui-btn layui-btn-primary" lay-event="cancel">不参与</button>
</script>

<script>
	var form, element, repeat_flag, table;
	layui.use(['form', 'laydate','element'], function() {
		form = layui.form, element = layui.element, laydate = layui.laydate;
		form.render();
		
		//渲染时间
		laydate.render({
			elem: '#start_time',
			type: 'datetime'
		});
		
		laydate.render({
			elem: '#end_time',
			type: 'datetime'
		});
		
		table = new Table({
			elem: '#fenxiao_list',
			url: ns.url("fenxiao://shop/goods/lists"),
			toolbar: '#toolbarOperation',
			bottomToolbar: "#batchOperation",
			cols: [
				[{
					type: 'checkbox',
					unresize: 'false',
					width: '3%'
				},
				{
					title: '商品名称',
					unresize: 'false',
					templet: '#goods_info',
					width: '21%'
				}, {
					field: 'price',
					title: '价格',
					unresize: 'false',
					align: 'right',
					width: '13%',
					templet: function (data) {
						return '￥' + data.price;
					}
				}, {
					unresize: 'false',
					width: '4%'
				}, {
					field: 'goods_stock',
					title: '库存',
					unresize: 'false',
					width: '8%'
				}, {
					field: 'sale_num',
					title: '销量',
					unresize: 'false',
					width: '8%'
				}, {
					title: '销售状态',
					unresize: 'false',
					width: '10%',
					templet: function (data) {
						var str = '';
						if (data.goods_state == 1) {
							str = '<span style="color: green">销售中</span>';
						} else if (data.goods_state == 0) {
							str = '<span style="color: red">仓库中</span>';
						}
						return str;
					}
				}, {
					title: '是否参与',
					unresize: 'false',
					width: '10%',
					templet: function (data) {
					    if(data.is_fenxiao == 1){
                            return '<input type="checkbox" name="is_fenxiao" data-goods-id="'+ data.goods_id + '" value="1" lay-filter="is_fenxiao" lay-skin="switch" checked>';
                        }else{
                            return '<input type="checkbox" name="is_fenxiao" data-goods-id="'+ data.goods_id + '" value="0" lay-filter="is_fenxiao" lay-skin="switch">';
						}
					}
				}, {
					title: '<div class="prompt-block">' + '规则' +
								'<div class="prompt">' +
									'<i class="iconfont iconwenhao1 required growth"></i>' +
									'<div class="growth-box reason-box reason-growth prompt-box" >' +
										'<div class="prompt-con">' +
											'<ul style="font-weight: 100">' +
												'<li>1、分销商层级与后台配置有关，最多三级分销。</li>' +
												'<li>2、分销商等级与分销商的分销订单数，分销订单总额，自购订单数，自购订单总额，分销商下线人数，分销商的下级分销商人数有关。</li>' +
												'<li>3、商品分销总佣金不得超过商品实际价格的50%。</li>' +
												'<li>4、分销佣金是根据当前分销订单所属分销商等级或者商品自定义的计算规则进行结算。</li>' +
												'<li>5、分销结算说明： A 、B 、C是分销商，C的上级为B，B的上级为A。' +
												'分佣按照所属分销商的等级佣金比率进行分配，分销商C的等级分佣比率为一级10%，二级5%，三级2%，' +
												'订单属于分销商C，则C获得商品实付金额10%，B获得商品实付金额5%，A获得商品实付金额2%。' +
												'若C推荐的普通用户D购买商品，则该订单属于C；若C购买商品，则该订单属于C。</li>' +
											'</ul>' +
										'</div>' +
									'</div>' +
								'</div>' +
							'</div>',
					unresize: 'false',
					width: '10%',
					templet: function (data) {
						var str = '';
						if (data.fenxiao_type == 1) {
							str = '默认规则';
						} else if (data.fenxiao_type == 2) {
							str = '单独设置';
						}
						return str;
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});

		//按钮监控
        form.on('switch(is_fenxiao)', function(data){
            var goods_id = $(data.elem).attr('data-goods-id');
            modifyFenxiao(goods_id, data.value, data.elem);
        });
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data,
				event = obj.event;
			switch (event) {
				case 'detail': //查看
					location.hash = ns.hash('fenxiao://shop/goods/detail', {'goods_id': data.goods_id});
					break;
				case 'config':
					//编辑
					location.hash = ns.hash("fenxiao://shop/goods/config", {"goods_id": data.goods_id});
					break;
				case 'modify':
					//删除
					modifyFenxiao(data.goods_id, data.is_fenxiao);
					break;
			}
		});

		// 批量操作
		table.bottomToolbar(function (obj) {
			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			var id_array = new Array();
			for (i in obj.data) id_array.push(obj.data[i].goods_id);
			switch (obj.event) {
				case "join":
					joinFenxiao(id_array);
					break;
				case 'cancel':
					//下架
					cancelFenxiao(id_array);
					break;
			}
		});
		
		// 批量操作
		table.toolbar(function (obj) {
			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			var id_array = new Array();
			for (i in obj.data) id_array.push(obj.data[i].goods_id);
			switch (obj.event) {
				case "join":
					joinFenxiao(id_array);
					break;
				case 'cancel':
					//下架
					cancelFenxiao(id_array);
					break;
			}
		});
		
	});
	
	//配置参与状态
	function modifyFenxiao(goods_id, is_fenxiao, data) {
	    if(is_fenxiao == 0){
	        var fenxiao_tip = "如果商品尚未设置佣金规则，首次开启默认按照分销等级发放佣金，是否继续开启?";
		}else{
            var fenxiao_tip = "是否设置商品不参与分销?";
		}
		layer.confirm(fenxiao_tip,{
            btn: ['确定', '取消'],
            cancel: function(index, layero){
                if(is_fenxiao == 1){
                    $(data).prop("checked",true);
                }else{
                    $(data).prop("checked",false);
                }
				form.render();
                layer.close(index);
                return false;
            }
        }, function () {
			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				url: ns.url("fenxiao://shop/goods/modify"),
				data: {goods_id: goods_id, is_fenxiao:is_fenxiao},
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						table.reload();
					}
				}
			});
		},function () {
            if(is_fenxiao == 1){
                $(data).prop("checked",true);
                form.render();
			}else{
                $(data).prop("checked",false);
                form.render();
			}
        });
	}

	function joinFenxiao(goods_ids){
		layer.confirm('批量参与分销的商品，如果之前未配置佣金规则，则默认按照分销等级发放佣金，是否继续？', {title: '提示'}, function (index) {
			if (repeat_flag) return;
			repeat_flag = true;
			layer.close(index);

			$.ajax({
				url: ns.url("fenxiao://shop/goods/setGoodsIsFenxiao"),
				data: {goods_ids: goods_ids.toString(), is_fenxiao: 1},
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						table.reload();
					}
				}
			});
		});
	}

	function cancelFenxiao(goods_ids) {
		layer.confirm('是否设置商品不参与分销?', {title: '提示'}, function (index) {
			if (repeat_flag) return;
			repeat_flag = true;
			layer.close(index);

			$.ajax({
				url: ns.url("fenxiao://shop/goods/setGoodsIsFenxiao"),
				data: {goods_ids: goods_ids.toString()},
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						table.reload();
					}
				}
			});
		});
	}
</script>
