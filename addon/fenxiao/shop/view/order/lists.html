<link rel="stylesheet" href="FENXIAO_CSS/order_list.css">
<style>
    .screen{margin-top: 0;}
</style>
<!-- 筛选面板 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">搜索方式：</label>
					<div class="layui-input-inline">
						<select name="search_text_type" >
							<option value="order_no">订单编号</option>
							<option value="sku_name">商品名称</option>
						</select>
					</div>
					
					<div class="layui-input-inline">
						<input type="text" name="search_text" placeholder="商品名称/订单编号" autocomplete="off" class="layui-input"/>
					</div>
				</div>
				
				<div class="layui-inline">
					<label class="layui-form-label">结算状态：</label>
					<div class="layui-input-inline">
						<select name="status" lay-filter="is_settlement">
							<option value="">全部</option>
							<option value="1">待结算</option>
							<option value="2">已结算</option>
						</select>
					</div>
				</div>
			</div>
				
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">下单时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>
			
			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="search">筛选</button>
				<button class="layui-btn layui-btn-primary" lay-submit lay-filter="batch_export_order" >导出订单</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab">
    <div class="layui-tab-content">
        <!-- 列表 -->
        <div id="order_list"></div>
    </div>
</div>

<div id="order_page"></div>

<script src="FENXIAO_JS/order_list.js"></script>
<script>
    var laypage,element, form;
	var orderDataAll = [];

    // 通过hash获取页数
    function getHashPage(){
        var page = 1;
        var hash_arr = getHashArr();
        $.each(hash_arr,function(index, itemobj){
            var item_arr = itemobj.split("=");
            if(item_arr.length == 2){
                if(item_arr[0].indexOf("page") != "-1"){
                    page = item_arr[1];
                }
            }
        });
        return page;
    }

    //从hash中获取数据
    function getHashData(){
		var hash_arr = getHashArr();
        var form_json = {
            "end_time" : "",
            "order_label" : $("select[name=order_label]").val(),
			"status" : "",
            "search" : "",
            "start_time" : "",
            "page" : ""
        };
        if(hash_arr.length > 0){
            $.each(hash_arr,function(index, itemobj){
                var item_arr = itemobj.split("=");
                if(item_arr.length == 2){
                    $.each(form_json,function(key, form_val){
                        if(item_arr[0].indexOf(key) != "-1"){
                            form_json[key] = item_arr[1];
                        }
                    })
                }
            })
        }
        form.val("order_list", form_json);
        return form_json;
    }

    layui.use(['laypage','laydate','form', 'element'], function(){
        form = layui.form;
        laypage = layui.laypage;
        element = layui.element;
        var laydate = layui.laydate;
		form.render();
		
        //渲染时间
        laydate.render({
            elem: '#start_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });
        laydate.render({
            elem: '#end_time'
            ,type: 'datetime'
            ,change: function(value, date, endDate){
                $(".date-picker-btn").removeClass("selected");
            }
        });

        //监听筛选事件
        form.on('submit(search)', function(data){
            data.field.page = 1;
			setHashOrderList(data.field);
            //getOrderList(data.field);
            return false;
        });

		form.on('submit(batch_export_order)', function(data){
			if(orderDataAll){
				var orderDataString = orderDataAll.toString();
				location.href = ns.url("fenxiao://shop/order/exportorder?request_mode=download&order_ids=" + orderDataString);
			}else{
				location.href = ns.url("fenxiao://shop/order/exportorder?request_mode=download",data.field);
			}
			return false;
		});

        getHashData();
        getOrderList();//筛选
    });

    //哈希值 订单数据
    function setHashOrderList(data){
		localStorage.setItem('formSubmit','search'); // 表单搜索标识，防止页面重新加载
		var hash = ['url=fenxiao://shop/order/lists'];
		for (var key in data) {
			if (data[key] != '' && data[key] != 'all') {
				hash.push(`${key}=${data[key]}`)
			}
		}
		location.hash = hash.join('&');
        getOrderList();
    }

    var order = new Order();
    function getOrderList(data){
        var url = ns.url("fenxiao://shop/order/lists",getHashArr().join('&'));
        $.ajax({
            type : 'post',
            dataType: 'json',
            url :url,
            data: data,
            success : function(res){
                if(res.code == 0){
                    order.setData(res.data);
                    $("#order_list").html(order.fetch());
					form.render();

					//批量选择
					form.on('checkbox(allCheckbox)', function(data){
						$(".sub-selected-checkbox input").prop("checked",data.elem.checked);
						$(".all-selected-checkbox input").prop("checked",data.elem.checked);
						form.render("checkbox");
						getOrderId();
					});

					//全选选择
					form.on('checkbox(subCheckbox)', function(data){
						var subLen = $(".sub-selected-checkbox input:checked").length;
						$(".all-selected-checkbox input").prop("checked",false);
						if (subLen == 10){
							$(".all-selected-checkbox input").prop("checked",true);
						}
						form.render("checkbox");
						getOrderId();
					});

					//获取选中的id
					function getOrderId(){
						var	lists = $(".sub-selected-checkbox input:checked");
						orderDataAll = [];
						lists.each(function(index,item){
							orderDataAll.push(JSON.parse($(item).parents(".sub-selected-checkbox").attr("data-json")));
						});
					}

                    laypage.render({
                        elem: 'order_page',
                        count: res.data.count,
                        curr: getHashPage(),
                        layout: ['count', 'prev', 'page', 'next'],
                        jump: function(obj, first){
                            //首次不执行
                            if(!first){
                                var hash_data = getHashData();
                                hash_data.page = obj.curr;
                                setHashOrderList(hash_data);
                            }
                        }
                    });
                }else{
                    layer.msg(res.message);
                }
            }
        });
    }
</script>
