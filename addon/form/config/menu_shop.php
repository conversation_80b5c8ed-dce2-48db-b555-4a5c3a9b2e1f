<?php
// +----------------------------------------------------------------------
// | 平台端菜单设置
// +----------------------------------------------------------------------
return [
    [
        'name' => 'SYSTEM_FORM',
        'title' => '系统表单',
        'url' => 'form://shop/form/lists',
        'parent' => 'PROMOTION_TOOL',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => '',
        'picture_select' => '',
        'sort' => 1,
        'child_list' => [
            [
                'name' => 'FORM_ADD',
                'title' => '添加表单',
                'url' => 'form://shop/form/addform',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
            [
                'name' => 'FORM_EDIT',
                'title' => '编辑表单',
                'url' => 'form://shop/form/editform',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
            [
                'name' => 'FORM_DELETE',
                'title' => '删除表单',
                'url' => 'form://shop/form/deleteform',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
            [
                'name' => 'FORM_IS_USE',
                'title' => '表单是否启用',
                'url' => 'form://shop/form/editisuse',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
            [
                'name' => 'FORM_DATA',
                'title' => '表单数据',
                'url' => 'form://shop/form/formdata',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
            [
                'name' => 'FORM_DATA_EXPORT',
                'title' => '表单数据详情',
                'url' => 'form://shop/form/exportform',
                'is_show' => 0,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => '',
                'picture_selected' => '',
                'type' => 'button',
            ],
        ]
    ],
];
