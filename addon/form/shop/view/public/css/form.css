.layui-layout-admin .layui-body .body-content {
	padding: 0;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.order-wrap .head-img {
	width: 100%;
	height: auto;
}

.draggable-element {
	position: relative;
	cursor: move;
}

.draggable-element:hover .del {
	display: block;
}

.form-gorup {
	padding: 15px;
	background: #fff;
	border-bottom: 1px solid #eee;
	display: flex;
	align-items: center;
}

.form-gorup .label {
	font-size: 14px;
	color: #666;
	width: 80px;
	min-width: 80px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 10px;
	cursor: move;
}

.form-gorup .input {
	flex: 1;
	border: none;
	font-size: 14px;
	text-align: left;
	cursor: move;
}

.form-gorup .textarea {
	flex: 1;
	border: none;
	font-size: 14px;
	text-align: left;
	cursor: move;
}

.form-gorup .input::placeholder,
.form-gorup .textarea::placeholder {
	color: #ddd;
	font-size: 14px;
	text-align: left;
}

.form-gorup>i {
	color: #999;
}

.form-gorup .more {
	flex: 1;
	font-size: 14px;
	text-align: right;
	color: #999
}

.edit-attribute .options-wrap .option-item {
	position: relative;
	margin-bottom: 15px;
	width: 100%;
}

.edit-attribute .option-item input {
	padding-right: 34px;
}

.edit-attribute .option-item .icontrash {
	position: absolute;
	right: 0;
	top: 0;
	width: 34px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	cursor: pointer;
}

/* 多行文本 */
.form-gorup .checkbox-list .textarea::placeholder {
	text-align: left;
}

.form-gorup .checkbox-list>.textarea {
	margin-top: 10px;
	width: 100%;
	height: 40px;
	text-align: left;
}

.add-item-btn-box {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.add-item-btn-box .add-item-btn {
	width: 48%;
	height: 34px;
	line-height: 34px;
	text-align: center;
	border: 1px dashed #999;
	border-radius: 2px;
	box-sizing: border-box;
	font-size: 12px;
	cursor: pointer;
}

.add-items-box {
	position: absolute;
	top: 40px;
	left: 0;
	z-index: 9;
	width: 100%;
	padding: 15px;
	box-sizing: border-box;
	background-color: #fff;
	box-shadow: 0 0 5px rgba(0, 0, 0, .3);
}

.add-items-title {
	font-size: 12px;
	line-height: 20px;
}

.add-items-desc {
	font-size: 12px;
	line-height: 20px;
	color: #909399;
}

.add-items-btn {
	text-align: right;
}

/* 多选框 */
.form-gorup-block {
	display: block;
}

.form-gorup-block label {
	display: inline-block;
	width: 100%;
	max-width: 100%;
	margin-right: 0;
}

.checkbox-list {
	width: 100%;
	display: flex;
}

.checkbox-item {
	display: flex;
	align-items: center;
	padding-right: 10px;
	box-sizing: border-box;
}

.checkbox-item>i {
	color: #999;
}

.checkbox-item .checkbox {
	width: 14px;
	height: 14px;
	line-height: 14px;
	text-align: center;
	border-radius: 2px;
	margin-right: 10px;
	border: 1px solid #E6E6E6;
}

.checkbox-item .checkbox i {
	font-size: 15px;
	color: #fff;
}

.checkbox-item .checkbox-title {
	line-height: 20px;
}

/* 单选框 */
.checkbox-item .checkbox.radio {
	border-radius: 50%;
}

/* 图片 */
.img-box {
	display: inline-block;
	width: 50px;
	height: 50px;
	border: 1px solid #E6E6E6;
	box-sizing: border-box;
	margin-right: 10px;
	margin-top: 10px;
	line-height: 48px;
	text-align: center;
	border-radius: 1px;
	vertical-align: top;
	position: relative;
}

.img-box img {
	max-width: 100%;
	max-height: 100%;
}

.img-box .close {
	background-color: #999;
	position: absolute;
	top: -7px;
	right: -7px;
	width: 14px;
	height: 14px;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.img-box .close i {
	color: #fff;
	font-size: 14px;
}

.img-box i.iconadd_light {
	color: #999;
	font-size: 20px;
}

/* 日期 */
.date-input {
	margin-top: 10px;
}

.form-gorup .checkbox-list .input::placeholder {
	text-align: left;
}

/* 日期范围 */
.form-gorup .interval {
	margin-right: 10px;
}

.date-limit-box input {
	border: 0;
	text-align: left !important;
}
.date-limit-box.date input {
	width: 75px;
}
.date-limit-box.time input {
	width: 40px;
}

.date-limit-form .layui-form-label {
	width: 100%;
	text-align: left;
}

.date-limit-form .layui-form-label + .layui-input-block {
	margin-left: 0;
}

/* 时间 */
.laydate-time-list>li:nth-child(3) {
	display: none;
}

.layui-laydate-list.laydate-time-list>li {
	width: 50%;
}

.laydate-time-list ol li {
	padding-left: 55px !important;
}

.layui-form-item {
	margin-bottom: 15px;
}

.edit-attribute .attr-wrap .restore-wrap .attr-title {
	margin-bottom: 20px;
}

.layui-checkbox-disbaled, .layui-checkbox-disbaled i {
	border-color: #d2d2d2 !important;
}

.layui-checkbox-disbaled[lay-skin=primary] span {
	color: #666;
}

[form-type="order"]{
	margin-top: 10px;
}
[form-type="order"] .draggable-element {
	padding: 0 12px;
}