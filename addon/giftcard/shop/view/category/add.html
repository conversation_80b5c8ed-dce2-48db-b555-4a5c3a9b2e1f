<style>
    .colorSelector{
        position: relative;
        width: 20px;
        height: 20px;
        border-radius: 3px;
        border: 1px solid #d7d7d7;
        display: inline-block;
        cursor: pointer;
        vertical-align: middle;
        padding: 2px;
    }
    .colorSelector div{
        width: 100%;
        height: 100%;
        border-radius: 3px;
    }
    .flexbox-fix-btn .btn{
        margin-top: 0;
        line-height: 1;
    }
</style>

<div class="layui-form">
    <div class="layui-card card-common card-brief">
        <div class="layui-card-body">
            <div class="layui-form ns-form">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>分组名称：</label>
                    <div class="layui-input-block">
                        <input name="category_name" type="text" lay-verify="required" class="layui-input len-mid">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>排序：</label>
                    <div class="layui-input-block">
                        <input name="sort" type="number" lay-verify="num" class="layui-input len-mid">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>分组颜色：</label>
                    <div class="layui-input-block flex">
                        <div class="flex_fill">
                            <div class="picker colorSelector" id="color-picker">
                                <div data-value="#333"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-row">
            <button class="layui-btn" lay-submit lay-filter="save">保存</button>
            <button class="layui-btn layui-btn-primary" onclick="backGiftCardCategoryList()">返回</button>
        </div>
    </div>
</div>

<script type="text/javascript" src="__STATIC__/ext/colorPicker/js/colorpicker.js"></script>
<script>
    var form,repeat_flag = false;//防重复标识
    layui.use(['form'], function() {
        form = layui.form;
        form.render();

        Colorpicker.create({
            el: "color-picker",
            color: "#333",
            change: function (elem, hex) {
                $('#color-picker').attr('data-value', hex);
                $('#color-picker div').css('background', hex);
            }
        });
        /**
         * 表单验证
         */
        form.verify({
            num: function(value) {
                if(!new RegExp("^-?[1-9]\\d*$").test(value)){
                    return "排序号只能是大于0的整数";
                }
                if(value < 0){
                    return "排序号必须大于0";
                }
            },
        });

        /**
         * 监听保存
         */
        form.on('submit(save)', function(data) {
            data.field.font_color = $('#color-picker').attr('data-value');
            
            if(repeat_flag) return false;
            repeat_flag = true;
            
            $.ajax({
                url: ns.url("giftcard://shop/category/add"),
                data: data.field,
                dataType: 'JSON', //服务器返回json格式数据
                type: 'POST', //http请求类型
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('添加成功', {
                            title:'操作提示',
                            btn: ['返回列表', '继续添加'],
                            closeBtn: 0,
                            yes: function(index, layero) {
                                location.hash = ns.hash("giftcard://shop/category/lists")
								layer.close(index);
                            },
                            btn2: function(index, layero) {
                                listenerHash(); // 刷新页面
								layer.close(index);
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            });
        });
    });

    // 返回
    function backGiftCardCategoryList() {
        location.hash = ns.hash("giftcard://shop/category/lists");
    }
</script>
