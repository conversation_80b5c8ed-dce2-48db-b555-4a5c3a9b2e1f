<style>
    .colorSelector{
        position: relative;
        width: 20px;
        height: 20px;
        border-radius: 3px;
        border: 1px solid #d7d7d7;
        display: inline-block;
        cursor: pointer;
        vertical-align: middle;
        padding: 2px;
    }
    .colorSelector div{
        width: 100%;
        height: 100%;
        border-radius: 3px;
    }
    .flexbox-fix-btn .btn{
        margin-top: 0;
        line-height: 1;
    }
</style>

<div class="layui-form">
    <div class="layui-card card-common card-brief">
        <div class="layui-card-body">
            <div class="layui-form ns-form">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>分组名称：</label>
                    <div class="layui-input-block">
                        <input name="category_name" type="text" lay-verify="required" class="layui-input len-mid" value="{$detail.category_name}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>排序：</label>
                    <div class="layui-input-block">
                        <input name="sort" type="number" lay-verify="num" class="layui-input len-mid" value="{$detail.sort}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span class="required">*</span>分组颜色：</label>
                    <div class="layui-input-block flex">
                        <div class="flex_fill">
                            <div class="picker colorSelector" id="color-picker">
                                <div data-value="{$detail.font_color}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="category_id" value="{$detail.category_id}">
        <div class="form-row">
            <button class="layui-btn" lay-submit lay-filter="save">保存</button>
            <button class="layui-btn layui-btn-primary" onclick="backGiftCardCategoryList()">返回</button>
        </div>
    </div>
</div>

<!--素材图片列表-->
<script type="text/html" id="mediaImage">
    {{# if(d.list.length){ }}
    {{# for(var i=0;i<d.list.length;i++){ }}
    <div class="item upload_img_square_item" data-index="{{i}}">
        <div class="img-wrap">
            <img src="{{ns.img(d.list[i])}}" layer-src="{{ns.img(d.list[i])}}">
        </div>
        <div class="operation">
            <i title="图片预览" class="iconfont iconreview js-preview"></i>
            <i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
            <div class="replace_img" data-index="{{i}}">点击替换</div>
        </div>
    </div>
    {{# } }}
    {{# if(d.list.length < d.max){ }}
    <div class="item js-add-media-image upload_img_square">+</div>
    {{# } }}
    {{# }else{ }}
    <div class="item js-add-media-image upload_img_square">+</div>
    {{# } }}
</script>
<script type="text/javascript" src="__STATIC__/ext/colorPicker/js/colorpicker.js"></script>
<script>
    var form,repeat_flag = false; //防重复标识
    layui.use(['form'], function() {
        form = layui.form;
        form.render();

        Colorpicker.create({
            el: "color-picker",
            color: "#333",
            change: function (elem, hex) {
                $('#color-picker').attr('data-value', hex);
                $('#color-picker div').css('background', hex);
            }
        });
        /**
         * 表单验证
         */
        form.verify({
            num: function(value) {
                if(!new RegExp("^-?[1-9]\\d*$").test(value)){
                    return "排序号只能是大于0的整数";
                }
                if(value < 0){
                    return "排序号必须大于0";
                }
            },
        });

        /**
         * 监听保存
         */
        form.on('submit(save)', function(data) {
            data.field.font_color = $('#color-picker').attr('data-value');
            
            if(repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: ns.url("giftcard://shop/category/edit"),
                data: data.field,
                dataType: 'JSON', //服务器返回json格式数据
                type: 'POST', //http请求类型
                success: function(res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('编辑成功', {
                            title:'操作提示',
                            btn: ['返回列表'],
                            closeBtn: 0,
                            yes: function(index, layero) {
                                location.hash = ns.hash("giftcard://shop/category/lists")
								layer.close(index);
                            }
                        });
                    }else{
                        layer.msg(res.message);
                    }
                }
            });
        });

    });

    function backGiftCardCategoryList() {
        location.hash = ns.hash("giftcard://shop/category/lists");
    }
</script>
