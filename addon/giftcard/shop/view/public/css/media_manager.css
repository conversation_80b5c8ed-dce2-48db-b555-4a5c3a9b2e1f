#media {
    padding: 10px 20px;
}

.media-box {
    display: flex;
}
.media-list-box::-webkit-scrollbar {
    display: none; /* Chrome Safari */
}

.media-list-box {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow: auto;
    margin-right: 25px;
    width: 180px;
    height: 385px;
    position: relative;
}
.media-list-box ul .layui-nav-bar{
    display: none;
}

.media-list-box button {
    position: absolute;
    left: 40px;
    bottom: 0;
}

.media-list {
    width: 180px;
    background-color: #F7F7F7;
}

.media-list li a{
    color: #333!important;
}
.media-list li a .num{position: absolute;right: 15px}
.media-list li a .layui-nav-more{border-color: #333 transparent transparent}
.media-list li.layui-nav-itemed a .layui-nav-more{border-color: transparent transparent #333!important;}
.media-list li a:hover{background-color: #F7F7F7!important;}
.media-list li a.layui-this{background-color: #E8E8E8!important;color: var(--base-color)!important;}
.media-list li dl.layui-nav-child{background-color: #E8E8E8!important;}
.media-list li dl.layui-nav-child a{padding-left: 20px}
.media-list li.active {
    color: var(--base-color)!important;
}

.media-content {
    flex: 1;
}

.media-img {
    display: flex;
    flex-wrap: wrap;
}

.media-img li {
    width: 125px;
    height: 155px;
    margin: 0 15px 15px 0;
}

.media-img li div {
    position: relative;
    width: 100%;
    height: 125px;
    line-height: 125px;
    text-align: center;
}

.media-img li div .image-box-active {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    border: 2px solid;
}

.media-img li div .image-box-active:after {
    content: '';
    display: block;
    position: absolute;
    top: 0px;
    right: 0;
    border: 15px solid;
    border-color: transparent;
    border-top-color: var(--base-color);
    border-right-color: var(--base-color);
}

.media-img li div .image-box-active i {
    position: absolute;
    top: 2px;
    right: 2px;
    color: #fff;
    z-index: 2;
    font-style: normal;
    line-height: 1;
}

.media-img li img {
    max-height: 100%;
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.media-img li video {
    max-height: 100%;
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.media-img li span {
    display: block;
    margin-top: 10px;
    height: 20px;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.empty-data {
    margin-top: 10px;
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: center;
}

/* 本地图片上传*/
.multuple-list {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    height: 300px;
    padding-top: 10px;
    box-sizing: border-box;
}

.multuple-list-img {
    border-color: #DDDDDD!important;
}

.multuple-list li {
    position: relative;
    display: flex;
    margin-bottom: 10px;
    margin-right: 10px;
    flex-direction: column;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    width: 120px;
    height: 120px;
    border: 1px solid;
}

.multuple-list li img {
    max-width: 100%;
    max-height: 100%;
}
.multuple-list li video {
    max-width: 100%;
    max-height: 100%;
}

.upload-close-modal {
    display: none;
    position: absolute;
    top: -10px;
    right: -10px;
    height: 20px;
    width: 20px;
    font-size: 18px;
    line-height: 20px;
    text-align: center;
    color: #fff;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
    z-index: 1;
}

.upload-image-curtain {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
    line-height: 122px;
    font-size: 18px;
    color: #fff;
    background-color: rgba(0, 0, 0, .5);
    z-index: 1;
}

.multuple-list li:hover .upload-close-modal {
    display: block;
}

.multuple-list li span {
    cursor: pointer;
    width: 100%;
    text-align: center;
}

.multuple-list li span:first-of-type {
    width: 30px;
    height: 30px;
    font-size: 18px;
    margin-bottom: 10px;
    line-height: 30px;
    border-radius: 50%;
    color: #fff;
}
.media-list .layui-tree-iconArrow:after{top: 5px}
.media-list .layui-tree-spread .layui-tree-iconArrow:after{transform: rotate(90deg);top: 8px;left: 0;}
.media-list .layui-tree .layui-tree-entry {padding: 0;line-height: 1;height: auto;}
.media-list .layui-tree .layui-tree-main {width: 100%;box-sizing: border-box;display: flex;align-items: center;}
.media-list .layui-tree .layui-tree-main .layui-tree-txt{width: 100%;display: flex;justify-content: space-between;padding: 12px 10px 12px 0;}