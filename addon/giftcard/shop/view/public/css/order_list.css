.layui-table {margin-top: 15px;}
.layui-table thead tr {background-color: #F8F6F9;}
.layui-table th {border-width: 0; font-size: 14px!important;}
.layui-table td {padding: 8px 15px;}
.layui-table .header-row {border-top: 1px solid #e6e6e6;background:  #f7f7f7;}
.layui-table .header-row:hover{background: #f7f7f7 !important;}
.separation-row hr {margin: 0;}

/* .layui-layout-admin .layui-body{min-width:1100px} */
/*.date-picker-btn.selected{background-color:#0d73f9;color:#fff}*/
.layui-table td,.layui-table th,.layui-table-fixed-r,.layui-table-header,.layui-table-page,.layui-table-tips-main,.layui-table-tool,.layui-table-view,.layui-table[lay-skin=line],.layui-table[lay-skin=row]{border-color:#f1f1f1 !important;}
.layui-table thead th{border-bottom:none;padding-left:0;padding-right:0;}
.order-list-table .separation-row td{padding:10px 20px;border: 0;}
.order-list-table .separation-row:hover{background-color: #fff;}
.order-list-table .remark-row{background: #FFF9DF!important;color: #D09B4C;}
.order-list-table .remark-row:hover{background: #FFF9DF!important}
.order-list-table .header-row td{padding: 8px 15px!important;}
.order-list-table .header-row td:nth-child(1){border-right: 0;}
.order-list-table .header-row td:nth-child(2){text-align:center;border-left: 0;}
.order-list-table .header-row span{vertical-align:middle}
.order-list-table .header-row td .order-item-header{text-align:left;color:#333;}
.order-list-table .header-row td .order-item-header.more{cursor: pointer;overflow: hidden;position: relative;}
.order-list-table .header-row td  .more-operation{display: none;font-size: 14px;line-height: 20px;background-color: #fff;border-radius: 2px;box-shadow: 0 2px 8px 0 rgba(200,201,204,.5);position: absolute;z-index: 2000;padding: 10px;top: 28px;transform: translateX(10px);left: -12px;width: 200px;}
.order-list-table .header-row td  .more-operation:before{left: 8px;top: -14px;border: solid transparent;content: "";height: 0;width: 0;position: absolute;pointer-events: none;border-color: transparent;border-bottom-color: #fff;border-width: 8px;}
.order-list-table .header-row td  .more-operation span{color:#333;}

.order-list-table .checkbox-all .layui-table-cell{padding:0;text-align:center;}
.order-list-table .product-info .layui-table-cell{padding:0 5px}
.order-list-table .content-row:hover{background-color:#fff !important;}
.order-list-table .content-row .product-info{border-right: 0;}
.order-list-table .content-row .product-info .img-block{width:60px;height:60px;float:left;border:1px solid #e2e2e2;display: flex;align-items: center;justify-content: center;}
.order-list-table .content-row .product-info .img-block>img{max-width:100%;max-height:100%;}
.order-list-table .content-row .product-info .info{margin-left:70px}
.order-list-table .content-row .product-info .info p{color:#8e8c8c;font-size:12px}
.order-list-table .content-row .order-price{border-left: 0;}
.order-list-table .content-row .product-list{border-right-width:1px}
.order-list-table .content-row .product-list p{font-size:12px}
.order-list-table .content-row .transaction-status{line-height: 24px;}
.order-list-table .content-row .buyers{line-height: 24px;}
.order-list-table .operation a{font-size: 14px;}
.order-list-table .text-tile{color:rgb(164,164,164);}
.order-no-data-block ul{width:200px;margin:20px auto; padding-bottom: 20px;}
.order-no-data-block ul li{text-align:center;color:#c2c2c2}
.order-no-data-block ul li:first-child{height:70px;line-height:70px}
.order-no-data-block ul li:first-child i{font-size:35px}
.footer-row{border:1px solid #f2f2f2}
.screen{margin-top: 0;}

/*.order-money span {color: red;}*/
#order_page {text-align: right;}
.bottom-row{color:#e0a723;background: #fffbec;}
.bottom-row:hover{background: #fffbec!important;}

.table-tab .layui-tab-content {padding-top: 0;}
.line-hiding{ cursor : default; -webkit-line-clamp: 2 !important;}
.address_box{width:87%; display: inline-block}
.address_input{width: 1px;height: 0px;overflow: hidden;border: 0px}
.screen .layui-colla-title .layui-colla-icon{color:var(--base-color) !important}
.screen .layui-colla-title .text-color{position: absolute;right: 45px;font-size: 14px;padding: 5px;}
.operation .operation-type{display: block;}
.operation .operation-type .layui-btn{display: inline;text-align: left;line-height: 23px;padding: 0px;margin: 0px;padding-left:8px;}
.layui-colla-title .put-open{position: absolute;right: 40px;padding: 5px;color: var(--base-color) ;}
.content-row .order-price {
    border-left: 0;
}
.content-row .card-info {
    border-right: 0;
}
/*订单顶部的一行*/
.order-list-top-line{justify-content:flex-end;}
.order-list-top-line a{padding-right: 0px !important;}

.order-list-table .order-goods{align-items: flex-start !important;}
.order-list-table .order-goods .info{margin-top: 3px;}
.order-list-table .order-goods , .order-list-table .order-goods-item{display: flex;align-items: center;}
.order-list-table .order-goods .img-block{margin-right: 10px;width: 60px;height: 60px;flex-shrink: 0;border: 1px solid #e2e2e2;display: flex;align-items: center;justify-content: center;}
.order-list-table .order-goods .img-block{width: 105px;height: 63px;}
.order-list-table .order-goods .img-block img{max-width: 100%;max-height: 100%;}
.order-list-table .order-goods-item{margin-top: 10px;}