
<style>
	.layui-form-switch{margin-top: 0;}
	.layui-card-body{display: flex;justify-content: space-between;align-items: center;}
	.layui-card-body p{color: #666666;}
	.card-common:first-child{margin-top: 0;}
	.card-common{border: 1px solid #f1f1f1;}
	.wechat-circle {width: 100%;}
	.wechat-wrap {width: 100%;}
	.wechat-circle-wrap {width: 1000px; margin: 50px auto; display: flex; justify-content: center; align-items: center;}
	.wechat-circle-wrap>div:first-child {margin-right: 50px;}
	.wechat-circle-intro {width: 360px;}
	.wechat-circle-intro h1 {font-size: 26px; font-weight: 600; color: #333333; line-height: 40px;}
	.wechat-circle-intro p {font-size: 16px; color: #858585; line-height: 28px; margin-top: 20px;}
	.wechat-circle-img {width: 360px;}
    .wechat-circle-img img {width: 100%;}
    .card-common .layui-card-header{height: 42px;line-height: 42px;padding: 0 15px;}
    .card-common .layui-card-body{padding: 10px 15px;}
</style>

<div class="layui-form">
	<div class="layui-card card-common">
		<div class="layui-card-header">
			<span class="card-title">微信圈子</span>
			<input type="checkbox" name="isUse" lay-skin="switch" {if $config.is_use == 1} checked {/if}  lay-filter="isUse"/>
		</div>
		<div class="layui-card-body">
			<p>启用后，将同步商品到微信圈子。</p>
			<!-- <p>好物圈是微信提供的服务信息管理和物品推荐工具。用户可以在这里浏览朋友推荐的物品，与朋友交流心得，还可以为朋友进行推荐。</p> -->
		</div>
	</div>
</div>

<div class="wechat-circle">
	<div class="wechat-wrap">
		<!-- <div class="wechat-circle-wrap">
			<div class="wechat-circle-intro">
				<h1>扫一扫识物</h1>
				<h1>直接曝光好物圈商品</h1>
				<p>商家开启好物圈后，用户同步至好物圈的商品，可以通过“微信扫一扫”-识物功能直接曝光展示。</p>
			</div>
			<div class="wechat-circle-img">
				<img src="CIRCLE_IMG/1.png" />
			</div>
		</div> -->
		
		<div class="wechat-circle-wrap">
			<div class="wechat-circle-intro">
				<h1>好物分享</h1>
				<p>商家开启好物圈之后，用户可以将商品分享到好物圈。</p>
			</div>
			<div class="wechat-circle-img">
				<img src="CIRCLE_IMG/1.png" />
			</div>
		</div>
		
		<div class="wechat-circle-wrap">
			<div class="wechat-circle-img">
				<img src="CIRCLE_IMG/2.png" />
			</div>
			<div class="wechat-circle-intro">
				<h1>搜索商品</h1>
				<p>开启好物圈，可搜索到商家发布在好物圈的商品，点击进入详情即可通过商品链接进入小程序</p>
			</div>
		</div>
	</div>
</div>

<script>
	layui.use(['form'], function() {
		var form = layui.form;

		form.on('switch(isUse)', function(data){
		  	$.ajax({
				type: 'POST',
				url: ns.url("goodscircle://shop/config/index"),
				data: {
					is_use: $('[name="isUse"]').is(':checked') ? 1 : 0
				},
				dataType: 'JSON',
				success: function (res) {
					layer.msg(res.message);
				}
			});
		});
	});
</script>
