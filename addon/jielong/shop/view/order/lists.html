<style>
	#order_page{height: 60px;}
	.topList{margin-top:16px;width:100%;background:#f1f1f1;height:46px;border-radius:2px;display: flex;align-items: center;}
	.topList-content{box-sizing: border-box; text-align: left;line-height: 46px;height: 46px;padding:0 10px;color:#666;font-weight: bold;}
	.topList-content:nth-child(1){text-align: left;}
	.layui-laypage-default{float:right}
	.shop{width:30%;}
	.moneyNum{width:8%;text-align: right !important;}
	.realMoney{width:10%;text-align: center !important;}
	.information{width:22%}
	.state{width:15%}
	.operation{width:15%}
	.bottomList{width:100%;}
	.bottomList-content{margin:16px 0;border:1px solid #f1f1f1;border-radius:4px;min-height:120px;}
	.bottomList-top{display: flex;align-items: center; width: 100%;background:#f1f1f1;height:40px;line-height: 40px;padding:0 15px;box-sizing: border-box;}
	.bottomList-top-text{margin-right:30px;text-align: left;color: #333;font-size:14px;}
	.bottomList-content-bottom{width:100%;min-height:80px;display: flex;align-items: center;}
	.bottomList-content-bottom:after{overflow: hidden;content: "";height: 0;display: block;clear: both;}
	.bottomList-content-bottom-shop{width:30%;min-height:80px;}
	.ListContentShop{display: flex;align-items: center;height: 80px; width: 100%;}
	.ListContentShop>img{width: 60px;height: 60px;}
	.ListContentShopText{float: left;margin-left:10px;width:76%;font-size:14px;color:#666;	display: -webkit-box;-webkit-line-clamp:2;overflow: hidden;text-overflow: ellipsis;-webkit-box-orient: vertical;}
	.borerBottom{border-bottom:1px solid #f1f1f1;padding:10px;box-sizing: border-box;}
	.borerBottom:last-child{border-bottom:none;}
	.bottomList-content-bottom-moneyNum{width:8%;border-right: 1px solid #f1f1f1;}
	.ListContentMoneyNum{width:100%;height:81px;display: flex;flex-direction: column;justify-content: space-between;}
	.ListContentMoneyNum>p{text-align: right;font-size:14px;color:#666;}
	.bottomList-content-bottom-consignee{box-sizing: border-box; border-right:1px solid #f1f1f1; width: 22%;height:80px;padding-left:10px;display: flex;flex-direction: column;justify-content:center;}
	.consignee-center{min-height:80px;display: flex;flex-direction: column;justify-content: space-around;}
	.consignee-center>p{color:#666;font-size:14px;}
	.bottomList-content-bottom-state{padding-left:10px;border-right: 1px solid #f1f1f1;width:15%;display: flex;flex-direction: column;justify-content: center;box-sizing: border-box;}
	.bottomList-content-bottom-state>p{color:#666;font-size:14px;}
	.bottomList-content-bottom-operation{width:15%;padding-left:10px;box-sizing: border-box;display: flex;align-items: center;justify-content: flex-end;}
	.look{font-size:14px;cursor:pointer;margin-right:10px;}
	.bottomList-content-bottom-realMoney{width: 10%;box-sizing: border-box;border-right: 1px solid #f1f1f1;color:#666;display: flex;align-items: center;justify-content: center;}
	.image-content{width: 100%;height:250px;display: flex;align-items: center;justify-content: center;}
	.image-content>div>li{font-size:35px;color: #c2c2c2;text-align: center;}
	.image-content>div>p{font-size:14px;text-align: center; color: #c2c2c2;}
	.display-imageNone{display: none !important;}
	.address{height: 0;width: 1px; border: none; overflow: hidden;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .screen{margin-top: 15px;}
</style>

<div class="layui-collapse tips-wrap">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>社群接龙展示商品相关信息</li>
		</ul>
	</div>
</div>

<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">搜索方式：</label>
					<div class="layui-input-inline">
						<select name="order_label" >
							{foreach $order_label_list as $k => $label_val}
							<option value="{$k}">{$label_val}</option>
							{/foreach}
						</select>
					</div>
					<div class="layui-input-inline">
						<input type="text" name="search" autocomplete="off" class="layui-input" />
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">订单来源：</label>
					<div class="layui-input-inline">
						<select name="order_from">
							<option value="">全部</option>
							{foreach $order_from_list as $order_from_k => $order_from_v}
							<option value="{$order_from_k}">{$order_from_v['name']}</option>
							{/foreach}
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">付款方式：</label>
					<div class="layui-input-inline">
						<select name="pay_type" >
							<option value="">全部</option>
							{foreach pay_type_list as $pay_type_k => $pay_type_v}
							<option value="{$pay_type_k}">{$pay_type_v}</option>
							{/foreach}
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">下单时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(7, this);return false;">近7天</button>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(30, this);return false;">近30天</button>
				</div>
			</div>
			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary" onclick="interface()">重置</button>
			</div>
		</form>
	</div>
</div>

{include file="order/order_common_action" /}

<div class="layui-tab table-tab" lay-filter="jielong_tab">

	<ul class="layui-tab-title">
		<li class="layui-this" data-status="" name="">全部</li>
<!-- 		{foreach $order_status_list as $status_val}
		<li data-status="{$status_val.status}" name="{$status_val.status}">{$status_val.name}</li>
		{/foreach} -->
		<li name="0">待支付</li>
		<li name="1">待发货</li>
		<li name="3">已发货</li>
		<li name="4">已收货</li>
		<li name="10">已完成</li>
		<li name="-1">已关闭</li>
		<li name="20">已退款</li>
	</ul>
	
	<div class="topList">
		<div class="topList-content shop">商品</div>
		<div class="topList-content moneyNum">单价/数量</div>
		<div class="topList-content realMoney">实付金额</div>
		<div class="topList-content information">收货人信息</div>
		<div class="topList-content state">交易状态</div>
		<div class="topList-content operation" style="text-align: right;">操作</div>
	</div>
	<div id="bottomList" class="bottomList"></div>

	<div id="order_page"></div>
	
	<div id="image-contents" class="image-content">
		<div>
			<li class="layui-icon layui-icon-tabs"></li>
			<p>暂无数据</p>
		</div>
	</div>
</div>
<script type="text/html" id="goodsList">
	{{# for(var index in d){ }}
		<div class="bottomList-content">
			<div class="bottomList-top">
				<p class="bottomList-top-text">
					<span>订单号:</span>
					<span class="bottomListTopNumber">{{d[index].order_no}}</span>
				</p>
				{{# if(d[index].pay_time){ }}
				<p class="bottomList-top-text">
					<span>下单时间:</span>
					<span class="bottomListTopTime">{{ns.time_to_date(d[index].pay_time)}}</span>
				</p>
				{{# } }}
			</div>
			<div class="bottomList-content-bottom">
				<div class="bottomList-content-bottom-shop">
					{{# for(var i in d[index].order_goods_list){ }}
					<div class="ListContentShop borerBottom">
						<img class="ListContentShopImage" src="{{ns.img(d[index].order_goods_list[i].sku_image)}}">
						<p class="ListContentShopText">{{d[index].order_goods_list[i].sku_name}}</p>
					</div>
					{{# }}}
				</div>
				<div class="bottomList-content-bottom-moneyNum">
					{{# for(var i in d[index].order_goods_list){ }}
					<div class="ListContentMoneyNum borerBottom">
						<p class="ListContentMoneyMoney">{{d[index].order_goods_list[i].price}}元</p>
						<p class="ListContentNum">{{d[index].order_goods_list[i].num}}件</p>
					</div>
					{{# }}}
				</div>
				<div class="bottomList-content-bottom-realMoney">{{d[index].order_money}}元</div>
				<div class="height bottomList-content-bottom-consignee">
					<div class="consignee-center">
						<p class="listBottomConsignee-name">{{d[index].name}}</p>
						<p class="listBottomConsignee-ipone">{{d[index].mobile}}</p>
						<p class="listBottomConsignee-dizhi">{{d[index].full_address}}<input type="text" class="address" id="address" value="{{d[index].full_address}}"><a href="javascript:ns.copy('address');" class="iconfont iconfuzhi" style="margin-top: 4px"></a></p>
					</div>
				</div>
				<div class="bottomList-content-bottom-state">
					<p class="listBottomState">{{d[index].order_status_name}}</p>
				</div>
				<div class="bottomList-content-bottom-operation">
					<a class="look text-color" onclick="see({{d[index].relate_order_id}})" style="text-align: right;">查看订单详情</a>
					{{# if (d[index].order_status == -1){ }}
					<a class="look text-color" href="javascript:orderAction('deleteOrder', '{{d[index].id}}')">删除订单</a>
					{{#  }  }}
				</div>
			</div>
		</div>
	{{# } }}
</script>

<script>
	var end_time,order_from,order_label,pay_type,search,start_time,switchs,page,page_size;
	layui.use(['form', 'element','laydate','laytpl','laypage'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		laydate = layui.laydate;
		element = layui.element;
		repeat_flag = false; //防重复标识
		laypage = layui.laypage;
		form.render();

		//渲染时间
		laydate.render({
			elem: '#start_time',
			type: 'datetime'
		});

		laydate.render({
			elem: '#end_time',
			type: 'datetime'
		});

		/**
		 * 搜索功能
		 */

		form.on('submit(search)', function (data) {
			end_time = data.field.end_time,
				order_from = data.field.order_from,
				// order_label=data.field.order_label,
				pay_type = data.field.pay_type,
				search = data.field.search,
				start_time = data.field.start_time;

			orderList();
			return false
		});

		element.on('tab(jielong_tab)', function (data) {
			switchs = $(this).attr('name');
			orderList();
		})

	});
	
	function orderList(data){
		var url = ns.url("jielong://shop/order/lists");
		$.ajax({
		    type : 'get',
		    dataType: 'json',
		    url :url,
			data:{
				end_time:end_time,
				order_from:order_from,
				order_label:order_label,
				pay_type:pay_type,
				search:search,
				start_time:start_time,
				order_status:switchs,
				page:page,
				page_size:page_size,
				jielong_id:{$jielong_id}
			},
		    success : function(res){
				let list = res.data.list;
				if(list==''){
					$("#order_page").css('display','none');
					$("#image-contents").removeClass("display-imageNone");
				}else{
					$("#order_page").css('display','block');
					$("#image-contents").addClass("display-imageNone");
				}
				
				laytpl($('#goodsList').html()).render(list,function(html){
					$('#bottomList').html(html)
				});

				if(!data){
					new Page({
						elem: 'order_page',
						count: res.data.count,
						curr: 1,
						limit:10,
						callback: function(obj, first) {
							let data={
								pages:obj.curr,
								page_sizes:obj.limit
							};
							page=obj.curr,
							page_size=obj.limit;
							if(!first){
								orderList(data);
							}
						}
					})
				}

                list.forEach((item,index)=>{
					let height=$(".bottomList-content-bottom").eq(index).height();
					$(".bottomList-content-bottom-consignee").eq(index).css('height',height);
					$(".bottomList-content-bottom-state").eq(index).css('height',height);
					$(".bottomList-content-bottom-realMoney").eq(index).css('height',height);
				})
			},
		});
	}
	orderList();

	// 查看
	function see(id){
		window.open(ns.href("/shop/order/detail", {"order_id": id}));
	}
	/**
	 * 七天时间
	 */
	function datePick(date_num,event_obj){
		$(".date-picker-btn").removeClass("selected");
		$(event_obj).addClass('selected');
		// alert(new Date().format("yyyy-MM-dd hh:mm"));
		var now_date = new Date();
	
		Date.prototype.Format = function (fmt,date_num) { //author: meizz
			this.setDate(this.getDate()-date_num);
			var o = {
				"M+": this.getMonth() + 1, //月份
				"d+": this.getDate(), //日
				"H+": this.getHours(), //小时
				"m+": this.getMinutes(), //分
				"s+": this.getSeconds(), //秒
				"q+": Math.floor((this.getMonth() + 3) / 3), //季度
				"S": this.getMilliseconds() //毫秒
			};
			if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
			for (var k in o)
				if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
			return fmt;
		};
		// var now_time =  new Date().Format("yyyy-MM-dd HH:mm:ss",0);//当前日期
		var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
		var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
		$("input[name=start_time]").val(before_time,0);
		$("input[name=end_time]").val(now_time,date_num-1);
	}
</script>