<style>
.progress-layer {width:400px;background:#fff;position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);box-shadow:1px 1px 50px rgba(0,0,0,.3);padding:20px 20px;z-index:100;display:none;}
.progress-layer h3 {line-height:1;margin-bottom:15px;text-align:center;font-size:14px;}
.progress-layer .layui-progress-big,.progress-layer .layui-progress-big .layui-progress-bar {height:14px;line-height:14px;}
.progress-layer .layui-progress-text {line-height:14px;}
.room-info {padding: 5px 0;align-items: center;flex-wrap:unset!important;}
.room-info .room-img{width:50px;height: 50px;background: #f5f5f5;} 
.room-info img {width:50px;height: 50px;}
.room-info .room-name {padding-left: 5px;line-height: 1;flex: 1}
.single-filter-box{justify-content: end}
.push-layer{text-align: center;padding: 30px 0;}
.push-layer .qrcode{width:100px;height:100px;margin:10px auto;display:block}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="sync()">同步直播间</button>
	<a href="{:href_url('live://shop/room/add')}" class="layui-btn layui-btn-primary">添加直播间</a>
</div>

<table id="room_list" lay-filter="room_list"></table>

<!-- 直播间信息 -->
<script type="text/html" id="roominfo">
	<div class="table-btn room-info">
		<div class="room-img">
			<img src="{{ ns.img(d.share_img) }}">
		</div>
		<div class="room-name" title="{{ d.name }}">{{ d.name }}</div>
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="operate">运营</a>
		{{#  if( d.live_status == '102' ){ }}
		<a class="layui-btn" lay-event="push">推流入口</a>
		{{#  } }}
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<div class="progress-layer">
	<h3>正在同步中...</h3>
	<div class="layui-progress layui-progress-big" lay-showPercent="true" lay-filter="progress">
		<div class="layui-progress-bar layui-bg-blue" lay-percent="0%"></div>
	</div>
</div>

<script type="text/html" id="push">
	<div class="push-layer">
		<img src="LIVE_IMG/weapp_push.png" class="qrcode">
		<p>主播可通过微信扫该小程序码或搜一搜“小程序直播”进入主播小程序进行推流</p>
	</div>
</script>

<!-- 批量操作 -->
<script type="text/html" id="batchOperation">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
</script>

<script>
	var form,table,element,syncClick = false,repeat_flag = false;
	layui.use(['form', 'element'], function() {
		form = layui.form;
		element = layui.element;
		
		table = new Table({
			elem: '#room_list',
			url: ns.url("live://shop/room/index"),
			bottomToolbar: "#batchOperation",
			cols: [
				[{
					type: 'checkbox',
					unresize: 'false',
					width: '3%'
				},{
					title: '直播间信息',
					unresize: 'false',
					width: '17%',
					templet: "#roominfo"
				}, {
					field: 'roomid',
					title: '直播间ID',
					unresize: 'false',
					width: '10%'
				}, {
					field: 'anchor_name',
					title: '主播昵称',
					unresize: 'false',
					width: '10%'
				}, {
					field: 'start_time',
					title: '开播时间',
					unresize: 'false',
					width: '15%',
					templet: function(data) {
						return ns.time_to_date(data.start_time);
					}
				}, {
					field: 'end_time',
					title: '结束时间',
					unresize: 'false',
					width: '15%',
					templet: function(data) {
						return ns.time_to_date(data.end_time);
					}
				}, {
					title: '商品数量',
					unresize: 'false',
					width: '10%',
					templet: function(data) {
						return data.goods.length;
					}
				}, {
					field: 'status_name',
					title: '状态',
					unresize: 'false',
					width: '10%',
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});

		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'operate': // 运营
					location.hash = ns.hash("live://shop/room/operate", {"id": data.id});
					break;
				case 'push':
					layer.open({
					  	type: 1,
					  	title: false,
					  	skin: 'weapp-preview',
					  	closeBtn: 1, //不显示关闭按钮
					  	anim: 0,
					  	shadeClose: false,
					  	content: $('#push').html()
					});
					break;
				case 'delete': 
					deleteRoom(data.id);
					break;
			}
		})

		// 批量操作
		table.bottomToolbar(function (obj) {
			if (obj.data.length < 1) {
				layer.msg('请选择要操作的数据');
				return;
			}
			var id_array = new Array();
			for (i in obj.data) id_array.push(obj.data[i].id);
			switch (obj.event) {
				case "delete":
					deleteRoom(id_array.toString());
					break;
			}
		});
	});

	// 同步直播间
	function sync(start){
		if (syncClick) return;
		syncClick = true;
		start = start == undefined ? 0 : start;

		$.ajax({
			url: ns.url("live://shop/room/sync"),
			data: {
				start: 0,
			},
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				syncClick = false;
				if (res.code == 0) {
					var data = res.data,
						next = parseInt(start) + 1;

						if (next < data.total_page) {
							if (start == 0) {
								$(".progress-layer").fadeOut();
							}
							var progress = (next / data.total_page * 100).toFixed(2);
							element.progress('progress', progress + '%');
							// 拉取下一页
							sync(next);
						} else {
							if (!$(".progress-layer").is(':hidden')) $(".progress-layer").fadeOut();
							layer.closeAll();
							layer.msg('同步成功');
							table.reload();
						}
				} else {
					layer.msg(res.message);
				}
			}
		});
	}

	function
	deleteRoom(room_ids){
		layer.confirm('是否确定要删除所选直播间？', {title: '提示'}, function (index) {
			if (repeat_flag) return;
			repeat_flag = true;
			layer.close(index);

			$.ajax({
				url: ns.url("live://shop/room/delete"),
				data: {room_ids: room_ids},
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;
					if (res.code == 0) {
						table.reload();
					}
				}
			});
		});
	}
</script>
