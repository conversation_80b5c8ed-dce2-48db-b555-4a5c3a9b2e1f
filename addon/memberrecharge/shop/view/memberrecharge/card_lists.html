<style>
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="layui-collapse tips-wrap">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>领卡客户</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="layui-tab table-tab" lay-filter="use_status">
	<!-- 搜索框 -->
	<div class="single-filter-box">
		<button class="layui-btn" onclick="exportRecharge()">导出记录</button>
	</div>
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		<li data-status="1">未使用</li>
		<li data-status="2">已使用</li>
	</ul>
	<div class="layui-tab-content">
	    <!-- 列表 -->
	    <table id="charge_list" lay-filter="charge_list"></table>
	</div>
</div>

<!-- 套餐信息 -->
<script type="text/html" id="cover_img">
	<div class="table-tuwen-box">
		<div class='font-box'>
			<p class="multi-line-hiding">面值：{{d.face_value}}</p>
			<p class="multi-line-hiding">价格：{{d.buy_price}}</p>
		</div>
	</div>
</script>

<!-- 礼包 -->
<script type="text/html" id="libao">
	<div class="table-tuwen-box">
		<div class='font-box'>
			<p class="multi-line-hiding">积分：{{d.point}}</p>
			<p class="multi-line-hiding">成长值：{{d.growth}}</p>
		</div>
	</div>
</script>

<!-- 用户信息 -->
<script type="text/html" id="user_info">
	<div class='table-title'>
		<div class='title-pic'>
			<img layer-src src="{{ns.img(d.member_img.split(',')[0])}}" onerror="this.src = '{:img('public/static/img/default_img/head.png')}' "/>
		</div>
		<div class='title-content'>
			<p class="layui-elip">{{d.nickname}}</p>
		</div>
	</div>
</script>

<!-- 状态 -->
<script type="text/html" id="use_status">
	{{d.use_status == 1 ? '未使用' : '已使用'}}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="card_detail">详情</a>
	</div>
</script>

<script>
	layui.use(['form', 'element'], function() {
		var table,
			form = layui.form,
			element = layui.element;
		form.render();

		table = new Table({
			elem: '#charge_list',
			url: ns.url("memberrecharge://shop/memberrecharge/cardlists"),
			where:{
                recharge_id:{$recharge_id}
			},
			cols: [
				[{
			    	field:'card_account',
					title: '充值卡号',
					unresize: 'false',
					width: '10%',
					templet: '#member_img'
				}, {
                    field: 'cover_img',
                    title: '套餐信息',
                    unresize: 'false',
                    width: '15%',
                    templet: '#cover_img'
                }, {
                    title: '赠送礼包',
                    unresize: 'false',
                    width: '10%',
                    templet: '#libao'
                }, {
                    field: 'pay_type_name',
                    title: '用户信息',
                    unresize: 'false',
                    width: '15%',
                    templet: '#user_info'
                }, {
					field: 'order_no',
					title: '订单号',
					unresize: 'false',
					width: '15%'
				}, {
					title: '购买时间',
					unresize: 'false',
					width: '15%',
					templet: function(data) {
						return ns.time_to_date(data.create_time);
					}
				}, {
					title: '使用状态',
					unresize: 'false',
					width: '8%',
					templet: '#use_status'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			],

		});

		//监听Tab切换
		element.on('tab(use_status)', function(data) {
			var status = $(this).attr("data-status");
			table.reload( {
				page: {
					curr: 1
				},
				where: {
					'use_status': status
				}
			});
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'card_detail': //编辑
					location.hash = ns.hash("memberrecharge://shop/memberrecharge/carddetail?card_id=" + data.card_id);
					break;
			}
		});

	});

	function exportRecharge(){
		var recharge_id = {$recharge_id};
		location.href = ns.url("memberrecharge://shop/memberrecharge/exportRecharge?request_mode=download&recharge_id=" + recharge_id);
	}
</script>
