<style>
.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加活动</button>
</div>

<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">活动名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="recommend_name" placeholder="请输入活动名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">活动时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab" lay-filter="recommend_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" lay-id="">所有活动</li>
		<li lay-id="1">进行中</li>
		<li lay-id="2">已结束</li>
		<li lay-id="-1">已关闭</li>
		<li lay-id="0">未开始</li>
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="recommend_list" lay-filter="recommend_list"></table>
	</div>
</div>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="receive">领取记录</a>
		<a class="layui-btn" lay-event="detail">详情</a>
		{{#  if(d.status == 0) {  }}
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
		{{#  }else if(d.status == 1) {  }}
		<a class="layui-btn" lay-event="close">关闭</a>
		{{#  }else if(d.status == 2 || d.status == -1) {  }}
		<a class="layui-btn" lay-event="delete">删除</a>
		{{#  }  }}
	</div>
</script>
<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>
<!-- 状态 -->
<script type="text/html" id="status">
	{foreach $recommend_status_arr as $recommend_status_k => $recommend_status_v}
		{{#  if(d.status == {$recommend_status_k}){  }}
			{$recommend_status_v}
		{{#  }  }}
	{/foreach}
</script>

<script>
	layui.use(['form','element','laydate'], function() {
		var table,
			form = layui.form,
            element = layui.element,
            laydate = layui.laydate,
			repeat_flag = false; //防重复标识
		form.render();

        element.on('tab(recommend_tab)', function(){
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'status':this.getAttribute('lay-id')
                }
            });
        });
	
		table = new Table({
			elem: '#recommend_list',
			url: ns.url("memberrecommend://shop/memberrecommend/lists"),
			cols: [
				[{
					field: 'recommend_name',
					title: '活动名称',
					unresize: 'false',
					width: '25%'
				}, {
					title: '活动时间',
					unresize: 'false',
					width: '21%',
                    templet: '#time'
				}, {
					field: 'status',
					title: '状态',
					unresize: 'false',
					width: '13%',
					templet: '#status'
				}, {
					field: 'count',
					title: '邀请人数',
					unresize: 'false',
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.hash = ns.hash("memberrecommend://shop/memberrecommend/edit", {"recommend_id": data.recommend_id});
					break;
				case 'detail': //详情
                    location.hash = ns.hash("memberrecommend://shop/memberrecommend/detail", {"recommend_id": data.recommend_id});
					break;
				case 'delete': //删除
					deleteRecommend(data.recommend_id);
					break;
				case 'close': //关闭
					close(data.recommend_id);
					break;
                case 'receive': //关闭
                    location.hash = ns.hash("memberrecommend://shop/memberrecommend/receive", {"recommend_id": data.recommend_id});
                    break;
			}
		});

        //开始时间
        laydate.render({
            elem: '#start_time', //指定元素
            type: 'datetime'
        });
        //结束时间
        laydate.render({
            elem: '#end_time', //指定元素
            type: 'datetime'
        });
		
		/**
		 * 删除
		 */
		function deleteRecommend(recommend_id) {
			if (repeat_flag) return false;
			repeat_flag = true;
		
			layer.confirm('确定要删除该活动吗?', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("memberrecommend://shop/memberrecommend/delete"),
					data: {
                        recommend_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
		
						if (res.code == 0) {
							table.reload({
							    page: {
							        curr: 1
							    },
							});
						}
					}
				});
			}, function () {
				layer.close();
				repeat_flag = false;
			});
		}
		
		/**
		 * 关闭
		 */
		function close(recommend_id) {
			if (repeat_flag) return false;
			repeat_flag = true;
			
			layer.confirm('确定关闭该活动吗?', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("memberrecommend://shop/memberrecommend/close"),
					data: {
                        recommend_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
					
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});
	
	function add() {
		location.hash = ns.hash("memberrecommend://shop/memberrecommend/add");
	}
</script>
