<style>
	.select-coupon-layer .layui-layer-content{
		overflow-y: scroll!important;
	}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" lay-filter="is_use" value="1" lay-skin="switch" {if !empty($config) && $config.is_use==1 }checked{/if}>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">注册送积分：</label>
		<div class="layui-input-block">
			<input type="number" name="point" lay-verify="required|number|int" value="{if empty( $config.value) }0{else/}{$config.value.point}{/if}" autocomplete="off" class="layui-input len-short">
		</div>
		<div class="word-aux">积分必须为整数,0表示不赠送</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">注册送红包：</label>
		<div class="layui-input-block">
			<input type="number" name="balance" lay-verify="required|number|flnum" value="{if empty( $config.value) }0{else/}{$config.value.balance}{/if}" autocomplete="off" class="layui-input len-short">
		</div>
		<div class="word-aux">会员注册即可获得红包，0表示不赠送，红包不能小于0，可保留两位小数</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">注册送成长值：</label>
		<div class="layui-input-block">
			<input type="number" name="growth" lay-verify="required|number|flnum" value="{if empty( $config.value) }0{else/}{$config.value.growth}{/if}" autocomplete="off" class="layui-input len-short">
		</div>
		<div class="word-aux">会员注册即可获得成长值，0表示不赠送，成长值不能小于0，可保留两位小数</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">送优惠券：</label>
		<div class="layui-input-block">
			<div class="coupon-item coupon">
				<div>
					<input type="checkbox" name="discount_type" value="coupon" class="input-checkbox" lay-skin="primary" {if $config.value.coupon }checked{/if}><label>送优惠券</label>
				</div>
				<div class="discount-cont {if $config.value.coupon == ''}layui-hide{/if}">
					<div><a href="javascript:;" class="text-color" id="select_coupon">选择优惠券</a></div>
					<div id="coupon_list"></div>
				</div>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="backPromotionMarket()">返回</button>
	</div>
</div>
<script type="text/javascript" src="STATIC_JS/coupon_select_new.js?time=20250320"></script>
<script type="text/javascript">
	var laytpl;

	var coupon_select = new CouponSelect({
		tableElem:'#coupon_list',
		selectElem:'#select_coupon',
		selectedIds:'{$config.value.coupon}',
		couponNew:'{:html_entity_decode($config.value.coupon_new_list)}'
	})

	layui.use(['form','laytpl'], function(){
		// 监听返积分是否启用
		var form = layui.form,
				repeat_flag = false; //防重复标识
		laytpl = layui.laytpl;
		form.render();

		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;

			var couponData =   coupon_select.getSelectedData().selectedIds;
			data.field.coupon = couponData.toString();
			if(data.field.discount_type == undefined){
				data.field.coupon = '';
			}

			var coupon=[];
			for (let i=0; i< couponData.length; i++){
				console.log(couponData[i])
				var send_num = $(".send_num_"+couponData[i]).val()
				coupon.push({"id":couponData[i],'num':send_num})
			}
			data.field.coupon_new = coupon

			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("memberregister://shop/config/index"),
				data: data.field,
				success: function(res) {
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero){
								location.hash = ns.hash("shop/promotion/market")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			})
		});
		
		form.verify({
			number: function (value) {
				if (value < 0) {
					return '请输入不小于0的数!'
				}
			},
			int: function (value) {
				if (value%1 != 0) {
					return '积分必须为整数!'
				}
			},
			flnum: function (value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '保留小数点后两位!'
				}
			}
		});

        form.on('submit(coupon-search)', function(data) {
            couponTable.reload({
                page: {
                    curr: 1
                },
				where: data.field
			})
		})
	});

	// 选择优惠
	$('body').off('click', '.coupon-item .layui-form-checkbox').on('click', '.coupon-item .layui-form-checkbox', function(e){
		if ($(this).prev('[name="discount_type"]').is(':checked')) {
			$(this).parents('.coupon-item').find('.discount-cont').removeClass('layui-hide');
		} else {
			$(this).parents('.coupon-item').find('.discount-cont').addClass('layui-hide');
		}
	})

	function backPromotionMarket() {
		location.hash = ns.hash("shop/promotion/market")
	}
</script>
