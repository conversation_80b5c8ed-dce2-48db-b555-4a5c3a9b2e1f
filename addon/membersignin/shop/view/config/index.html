<style>
	.width-wrap {width: 115px;}
	.layui-block {overflow: hidden; margin-bottom: 20px;}
	.layui-table+button {margin-top: 15px;}
	.empty-wrap {text-align: center;}
	.layui-table[lay-size=lg] td, .layui-table[lay-size=lg] th {
		padding: 15px;
	}
</style>

<div class="layui-form form-wrap signin">
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" lay-filter="is_use" value="1" lay-skin="switch" {if !empty($config) && $config.is_use==1 }checked{/if}>
		</div>
	</div>
	
	<div class="reward-wrap {if !empty($config) && $config.is_use!=1 }layui-hide{/if}">
		<div class="layui-form-item">
			<label class="layui-form-label"><span class="required">*</span>签到周期：</label>
			<div class="layui-input-block">
				<div class="layui-input-inline">
					<input type="number" name="cycle" lay-verify="required|number|int|cycle" value="{$config['value']['cycle'] ?: 30}" autocomplete="off" class="layui-input len-short"><input type="hidden" value="签到周期" />
				</div>
				<div class="layui-form-mid">天</div>
			</div>
			<div class="word-aux">
				<p>签到周期需在7-100天内</p>
			</div>
		</div>

		<div class="layui-form-item">
			<div class="layui-block">
				<label class="layui-form-label"><span class="required">*</span>每日签到：</label>
				<label class="layui-form-mid width-wrap">每日签到赠送奖励</label>
				<div class="layui-input-inline len-short">
					<input type="number" name="point" lay-verify="everyday|number|int" value="{$config['value']['reward'][0]['point'] ?: ''}" autocomplete="off" class="layui-input ">
				</div>
				<span class="layui-form-mid">积分</span>
			</div>
			
			<div class="layui-block">
				<label class="layui-form-label"></label>
				<label class="layui-form-mid width-wrap"></label>
				<div class="layui-input-inline len-short">
					<input type="number" name="growth" lay-verify="everyday|number|flnum" value="{$config['value']['reward'][0]['growth'] ?: ''}" autocomplete="off" class="layui-input ">
				</div>
				<span class="layui-form-mid">成长值</span>
			</div>
		</div>
		<div class="word-aux">用户可在签到页每日签到一次，签到后可获得每日签到奖励</div>

		<div class="layui-form-item">
			<label class="layui-form-label">连续签到：</label>
			
			<div class="layui-input-block">
				<table class="layui-table" id="sign" lay-skin="line" lay-size="lg">
					<colgroup>
						<col width="40%">
						<col width="20%">
						<col width="20%">
						<col width="20%">
					</colgroup>
					<thead>
						<tr>
							<th>连续签到天数</th>
							<th>奖励积分</th>
							<th>奖励成长值</th>
							<th class="operation" style="text-align: right;">操作</th>
						</tr>
					</thead>
					<tbody>
						{if count($config.value.reward) > 1}
							{foreach $config.value.reward as $k =>$v}
							{if $k>0}
							<tr>
								<td><input type="number" class="layui-input len-short day" value="{$v.day}" lay-verify="required|int|day" autocomplete="off" /><input type="hidden" value="连续签到天数" /></td>
								<td><input type="number" class="layui-input len-short point" value="{$v.point}" lay-verify="signDay|number|int" autocomplete="off" /></td>
								<td><input type="number" class="layui-input len-short growth" value="{$v.growth}" lay-verify="signDay|number|int" autocomplete="off" /></td>
								<td><div class="table-btn"><a href="javascript:;" class="layui-btn" onclick="deleteSign(this)">删除</a></div></td>
							</tr>
							{/if}
							{/foreach}
						{else/}
						<tr class="empty-box">
							<td colspan="5">
								<div class="empty-wrap">暂无数据</div>
							</td>
						</tr>
						{/if}
					</tbody>
				</table>
				<button class="layui-btn" onclick="addSign()">添加连续签到奖励</button>
			</div>
		</div>
		<div class="word-aux">连续签到天数达到连签奖励的当天，可额外获得连签奖励；连续签到天数签满一个周期或者签到中断，将清空连签天数重新计算签到天数</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

<script>
	layui.use('form', function() {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {
			var arr = [];
			arr.push({point: data.field.point, growth:data.field.growth, day: 1});
			
			$(".layui-table tbody tr").each(function () {
				if (!$(this).hasClass("empty-box")) {
					var point = $(this).find(".point").val(),
						growth = $(this).find(".growth").val(),
						day = $(this).find(".day").val();
					arr.push({point: point, growth: growth, day: day});
				}
			});
			data.field.json = JSON.stringify(arr);
			
			if (repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				type: 'POST',
				url: ns.url("membersignin://shop/config/index"),
				dataType: 'JSON',
				data: data.field,
				success: function (res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title: '操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function (index, layero) {
								location.hash = ns.hash("shop/promotion/market")
								layer.close(index);
							},
							btn2: function (index, layero) {
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			})
		});

		var isEnable = $('.signin [name="is_use"]').is(':checked');

		form.on('switch(is_use)', function(data) {
			isEnable = $(data.elem).is(':checked');
			if (isEnable) {
				$('.reward-wrap').removeClass('layui-hide');
			} else {
				$('.reward-wrap').addClass('layui-hide');
			}
		})

		form.verify({
			everyday: function(){
				var point = $('[name="point"]').val(),
					growth = $('[name="growth"]').val();
				if (isEnable && (!/[\S]+/.test(point) && !/[\S]+/.test(growth))) {
					return "每日签到奖励至少设置一项";
				}
			},
			number: function (value, item) {
				if (isEnable) {
					var str = $(item).parents(".layui-block").find("span").text();
					var _str = $(item).siblings().val();
					if (value != '' && value <= 0) {
						if (str) {
							return str + "不能小于等于0";
						} else {
							return _str + "不能小于等于0";
						}
					}
				}
			},
			int: function (value, item) {
				if (isEnable && value != '') {
					var str = $(item).parents(".layui-block").find("span").text();
					var _str = $(item).siblings().val();
					if (value%1 != 0) {
						if (str) {
							return str + "必须为整数";
						} else {
							return _str + "必须为整数";
						}
					}
				}
			},
			flnum: function (value, item) {
				if (isEnable && value != '') {
					var str = $(item).parents(".layui-block").find("span").text();
					var _str = $(item).siblings().val();
					var arrMen = value.split(".");
					var val = 0;
					if (arrMen.length == 2) {
						val = arrMen[1];
					}
					if (val.length > 2) {
						if (str) {
							return str + "最多可保留两位小数";
						} else {
							return _str + "最多可保留两位小数";
						}
					}
				}
			},
			required: function (value, item) {
				if (isEnable) {
					var str = $(item).parents(".layui-block").find("span").text();
					var _str = $(item).siblings().val();
					
					if (value == null || value.trim() == "" || value == undefined || value == null) {
						if (str) {
							return str + "不能为空";
						} else {
							return _str + "不能为空";
						}
					}
				}
			},
			day: function(value, item){
				if (isEnable) {
					var cycle = $('[name="cycle"]').val();
					if (parseInt(value) < 2) {
						return "连签天数不能小于2天";
					}
					if (parseInt(value) > parseInt(cycle)) {
						return "最大连签天数不能大于签到周期";
					}
					var index = $(item).parents('tr').index();
					if (index) {
						var agoDay = $('.signin tbody tr:eq('+ (index - 1) +') .day').val();
						if (parseInt(value) <= parseInt(agoDay)) {
							return "连续签到天数不能相同或者小于上一个连签天数";
						}
					}
				}
			},
			signDay: function(value, item){
				if (isEnable) {
					var point = $(item).parents('tr').find('.point').val(),
						growth = $(item).parents('tr').find('.growth').val();
					if (isEnable && (!/[\S]+/.test(point) && !/[\S]+/.test(growth))) {
						return "连签奖励至少设置一项";
					}
				}
			},
			cycle: function(value) {
				if (isEnable) {
					if (parseInt(value) < 7 || parseInt(value) > 100) {
						return '签到周期需在7-100天内';
					}
				}
			}
		});
	});
	
	function addSign() {
		$("#sign").find("tbody .empty-box").remove();
		var html = '';
		html += `<tr>`+
					`<td><input type="number" class="layui-input len-short day" lay-verify="required|int|day" autocomplete="off" /><input type="hidden" value="连续签到天数" /></td>`+
					`<td><input type="number" class="layui-input len-short point" lay-verify="signDay|number|int" autocomplete="off" /></td>`+
					`<td><input type="number" class="layui-input len-short growth" lay-verify="signDay|number|int" autocomplete="off" /></td>`+
					`<td><div class="table-btn"><a href="javascript:;" class="layui-btn" onclick="deleteSign(this)">删除连续签到</a></div></td>`+
				`</tr>`;
				
		$("#sign").find("tbody").append(html);
	}

	function deleteSign(e) {
		$(e).parents("tr").remove();
		
		if ($(".layui-table tbody tr").length == 0) {
			$(".layui-table tbody").html('<tr class="empty-box"><td colspan="5"><div class="empty-wrap">暂无数据</div></td></tr>');
		}
	}
	
	function back(){
		location.hash = ns.hash("shop/promotion/market");
	}
</script>
