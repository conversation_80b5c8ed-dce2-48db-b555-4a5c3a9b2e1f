<div class="layui-form form-wrap">
    <div class="form-row">
        <button class="layui-btn" onclick="getSmsPackageList()">获取短信套餐包</button>
	    <button class="layui-btn" onclick="register()">注册</button>
	    <button class="layui-btn" onclick="alert()">发送短信</button>
	    <button class="layui-btn" onclick="removeChildAccount()">删除</button>
    </div>
</div>

<script>
    layui.use('form', function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
		form.render();

        form.on('submit(save)', function(data) {
            if (repeat_flag) return;
            repeat_flag = true;
			
            $.ajax({
                url: ns.url("alisms://shop/sms/config"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero){
								location.hash = ns.hash("shop/message/sms")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
                }
            });
        });
    });

    function getSmsPackageList() {
		$.ajax({
			url: ns.url("niusms://shop/sms/getSmsPackageList"),
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				console.log("getSmsPackageList",res);
			}
		});
	}

	function register() {
		$.ajax({
			url: ns.url("niusms://shop/sms/register"),
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				console.log("register",res);
			}
		});
	}

	function removeChildAccount() {
		$.ajax({
			url: ns.url("niusms://shop/sms/removeChildAccount"),
			dataType: 'JSON',
			type: 'POST',
			success: function(res) {
				console.log("removeChildAccount",res);
			}
		});
	}
</script>
