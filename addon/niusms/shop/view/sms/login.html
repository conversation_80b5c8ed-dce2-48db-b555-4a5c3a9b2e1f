<style>
	.form-wrap {
		margin-top: 0;
	}
	.tips {
		padding: 10px;
		margin-bottom: 15px;
	}
	.form-row{
		width: 240px;
		display: flex;
		justify-content: space-between;
	}
	button{
		width:100px;
		height:34px;
	}
</style>

<div class="layui-form form-wrap">

	<p class="tips bg-color-light-9 text-color">还未注册牛云短信？<a href="{:href_url('niusms://shop/sms/register')}">去注册</a></p>

	<div class="layui-form-item">
		<label class="layui-form-label">用户名：</label>
		<div class="layui-input-block">
			<input type="text" name="username" lay-verify="required" placeholder="请输入用户名" autocomplete="off" class="layui-input len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">密码：</label>
		<div class="layui-input-block">
			<input type="password" name="password" lay-verify="required" placeholder="请输入密码" autocomplete="off" class="layui-input len-long">
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">登录</button>
		<button class="layui-btn layui-btn-primary btn-sign" onclick="location.hash = ns.hash('niusms://shop/sms/forget')">忘记密码</button>
	</div>

</div>

<script>
	layui.use('form', function () {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function (data) {
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("niusms://shop/sms/login"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					if (res.code == 0) {
						layer.msg("登录成功");
						location.hash = ns.hash("niusms://shop/sms/index");
					} else {
						layer.msg(res.message);
						repeat_flag = false;
					}
				}
			});
		});
	});
</script>
