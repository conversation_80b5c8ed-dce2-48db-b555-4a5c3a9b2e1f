<style>
    .addsign {
        margin-left: 10px
    }

    .line-feed {
        white-space: break-spaces;
    }

    .word-aux {
        display: block;
        margin-top: 5px;
        color: rgb(178, 178, 178);
        font-size: 12px;
        line-height: 1.6;
    }

    .signature-annotation {
        display: inline-block;
        height: 20px;
        line-height: 20px;
        padding: 0 !important;
    }
</style>

<div class="single-filter-box">
    <span>
        <button class="layui-btn addsign" onclick="add()">添加短信签名</button>
        <button class="layui-btn addsign" onclick="refresh()">刷新</button>
    </span>
</div>

<div class="layui-tab table-tab" lay-filter="sign_tab">

    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="sign_list" lay-filter="sign_list"></table>
        <input class="signature" type="hidden" value="{$sms_config['signature']}"/>
    </div>
</div>

<script type="text/html" id="use_status">
    {{# if(d.sign == "{$sms_config['signature']}" ){ }}
    使用中
    {{# }else { }}
    未使用
    {{# } }}
</script>

<!-- 审核状态 -->
<script type="text/html" id="status">
    {{#  if(d.auditResult == 1){  }}
    <span style="color:red">待审核</span>
    {{#  }else if(d.auditResult == 2){  }}
    <span style="color:green">审核通过</span>
    {{#  }else if(d.auditResult == 3){  }}
    <p style="color:grey">审核不通过</p>
    <p style="color:red" class="line-feed" title="{{ d.auditMsg }}">（{{ d.auditMsg }}）</p>
    {{#  }  }}
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        {{# if(d.sign != "{$sms_config['signature']}" && d.auditResult == 2){ }}
        <a class="layui-btn" lay-event="use">使用</a>
        {{# } }}
        {{# if(d.auditResult != 2){ }}
        <a class="layui-btn" lay-event="delete">删除</a>
        {{# } }}
    </div>
</script>

<script>
    var table, laytpl, element, repeat_flag, form, layer_signature;
    layui.use(['form', 'element', 'laytpl'], function () {
        laytpl = layui.laytpl;
        form = layui.form;
        element = layui.element;
        repeat_flag = false; //防重复标识
        form.render();

        element.on('tab(sign_tab)', function () {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'status': this.getAttribute('data-status')
                }
            });
        });

        table = new Table({
            elem: '#sign_list',
            url: ns.url("niusms://shop/sms/signlist"),
            parseData: function (res) { //res 即为原始返回的数据
                return {
                    "code": 0, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.page.total, //解析数据长度
                    "data": res.data.signatures //解析数据列表
                };
            },
            cols: [
                [{
                    field: 'sign',
                    title: '签名名称',
                    unresize: 'false',
                    width: '15%'
                }, {
                    title: '使用状态',
                    unresize: 'false',
                    templet: '#use_status',
                    width: '15%'
                }, {
                    title: '审核状态',
                    unresize: 'false',
                    templet: '#status',
                    width: '45%'
                }, {
                    title: '添加时间',
                    unresize: 'false',
                    width: '15%',
                    templet: function (data) {
                        return ns.millisecond_to_date(data.createTime);
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
				    align:'right'
                }]
            ]

        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function (data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function (obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'use': //使用
                    changeSign(data.sign);
                    break;
                case 'delete': //删除
                    deleteSign(data.sign);
                    break;
            }
        });

        /**
         * 使用签名
         */
        function changeSign(sign) {

            layer.confirm('确定要使用该签名?', function (index) {
                if (repeat_flag) return;
                repeat_flag = true;
				layer.close(index);

                $.ajax({
                    url: ns.url("niusms://shop/sms/changeSignature"),
                    data: {
                        signature: sign
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            parent.listenerHash(); // 刷新页面
                            parent.layer.closeAll();
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        }

        /**
         * 使用签名
         */
        function deleteSign(sign) {

            layer.confirm('确定要删除该签名吗？删除操作会有一定延迟，如果删除后还能看到签名数据请刷新列表。', function (index) {
                if (repeat_flag) return;
                repeat_flag = true;
                layer.close(index);

                $.ajax({
                    url: ns.url("niusms://shop/sms/deleteSignature"),
                    data: {
                        signature: sign
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            layer.closeAll();
                            table.reload();
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        }

        form.verify({
            mobile: function (value){
                if(!ns.getRegexp('mobile').test(value)){
                    return '请输入正确的手机号';
                }
            },
            id_card: function (value){
                if(!ns.getRegexp('idcard15').test(value) && !ns.getRegexp('idcard18').test(value)){
                    return '请输入正确的身份证号';
                }
            },
            img_url: function (value){
                let sign_source = $("input[name=sign_source]:checked").val();
                //企业单位和事业单位可以不填，其他的必填
                if(sign_source !== '1' && sign_source !== '2' && !value){
                    return '请上传图片';
                }
            }
        })

        form.on('radio(sign_source)', function (data){
            if(data.value === '1' || data.value === '2'){
                $("#img_url_required").hide();
            }else{
                $("#img_url_required").show();
            }
        })

        //添加签名
        form.on('submit(add_signature)', function (data) {

            var signature = data.field.signature;
            if (signature.trim().length == 0) {
                layer.msg("请填写短信签名！");
                return false;
            }

            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                type: "post",
                url: ns.url("niusms://shop/sms/addChildSignature"),
                async: true,
                dataType: 'json',
                data: data.field,
                success: function (res) {
                    repeat_flag = false;
                    layer.msg(res.message, {}, function () {
                        if (res.code == 0) {
                            layer.closeAll();
                            table.reload();
                        }
                    });
                }
            })

        });

    });

    function add() {
        laytpl($("#add_signature").html()).render({}, function (html) {
            layer_signature = layer.open({
                title: '添加短信签名',
                skin: 'layer-tips-class',
                type: 1,
                area: ['800px', '730px'], //自定义文本域宽高
                content: html,
                success: ()=>{
                    form.render();
                }
            });

            var upload = new Upload({
                elem: '#imgUploadGoods'
            });
        });
    }

     function refresh(){
        table.reload();
     }

    function closeSignature() {
        layer.close(layer_signature);
    }
</script>

<!-- 添加签名html -->
<script type="text/html" id="add_signature">
    <div class="layui-form" lay-filter="form">

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>短信签名：</label>
            <div class="layui-input-block mid">
                <input type="text" name="signature" lay-verify="required" placeholder="请输入短信签名" autocomplete="off" class="layui-input len-long">
                <span class="layui-word-aux signature-annotation">字数要求在2-20个字符，不能使用空格和特殊符号“ - + = * & % # @ ~等;</span>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>企业名称：</label>
            <div class="layui-input-block mid">
                <input type="text" name="company_name" lay-verify="required" placeholder="请输入企业名称" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>社会统一信用代码：</label>
            <div class="layui-input-block mid">
                <input type="text" name="credit_code" lay-verify="required" placeholder="请输入社会统一信用代码" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>法人姓名：</label>
            <div class="layui-input-block mid">
                <input type="text" name="legal_person" lay-verify="required" placeholder="请输入法人姓名" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>经办人姓名：</label>
            <div class="layui-input-block mid">
                <input type="text" name="principal_name" lay-verify="required" placeholder="请输入经办人姓名" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>经办人身份证：</label>
            <div class="layui-input-block mid">
                <input type="text" name="principal_id_card" lay-verify="required|id_card" placeholder="请输入经办人身份证" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid"><span class="required">*</span>经办人手机号：</label>
            <div class="layui-input-block mid">
                <input type="text" name="principal_mobile" lay-verify="required|mobile" placeholder="请输入经办人手机号" autocomplete="off" class="layui-input len-long">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid">签名来源：</label>
            <div class="layui-input-block mid">
                <input type="radio" name="sign_source" value="1" title="企业名称" lay-filter="sign_source">
                <input type="radio" name="sign_source" value="2" title="事业单位" lay-filter="sign_source">
                <input type="radio" name="sign_source" value="3" title="商标（需提供商标图片）" lay-filter="sign_source">
                <input type="radio" name="sign_source" value="4" title="APP（需提供APP截图）" lay-filter="sign_source">
                <input type="radio" name="sign_source" value="5" title="小程序（需提供小程序截图）" lay-filter="sign_source" checked>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label mid">签名类型：</label>
            <div class="layui-input-block mid">
                <input type="radio" name="sign_type" value="0" title="全称" lay-filter="sign_source" >
                <input type="radio" name="sign_type" value="1" title="简称" lay-filter="sign_source" checked>
            </div>
        </div>

        <div class="layui-form-item goods-image-wrap">
            <label class="layui-form-label mid"><span class="required" id="img_url_required">*</span>上传图片：</label>
            <div class="layui-input-inline mid">
                <div class="upload-img-block">
                    <div class="upload-img-box">
                        <div class="upload-default" id="imgUploadGoods">
                            <div class="upload">
                                <i class="iconfont iconshangchuan"></i>
                                <p>点击上传</p>
                            </div>
                        </div>
                        <div class="operation">
                            <div>
                                <i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
                            </div>
                            <div class="replace_img js-replace">点击替换</div>
                            <input type="hidden" name="img_url" lay-verify="img_url">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-row mid">
            <button class="layui-btn" lay-submit lay-filter="add_signature">确定</button>
            <button class="layui-btn layui-btn-primary" onclick="closeSignature()">返回</button>
        </div>
    </div>
</script>
