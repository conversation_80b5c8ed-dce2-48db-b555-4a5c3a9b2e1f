<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label mid">支付凭证：</label>
		<div class="layui-input-inline mid" id="upload_payment_img">

		</div>
		<input lay-verify="imgs" type="hidden"/>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label mid">支付说明：</label>
		<div class="layui-input-block mid">
			<textarea name="desc" class="layui-textarea len-long" maxlength="200"></textarea>
		</div>
	</div>
	<input type="hidden" name="out_trade_no" value="{$out_trade_no}">
	<input type="hidden" name="member_id" value="{$member_id}">
	<div class="form-row layui-hide">
		<button class="layui-btn" lay-submit lay-filter="save" id="pay_submit">保存</button>
	</div>
</div>

<script>
	var callbackFunc;
	layui.use(['form'], function() {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		var multi_upload = new MultiUpload({
			container:'#upload_payment_img',
			max:5,
		})

		form.verify({
			imgs: function(value, elem){
				if(!multi_upload.getData().length){
					return '请上传支付凭证';
				}
			},
		})

		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return false;
			repeat_flag = true;
			data.field.imgs = multi_upload.getData().join(',');

			$.ajax({
				url: ns.url("offlinepay://shop/pay/pay"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				beforeSend: function () {layer_index = layer.load();},
				complete: function () {layer.close(layer_index);},
				success: function(res) {
					repeat_flag = false;
					if (res.code >= 0) {
						typeof callbackFunc == 'function' && callbackFunc(res);
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
	});

	function paySubmit(callback){
		callbackFunc = callback;
		$("#pay_submit").click();
	}
</script>