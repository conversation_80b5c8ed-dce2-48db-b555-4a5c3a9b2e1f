<style>
	.adv-url-show{margin-right: 10px;}
	.layui-colorpicker-main-input div.layui-inline{margin-right: 0;}
	.layui-colorpicker-main-input input.layui-input{width: 130px;height: 34px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>广告名称：</label>
		<div class="layui-input-block">
			<input name="adv_title" type="text" lay-verify="required" class="layui-input len-long">
		</div>
	</div>

	{notempty name="$adv_position_list"}
	<div class="layui-form-item">
		<label class="layui-form-label">所属广告位：</label>
		<div class="layui-input-block len-mid">
			<select name="ap_id" lay-filter="ap_id">
				{foreach $adv_position_list as $adv_position_k => $adv_position_v}
				<option value="{$adv_position_v.ap_id}" {if $ap_id == $adv_position_v.ap_id}selected{/if} data-type="{$adv_position_v.type}" data-width="{$adv_position_v.ap_width}" data-height="{$adv_position_v.ap_height}">{$adv_position_v.ap_name}</option>
				{/foreach}
			</select>
		</div>
	</div>
	<div class="layui-form-item" data-type="1" {if $adv_position_list[0]['type'] == 2}style="display: none"{/if}>
		<label class="layui-form-label">PC端广告链接：</label>
		<div class="layui-input-block len-mid">
			<select name="pc_link" lay-filter="pc_link">
				<option value="">请选择</option>
				{foreach $pc_link as $k => $v}
				<option value="{$v.url}">{$v.title}</option>
				{/foreach}
				<option value="diy">自定义</option>
			</select>
		</div>
	</div>
	
	<div class="layui-form-item" data-type="2" {if $adv_position_list[0]['type'] == 1}style="display: none"{/if}>
		<label class="layui-form-label">移动端广告链接：</label>
		<div class="layui-input-block len-mid">
			<span class="adv-url-show"></span>
			<button class="layui-btn layui-btn-primary sm" onclick="selectedLink()">选择链接</button>
		</div>
	</div>
	<input name="adv_url" type="hidden" />
	{/notempty}
	
	<div class="layui-form-item">
		<label class="layui-form-label">广告图片：</label>
		<div class="layui-input-block">
			<div class="upload-img-block img-upload">
				<div class="upload-img-box">
					<div class="upload-default" id="adv_image">
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="adv_image" value="">
				</div>
			</div>
		</div>
		<div class="word-aux">建议宽度<span class="pic_width">{$adv_position_list[0]['ap_width']}</span>px  建议高度<span class="pic_height">{$adv_position_list[0]['ap_height']}</span>px</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-block">
			<input name="slide_sort" value="0" type="number" placeholder="排序" lay-verify="num" class="layui-input len-short">
		</div>
		<div class="word-aux">排序值必须为整数</div>
	</div>
	
	<div class="layui-form-item" style="display: none">
		<label class="layui-form-label">广告价格：</label>
		<div class="layui-input-block">
			<input name="price" value="0.00" type="number" placeholder="广告价格" lay-verify="flo" class="layui-input len-short">
		</div>
		<div class="word-aux">广告价格 / 月</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">背景色：</label>
		<div class="layui-input-inline">
			<div id="bg_color"></div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="state" value="1" lay-skin="switch" lay-filter="state" checked />
		</div>
	</div>
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backPcAdvList()">返回</button>
	</div>
	
	<input type="hidden" name="background" id="ap_background_color" value="#FFFFFF" />
</div>

<script>
	layui.use(['form', 'colorpicker'], function () {
		var form = layui.form,
			colorpicker = layui.colorpicker,
			repeat_flag = false; //防重复标识
		form.render();

		form.on('select(ap_id)', function(data){
			var type = $(data.elem).find("option:selected").attr("data-type");
			var width = $(data.elem).find("option:selected").attr("data-width");
			var height = $(data.elem).find("option:selected").attr("data-height");
			$(".pic_width").text(width);
			$(".pic_height").text(height);
			$("[data-type]").hide();
			$("[data-type='"+ type+"']").show();
		});

		form.on('select(pc_link)', function(data){
			var title = $(data.elem).find("option:selected").text();

			if(data.value != 'diy'){
				$("input[name='adv_url']").val(JSON.stringify({
					"title": title,
					"url": data.value
				}));
			}else{
				layer.prompt({
					formType: 2,
					value :$("input[name='adv_url']").val() ? JSON.parse($("input[name='adv_url']").val()).url : '',
					title: '自定义链接地址',
					area: ['450px', '100px'],
					cancel : function () {
						$("input[name='adv_url']").val("");
					}
				}, function(value, index, elem){
					$("input[name='adv_url']").val(JSON.stringify({
						"title": title,
						"url": value
					}));
					layer.close(index);
				});
			}
		});

		/**
		 * 颜色
		 */
		colorpicker.render({
			elem: '#bg_color', //绑定元素
			color: "#FFFFFF",
			done: function(color) {
				$(".tdrag-name").css("color", color);
				$("#ap_background_color").val(color);
			}
		});

		// 广告图片
		var logo_upload = new Upload({
			elem: '#adv_image'
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;
		
			$.ajax({
				url: ns.url("pc://shop/adv/addAdv"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
		
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(index, layero) {
								location.hash = ns.hash("pc://shop/adv/lists", {ap_id: '{$ap_id}'})
								layer.close(index);
							},
							btn2: function(index, layero) {
								listenerHash(); // 刷新页面
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			});
		});
		
		/**
		 * 表单验证
		 */
		form.verify({
			num: function (value) {
				let reg = ns.getRegexp('>=0num');
				if (value !== '' && !reg.test(value)) {
					return '排序数值必须为大于或等于0的整数'
				}
			},
			flo: function (value) {
				let reg = ns.getRegexp('>=0float2');
				if (value !== '' && !reg.test(value)) {
					return '价格必须大于或等于0，最多保留两位小数'
				}
			}
		});
	});
	
	function backPcAdvList() {
		location.hash = ns.hash("pc://shop/adv/lists", {ap_id : '{$ap_id}'})
	}
	
	function selectedLink() {
		ns.select_link('', function (data) {
			for (var o in data) {
				if (data[o] == null) delete data[o];
			}

			$("input[name='adv_url']").val(JSON.stringify(data));
			$(".adv-url-show").text(data.title);
		});
	}
</script>
