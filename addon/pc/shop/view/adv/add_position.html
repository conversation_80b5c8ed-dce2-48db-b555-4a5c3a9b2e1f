<style>
	.layui-colorpicker-main-input div.layui-inline{margin-right: 0;}
	.layui-colorpicker-main-input input.layui-input{width: 130px;height: 34px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>广告位置名：</label>
		<div class="layui-input-block">
			<input name="ap_name" type="text" lay-verify="required" class="layui-input len-long">
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>广告关键字：</label>
		<div class="layui-input-block">
			<input name="keyword" type="text" lay-verify="required" class="layui-input len-long">
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">广告位简介：</label>
		<div class="layui-input-block len-long">
			<textarea name="ap_intro" class="layui-textarea" maxlength="150"></textarea>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">建议广告图片宽度：</label>
		<div class="layui-input-block">
			<input name="ap_width" value="1920" type="number" lay-verify="int" class="layui-input len-short">
		</div>
		<div class="word-aux">单位：px &nbsp;&nbsp; 宽度值不能小于0（例：首页轮播图一般为1920px * 400px）</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">建议广告图片高度：</label>
		<div class="layui-input-block">
			<input name="ap_height" value="400" type="number" lay-verify="int" class="layui-input len-short">
		</div>
		<div class="word-aux">单位：px &nbsp;&nbsp; 高度值不能小于0（例：首页轮播图一般为1920px * 400px）</div>
	</div>
	
	<!--<div class="layui-form-item">-->
		<!--<label class="layui-form-label">广告位类型：</label>-->
		<!--<div class="layui-input-inline" id="type">-->
			<!--<input type="radio" name="type" lay-filter="type" value="1" title="电脑端" checked>-->
			<!--<input type="radio" name="type" lay-filter="type" value="2" title="手机端" >-->
		<!--</div>-->
	<!--</div>-->

	<div class="layui-form-item">
		<label class="layui-form-label">广告位背景色：</label>
		<div class="layui-input-inline">
			<div id="bg_color"></div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否启用：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="state" value="1" lay-skin="switch" lay-filter="state" checked/>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backPcAdvIndex()">返回</button>
	</div>
	
	<input type="hidden" name="ap_background_color" id="ap_background_color" value="#FFFFFF" />
</div>

<script>
	layui.use(['form', 'colorpicker'], function () {
		var form = layui.form,
			colorpicker = layui.colorpicker,
			repeat_flag = false; //防重复标识
		form.render();

		/**
		 * 颜色
		 */
		colorpicker.render({
			elem: '#bg_color', //绑定元素
			color: "#FFFFFF",
			done: function(color) {
				$(".tdrag-name").css("color", color);
				$("#ap_background_color").val(color);
			}
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;
		
			$.ajax({
				url: ns.url("pc://shop/adv/addPosition"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
		
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							yes: function(index, layero) {
								location.hash = ns.hash("pc://shop/adv/index")
								layer.close(index);
							},
							btn2: function(index, layero) {
								listenerHash(); // 刷新页面
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			});
		});
		
		/**
		 * 表单验证
		 */
		form.verify({
			int: function (value) {
				if (value == '') {
					return;
				}
				if (value < 0 || value%1 != 0) {
					return '请输入0或正整数！'
				}
			}
		});
	});
	
	function backPcAdvIndex() {
		location.hash = ns.hash("pc://shop/adv/index")
	}
</script>
