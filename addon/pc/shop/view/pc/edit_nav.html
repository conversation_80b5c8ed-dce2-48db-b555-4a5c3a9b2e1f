<div class="layui-form form-wrap">
	<input type="hidden" name="id" value="{$nav_info.id}">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>导航名称：</label>
		<div class="layui-input-block">
			<input name="nav_title" type="text" lay-verify="required" class="layui-input len-mid" {if $nav_info} value="{$nav_info.nav_title}" {/if}>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>链接地址：</label>
		<div class="layui-input-block len-mid">
			<select name="link" lay-filter="link">
				<option value="">请选择</option>
				{foreach $link as $k => $v}
				<option value="{$v.url}" {if $nav_info && $nav_info.nav_url && json_decode($nav_info.nav_url,true)['title'] == $v.title}selected{/if}>{$v.title}</option>
				{/foreach}
				<option value="diy" {if $nav_info && $nav_info.nav_url && json_decode($nav_info.nav_url,true)['title'] == '自定义'}selected{/if}>自定义</option>
			</select>
			<input name="nav_url" type="hidden" {if $nav_info} value="{$nav_info.nav_url}" {/if}>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否显示：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_show" value="1" lay-skin="switch" {if condition="$nav_info && $nav_info.is_show == 1"} checked {/if}  />
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">是否新窗口打开：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_blank" value="1" lay-skin="switch" {if condition="$nav_info && $nav_info.is_blank == 1"} checked {/if} />
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required"></span>排序：</label>
		<div class="layui-input-block">
			<input name="sort" type="text" class="layui-input len-short" {if $nav_info} value="{$nav_info.sort}"{else /}value="0" {/if}>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">导航图标：</label>
		<div class="layui-input-block">
			<div class="upload-img-block img-upload">
				<div class="upload-img-box {notempty name="$nav_info['nav_icon']"}hover{/notempty}">
					<div class="upload-default" id="imgUpload">
						{if condition="$nav_info.nav_icon"}
						<div id="preview_imgUpload" class="preview_img">
							<img layer-src src="{:img($nav_info.nav_icon)}" class="img_prev"/>
						</div>
						{else/}
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
						{/if}
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="nav_icon" value="{$nav_info.nav_icon}">
				</div>
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：不能大于100k。图片格式：jpg、png、jpeg。</p>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>

</div>

<script>
	layui.use(['form'], function() {
		var form = layui.form, repeat_flag=false;
		form.render();
		form.on('select(link)', function(data){
			var title = $(data.elem).find("option:selected").text();
			if(data.value != 'diy'){
				$("input[name='nav_url']").val(JSON.stringify({
					title : title,
					url:data.value
				}));
			}else{
				var value = $("input[name='nav_url']").val();
				if(value) value = JSON.parse(value).url;
				layer.prompt({
					formType: 2,
					value :value,
					title: '自定义链接地址',
					area: ['450px', '100px'],
					cancel : function () {
						$("input[name='nav_url']").val("");
					}
				}, function(value, index, elem){
					$("input[name='nav_url']").val(JSON.stringify({
						title : title,
						url:value
					}));
					layer.close(index);
				});
			}
		});

		// 普通图片上传
		var logo_upload = new Upload({
			elem: '#imgUpload',
			size:100
		});

		form.on('submit(save)', function(data) {
			if(!data.field.nav_url){
				layer.msg("请输入链接地址");
				return;
			}
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("pc://shop/pc/editNav"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					layer.msg(res.message);
					if (res.code == 0) {
						location.hash = ns.hash("pc://shop/pc/navlist");
					}
					repeat_flag = false;
				}
			});
		});
	});
</script>
