<!-- 搜索框 -->
<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加楼层</button>

    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_text" placeholder="请输入楼层名称" autocomplete="off" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="floor_list" lay-filter="floor_list"></table>

<!-- 操作 -->
<script type="text/html" id="action">
    <div class="table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>
<script type="text/html" id="sort">
    <input name="sort" type="number" onchange="editSort({{d.id}},this)" value="{{d.sort}}" placeholder="请输入排序" class="layui-input edit-sort sort-len">
</script>

<script>
    var table, repeat_flag = false;//防重复标识;
    layui.use('form', function() {
        var form = layui.form;
        form.render();

        table = new Table({
            elem: '#floor_list',
            url: ns.url("pc://shop/pc/floor"),
            cols: [
                [{
                    field: 'title',
                    title: '楼层名称',
                    width: '25%',
                    unresize: 'false'
                }, {
                    field: 'block_title',
                    title: '所属模板',
                    width: '20%',
                    unresize: 'false'
                }, {
                    field: 'state',
                    title: '状态',
                    width: '10%',
                    unresize: 'false',
                    templet: function (data) {
                        return data.state == 1 ? "开启" : "禁用";
                    }
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    width: '20%',
                    unresize: 'false',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    field: 'sort',
                    title: '排序',
                    width: '10%',
                    sort : true,
                    unresize: 'false',
                    templet: '#sort'
                }, {
                    title: '操作',
                    toolbar: '#action',
                    unresize: 'false',
                    align : 'right'
                }]
            ]
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        table.on("sort",function (obj) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    order:obj.field,
                    sort:obj.type
                }
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.hash = ns.hash("pc://shop/pc/editfloor?id=" + data.id);
                    break;
                case 'delete': //删除
                    deleteFloor(data.id);
                    break;
            }
        });
    });

    /**
     * 删除
     */
    function deleteFloor(id) {
        if (repeat_flag) return;
        repeat_flag = true;
        layer.confirm('确定要删除该首页楼层吗?', function (index) {
			layer.close(index);
                $.ajax({
                    url: ns.url("pc://shop/pc/deletefloor"),
                    data: {id: id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function (res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            },
            function () {
                repeat_flag = false;
                layer.close();
            });
    }

    // 监听单元格编辑
    function editSort(id, event) {
        var data = $(event).val();
        if (!new RegExp("^-?[1-9]\\d*$").test(data)) {
            layer.msg("排序号只能是整数");
            return;
        }
        if(data<0){
            layer.msg("排序号必须大于0");
            return ;
        }
        $.ajax({
            type: 'POST',
            url: ns.url("pc://shop/pc/modifyFloorSort"),
            data: {
                sort: data,
                id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
            }
        });
    }

    function add() {
        location.hash = ns.hash("pc://shop/pc/editfloor");
    }
</script>
