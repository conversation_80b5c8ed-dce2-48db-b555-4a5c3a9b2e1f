<!-- 搜索框 -->
<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加链接</button>

    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_text" placeholder="请输入链接名称" autocomplete="off" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="nav_list" lay-filter="nav_list"></table>

<!-- 操作 -->
<script type="text/html" id="action">
    <div class="table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>
<script type="text/html" id="sort">
    <input name="sort" type="number" onchange="editSort({{d.id}},this)" value="{{d.link_sort}}" placeholder="请输入排序" class="layui-input edit-sort sort-len">
</script>

<script>
    var table, repeat_flag = false;//防重复标识;
    layui.use('form', function() {
        var form = layui.form;
        form.render();

        table = new Table({
            elem: '#nav_list',
            url: ns.url("pc://shop/pc/linkList"),
            cols: [
                [ {
                    field: 'link_sort',
                    title: '排序',
                    width: '8%',
                    sort : true,
                    unresize: 'false',
                    templet: '#sort'
                },{
                    field: 'link_title',
                    title: '链接名称',
                    width: '15%',
                    unresize: 'false'
                }, {
                    field: 'link_url',
                    title: '链接地址',
                    width: '30%',
                    unresize: 'false'
                }, {
                    field: 'is_show',
                    title: '是否显示',
                    width: '15%',
                    unresize: 'false',
                    templet: function (data) {
                        if(data.is_show){
                            return '是';
                        }else {
                            return '否';
                        }
                    }
                }, {
                    field: 'is_blank',
                    title: '新窗口打开',
                    width: '15%',
                    unresize: 'false',
                    templet: function (data) {
                        if(data.is_blank){
                            return '是';
                        }else {
                            return '否';
                        }
                    }
                }, {
                    title: '操作',
                    toolbar: '#action',
                    unresize: 'false',
                    align : 'right'
                }]
            ],
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        table.on("sort",function (obj) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    order:obj.field,
                    sort:obj.type
                }
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.hash = ns.hash("pc://shop/pc/editLink?id=" + data.id);
                    break;
                case 'delete': //删除
                    deleteHelp(data.id);
                    break;
            }
        });
    });

    /**
     * 删除
     */
    function deleteHelp(id) {
        if (repeat_flag) return false;
        repeat_flag = true;

        layer.confirm('确定要删除该链接吗?', function(index) {
			layer.close(index);
                $.ajax({
                    url: ns.url("pc://shop/pc/deleteLink"),
                    data: {id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            },
            function() {
                repeat_flag = false;
                layer.close();
            });
    }

    // 监听单元格编辑
    function editSort(id, event) {
        var data = $(event).val();
        if (!new RegExp("^-?[1-9]\\d*$").test(data)) {
            layer.msg("排序号只能是整数");
            return;
        }
        if(data<0){
            layer.msg("排序号必须大于0");
            return ;
        }
        $.ajax({
            type: 'POST',
            url: ns.url("pc://shop/pc/modifyLinkSort"),
            data: {
                sort: data,
                id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
            }
        });
    }

    function add() {
        location.hash = ns.hash("pc://shop/pc/addLink");
    }
</script>
