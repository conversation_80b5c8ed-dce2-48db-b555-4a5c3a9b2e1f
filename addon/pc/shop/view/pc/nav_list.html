<!-- 搜索框 -->
<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加导航</button>

    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_text" placeholder="请输入导航名称" autocomplete="off" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="nav_list" lay-filter="nav_list"></table>

<!-- 操作 -->
<script type="text/html" id="action">
    <div class="table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>
<script type="text/html" id="sort">
    <input name="sort" type="number" onchange="editSort({{d.id}},this)" value="{{d.sort}}" placeholder="请输入排序" class="layui-input edit-sort sort-len">
</script>

<script>
    var table, repeat_flag = false;//防重复标识;
    layui.use('form', function() {
        var form = layui.form;
        form.render();

        form.on('switch(is_show)', function(data){
            let is_show = data.elem.checked ? 1 : 0;
            let id = $(data.elem).attr('data-id');

            if(repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                url: ns.url("pc://shop/pc/modifyNavIsShow"),
                data: {
                    id : id,
                    is_show : is_show,
                },
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    layer.msg(res.message);
                    repeat_flag = false;
                }
            });
        });

        table = new Table({
            elem: '#nav_list',
            url: ns.url("pc://shop/pc/navList"),
            cols: [
                [ {
                    field: 'nav_title',
                    title: '导航名称',
                    width: '15%',
                    unresize: 'false'
                }, {
                    field: 'nav_url',
                    title: '链接地址',
                    width: '17%',
                    unresize: 'false',
                    templet: function (data) {
                        return JSON.parse(data.nav_url).url;
                    }
                }, {
                    field: 'create_time',
                    title: '创建时间',
                    width: '20%',
                    unresize: 'false',
                    templet: function (data) {
                        return ns.time_to_date(data.create_time);
                    }
                },{
                    field: 'sort',
                    title: '排序',
                    width: '8%',
                    sort : true,
                    unresize: 'false',
                    templet: '#sort'
                },{
                    title: '是否显示',
                    unresize: 'false',
                    templet: function(data) {
                        return '<input type="checkbox" name="state" value="1" lay-skin="switch" lay-filter="is_show" data-id="'+ data.id +'" '+ (data.is_show == 1 ? 'checked' : '') +' />';
                    },
                    width: '10%',
                }, {
                    title: '操作',
                    toolbar: '#action',
                    unresize: 'false',
                    align : 'right'
                }]
            ],
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        table.on("sort",function (obj) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    order:obj.field,
                    sort:obj.type
                }
            });
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.hash = ns.hash("pc://shop/pc/editNav?id=" + data.id);
                    break;
                case 'delete': //删除
                    deleteHelp(data.id);
                    break;
            }
        });
    });

    /**
     * 删除
     */
    function deleteHelp(id) {
        if (repeat_flag) return false;
        repeat_flag = true;

        layer.confirm('确定要删除该导航吗?', function(index) {
			layer.close(index);
                $.ajax({
                    url: ns.url("pc://shop/pc/deleteNav"),
                    data: {id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;

                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            },
            function() {
                repeat_flag = false;
                layer.close();
            });
    }

    // 监听单元格编辑
    function editSort(id, event) {
        var data = $(event).val();

        let reg = ns.getRegexp('>=0num');
        if (!reg.test(data)) {
            layer.msg("排序号必须是大于或等于0的整数");
            return;
        }

        $.ajax({
            type: 'POST',
            url: ns.url("pc://shop/pc/modifySort"),
            data: {
                sort: data,
                id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
                if (res.code == 0) {
                    table.reload();
                }
            }
        });
    }

    function add() {
        location.hash = ns.hash("pc://shop/pc/addNav");
    }
</script>
