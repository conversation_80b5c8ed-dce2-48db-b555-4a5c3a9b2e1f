(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{535:function(t,e,n){"use strict";var r=n(3),o=n(35),h=n(14),m=n(8),path=n(319),c=n(5),l=n(113),f=n(16),d=n(205),v=n(63),T=n(112),w=n(318),y=n(4),x=n(93).f,I=n(57).f,_=n(26).f,S=n(320),U=n(316).trim,N="Number",E=m[N],k=path[N],X=E.prototype,Y=m.TypeError,M=c("".slice),O=c("".charCodeAt),W=function(t){var e=w(t,"number");return"bigint"==typeof e?e:A(e)},A=function(t){var e,n,r,o,h,m,c,code,l=w(t,"number");if(T(l))throw Y("Cannot convert a Symbol value to a number");if("string"==typeof l&&l.length>2)if(l=U(l),43===(e=O(l,0))||45===e){if(88===(n=O(l,2))||120===n)return NaN}else if(48===e){switch(O(l,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+l}for(m=(h=M(l,2)).length,c=0;c<m;c++)if((code=O(h,c))<48||code>o)return NaN;return parseInt(h,r)}return+l},H=l(N,!E(" 0o1")||!E("0b1")||E("+0x1")),C=function(t){return v(X,t)&&y((function(){S(t)}))},L=function(t){var e=arguments.length<1?0:E(W(t));return C(this)?d(Object(e),this,L):e};L.prototype=X,H&&!o&&(X.constructor=L),r({global:!0,constructor:!0,wrap:!0,forced:H},{Number:L});var $=function(t,source){for(var e,n=h?x(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),r=0;n.length>r;r++)f(source,e=n[r])&&!f(t,e)&&_(t,e,I(source,e))};o&&k&&$(path[N],k),(H||o)&&$(path[N],E)},538:function(t,e,n){t.exports=n(543)},543:function(t,e,n){"use strict";n.r(e);n(535),n(7),n(74),n(73),n(315);var r={replace:!0,data:function(){return{tipShow:!0,msTime:{show:!1,day:"",hour:"",minutes:"",seconds:""},star:"",end:"",current:""}},watch:{currentTime:function(t,e){this.gogogo()}},props:{tipText:{type:String,default:"距离开始"},tipTextEnd:{type:String,default:"距离结束"},id:{type:String,default:"1"},currentTime:{type:Number},startTime:{type:Number},endTime:{type:Number},endText:{type:String,default:"已结束"},dayTxt:{type:String,default:":"},hourTxt:{type:String,default:":"},minutesTxt:{type:String,default:":"},secondsTxt:{type:String,default:":"},secondsFixed:{type:Boolean,default:!1}},mounted:function(){console.log(this),this.gogogo()},methods:{gogogo:function(){var t=this;10==this.startTime.toString().length?this.star=1e3*this.startTime:this.star=this.startTime,10==this.endTime.toString().length?this.end=1e3*this.endTime:this.end=this.endTime,this.currentTime?10==this.currentTime.toString().length?this.current=1e3*this.currentTime:this.current=this.currentTime:this.current=(new Date).getTime(),this.end<this.current?(this.msTime.show=!1,this.end_message()):this.current<this.star?(this.$set(this,"tipShow",!0),setTimeout((function(){t.runTime(t.star,t.current,t.start_message)}),1)):(this.end>this.current&&this.star<this.current||this.star==this.current)&&(this.$set(this,"tipShow",!1),this.msTime.show=!0,this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1))},runTime:function(t,e,n,r){var o=this,h=this.msTime,m=t-e;if(m>0){this.msTime.show=!0,h.day=Math.floor(m/864e5),m-=864e5*h.day,h.hour=Math.floor(m/36e5),m-=36e5*h.hour,h.minutes=Math.floor(m/6e4),m-=6e4*h.minutes,h.seconds=Math.floor(m/1e3).toFixed(0),m-=1e3*h.seconds,h.hour<10&&(h.hour="0"+h.hour),h.minutes<10&&(h.minutes="0"+h.minutes),h.seconds<10&&(h.seconds="0"+h.seconds);var c=Date.now(),l=Date.now();setTimeout((function(){r?o.runTime(o.end,e+=1e3,n,!0):o.runTime(o.star,e+=1e3,n)}),1e3-(l-c))}else n()},start_message:function(){var t=this;this.$set(this,"tipShow",!1),this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1)},end_message:function(){this.msTime.show=!1,this.currentTime<=0||this.$emit("end_callback",this.msTime.show)}}},o=n(6),component=Object(o.a)(r,(function(){var t=this,e=t._self._c;return e("div",[t.msTime.show?e("p",[t.msTime.day>0?e("span",[e("span",[t._v(t._s(t.msTime.day))]),e("i",[t._v(t._s(t.dayTxt))])]):t._e(),t._v(" "),e("span",[t._v(t._s(t.msTime.hour))]),e("i",[t._v(t._s(t.hourTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.minutes))]),e("i",[t._v(t._s(t.minutesTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.seconds))]),e("i",[t._v(t._s(t.secondsTxt))])]):t._e()])}),[],!1,null,null,null);e.default=component.exports},577:function(t,e,n){"use strict";n(535),n(7),n(74),n(73);var r={props:{scale:{type:Number,default:2.5},url:{type:String,required:!0},bigUrl:{type:String,default:null},scroll:{type:Boolean,default:!1},showEidt:{type:Boolean,default:!1}},data:function(){return{id:null,cover:null,imgbox:null,imgwrap:null,orginUrl:null,bigImgUrl:null,bigOrginUrl:null,imgUrl:null,img:null,canvas:null,ctx:null,rectTimesX:0,rectTimesY:0,imgTimesX:0,imgTimesY:0,init:!1,step:0,bigStep:0,vertical:!1,showImg:!0}},created:function(){for(var t="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",e=t.length,n="",i=0;i<10;i++)n+=t.charAt(Math.floor(Math.random()*e));this.id=n,this.imgUrl=this.url,this.orginUrl=this.url,this.bigImgUrl=this.bigUrl,this.bigOrginUrl=this.bigUrl},watch:{url:function(t){this.imgUrl=t,this.orginUrl=t,this.initTime()},bigUrl:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){this.bigImgUrl=bigUrl,this.bigOrginUrl=bigUrl,this.initTime()}))},mounted:function(){var t=this;this.$nextTick((function(){t.initTime()}))},methods:{initTime:function(){var t=this;this.init=!1;var e,n=this.$refs[this.id];this.imgbox=n,this.cover=n.querySelector(".mouse-cover"),this.cover.style.width=this.imgbox.offsetWidth/this.scale+"px",this.cover.style.height=this.imgbox.offsetHeight/this.scale+"px",this.cover.style.left="-100%",this.cover.style.top="-100%",this.imgwrap=n.querySelector("img"),e=this.bigImgUrl?this.bigImgUrl:this.imgUrl,this.img=new Image,this.img.src=e,this.img.onload=function(){t.rectTimesX=t.imgbox.offsetWidth/t.scale/t.imgwrap.offsetWidth,t.rectTimesY=t.imgbox.offsetHeight/t.scale/t.imgwrap.offsetHeight,t.imgTimesX=t.img.width/t.imgwrap.offsetWidth,t.imgTimesY=t.img.height/t.imgwrap.offsetHeight,t.vertical=t.img.width<t.img.height,t.init=!0},this.canvas&&(this.canvas.parentNode.removeChild(this.canvas),this.canvas=null),this.canvas=document.createElement("canvas"),this.canvas.className="mouse-cover-canvas",this.canvas.style.position="absolute",this.canvas.style.left=this.imgbox.offsetLeft+this.imgbox.offsetWidth+10+"px",this.canvas.style.top=this.imgbox.offsetTop+"px",this.canvas.style.border="1px solid #eee",this.canvas.style.zIndex="99999",this.canvas.height=this.imgbox.offsetHeight,this.canvas.width=this.imgbox.offsetWidth,this.canvas.style.display="none",document.body.append(this.canvas),this.ctx=this.canvas.getContext("2d"),this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height)},initBox:function(){var t=this;this.showImg=!1,this.canvas.style.display="none";var e,n=this.$refs[this.id];e=this.bigImgUrl?this.bigImgUrl:this.imgUrl,this.img=new Image,this.img.src=e,this.img.onload=function(){t.vertical=t.img.width<t.img.height,t.showImg=!0;n.querySelector("img");setTimeout((function(){t.rectTimesX=t.imgbox.offsetWidth/t.scale/n.querySelector("img").offsetWidth,t.rectTimesY=t.imgbox.offsetHeight/t.scale/n.querySelector("img").offsetHeight}),20)}},mousemove:function(t){if(!this.init)return!1;var e=this;var n,r=t||event,o={x:(n=r).clientX-e.cover.offsetWidth/2,y:n.clientY-e.cover.offsetHeight/2},h=function(t){var e=null,n=null,r=t.offsetParent;for(e+=t.offsetLeft,n+=t.offsetTop;r;)-1===navigator.userAgent.indexOf("MSIE 8.0")&&(e+=r.clientLeft,n+=r.clientTop),e+=r.offsetLeft,n+=r.offsetTop,r=r.offsetParent;return{left:e,top:n}}(this.imgwrap),m={minX:h.left,maxX:h.left+this.imgwrap.offsetWidth-this.cover.offsetWidth,minY:h.top-document.documentElement.scrollTop,maxY:h.top-document.documentElement.scrollTop+this.imgwrap.offsetHeight-this.cover.offsetHeight};o.x>m.maxX&&(o.x=m.maxX),o.x<m.minX&&(o.x=m.minX),o.y>m.maxY&&(o.y=m.maxY),o.y<m.minY&&(o.y=m.minY),this.cover.style.left=o.x+"px",this.cover.style.top=o.y+"px",this.ctx.clearRect(0,0,this.imgwrap.offsetWidth,this.imgwrap.offsetHeight);var c=o.x-(h.left-document.documentElement.scrollLeft),l=o.y-(h.top-document.documentElement.scrollTop);this.ctx.drawImage(this.img,c*this.imgTimesX,l*this.imgTimesY,this.img.width*this.rectTimesX,this.img.height*this.rectTimesY,0,0,this.imgbox.offsetWidth,this.imgbox.offsetHeight)},mouseover:function(t){if(!this.init)return!1;t=t||event,this.scroll||(t.currentTarget.addEventListener("mousewheel",(function(t){t.preventDefault()}),!1),t.currentTarget.addEventListener("DOMMouseScroll",(function(t){t.preventDefault()}),!1)),this.cover.style.display="block",this.canvas.style.display="block"},mouseleave:function(){if(!this.init)return!1;this.cover.style.display="none",this.canvas.style.display="none"},rotate:function(t){var e=this,n=new Image;if(n.crossOrigin="Anonymous",n.src=this.orginUrl,n.onload=function(){e.rotateImg(n,t,e.step)},this.bigOrginUrl){var r=new Image;n.crossOrigin="Anonymous",r.src=this.bigOrginUrl,r.onload=function(){e.rotateImg(r,t,e.bigStep,!0)}}},rotateImg:function(img,t,e){var n=this,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=0,h=3;if(null!=img){var m=img.height,c=img.width;null==e&&(e=o),"right"==t?++e>h&&(e=o):--e<o&&(e=h);var canvas=document.createElement("canvas"),l=90*e*Math.PI/180,f=canvas.getContext("2d");switch(canvas.width=m,canvas.height=c,f.rotate(l),f.drawImage(img,0,-m),e){case 0:canvas.width=c,canvas.height=m,f.drawImage(img,0,0);break;case 1:canvas.width=m,canvas.height=c,f.rotate(l),f.drawImage(img,0,-m);break;case 2:canvas.width=c,canvas.height=m,f.rotate(l),f.drawImage(img,-c,-m);break;case 3:canvas.width=m,canvas.height=c,f.rotate(l),f.drawImage(img,-c,0)}var d=canvas.toDataURL();r?(this.bigImgUrl=d,this.bigStep=e,this.initBox()):(this.imgUrl=d,this.step=e,this.$nextTick((function(){n.initBox()})))}}}},o=r,h=(n(742),n(6)),component=Object(h.a)(o,(function(){var t=this,e=t._self._c;return e("div",{ref:t.id,staticClass:"magnifier-box",class:t.vertical?"vertical":"",on:{mousemove:t.mousemove,mouseover:t.mouseover,mouseleave:t.mouseleave}},[e("img",{directives:[{name:"show",rawName:"v-show",value:t.showImg,expression:"showImg"}],attrs:{src:t.imgUrl,alt:""}}),t._v(" "),e("div",{staticClass:"mouse-cover"}),t._v(" "),t.showEidt?e("div",{staticClass:"edit-wrap"},[e("span",{staticClass:"rotate-left",on:{click:function(e){return t.rotate("left")}}}),t._v(" "),e("span",{staticClass:"rotate-right",on:{click:function(e){return t.rotate("right")}}})]):t._e()])}),[],!1,null,"69c01ff0",null);e.a=component.exports},649:function(t,e,n){},742:function(t,e,n){"use strict";n(649)}}]);