(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{594:function(e,t,r){},682:function(e,t,r){"use strict";r(594)},770:function(e,t,r){"use strict";r.r(t);r(31),r(73);var n=r(207),o={name:"account_edit",layout:"member",data:function(){return{formData:{id:"",realname:"",mobile:"",withdraw_type:"",bank_account:"",branch_bank_name:""},flag:!1,payList:[],loading:!0,index:0,transferType:[],rules:{realname:[{required:!0,message:"请输入真实姓名",trigger:"blur"}],mobile:[{required:!0,validator:function(e,t,r){if(!t)return r(new Error("手机号不能为空"));/^\d{11}$/.test(t)?r():r(new Error("请输入正确的手机号"))},trigger:"blur"}],withdraw_type:[{required:!0,message:"请选择账号类型",trigger:"change"}],branch_bank_name:[{required:!0,message:"请输入银行名称",trigger:"blur"}],bank_account:[{required:!0,message:"请输入提现账号",trigger:"blur"}]},yes:!0}},created:function(){this.formData.id=this.$route.query.id,this.getTransferType(),this.formData.id&&this.getAccountDetail(this.formData.id)},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getTransferType:function(){var e=this;Object(n.o)().then((function(t){e.transferType=t.data,e.formData.id||(e.loading=!1)})).catch((function(t){e.formData.id||(e.loading=!1)}))},getAccountDetail:function(e){var t=this;Object(n.b)({id:this.formData.id}).then((function(e){0==e.code&&e.data&&(t.formData.realname=e.data.realname,t.formData.mobile=e.data.mobile,t.formData.bank_account=e.data.bank_account,t.formData.branch_bank_name=e.data.branch_bank_name,t.formData.withdraw_type=e.data.withdraw_type),t.loading=!1})).catch((function(e){t.loading=!1}))},saveAccount:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var data={id:t.formData.id,realname:t.formData.realname,mobile:t.formData.mobile,withdraw_type:t.formData.withdraw_type,bank_account:t.formData.bank_account,branch_bank_name:t.formData.branch_bank_name,url:"add"};t.formData.id&&(data.url="edit"),t.flag||(t.flag=!0,Object(n.l)(data).then((function(e){0==e.code?t.$router.push({path:"/member/account_list"}):(t.flag=!1,t.$message({message:e.message,type:"warning"}))})).catch((function(e){t.flag=!1,t.$message.error(e.message)})))}))}}},l=(r(682),r(6)),component=Object(l.a)(o,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("编辑账户")])]),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-form",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[t("el-input",{staticClass:"ns-len-input",attrs:{placeholder:"请输入真实姓名"},model:{value:e.formData.realname,callback:function(t){e.$set(e.formData,"realname",t)},expression:"formData.realname"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"请输入手机号",maxlength:"11"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"账号类型",prop:"withdraw_type"}},[t("el-select",{attrs:{placeholder:"请选择账号类型"},model:{value:e.formData.withdraw_type,callback:function(t){e.$set(e.formData,"withdraw_type",t)},expression:"formData.withdraw_type"}},e._l(e.transferType,(function(e,r){return t("el-option",{key:r,attrs:{label:e,value:r,disabled:"wechatpay"==r}})})),1)],1),e._v(" "),"bank"==e.formData.withdraw_type?t("el-form-item",{attrs:{label:"银行名称",prop:"branch_bank_name"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"请输入银行名称",maxlength:"50"},model:{value:e.formData.branch_bank_name,callback:function(t){e.$set(e.formData,"branch_bank_name",t)},expression:"formData.branch_bank_name"}})],1):e._e(),e._v(" "),"wechatpay"!=e.formData.withdraw_type&&e.formData.withdraw_type?t("el-form-item",{attrs:{label:"提现账号",prop:"bank_account"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"请输入提现账号",maxlength:"30"},model:{value:e.formData.bank_account,callback:function(t){e.$set(e.formData,"bank_account",t)},expression:"formData.bank_account"}})],1):e._e(),e._v(" "),t("el-form-item",[t("el-button",{attrs:{size:"medium",type:"primary"},on:{click:function(t){return e.saveAccount("ruleForm")}}},[e._v("保存")])],1)],1)],1)])],1)}),[],!1,null,"5ff5db24",null);t.default=component.exports}}]);