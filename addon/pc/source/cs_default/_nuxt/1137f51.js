(window.webpackJsonp=window.webpackJsonp||[]).push([[37],{550:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"a",(function(){return m}));var r=n(1);function o(e){return Object(r.a)({url:"/api/member/info",data:e,forceLogin:!0})}function c(e){return Object(r.a)({url:"/api/member/modifynickname",data:e,forceLogin:!0})}function m(e){return Object(r.a)({url:"/api/member/modifyheadimg",data:e,forceLogin:!0})}},551:function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"h",(function(){return c})),n.d(t,"g",(function(){return m})),n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return f})),n.d(t,"i",(function(){return d})),n.d(t,"c",(function(){return v})),n.d(t,"e",(function(){return h})),n.d(t,"f",(function(){return O}));var r=n(1);function o(e){return Object(r.a)({url:"/api/member/modifypassword",data:e,forceLogin:!0})}function c(e){return Object(r.a)({url:"/api/member/bindmobliecode",data:e,forceLogin:!0})}function m(e){return Object(r.a)({url:"/api/member/modifymobile",data:e,forceLogin:!0})}function l(e){return Object(r.a)({url:"/api/member/checkemail",data:e,forceLogin:!0})}function f(e){return Object(r.a)({url:"/api/member/modifyemail",data:e,forceLogin:!0})}function d(e){return Object(r.a)({url:"/api/member/verifypaypwdcode",data:e,forceLogin:!0})}function v(e){return Object(r.a)({url:"/api/member/modifypaypassword",data:e,forceLogin:!0})}function h(e){return Object(r.a)({url:"/api/member/paypwdcode",data:e,forceLogin:!0})}function O(e){return Object(r.a)({url:"/api/member/pwdmobliecode",data:e,forceLogin:!0})}},605:function(e,t,n){},606:function(e,t,n){},698:function(e,t,n){"use strict";n(605)},699:function(e,t,n){"use strict";n(606)},779:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(73),n(7),n(48),n(50),n(694),n(328),n(24),n(25),n(23),n(29),n(18),n(30),n(550)),c=n(33),m=n(12);n(551);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}var f={name:"info",layout:"member",components:{},data:function(){return{memberInfo:{userHeadImg:"",userName:"",nickName:"",email:"",tell:""},infoRules:{nickName:[{required:!0,message:"请输入昵称",trigger:"blur"},{max:30,message:"最大长度为30个字符",trigger:"blur"}]},activeName:"first",loading:!0,uploadActionUrl:c.a.baseUrl+"/api/upload/headimg",imgUrl:"",yes:!0}},created:function(){this.getInfo()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getInfo:function(){var e=this;Object(o.b)().then((function(t){0==t.code&&(e.memberInfo.userHeadImg=t.data.headimg,e.memberInfo.userName=t.data.username,e.memberInfo.nickName=t.data.nickname,e.memberInfo.email=t.data.email,e.memberInfo.tell=t.data.mobile),e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error(t.message)}))},handleClick:function(e,t){},saveInfo:function(){var e=this;this.$refs.infoRef.validate((function(t){if(!t)return!1;Object(o.c)({nickname:e.memberInfo.nickName}).then((function(t){0==t.code&&(e.$message({message:"修改昵称成功",type:"success"}),e.getInfo(),e.$store.dispatch("member/member_detail",{refresh:1}))})).catch((function(t){e.$message.error(t.message)}))}))},handleAvatarSuccess:function(e,t){this.imgUrl=e.data.pic_path,this.memberInfo.userHeadImg=URL.createObjectURL(t.raw)},uploadHeadImg:function(){var e=this;Object(o.a)({headimg:this.imgUrl}).then((function(t){0==t.code&&(e.$message({message:"头像修改成功",type:"success"}),e.$store.dispatch("member/member_detail",{refresh:1}))})).catch((function(t){e.$message.error(t.message)}))}},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(m.b)(["defaultHeadImage"]))},d=f,v=(n(698),n(699),n(6)),component=Object(v.a)(d,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"member-info"},[t("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[t("el-tab-pane",{attrs:{label:"基本信息",name:"first"}},[t("el-form",{ref:"infoRef",attrs:{model:e.memberInfo,rules:e.infoRules,"label-width":"80px"}},[e.memberInfo.userName?t("el-form-item",{attrs:{label:"账号"}},[t("p",[e._v(e._s(e.memberInfo.userName))])]):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"手机号"}},[e.memberInfo.tell?t("p",[e._v(e._s(e.memberInfo.tell))]):t("p",{staticClass:"toBind",on:{click:function(t){return e.$router.push({path:"/member/security"})}}},[e._v("去绑定")])]),e._v(" "),t("el-form-item",{attrs:{label:"昵称",prop:"nickName"}},[t("el-input",{model:{value:e.memberInfo.nickName,callback:function(t){e.$set(e.memberInfo,"nickName",t)},expression:"memberInfo.nickName"}})],1)],1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{size:"medium",type:"primary"},on:{click:e.saveInfo}},[e._v("保存")])],1)],1),e._v(" "),t("el-tab-pane",{attrs:{label:"头像照片",name:"second"}},[t("div",{staticClass:"preview"},[t("div",{staticClass:"title"},[e._v("头像预览")]),e._v(" "),t("div",{staticClass:"content"},[e._v("\n              完善个人信息资料，上传头像图片有助于您结识更多的朋友。\n              "),t("br"),e._v("\n              头像最佳默认尺寸为120x120像素。\n            ")])]),e._v(" "),t("el-upload",{staticClass:"upload",attrs:{action:e.uploadActionUrl,"show-file-list":!1,"on-success":e.handleAvatarSuccess,accept:".png,.jpg,.jpeg"}},[t("div",{staticClass:"img-wrap"},[e.memberInfo.userHeadImg?t("img",{attrs:{src:e.$img(e.memberInfo.userHeadImg)}}):t("img",{attrs:{src:e.$img(e.defaultHeadImage)}})])]),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{size:"medium",type:"primary"},on:{click:e.uploadHeadImg}},[e._v("保存")])],1)],1)],1)],1)])}),[],!1,null,"64674d58",null);t.default=component.exports}}]);