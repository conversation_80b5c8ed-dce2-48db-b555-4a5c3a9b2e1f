(window.webpackJsonp=window.webpackJsonp||[]).push([[32],{562:function(t,e,o){"use strict";o.d(e,"b",(function(){return c})),o.d(e,"a",(function(){return r}));var n=o(1);function c(t){return Object(n.a)({url:"/api/goodscollect/page",data:t,forceLogin:!0})}function r(t){return Object(n.a)({url:"/api/goodscollect/delete",data:t,forceLogin:!0})}},599:function(t,e,o){},688:function(t,e,o){"use strict";o(599)},775:function(t,e,o){"use strict";o.r(e);o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),c=(o(73),o(562)),r=o(12),l=o(206);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var f={name:"collection",layout:"member",components:{},data:function(){return{goodsInfo:{page:1,page_size:10},shopInfo:{page:1,page_size:10},activeName:"goods",goodsTotal:0,goodsList:[],loading:!0,yes:!0,list:[],page:1,pageSize:5}},created:function(){this.getGoodsCollect(),this.getGoodsRecommend()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},methods:{getGoodsRecommend:function(){var t=this;Object(l.e)({page:this.page,page_size:this.pageSize}).then((function(e){0==e.code&&(t.list=e.data.list),t.loading=!1})).catch((function(e){t.loading=!1}))},getGoodsCollect:function(){var t=this;Object(c.b)(this.goodsInfo).then((function(e){t.goodsTotal=e.data.count,t.goodsList=e.data.list,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},deleteGoods:function(t){var e=this;Object(c.a)({goods_id:t}).then((function(t){0==t.code&&(e.$message({message:"取消关注成功",type:"success"}),e.getGoodsCollect())})).catch((function(t){e.$message.error(t.message)}))},handleClick:function(t,e){"0"==t.index&&(this.loading=!0,this.getGoodsCollect())},handleSizeChange:function(t){this.goodsInfo.page_size=t,this.loading=!0,this.getGoodsCollect()},handleCurrentChange:function(t){this.goodsInfo.page=t,this.loading=!0,this.getGoodsCollect()},imageError:function(t){this.goodsList[t].sku_image=this.defaultGoodsImage},imageImgError:function(t){this.list[t].sku_image=this.defaultGoodsImage}}},m=f,h=(o(688),o(6)),component=Object(h.a)(m,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"collection"},[e("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"宝贝",name:"goods"}},[t.goodsList.length>0?e("div",[e("div",{staticClass:"goods"},t._l(t.goodsList,(function(o,n){return e("div",{key:o.goods_id,staticClass:"goods-wrap"},[e("div",{staticClass:"goods-item"},[e("div",{staticClass:"img",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+o.sku_id})}}},[e("img",{attrs:{src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}}),t._v(" "),e("i",{staticClass:"del el-icon-delete",on:{click:function(e){return e.stopPropagation(),t.deleteGoods(o.goods_id)}}})]),t._v(" "),e("div",{staticClass:"goods-name"},[t._v(t._s(o.goods_name))]),t._v(" "),e("div",{staticClass:"price"},[t._v("￥"+t._s(o.price))])])])})),0),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{"current-page":t.goodsInfo.page,"page-size":t.goodsInfo.page_size,background:"","pager-count":5,"prev-text":"上一页","next-text":"下一页","hide-on-single-page":"",total:t.goodsTotal},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]):t.loading||t.goodsList.length?t._e():e("div",{staticClass:"empty"},[t._v("您还没有关注商品哦")])])],1)],1),t._v(" "),e("div",{staticClass:"goods-recommended"},[t._m(0),t._v(" "),e("div",{staticClass:"body-wrap"},[e("ul",{staticClass:"goods-list"},t._l(t.list,(function(o,n){return e("li",{key:n,attrs:{title:o.goods_name},on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+o.sku_id})}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageImgError(n)}}})]),t._v(" "),e("h3",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v(t._s(o.discount_price)+"元")]),t._v(" "),e("del",[t._v(t._s(o.market_price)+"元")])])])})),0)])])])}),[function(){var t=this._self._c;return t("div",{staticClass:"youLike"},[t("span",[this._v("猜你喜欢")])])}],!1,null,"1e841a55",null);e.default=component.exports}}]);