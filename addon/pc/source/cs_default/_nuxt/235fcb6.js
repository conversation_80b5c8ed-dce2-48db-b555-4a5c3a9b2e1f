(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{563:function(t,e,n){"use strict";n.d(e,"d",(function(){return l})),n.d(e,"e",(function(){return r})),n.d(e,"a",(function(){return c})),n.d(e,"g",(function(){return d})),n.d(e,"b",(function(){return f})),n.d(e,"c",(function(){return y})),n.d(e,"f",(function(){return v}));var o=n(1);function l(t){return Object(o.a)({url:"/api/pay/info",data:t,forceLogin:!0})}function r(t){return Object(o.a)({url:"/api/pay/type",data:t,forceLogin:!0})}function c(t){return Object(o.a)({url:"/api/pay/status",data:t,forceLogin:!0})}function d(t){return Object(o.a)({url:"/api/pay/pay",data:t,forceLogin:!0})}function f(){return Object(o.a)({url:"/offlinepay/api/pay/config"})}function y(t){return Object(o.a)({url:"/offlinepay/api/pay/info",data:t,forceLogin:!0})}function v(t){return Object(o.a)({url:"/offlinepay/api/pay/pay",data:t,forceLogin:!0})}},580:function(t,e,n){},581:function(t,e,n){},667:function(t,e,n){"use strict";n(580)},668:function(t,e,n){"use strict";n(581)},764:function(t,e,n){"use strict";n.r(e);n(56),n(23),n(7),n(18),n(24),n(75),n(92),n(74),n(73);var o=n(563),l=n(33),r={name:"pay",components:{},data:function(){return{orderOpen:!1,outTradeNo:"",payInfo:{pay_money:0},payIndex:0,payTypeList:[{name:"支付宝支付",icon:"iconzhifubaozhifu-",type:"alipay"},{name:"微信支付",icon:"iconweixinzhifu",type:"wechatpay"},{name:"线下支付",icon:"",type:"offlinepay"}],payUrl:"",timer:null,payQrcode:"",openQrcode:!1,loading:!0,test:null,dialogVisible:!1,offlinepayConfig:[],offlinepayInfo:{out_trade_no:"",imgs:"",imgList:[],desc:""},type:"add",dialogImageUrl:"",imgDialogVisible:!1,uploadActionUrl:l.a.baseUrl+"/offlinepay/api/pay/uploadimg",hide:!1,imgList:[]}},created:function(){this.$route.query.code?(this.outTradeNo=this.$route.query.code,this.getPayInfo(),this.getPayType(),this.getOfflinepayPayInfoFn(this.outTradeNo)):this.$router.push({path:"/"})},methods:{getPayInfo:function(){var t=this;Object(o.d)({out_trade_no:this.outTradeNo,forceLogin:!0}).then((function(e){e.code,e.message;e.data&&(t.payInfo=e.data),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error({message:e.message,duration:2e3,onClose:function(){t.$router.push({path:"/member/order_list"})}})}))},getPayType:function(){var t=this;Object(o.e)({}).then((function(e){var code=e.code;e.message,e.data;code>=0&&(""==e.data.pay_type?t.payTypeList=[]:(t.payTypeList=t.payTypeList.filter((function(t,n){return-1!=e.data.pay_type.indexOf(t.type)})),t.payTypeList.some((function(t){return"offlinepay"==t.type}))&&t.getOfflinepayConfigFn()))})).catch((function(e){t.$message.error(e.message)}))},payTypeChange:function(t){"edit"!=this.type&&(this.payIndex=t,this.offlinepayInfo={out_trade_no:"",imgs:"",imgList:[],desc:""})},getOfflinepayConfigFn:function(){var t=this;Object(o.b)({}).then((function(e){var code=e.code,data=(e.message,e.data);if(code>=0){var n=data.value;Object.keys(n).forEach((function(e){"1"==n[e].status&&(n[e].key=e,t.offlinepayConfig.push(n[e]))}))}})).catch((function(){}))},copy:function(t){this.$copy(t)},getOfflinepayPayInfoFn:function(t){var e=this;Object(o.c)({out_trade_no:t}).then((function(n){var code=n.code,data=(n.message,n.data);code>=0&&data?(e.type="edit",e.payIndex=2,e.offlinepayInfo=data,e.offlinepayInfo.imgList=e.offlinepayInfo.imgs?e.offlinepayInfo.imgs.split(",").map((function(t){return{url:t}})):[],e.imgList=e.$util.deepClone(e.offlinepayInfo.imgList),e.$forceUpdate()):(e.type="add",e.offlinepayInfo={out_trade_no:t,imgs:"",imgList:[],desc:""})})).catch((function(){}))},handleSuccess:function(t,e){this.offlinepayInfo.imgList=this.offlinepayInfo.imgList.concat({url:t.data.pic_path}),this.offlinepayInfo.imgs=this.offlinepayInfo.imgList.map((function(t){return t.url})).toString(),this.offlinepayInfo.imgList.length>=5&&(this.hide=!0)},handleRemove:function(t,e){this.offlinepayInfo.imgList=e.map((function(t){return{url:t.url}}))},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.imgDialogVisible=!0},handleExceed:function(t,e){this.$message.warning("上传图片最大数量为5张")},checkPayStatus:function(){var t=this;this.timer=setInterval((function(){Object(o.a)({out_trade_no:t.outTradeNo}).then((function(e){var code=e.code,data=(e.message,e.data);code>=0&&(0==code?2==data.pay_status&&(clearInterval(t.timer),t.dialogVisible=!1,t.$router.push({path:"/pay/result?code="+t.payInfo.out_trade_no})):clearInterval(t.timer))})).catch((function(e){clearInterval(t.timer),t.$router.push({path:"/"})}))}),2e3)},pay:function(){var t=this,e=this.payTypeList[this.payIndex];if(e){var n=encodeURIComponent(l.a.webDomain+"/pay/result?code="+this.outTradeNo);if("offlinepay"!=e.type)Object(o.g)({out_trade_no:this.payInfo.out_trade_no,pay_type:e.type,app_type:"pc",return_url:n}).then((function(n){var code=n.code,o=n.message;n.data;if(code>=0)switch(t.checkPayStatus(),e.type){case"alipay":t.payUrl=n.data.data,window.open(t.payUrl),t.open();break;case"wechatpay":t.payQrcode=n.data.qrcode,t.openQrcode=!0}else t.$message({message:o,type:"warning"})})).catch((function(e){t.$message.error(e.message)}));else{if(!this.offlinepayInfo.imgList.length)return void this.$message({message:"请至少上传一张凭证",type:"warning"});this.offlinepayInfo.out_trade_no=this.outTradeNo,Object(o.f)(this.offlinepayInfo).then((function(e){var code=e.code,n=e.message;e.data;if(code>=0){t.$router.push({path:{1:"/member/order_detail",2:"/member/order_detail_pickup",3:"/member/order_detail_local_delivery",4:"/member/order_detail_virtual"}[t.payInfo.order_type],query:{order_id:t.payInfo.order_id}})}else t.$message({message:n,type:"warning"})})).catch((function(){}))}}},open:function(){this.dialogVisible=!0},goIndex:function(){clearInterval(this.timer),this.dialogVisible=!1,this.$router.push({path:"/"})},goOrderList:function(){clearInterval(this.timer),this.dialogVisible=!1,this.$router.push({path:"/member/order_list"})}}},c=r,d=(n(667),n(668),n(6)),component=Object(d.a)(c,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"pay-wrap"},[e("div",{staticClass:"item-block"},[e("div",{staticClass:"payment-detail"},[e("div",{staticClass:"payment-media"},[e("el-row",[e("el-col",{attrs:{span:4}},[e("div",{staticClass:"media-left"},[e("i",{staticClass:"el-icon-circle-check ns-text-color"})])]),t._v(" "),e("el-col",{attrs:{span:16}},[e("div",{staticClass:"media-body"},[e("el-row",[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"payment-text"},[t._v("您的订单已提交成功，正在等待处理！")]),t._v(" "),e("div",[e("span",[t._v("应付金额：")]),t._v(" "),e("span",{staticClass:"payment-money ns-text-color"},[t._v("￥"+t._s(t.payInfo.pay_money)+"元")])])]),t._v(" "),e("el-col",{attrs:{span:12}})],1)],1)]),t._v(" "),e("el-col",{attrs:{span:4}},[e("div",{staticClass:"media-right"},[e("div",{staticClass:"el-button--text",on:{click:function(e){t.orderOpen?t.orderOpen=!1:t.orderOpen=!0}}},[t._v("\n                订单信息\n                "),e("i",{staticClass:"el-icon-arrow-down",class:t.orderOpen?"rotate":""})])])])],1)],1),t._v(" "),t.orderOpen?e("div",{staticClass:"order-info"},[e("el-row",[e("el-col",{staticClass:"order-info-left",attrs:{span:4}}),t._v(" "),e("el-col",{attrs:{span:20}},[e("div",{staticClass:"line"}),t._v(" "),e("div",{staticClass:"order-item"},[e("div",{staticClass:"item-label"},[t._v("交易单号：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(t.payInfo.out_trade_no))])]),t._v(" "),e("div",{staticClass:"order-item"},[e("div",{staticClass:"item-label"},[t._v("订单内容：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(t.payInfo.pay_detail))])]),t._v(" "),e("div",{staticClass:"order-item"},[e("div",{staticClass:"item-label"},[t._v("订单金额：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v("￥"+t._s(t.payInfo.pay_money))])]),t._v(" "),e("div",{staticClass:"order-item"},[e("div",{staticClass:"item-label"},[t._v("创建时间：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(t.$timeStampTurnTime(t.payInfo.create_time)))])])])],1)],1):t._e()])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"block-text"},[t._v("支付方式")]),t._v(" "),t.payTypeList.length?e("div",{staticClass:"pay-type-list"},[t._l(t.payTypeList,(function(n,o){return["offlinepay"!=n.type||"offlinepay"==n.type&&"OrderPayNotify"==t.payInfo.event?e("div",{key:o,staticClass:"pay-type-item",class:{active:t.payIndex==o,disable:"edit"==t.type&&"offlinepay"!=n.type},on:{click:function(e){return t.payTypeChange(o)}}},[t._v("\n          "+t._s(n.name)+"\n        ")]):t._e()]})),t._v(" "),e("div",{staticClass:"clear"})],2):e("div",{staticClass:"no-pay-type"},[e("p",[t._v("商家未配置支付方式")])]),t._v(" "),"offlinepay"===t.payTypeList[t.payIndex].type?e("div",{staticClass:"offlinepay"},[t._l(t.offlinepayConfig,(function(n){return["bank"==n.key?e("div",{key:n.key,staticClass:"offlinepay-item bank"},[e("div",{staticClass:"title"},[t._v("\n          银行卡\n          ")]),t._v(" "),e("div",{staticClass:"item"},[e("div",{staticClass:"item-label"},[t._v("银行名称：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(n.bank_name))])]),t._v(" "),e("div",{staticClass:"item"},[e("div",{staticClass:"item-label"},[t._v("账号名称：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(n.account_name))])]),t._v(" "),e("div",{staticClass:"item"},[e("div",{staticClass:"item-label"},[t._v("银行账号：")]),t._v(" "),e("div",{staticClass:"item-value"},[e("span",[t._v(t._s(n.account_number))]),t._v(" "),e("span",{staticClass:"copy ns-text-color",on:{click:function(e){return t.copy(n.account_number)}}},[t._v("复制")])])]),t._v(" "),e("div",{staticClass:"item"},[e("div",{staticClass:"item-label"},[t._v("开户支行：")]),t._v(" "),e("div",{staticClass:"item-value"},[t._v(t._s(n.branch_name))])])]):e("div",{staticClass:"offlinepay-item"},[e("div",{staticClass:"title"},[t._v("\n            "+t._s("wechat"==n.key?"微信支付":"支付宝支付")+"\n          ")]),t._v(" "),e("div",{staticClass:"code"},[e("div",{staticClass:"centent"},[e("el-image",{key:n.key,staticClass:"qrcode",attrs:{src:t.$util.img(n.payment_code),fit:"cover"}})],1),t._v(" "),e("div",{staticClass:"bottom"},[t._v("\n              "+t._s(n.account_name)+"\n            ")])])])]}))],2):t._e()]),t._v(" "),"offlinepay"===t.payTypeList[t.payIndex].type?e("div",{staticClass:"item-block"},[e("div",{staticClass:"offlinepay-form"},[e("div",{staticClass:"upload-wrap-title"},[e("div",{staticClass:"title"},[t._v("上传支付凭证")]),t._v(" "),e("div",{staticClass:"title-tips"},[t._v("最多还可上传"+t._s(5-t.offlinepayInfo.imgList.length)+"张")])]),t._v(" "),e("div",{staticClass:"upload-wrap"},[e("el-upload",{ref:"upload",class:{ishide:t.hide},attrs:{action:t.uploadActionUrl,"file-list":t.imgList,"list-type":"picture-card","on-success":function(e,n){return t.handleSuccess(e,n)},"on-preview":t.handlePictureCardPreview,"on-remove":function(e,n){return t.handleRemove(e,n)},"on-exceed":t.handleExceed,multiple:"",drag:"",limit:5}},[e("i",{staticClass:"el-icon-plus"})]),t._v(" "),e("el-dialog",{attrs:{visible:t.imgDialogVisible},on:{"update:visible":function(e){t.imgDialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})])],1),t._v(" "),e("div",{staticClass:"bottom"},[e("el-input",{staticClass:"remark",attrs:{placeholder:"请详细说明您的支付情况",type:"textarea",maxlength:200},model:{value:t.offlinepayInfo.desc,callback:function(e){t.$set(t.offlinepayInfo,"desc",e)},expression:"offlinepayInfo.desc"}})],1)])]):t._e(),t._v(" "),t.payTypeList.length?e("div",{staticClass:"item-block"},[e("div",{staticClass:"order-submit"},[e("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:t.pay}},[t._v("立即支付")])],1),t._v(" "),e("div",{staticClass:"clear"})]):t._e(),t._v(" "),e("el-dialog",{staticClass:"confirm-pay-wrap",attrs:{title:"请确认支付是否完成",visible:t.dialogVisible,width:"23%",top:"30vh"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"info-wrap"},[e("i",{staticClass:"el-message-box__status el-icon-warning"}),t._v(" "),e("span",[t._v("完成支付前请根据您的情况点击下面的按钮")])]),t._v(" "),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{size:"small"},on:{click:t.goIndex}},[t._v("返回首页")]),t._v(" "),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.goOrderList}},[t._v("已完成支付")])],1)]),t._v(" "),e("el-dialog",{attrs:{title:"请用微信扫码支付",visible:t.openQrcode,width:"300px",center:""},on:{"update:visible":function(e){t.openQrcode=e}}},[e("div",{staticClass:"wechatpay-box"},[e("img",{attrs:{src:t.payQrcode}})])])],1)}),[],!1,null,"3b1a1148",null);e.default=component.exports}}]);