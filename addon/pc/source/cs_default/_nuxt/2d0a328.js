(window.webpackJsonp=window.webpackJsonp||[]).push([[59],{552:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return f})),n.d(t,"d",(function(){return v}));var c=n(1);function r(e){return Object(c.a)({url:"/api/verify/verifyInfo",data:e,forceLogin:!0})}function o(e){return Object(c.a)({url:"/api/verify/verify",data:e,forceLogin:!0})}function f(e){return Object(c.a)({url:"/api/verify/getVerifyType",data:e})}function v(e){return Object(c.a)({url:"/api/verify/lists",data:e,forceLogin:!0})}},631:function(e,t,n){},724:function(e,t,n){"use strict";n(631)},798:function(e,t,n){"use strict";n.r(t);n(73),n(31);var c=n(552),r={name:"verification",components:{},data:function(){return{verify_code:"",yes:!0}},created:function(){},layout:"member",mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{confirm:function(){var e=this;if(!/[\S]+/.test(this.verify_code))return this.$message({message:"请输入核销码",type:"warning"}),!1;Object(c.c)({verify_code:this.verify_code}).then((function(t){t.code>=0?e.$router.push({path:"/order/verification_detail",query:{code:e.verify_code}}):e.$message({message:t.message,type:"warning"})})).catch((function(t){e.$message.error(t.message)}))}}},o=(n(724),n(6)),component=Object(o.a)(r,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card order-list"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("核销台")])]),e._v(" "),t("div",{staticClass:"ns-verification"},[t("div",{staticClass:"ns-verification-flow"},[t("div",{staticClass:"ns-verification-icon"},[t("div",[t("i",{staticClass:"iconfont icon-shurutianxiebi"})]),e._v(" "),t("p",[e._v("输入核销码")])]),e._v(" "),t("div",[t("i",{staticClass:"iconfont icon-jiang-copy"})]),e._v(" "),t("div",{staticClass:"ns-verification-icon"},[t("div",[t("i",{staticClass:"iconfont icon-hexiao"})]),e._v(" "),t("p",[e._v("核销")])])]),e._v(" "),t("div",{staticClass:"ns-verification-wrap"},[t("el-input",{attrs:{placeholder:"请输入核销码"},model:{value:e.verify_code,callback:function(t){e.verify_code=t},expression:"verify_code"}}),e._v(" "),t("el-button",{on:{click:e.confirm}},[e._v("确认")])],1)])])],1)}),[],!1,null,"66d369ce",null);t.default=component.exports}}]);