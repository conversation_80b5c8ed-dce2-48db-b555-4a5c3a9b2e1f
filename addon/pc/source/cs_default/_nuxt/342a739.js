(window.webpackJsonp=window.webpackJsonp||[]).push([[8],{586:function(e,t,r){},673:function(e,t,r){"use strict";r(586)},765:function(e,t,r){"use strict";r.r(t);r(215),r(31),r(74),r(73),r(317),r(64);var o=r(199),n=r(27),c={name:"register",layout:"login",components:{},data:function(){var e=this,t=this;return{registerForm:{username:"",password:"",checkPass:"",code:"",mobile:"",vercode:"",dynacode:"",key:""},registerRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,validator:function(e,r,o){var n=t.registerConfig;if(!r)return o(new Error("请输入密码"));if(n.pwd_len>0){if(r.length<n.pwd_len)return o(new Error("密码长度不能小于"+n.pwd_len+"位"));o()}if(""!=n.pwd_complexity){var c="密码需包含",l="";if(-1!=n.pwd_complexity.indexOf("number")?(l+="(?=.*?[0-9])",c+="数字"):-1!=n.pwd_complexity.indexOf("letter")?(l+="(?=.*?[a-z])",c+="、小写字母"):-1!=n.pwd_complexity.indexOf("upper_case")?(l+="(?=.*?[A-Z])",c+="、大写字母"):-1!=n.pwd_complexity.indexOf("symbol")?(l+="(?=.*?[#?!@$%^&*-])",c+="、特殊字符"):(l+="",c+=""),(l=new RegExp(l)).test(r))return o(new Error(c));o()}},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,r,o){""===r?o(new Error("请再次输入密码")):r!==e.registerForm.password?o(new Error("两次输入密码不一致!")):o()},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}]},mobileRules:{mobile:[{required:!0,validator:function(e,t,r){if(!t)return r(new Error("手机号不能为空"));/^\d{11}$/.test(t)?r():r(new Error("请输入正确的手机号"))},trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}],dynacode:[{required:!0,message:"请输入短信动态码",trigger:"blur"}]},dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},ischecked:!1,agreement:"",aggrementVisible:!1,captcha:{id:"",img:""},registerConfig:{register:""},activeName:"first",reward:null,is_show:!1,bgWidth:"",bgHeight:""}},created:function(){this.getCode(),this.registerAggrement(),this.getRegisterConfig(),this.getRegisterReward(),this.bgWidth=document.documentElement.clientWidth+"px",this.bgHeight=document.documentElement.clientHeight+"px"},head:function(){return{title:"注册-"+this.$store.state.site.siteInfo.site_name}},methods:{closeRewardPopup:function(e){switch(this.is_show=!1,e){case"point":this.$router.push("/member/my_point");break;case"balance":this.$router.push("/member/account");break;case"growth":this.$router.push("/member");break;case"coupon":this.$router.push("/member/coupon");break;default:this.$router.push("/member"),this.is_show=!1}},getRegisterReward:function(){var e=this;Object(o.b)().then((function(t){if(t.code>=0){var data=t.data;1==data.is_use&&(data.value.point>0||data.value.balance>0||data.value.growth>0||data.value.coupon_list.length>0)&&(e.reward=data.value)}}))},sendMobileCode:function(e){var t=this;120==this.dynacodeData.seconds&&(this.$refs[e].clearValidate("dynacode"),this.$refs[e].validateField("mobile",(function(e){if(e)return!1})),this.$refs[e].validateField("vercode",(function(e){if(e)return!1;Object(o.f)({mobile:t.registerForm.mobile,captcha_id:t.captcha.id,captcha_code:t.registerForm.vercode}).then((function(e){e.code>=0&&(t.registerForm.key=e.data.key,120==t.dynacodeData.seconds&&null==t.dynacodeData.timer&&(t.dynacodeData.timer=setInterval((function(){t.dynacodeData.seconds--,t.dynacodeData.codeText=t.dynacodeData.seconds+"s后可重新获取"}),1e3)))})).catch((function(e){t.$message.error(e.message)}))})))},handleClick:function(e,t){},check:function(){this.ischecked=!this.ischecked},toLogin:function(){this.$router.push("/auth/login")},getRegisterConfig:function(){var e=this;Object(o.d)().then((function(t){t.code>=0&&(e.registerConfig=t.data.value,""==e.registerConfig.register?e.$message({message:"平台未启用注册",type:"warning",duration:2e3,onClose:function(){e.$router.push({name:"login",params:{third_party:!0}})}}):-1!=e.registerConfig.register.indexOf("username")?e.activeName="first":e.activeName="second")}))},register:function(){var e=this;this.$refs.registerRef.validate((function(t){if(!t)return!1;if(!e.ischecked)return e.$message({message:"请先阅读协议并勾选",type:"warning"});var data={username:e.registerForm.username.trim(),password:e.registerForm.password};if(!/^[A-Za-z0-9]+$/.test(data.username))return e.$message({message:"用户名只能输入数字跟英文",type:"warning"});""!=e.captcha.id&&(data.captcha_id=e.captcha.id,data.captcha_code=e.registerForm.code),e.$store.dispatch("member/register_token",data).then((function(t){t.code>=0&&(e.reward?e.is_show=!0:e.$router.push("/member"))})).catch((function(t){e.$message.error(t.message),e.getCode()}))}))},registerMobile:function(){var e=this;this.$refs.mobileRuleForm.validate((function(t){if(!t)return!1;if(!e.ischecked)return e.$message({message:"请先阅读协议并勾选",type:"warning"});var data={mobile:e.registerForm.mobile,key:e.registerForm.key,code:e.registerForm.dynacode};""!=e.captcha.id&&(data.captcha_id=e.captcha.id,data.captcha_code=e.registerForm.code),e.$store.dispatch("member/registerMobile_token",data).then((function(t){t.code>=0&&(e.reward?e.is_show=!0:e.$router.push("/member"))})).catch((function(t){e.$message.error(t.message),e.getCode()}))}))},aggrementClose:function(){this.aggrementVisible=!1},registerAggrement:function(){var e=this;Object(o.a)().then((function(t){t.code>=0&&(e.agreement=t.data)}))},getAggrement:function(){this.aggrementVisible=!0},getCode:function(){var e=this;Object(n.b)({captcha_id:"this.captcha.id"}).then((function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))})).catch((function(t){e.$message.error(t.message)}))}}},l=(r(673),r(6)),component=Object(l.a)(c,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"register"},[t("div",{staticClass:"box-card"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[-1!=e.registerConfig.register.indexOf("username")?t("el-tab-pane",{attrs:{label:"用户注册",name:"first"}},["first"==e.activeName?t("el-form",{ref:"registerRef",attrs:{model:e.registerForm,rules:e.registerRules,"label-width":"80px","label-position":"right","show-message":""}},[t("el-form-item",{attrs:{label:"用户名",prop:"username"}},[t("el-input",{attrs:{placeholder:"请输入用户名"},model:{value:e.registerForm.username,callback:function(t){e.$set(e.registerForm,"username",t)},expression:"registerForm.username"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"密码",prop:"password"}},[t("el-input",{attrs:{placeholder:"请输入密码",type:"password"},model:{value:e.registerForm.password,callback:function(t){e.$set(e.registerForm,"password",t)},expression:"registerForm.password"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"确认密码",prop:"checkPass"}},[t("el-input",{attrs:{placeholder:"请输入确认密码",type:"password"},model:{value:e.registerForm.checkPass,callback:function(t){e.$set(e.registerForm,"checkPass",t)},expression:"registerForm.checkPass"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"验证码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入验证码",maxlength:"4"},model:{value:e.registerForm.code,callback:function(t){e.$set(e.registerForm,"code",t)},expression:"registerForm.code"}},[t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCode}})])],2)],1)],1):e._e(),e._v(" "),t("div",{staticClass:"xy",on:{click:e.check}},[t("div",{staticClass:"xy-wrap"},[t("div",{staticClass:"iconfont",class:e.ischecked?"icon-xuanze-duoxuan":"icon-xuanze"}),e._v(" "),t("div",{staticClass:"content"},[e._v("\n              阅读并同意\n              "),t("b",{on:{click:function(t){return t.stopPropagation(),e.getAggrement.apply(null,arguments)}}},[e._v("《服务协议》")])])]),e._v(" "),t("div",{staticClass:"toLogin",on:{click:e.toLogin}},[e._v("已有账号，立即登录")])]),e._v(" "),t("el-button",{on:{click:e.register}},[e._v("立即注册")])],1):e._e(),e._v(" "),-1!=e.registerConfig.register.indexOf("mobile")?t("el-tab-pane",{attrs:{label:"手机动态码注册",name:"second"}},["second"==e.activeName?t("el-form",{ref:"mobileRuleForm",attrs:{model:e.registerForm,rules:e.mobileRules}},[t("el-form-item",{attrs:{prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.registerForm.mobile,callback:function(t){e.$set(e.registerForm,"mobile",t)},expression:"registerForm.mobile"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-shouji-copy"})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"vercode"}},[t("el-input",{attrs:{autocomplete:"off",placeholder:"请输入验证码",maxlength:"4"},model:{value:e.registerForm.vercode,callback:function(t){e.$set(e.registerForm,"vercode",t)},expression:"registerForm.vercode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-yanzhengma"})]),e._v(" "),t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCode}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"dynacode"}},[t("el-input",{attrs:{maxlength:"4",placeholder:"请输入短信动态码"},model:{value:e.registerForm.dynacode,callback:function(t){e.$set(e.registerForm,"dynacode",t)},expression:"registerForm.dynacode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-dongtaima"})]),e._v(" "),t("template",{slot:"append"},[t("div",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"ns-text-color":"ns-text-color-gray",on:{click:function(t){return e.sendMobileCode("mobileRuleForm")}}},[e._v("\n                  "+e._s(e.dynacodeData.codeText)+"\n                ")])])],2)],1)],1):e._e(),e._v(" "),t("div",{staticClass:"xy",on:{click:e.check}},[t("div",{staticClass:"xy-wrap"},[t("div",{staticClass:"iconfont",class:e.ischecked?"icon-xuanze-duoxuan":"icon-xuanze"}),e._v(" "),t("div",{staticClass:"content"},[e._v("\n              阅读并同意\n              "),t("b",{on:{click:function(t){return t.stopPropagation(),e.getAggrement.apply(null,arguments)}}},[e._v("《服务协议》")])])]),e._v(" "),t("div",{staticClass:"toLogin",on:{click:e.toLogin}},[e._v("已有账号，立即登录")])]),e._v(" "),t("el-button",{on:{click:e.registerMobile}},[e._v("立即注册")])],1):e._e()],1),e._v(" "),t("el-dialog",{attrs:{title:e.agreement.title,visible:e.aggrementVisible,width:"60%","before-close":e.aggrementClose,"lock-scroll":!1,center:""},on:{"update:visible":function(t){e.aggrementVisible=t}}},[t("div",{staticClass:"xyContent",domProps:{innerHTML:e._s(e.agreement.content)}})])],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.is_show&&e.reward,expression:"is_show && reward"}],staticClass:"floatLayer-wrap",style:{width:e.bgWidth,height:e.bgHeight}},[t("div",{staticClass:"reward-wrap"},[t("img",{staticClass:"bg-img-head",attrs:{src:e.$util.img("public/uniapp/register_reward/register_reward_img.png"),mode:"widthFix"}}),e._v(" "),t("img",{staticClass:"bg-img-money",attrs:{src:e.$util.img("public/uniapp/register_reward/register_reward_money.png"),mode:"widthFix"}}),e._v(" "),t("img",{staticClass:"bg-img",attrs:{src:e.$util.img("public/uniapp/register_reward/register_reward_head.png"),mode:"widthFix"}}),e._v(" "),t("div",{staticClass:"wrap"},[t("div",[t("div",{staticClass:"reward-content"},[e.reward&&e.reward.point>0?t("div",{staticClass:"reward-item"},[t("div",{staticClass:"head"},[e._v("积分奖励")]),e._v(" "),t("div",{staticClass:"content"},[t("div",{staticClass:"info"},[t("div",[t("span",{staticClass:"num"},[e._v(e._s(e.reward.point))]),e._v(" "),t("span",{staticClass:"type"},[e._v("积分")])]),e._v(" "),t("div",{staticClass:"desc"},[e._v("用于下单时抵现或兑换商品等")])]),e._v(" "),t("div",{staticClass:"tip",on:{click:function(t){return e.closeRewardPopup("point")}}},[e._v("立即查看")])])]):e._e(),e._v(" "),e.reward&&e.reward.growth>0?t("div",{staticClass:"reward-item"},[t("div",{staticClass:"head"},[e._v("成长值")]),e._v(" "),t("div",{staticClass:"content"},[t("div",{staticClass:"info"},[t("div",[t("span",{staticClass:"num"},[e._v(e._s(e.reward.growth))]),e._v(" "),t("span",{staticClass:"type"},[e._v("成长值")])]),e._v(" "),t("div",{staticClass:"desc"},[e._v("用于提升会员等级")])]),e._v(" "),t("div",{staticClass:"tip",on:{click:function(t){return e.closeRewardPopup("growth")}}},[e._v("立即查看")])])]):e._e(),e._v(" "),e.reward&&e.reward.balance>0?t("div",{staticClass:"reward-item"},[t("div",{staticClass:"head"},[e._v("红包奖励")]),e._v(" "),t("div",{staticClass:"content"},[t("div",{staticClass:"info"},[t("div",[t("span",{staticClass:"num"},[e._v(e._s(e.reward.balance))]),e._v(" "),t("span",{staticClass:"type"},[e._v("元")])]),e._v(" "),t("div",{staticClass:"desc"},[e._v("不可提现下单时可用")])]),e._v(" "),t("div",{staticClass:"tip",on:{click:function(t){return e.closeRewardPopup("balance")}}},[e._v("立即查看")])])]):e._e(),e._v(" "),e.reward&&e.reward.coupon_list.length>0?t("div",{staticClass:"reward-item"},[t("div",{staticClass:"head"},[e._v("优惠券奖励")]),e._v(" "),e._l(e.reward.coupon_list,(function(r,o){return t("div",{key:o,staticClass:"content"},[t("div",{staticClass:"info"},[t("div",[t("span",{staticClass:"num coupon-name"},[e._v(e._s(r.coupon_name))])]),e._v(" "),r.at_least>0?t("div",{staticClass:"desc"},[e._v("满"+e._s(r.at_least)+e._s("discount"==r.type?"打"+r.discount+"折":"减"+r.money))]):t("div",{staticClass:"desc"},[e._v("无门槛，"+e._s("discount"==r.type?"打"+r.discount+"折":"减"+r.money))])]),e._v(" "),t("div",{staticClass:"tip",on:{click:function(t){return e.closeRewardPopup("coupon")}}},[e._v("立即查看")])])}))],2):e._e()])])]),e._v(" "),t("div",{staticClass:"close-btn",on:{click:function(t){return e.closeRewardPopup()}}},[t("i",{staticClass:"iconfont icon-guanbi"})])])])])}),[],!1,null,"7aeb04da",null);t.default=component.exports}}]);