(window.webpackJsonp=window.webpackJsonp||[]).push([[30],{597:function(e,t,r){},685:function(e,t,r){"use strict";var l=r(3),n=r(115).find,o=r(211),d="find",f=!0;d in[]&&Array(1)[d]((function(){f=!1})),l({target:"Array",proto:!0,forced:f},{find:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}}),o(d)},686:function(e,t,r){"use strict";r(597)},773:function(e,t,r){"use strict";r.r(t);r(56),r(317);var l=r(42),n=(r(31),r(73),r(151),r(685),r(7),r(18),r(207)),o=r(208),d={name:"address_edit",layout:"member",components:{},data:function(){var e=this;return{formData:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",latitude:0,longitude:0,is_default:1},addressValue:"",flag:!1,defaultRegions:[],rules:{name:[{required:!0,message:"请输入收货人姓名",trigger:"blur"}],mobile:[{required:!0,validator:function(e,t,r){if(!t)return r(new Error("手机号不能为空"));/^\d{11}$/.test(t)?r():r(new Error("请输入正确的手机号"))},trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],full_address:[{required:!0,validator:function(t,r,l){return e.formData.province_id?e.formData.city_id?e.district.length>0?e.formData.district_id?l():l(new Error("请选择区/县")):l():l(new Error("请选择市")):l(new Error("请选择省"))},trigger:"blur"}]},province:[],city:[],district:[],pickerValueArray:[],multiIndex:[0,0,0],isInitMultiArray:!1,isLoadDefaultAreas:!1,loading:!0,yes:!0}},created:function(){this.formData.id=this.$route.query.id,this.getAddressDetail(),this.getDefaultAreas(0,{level:0})},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},watch:{defaultRegions:{handler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];3===e.length&&e.join("")!==t.join("")&&this.handleDefaultRegions()},immediate:!0}},computed:{pickedArr:function(){return this.isInitMultiArray?[this.pickerValueArray[0],this.pickerValueArray[1],this.pickerValueArray[2]]:[this.pickerValueArray[0],this.city,this.district]}},methods:{changeProvice:function(e){var t=this;this.formData.province_id=e,this.getAreas(e,(function(data){return t.city=data}));var r;r=this.province.find((function(t){return t.id===e})),this.formData.city_id="",this.formData.district_id="",this.formData.full_address=r.name},changeCity:function(e){var t=this;this.formData.city_id=e,this.getAreas(e,(function(data){return t.district=data}));var r;r=this.city.find((function(t){return t.id===e})),this.formData.district_id="",this.formData.full_address=this.formData.full_address+"-"+r.name},changeDistrict:function(e){this.formData.district_id=e;var t;t=this.district.find((function(t){return t.id===e})),this.formData.full_address=this.formData.full_address+"-"+t.name},getAddressDetail:function(){var e=this;Object(n.d)({id:this.formData.id}).then((function(t){var data=t.data;null!=data&&(e.formData.name=data.name,e.formData.mobile=data.mobile,e.formData.telephone=data.telephone,e.formData.address=data.address,e.formData.full_address=data.full_address,e.formData.latitude=data.latitude,e.formData.longitude=data.longitude,e.formData.is_default=data.is_default,e.formData.province_id=data.province_id,e.formData.city_id=data.city_id,e.formData.district_id=data.district_id,e.defaultRegions=[data.province_id,data.city_id,data.district_id])}))},getAreas:function(e,t){Object(o.a)({pid:e}).then((function(e){if(0==e.code){var data=[];e.data.forEach((function(e,t){data.push(e)})),t&&t(data)}}))},getDefaultAreas:function(e,t){var r=this;Object(o.a)({pid:e}).then((function(e){if(0==e.code){var data=[],l=void 0;e.data.forEach((function(e,r){null!=t&&(0==t.level&&null!=t.province_id?l=t.province_id:1==t.level&&null!=t.city_id?l=t.city_id:2==t.level&&null!=t.district_id&&(l=t.district_id)),null==l&&0==r&&(l=e.id),data.push(e)})),r.pickerValueArray[t.level]=data,t.level+1<3?(t.level++,r.getDefaultAreas(l,t)):(r.isInitMultiArray=!0,r.isLoadDefaultAreas=!0),r.province=r.pickerValueArray[0]}setTimeout((function(){r.loading=!1}),500)})).catch((function(e){r.loading=!1}))},handleDefaultRegions:function(){var e=this,time=setInterval((function(){if(e.isLoadDefaultAreas){e.isInitMultiArray=!1;for(var i=0;i<e.defaultRegions.length;i++)for(var t=function(t){e.province=e.pickerValueArray[0],e.defaultRegions[i]==e.pickerValueArray[i][t].id&&(e.$set(e.multiIndex,i,t),e.getAreas(e.pickerValueArray[i][t].id,(function(data){e.city=data;for(var r=function(r){if(e.defaultRegions[1]==e.city[r].id)return e.$set(e.multiIndex,1,r),e.getAreas(e.city[r].id,(function(data){e.district=data;for(var u=0;u<e.district.length;u++)if(e.defaultRegions[2]==e.district[u].id){e.$set(e.multiIndex,2,u),e.handleValueChange({detail:{value:[t,r,u]}});break}})),"break"},l=0;l<e.city.length;l++){if("break"===r(l))break}})))},r=0;r<e.pickerValueArray[i].length;r++)t(r);e.isLoadDefaultAreas&&clearInterval(time)}}),100)},handleValueChange:function(e){var t=Object(l.a)(e.detail.value,3),r=t[0],n=t[1],o=t[2],d=Object(l.a)(this.pickedArr,3),f=d[0],c=d[1],m=d[2],address=[f[r],c[n],m[o]];this.formData.full_address="";for(var i=0;i<address.length;i++)this.formData.full_address?this.formData.full_address=this.formData.full_address+"-"+address[i].name:this.formData.full_address=this.formData.full_address+address[i].name},saveAddress:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var data={name:t.formData.name,mobile:t.formData.mobile,telephone:t.formData.telephone,province_id:t.formData.province_id,city_id:t.formData.city_id,district_id:t.formData.district_id,community_id:"",address:t.formData.address,full_address:t.formData.full_address,latitude:t.formData.latitude,longitude:t.formData.longitude,is_default:t.formData.is_default,url:"add"};t.formData.id&&(data.url="edit",data.id=t.formData.id),t.flag||(t.flag=!0,Object(n.m)(data).then((function(e){0==e.code?t.$router.push({path:"/member/delivery_address"}):(t.flag=!1,t.$message({message:e.message,type:"warning"}))})).catch((function(e){t.flag=!1,t.$message.error(e.message)})))}))}}},f=d,c=(r(686),r(6)),component=Object(c.a)(f,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("编辑收货地址")])]),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"ns-member-address-list"},[t("el-form",{ref:"ruleForm",attrs:{model:e.formData,rules:e.rules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{staticClass:"ns-len-input",attrs:{placeholder:"收货人姓名"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"收货人手机号"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"电话"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"收货人固定电话（选填）"},model:{value:e.formData.telephone,callback:function(t){e.$set(e.formData,"telephone","string"==typeof t?t.trim():t)},expression:"formData.telephone"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"地址",prop:"full_address"}},[t("el-select",{attrs:{value:e.formData.province_id,placeholder:"请选择省"},on:{change:e.changeProvice}},e._l(e.province,(function(option){return t("el-option",{key:option.id,attrs:{label:option.name,value:option.id}},[e._v("\n              "+e._s(option.name)+"\n            ")])})),1),e._v(" "),t("el-select",{attrs:{value:e.formData.city_id,placeholder:"请选择市"},on:{change:e.changeCity}},e._l(e.city,(function(option){return t("el-option",{key:option.id,attrs:{label:option.name,value:option.id}},[e._v("\n              "+e._s(option.name)+"\n            ")])})),1),e._v(" "),t("el-select",{attrs:{value:e.formData.district_id,placeholder:"请选择区/县"},on:{change:e.changeDistrict}},e._l(e.district,(function(option){return t("el-option",{key:option.id,attrs:{label:option.name,value:option.id}},[e._v("\n              "+e._s(option.name)+"\n            ")])})),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[t("el-input",{staticClass:"ns-len-input",attrs:{autocomplete:"off",placeholder:"定位到小区、街道、写字楼"},model:{value:e.formData.address,callback:function(t){e.$set(e.formData,"address","string"==typeof t?t.trim():t)},expression:"formData.address"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"是否默认"}},[t("el-radio-group",{model:{value:e.formData.is_default,callback:function(t){e.$set(e.formData,"is_default",t)},expression:"formData.is_default"}},[t("el-radio",{attrs:{label:1}},[e._v("是")]),e._v(" "),t("el-radio",{attrs:{label:0}},[e._v("否")])],1)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(t){return e.saveAddress("ruleForm")}}},[e._v("保存")])],1)],1)],1)])],1)}),[],!1,null,"3eaeebb2",null);t.default=component.exports}}]);