(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{540:function(t,e,r){"use strict";r.d(e,"h",(function(){return o})),r.d(e,"i",(function(){return d})),r.d(e,"g",(function(){return l})),r.d(e,"f",(function(){return c})),r.d(e,"e",(function(){return _})),r.d(e,"a",(function(){return f})),r.d(e,"d",(function(){return v})),r.d(e,"b",(function(){return m})),r.d(e,"c",(function(){return h})),r.d(e,"j",(function(){return y}));var n=r(1);function o(t){return Object(n.a)({url:"/api/orderrefund/refundData",data:t,forceLogin:!0})}function d(t){return Object(n.a)({url:"/api/orderrefund/refundDataBatch",data:t,forceLogin:!0})}function l(t){return Object(n.a)({url:"/api/orderrefund/refund",data:t,forceLogin:!0})}function c(t){return Object(n.a)({url:"/api/orderrefund/detail",data:t,forceLogin:!0})}function _(t){return Object(n.a)({url:"/api/orderrefund/delivery",data:t,forceLogin:!0})}function f(t){return Object(n.a)({url:"/api/orderrefund/cancel",data:t,forceLogin:!0})}function v(t){return Object(n.a)({url:"/api/ordercomplain/detail",data:t,forceLogin:!0})}function m(t){return Object(n.a)({url:"/api/ordercomplain/complain",data:t,forceLogin:!0})}function h(t){return Object(n.a)({url:"/api/ordercomplain/cancel",data:t,forceLogin:!0})}function y(t){return Object(n.a)({url:"/api/orderrefund/lists",data:t,forceLogin:!0})}},629:function(t,e,r){},630:function(t,e,r){},722:function(t,e,r){"use strict";r(629)},723:function(t,e,r){"use strict";r(630)},797:function(t,e,r){"use strict";r.r(e);r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=(r(73),r(540)),d=r(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var c={name:"refund_detail",components:{},data:function(){return{orderGoodsId:"",isSub:!1,detail:{refund_action:[]},formData:{refund_delivery_name:"",refund_delivery_no:"",refund_delivery_remark:""},actionOpen:!1,refundDeliveryDialog:!1,loading:!0,yes:!0}},created:function(){this.$route.query.order_goods_id&&(this.orderGoodsId=this.$route.query.order_goods_id),this.getRefundDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(d.b)(["defaultGoodsImage"])),layout:"member",mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},methods:{getRefundDetail:function(){var t=this;this.loading=!0,Object(o.f)({order_goods_id:this.orderGoodsId}).then((function(e){var code=e.code,data=(e.message,e.data);code>=0?t.detail=data:t.$message({message:"未获取到该订单项退款信息！",type:"warning",duration:2e3,onClose:function(){t.$router.push({path:"/member/activist"})}}),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error({message:e.message,duration:2e3,onClose:function(){t.$router.push({path:"/member/activist"})}})}))},refundAction:function(t){switch(t){case"orderRefundCancel":this.cancleRefund(this.detail.order_goods_id);break;case"orderRefundDelivery":this.refundDeliveryDialog=!0;break;case"orderRefundAsk":this.$router.push({path:"/order/refund?order_goods_id="+this.detail.order_goods_id});break;case"complain":this.$router.push({path:"/order/complain?order_goods_id="+this.detail.order_goods_id});break;case"orderRefundApply":this.$router.push({path:"/order/refund",query:{order_goods_id:this.detail.order_goods_id,order_id:this.detail.order_id}})}},refundGoods:function(){var t=this;return""==this.formData.refund_delivery_name?(this.$message({message:"请输入物流公司",type:"warning"}),!1):""==this.formData.refund_delivery_no?(this.$message({message:"请输入物流单号",type:"warning"}),!1):(this.formData.order_goods_id=this.orderGoodsId,void(this.isSub||(this.isSub=!0,Object(o.e)(this.formData).then((function(e){var code=e.code;e.message,e.data;code>=0?(t.getRefundDetail(),t.refundDeliveryDialog=!1):t.$message({message:"未获取到该订单项退款信息！",type:"warning",duration:2e3,onClose:function(){t.$router.push({path:"/member/activist"})}})})).catch((function(e){t.$message.error({message:e.message,duration:2e3,onClose:function(){t.$router.push({path:"/member/activist"})}})})))))},cancleRefund:function(t){var e=this;this.$confirm("撤销之后本次申请将会关闭,如后续仍有问题可再次发起申请","提示",{confirmButtonText:"确认撤销",cancelButtonText:"暂不撤销",type:"warning"}).then((function(){e.isSub||(e.isSub=!0,Object(o.a)({order_goods_id:t}).then((function(t){var code=t.code,r=t.message;t.data;code>=0?e.$message({message:"撤销成功！",type:"success",duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}}):e.$message({message:r,type:"warning"})})).catch((function(t){e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/activist"})}})})))}))}}},_=c,f=(r(722),r(723),r(6)),component=Object(f.a)(_,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",[e("el-card",{staticClass:"box-card order-list"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("el-breadcrumb",{attrs:{separator:"/"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/member/activist"}}},[t._v("退款/售后")]),t._v(" "),e("el-breadcrumb-item",[t._v("退款详情")])],1)],1),t._v(" "),e("div",[-1==t.detail.refund_status?e("div",{staticClass:"block-text color-red"},[t._v(t._s(t.detail.refund_status_name))]):t._e(),t._v(" "),3==t.detail.refund_status?e("div",{staticClass:"block-text color-green"},[t._v(t._s(t.detail.refund_status_name))]):t._e(),t._v(" "),-1==t.detail.refund_status?e("div",{staticClass:"fail-text"},[e("div",{staticClass:"fail-title"},[t._v("拒绝原因:")]),t._v(" "),e("div",{staticClass:"fail-detail"},[t._v(t._s(t.detail.refund_refuse_reason))])]):t._e(),t._v(" "),-1!=t.detail.refund_status&&3!=t.detail.refund_status?e("div",{staticClass:"block-text"},[t._v("\n              "+t._s(t.detail.refund_status_name)+"\n            ")]):t._e(),t._v(" "),e("div",{staticClass:"status-wrap"},[1==t.detail.refund_status?e("div",{staticClass:"refund-explain"},[e("div",[t._v("如果商家拒绝，你可重新发起申请")]),t._v(" "),e("div",[t._v("如果商家同意，将通过申请并退款给你")]),t._v(" "),e("div",[t._v("如果商家逾期未处理，平台将自动通过申请并退款给你")])]):t._e(),t._v(" "),5==t.detail.refund_status?e("div",{staticClass:"refund-explain"},[e("div",[t._v("如果商家确认收货将会退款给你")]),t._v(" "),e("div",[t._v("如果商家拒绝收货，该次退款将会关闭，你可以重新发起退款")])]):t._e()])])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"action-box"},[e("span",{staticClass:"media-left"},[t._v("协商记录")]),t._v(" "),e("div",{staticClass:"media-right"},[e("div",{staticClass:"el-button--text",on:{click:function(e){t.actionOpen?t.actionOpen=!1:t.actionOpen=!0}}},[t._v("\n                协商记录\n                "),e("i",{staticClass:"el-icon-arrow-down",class:t.actionOpen?"rotate":""})])]),t._v(" "),e("div",{staticClass:"clear"})]),t._v(" "),t.actionOpen?e("div",[e("el-timeline",t._l(t.detail.refund_log_list,(function(r,n){return e("el-timeline-item",{key:n,class:1==r.action_way?"buyer":"seller",attrs:{timestamp:t.$util.timeStampTurnTime(r.action_time),placement:"top"}},[e("div",[e("h4",[t._v(t._s(1==r.action_way?"买家":"卖家"))]),t._v(" "),e("p",[t._v(t._s(r.action))])])])})),1)],1):t._e()]),t._v(" "),4==t.detail.refund_status?e("div",{staticClass:"item-block"},[e("div",{staticClass:"block-text"},[e("span",[t._v("收货人："+t._s(t.detail.shop_contacts))]),t._v(" "),e("span",[t._v("联系电话："+t._s(t.detail.shop_mobile))])]),t._v(" "),e("div",{staticClass:"block-text"},[e("span",[t._v("退货地址："+t._s(t.detail.shop_address))])])]):t._e(),t._v(" "),t._m(0),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[e("div",{staticClass:"goods-info"},[e("div",{staticClass:"goods-info-left"},[e("router-link",{attrs:{to:{path:"/sku/"+t.detail.sku_id},target:"_blank"}},[e("img",{staticClass:"goods-img",attrs:{src:t.$img(t.detail.sku_image)},on:{error:function(e){t.detail.sku_image=t.defaultGoodsImage}}})])],1),t._v(" "),e("div",{staticClass:"goods-info-right"},[e("router-link",{attrs:{to:{path:"/sku/"+t.detail.sku_id},target:"_blank"}},[e("div",{staticClass:"goods-name"},[t._v(t._s(t.detail.sku_name))])])],1)])]),t._v(" "),e("td",{staticClass:"goods-num",attrs:{width:"12.5%"}},[t._v(t._s(t.detail.num))]),t._v(" "),e("td",{staticClass:"goods-money",attrs:{width:"12.5%"}},[t._v("￥"+t._s(t.detail.refund_apply_money))])])])])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"order-statistics"},[t.detail.refund_apply_money>0?e("table",[e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款方式：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(1==t.detail.refund_type?"仅退款":"退款退货"))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("申请原因：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.refund_reason))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("申请金额：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v("￥"+t._s(t.detail.refund_apply_money))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("申请时间：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.refund_action_time)))])]),t._v(" "),t.detail.refund_images?e("tr",{staticClass:"refund-images"},[e("td",{attrs:{align:"right refund-title"}},[t._v("退款图片：")]),t._v(" "),e("td",{attrs:{align:"left"}},t._l(t.detail.refund_images.split(","),(function(r,n){return e("img",{key:n,staticClass:"image",attrs:{src:t.$img(r),alt:""}})})),0)]):t._e()]):t._e(),t._v(" "),t.detail.refund_apply_money>0&&3==t.detail.refund_status?e("table",[e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款金额：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v("￥"+t._s(t.detail.refund_apply_money)+" ("+t._s(t.detail.refund_money_type_name)+")")])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款编号：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.refund_no))])]),t._v(" "),t.detail.refund_time?e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款时间：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.refund_time)))])]):t._e()]):t._e(),t._v(" "),1==t.detail.shop_active_refund?e("table",[e("tr",[e("td",{attrs:{align:"right"}},[t._v("主动退款金额：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v("￥"+t._s(t.detail.shop_active_refund_money)+" ("+t._s(t.detail.shop_active_refund_money_type_name)+")")])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("主动退款编号：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.shop_active_refund_no))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("主动退款说明：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.shop_active_refund_remark))])])]):t._e()]),t._v(" "),e("div",{staticClass:"clear"})]),t._v(" "),t.detail.refund_action.length?e("div",{staticClass:"item-block"},[t._l(t.detail.refund_action,(function(r,n){return e("div",{key:n,staticClass:"order-submit"},[e("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:function(e){return t.refundAction(r.event)}}},[t._v(t._s(r.title))])],1)})),t._v(" "),t.detail.complain_action?e("div",{staticClass:"order-submit"},[e("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:function(e){return t.refundAction("complain")}}},[t._v("平台维权")])],1):t._e(),t._v(" "),e("div",{staticClass:"clear"})],2):t._e()],1),t._v(" "),e("el-dialog",{attrs:{title:"输入发货物流",visible:t.refundDeliveryDialog,width:"50%"},on:{"update:visible":function(e){t.refundDeliveryDialog=e}}},[e("el-form",{ref:"form",attrs:{model:t.formData,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"物流公司"}},[e("el-input",{attrs:{placeholder:"请输入物流公司"},model:{value:t.formData.refund_delivery_name,callback:function(e){t.$set(t.formData,"refund_delivery_name",e)},expression:"formData.refund_delivery_name"}})],1),t._v(" "),e("el-form-item",{attrs:{label:"物流单号"}},[e("el-input",{attrs:{placeholder:"请输入物流单号"},model:{value:t.formData.refund_delivery_no,callback:function(e){t.$set(t.formData,"refund_delivery_no",e)},expression:"formData.refund_delivery_no"}})],1),t._v(" "),e("el-form-item",{attrs:{label:"物流说明"}},[e("el-input",{attrs:{placeholder:"选填"},model:{value:t.formData.refund_delivery_remark,callback:function(e){t.$set(t.formData,"refund_delivery_remark",e)},expression:"formData.refund_delivery_remark"}})],1)],1),t._v(" "),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.refundDeliveryDialog=!1}}},[t._v("取 消")]),t._v(" "),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.refundGoods("form")}}},[t._v("确 定")])],1)],1)],1)])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"item-block"},[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[t._v("商品")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("数量")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("退款金额")])])])])])}],!1,null,"5c172cb4",null);e.default=component.exports}}]);