(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{590:function(e,t,o){},678:function(e,t,o){"use strict";o(590)},767:function(e,t,o){"use strict";o.r(t);var n=o(152),r={name:"category",components:{},data:function(){return{goodsCategory:[],categoryFixed:!1,clickIndex:0,loading:!0}},created:function(){this.getGoodsCategory()},mounted:function(){window.addEventListener("scroll",this.handleScroll)},methods:{getGoodsCategory:function(){var e=this;Object(n.d)({level:3,template:2}).then((function(t){0==t.code&&(e.goodsCategory=t.data),e.loading=!1})).catch((function(t){e.$message.error(t.message),e.loading=!1}))},handleScroll:function(){var e=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop,t=document.querySelector(".newCategory").offsetTop;this.categoryFixed=e>t;for(var o=[],i=0;i<this.goodsCategory.length;i++){var n=this.$refs["category"+i][0].offsetTop;o.push(n);var r=e-t;r<o[o.length-1]?r>=o[i]&&r<o[i+1]&&(this.clickIndex=i):this.clickIndex=o.length-1}},changeCate:function(e,t){this.clickIndex=e,document.querySelector(t).scrollIntoView(!0)}},destroyed:function(){window.removeEventListener("scroll",this.handleScroll)}},c=(o(678),o(6)),component=Object(c.a)(r,(function(){var e=this,t=e._self._c;return t("div",[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"newCategory"},[t("div",{staticClass:"categoryLink"},[t("ul",{class:1==e.categoryFixed?"category-fixed":"",attrs:{id:"categoryUl"}},e._l(e.goodsCategory,(function(o,n){return t("li",{key:n,class:n==e.clickIndex?"selected":"",on:{click:function(t){return e.changeCate(n,"#category"+n)}}},[t("a",[t("span",[e._v(e._s(o.category_name))])])])})),0)]),e._v(" "),t("div",{staticClass:"categoryCon"},e._l(e.goodsCategory,(function(o,n){return t("div",{key:n,ref:"category"+n,refInFor:!0,staticClass:"items",class:"items-"+n,attrs:{id:"category"+n}},[t("h2",[t("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:o.category_id,level:o.level}},target:"_blank"}},[e._v(e._s(o.category_name))])],1),e._v(" "),e._l(o.child_list,(function(o,n){return t("dl",{key:n},[t("dt",[t("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:o.category_id,level:o.level}},target:"_blank"}},[e._v(e._s(o.category_name))])],1),e._v(" "),t("dd",e._l(o.child_list,(function(o,n){return t("router-link",{key:n,attrs:{to:{path:"/goods/list",query:{category_id:o.category_id,level:o.level}},target:"_blank"}},[e._v(e._s(o.category_name))])})),1)])}))],2)})),0),e._v(" "),e.goodsCategory.length<=0?t("div",{staticClass:"empty-wrap"},[t("div",{staticClass:"ns-text-align"},[e._v("暂无商品分类")])]):e._e()])])}),[],!1,null,"31ec0007",null);t.default=component.exports}}]);