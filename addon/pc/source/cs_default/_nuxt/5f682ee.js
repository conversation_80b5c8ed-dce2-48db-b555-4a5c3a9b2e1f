(window.webpackJsonp=window.webpackJsonp||[]).push([[23],{535:function(t,e,r){"use strict";var o=r(3),n=r(35),l=r(14),c=r(8),path=r(319),v=r(5),f=r(113),d=r(16),m=r(205),_=r(63),I=r(112),h=r(318),y=r(4),N=r(93).f,O=r(57).f,w=r(26).f,k=r(320),j=r(316).trim,E="Number",T=c[E],x=path[E],S=T.prototype,C=c.TypeError,A=v("".slice),P=v("".charCodeAt),F=function(t){var e=h(t,"number");return"bigint"==typeof e?e:G(e)},G=function(t){var e,r,o,n,l,c,v,code,f=h(t,"number");if(I(f))throw C("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=j(f),43===(e=P(f,0))||45===e){if(88===(r=P(f,2))||120===r)return NaN}else if(48===e){switch(P(f,1)){case 66:case 98:o=2,n=49;break;case 79:case 111:o=8,n=55;break;default:return+f}for(c=(l=A(f,2)).length,v=0;v<c;v++)if((code=P(l,v))<48||code>n)return NaN;return parseInt(l,o)}return+f},$=f(E,!T(" 0o1")||!T("0b1")||T("+0x1")),L=function(t){return _(S,t)&&y((function(){k(t)}))},D=function(t){var e=arguments.length<1?0:T(F(t));return L(this)?m(Object(e),this,D):e};D.prototype=S,$&&!n&&(S.constructor=D),o({global:!0,constructor:!0,wrap:!0,forced:$},{Number:D});var M=function(t,source){for(var e,r=l?N(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;r.length>o;o++)d(source,e=r[o])&&!d(t,e)&&w(t,e,O(source,e))};n&&x&&M(path[E],x),($||n)&&M(path[E],T)},542:function(t,e,r){var o=r(3),n=r(548);o({target:"Number",stat:!0,forced:Number.parseInt!=n},{parseInt:n})},548:function(t,e,r){var o=r(8),n=r(4),l=r(5),c=r(19),v=r(316).trim,f=r(209),d=o.parseInt,m=o.Symbol,_=m&&m.iterator,I=/^[+-]?0x/i,h=l(I.exec),y=8!==d(f+"08")||22!==d(f+"0x16")||_&&!n((function(){d(Object(_))}));t.exports=y?function(t,e){var r=v(c(t));return d(r,e>>>0||(h(I,r)?16:10))}:d},558:function(t,e,r){},574:function(t,e,r){"use strict";r(558)},657:function(t,e,r){"use strict";r.r(e);r(321),r(542),r(535),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var o=r(10),n=r(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var c={name:"floor-style-2",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(n.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$router.push("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage}}},v=c,f=(r(574),r(6)),component=Object(f.a)(v,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-2"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{textAlign:t.data.value.title.value.textAlign,color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))]),t._v(" "),t.data.value.subTitle.value.text?e("p",{style:{color:t.data.value.subTitle.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.subTitle.value.link.url)}}},[t._v(t._s(t.data.value.subTitle.value.text))]):t._e()]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[e("ul",{staticClass:"goods-list"},t._l(t.data.value.goodsList.value.list,(function(r,o){return e("li",{key:o,attrs:{title:r.goods_name},on:{click:function(e){return t.goSku(r.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:r.goods_image?t.$img(r.goods_image.split(",")[0],{size:"mid"}):t.defaultGoodsImage},on:{error:function(e){return t.imageError(o)}}})]),t._v(" "),e("h3",[t._v(t._s(r.goods_name))]),t._v(" "),e("p",{staticClass:"desc"},[t._v(t._s(r.introduction))]),t._v(" "),e("p",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v(t._s(r.discount_price)+"元")]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(r.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v(t._s(r.market_price)+"元")])])])})),0)]),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"cf1bbd00",null);e.default=component.exports}}]);