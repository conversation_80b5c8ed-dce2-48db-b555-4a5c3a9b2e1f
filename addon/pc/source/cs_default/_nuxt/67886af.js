(window.webpackJsonp=window.webpackJsonp||[]).push([[13],{642:function(t,e,n){},735:function(t,e,n){"use strict";n(642)},806:function(t,e,n){"use strict";n.r(e);var c=n(216),r={name:"help",components:{},data:function(){return{helpList:[],helpOther:[],currentId:0,loading:!0}},head:function(){return{title:"帮助列表-"+this.$store.state.site.siteInfo.site_name}},created:function(){this.getInfo()},methods:{menuOther:function(t){this.currentId=t,this.getHelpOtherInfo()},getInfo:function(){var t=this;Object(c.b)().then((function(e){0==e.code&&e.data.length>0&&(t.currentId=e.data[0].class_id,t.helpList=e.data,t.getHelpOtherInfo()),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},getHelpOtherInfo:function(){var t=this;Object(c.c)({class_id:this.currentId}).then((function(e){0==e.code&&e.data&&(t.helpOther=e.data)})).catch((function(e){t.$message.error(e.message)}))},detail:function(t){this.$router.push({path:"/cms/help/detail",query:{id:t}})}}},l=(n(735),n(6)),component=Object(l.a)(r,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"help-wrap"},[e("el-breadcrumb",{staticClass:"path",attrs:{separator:"/"}},[e("el-breadcrumb-item",{staticClass:"path-home",attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{staticClass:"path-help"},[t._v("帮助")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"help"},[e("div",{staticClass:"menu"},[e("div",{staticClass:"title"},[t._v("帮助列表")]),t._v(" "),t._l(t.helpList,(function(n,c){return e("div",{key:c,staticClass:"item"},[e("div",{class:t.currentId==n.class_id?"active item-name":"item-name",on:{click:function(e){return t.menuOther(n.class_id)}}},[t._v(t._s(n.class_name))])])}))],2),t._v(" "),e("div",{staticClass:"list-other"},[e("div",{staticClass:"item-info"},t._l(t.helpOther.list,(function(n,c){return e("div",{key:c,staticClass:"item",on:{click:function(e){return t.detail(n.id)}}},[e("div",{staticClass:"item-title"},[t._v(t._s(n.title))]),t._v(" "),e("div",{staticClass:"info"},[e("div",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(n.create_time)))])])])})),0)])])],1)}),[],!1,null,"6d1a51da",null);e.default=component.exports}}]);