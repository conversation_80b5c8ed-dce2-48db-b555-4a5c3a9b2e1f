(window.webpackJsonp=window.webpackJsonp||[]).push([[50],{537:function(t,e,n){"use strict";n.d(e,"b",(function(){return l})),n.d(e,"c",(function(){return c})),n.d(e,"j",(function(){return d})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return v})),n.d(e,"k",(function(){return _})),n.d(e,"i",(function(){return m})),n.d(e,"f",(function(){return f})),n.d(e,"e",(function(){return w})),n.d(e,"d",(function(){return C})),n.d(e,"g",(function(){return h}));var r=n(1);function l(t){return Object(r.a)({url:"/api/memberaccount/info",data:t,forceLogin:!0})}function c(t){return Object(r.a)({url:"/api/memberaccount/page",data:t,forceLogin:!0})}function d(t){return Object(r.a)({url:"/api/memberwithdraw/info",data:t})}function o(t){return Object(r.a)({url:"/api/memberbankaccount/defaultinfo",data:t})}function v(t){return Object(r.a)({url:"/api/memberwithdraw/apply",data:t})}function _(t){return Object(r.a)({url:"/api/memberwithdraw/page",data:t})}function m(t){return Object(r.a)({url:"/api/memberwithdraw/detail",data:t})}function f(t){return Object(r.a)({url:"/memberrecharge/api/memberrecharge/page",data:t})}function w(t){return Object(r.a)({url:"/memberrecharge/api/memberrecharge/info",data:t})}function C(t){return Object(r.a)({url:"/memberrecharge/api/ordercreate/create",data:t})}function h(t){return Object(r.a)({url:"/memberrecharge/api/order/page",data:t})}},619:function(t,e,n){},711:function(t,e,n){"use strict";n(619)},790:function(t,e,n){"use strict";n.r(e);n(73);var r=n(537),l={name:"withdrawal_detail",layout:"member",components:{},data:function(){return{loading:!0,id:"",detail:{},yes:!0}},created:function(){this.getDetail()},mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},methods:{getDetail:function(){var t=this;this.id=this.$route.query.id,Object(r.i)({id:this.id}).then((function(e){e.data&&(t.detail=e.data),t.loading=!1})).catch((function(e){t.loading=!1}))}}},c=(n(711),n(6)),component=Object(c.a)(l,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("el-breadcrumb",{attrs:{separator:"/"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/member/withdrawal"}}},[t._v("提现记录")]),t._v(" "),e("el-breadcrumb-item",[t._v("提现详情")])],1)],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",{staticClass:"money-wrap"},[e("span",[t._v("-"+t._s(t.detail.apply_money))])]),t._v(" "),e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("当前状态")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.status_name))])]),t._v(" "),e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("交易号")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.withdraw_no))])]),t._v(" "),e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("手续费")]),t._v(" "),e("span",{staticClass:"value"},[t._v("￥"+t._s(t.detail.service_money))])]),t._v(" "),e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("申请时间")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.apply_time)))])]),t._v(" "),t.detail.status?e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("审核时间")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.audit_time)))])]):t._e(),t._v(" "),t.detail.bank_name?e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("银行名称")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.bank_name))])]):t._e(),t._v(" "),e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("收款账号")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.account_number))])]),t._v(" "),-1==t.detail.status&&t.detail.refuse_reason?e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("拒绝理由")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.refuse_reason))])]):t._e(),t._v(" "),2==t.detail.status?e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("转账方式名称")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.detail.transfer_type_name))])]):t._e(),t._v(" "),2==t.detail.status?e("div",{staticClass:"line-wrap"},[e("span",{staticClass:"label"},[t._v("转账时间")]),t._v(" "),e("span",{staticClass:"value"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.payment_time)))])]):t._e()])])],1)}),[],!1,null,"630fcd2e",null);e.default=component.exports}}]);