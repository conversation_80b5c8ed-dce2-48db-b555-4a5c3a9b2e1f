(window.webpackJsonp=window.webpackJsonp||[]).push([[34],{602:function(e,t,n){},691:function(e,t,n){"use strict";n(602)},777:function(e,t,n){"use strict";n.r(t);n(56),n(73);var r=n(207),d={name:"delivery_address",layout:"member",components:{},data:function(){return{addressList:[],total:0,currentPage:1,pageSize:8,loading:!0,yes:!0}},created:function(){this.getListData()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getListData:function(){var e=this;Object(r.e)({page:this.currentPage,page_size:this.pageSize,type:1}).then((function(t){var n=t.data,r=n.count,d=(n.page_count,n.list);e.total=r,e.addressList=d,e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error(t.message)}))},handlePageSizeChange:function(e){this.pageSize=e,this.refresh()},handleCurrentPageChange:function(e){this.currentPage=e,this.refresh()},refresh:function(){this.loading=!0,this.getListData()},setDefault:function(e){var t=this;Object(r.n)({id:e}).then((function(e){t.refresh(),t.$message({message:"修改默认地址成功",type:"success"})})).catch((function(e){t.$message.error(e.message)}))},addAddress:function(e,t){"edit"==e?this.$router.push({path:"/member/address_edit",query:{id:t}}):this.$router.push({path:"/member/address_edit"})},delAddress:function(e,t){var n=this;this.$confirm("确定要删除该地址吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){1!=t?Object(r.i)({id:e}).then((function(e){n.refresh(),n.$message({message:e.message,type:"success"})})).catch((function(e){n.$message.error(e.message)})):n.$message({type:"warning",message:"默认地址，不能删除!"})}))}}},c=(n(691),n(6)),component=Object(c.a)(d,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("收货地址")])]),e._v(" "),t("div",[t("div",{staticClass:"ns-member-address-list"},[t("div",{staticClass:"text item ns-add-address",on:{click:function(t){return e.addAddress("add")}}},[t("span",[e._v("+ 添加收货地址")])]),e._v(" "),e._l(e.addressList,(function(n,r){return t("div",{key:r,staticClass:"text item"},[t("div",{staticClass:"text-name"},[t("span",[e._v(e._s(n.name))]),e._v(" "),1==n.is_default?t("span",{staticClass:"text-default"},[e._v("默认")]):e._e()]),e._v(" "),t("div",{staticClass:"text-content"},[t("p",[e._v(e._s(n.mobile))]),e._v(" "),t("p",{staticClass:"ns-address-detail",attrs:{title:n.full_address+n.address}},[e._v("\n              "+e._s(n.full_address)+e._s(n.address))])]),e._v(" "),t("div",{staticClass:"text-operation"},[1!=n.is_default?t("span",{on:{click:function(t){return e.setDefault(n.id)}}},[e._v("设为默认")]):e._e(),e._v(" "),t("span",{on:{click:function(t){return e.addAddress("edit",n.id)}}},[e._v("编辑")]),e._v(" "),1!=n.is_default?t("span",{on:{click:function(t){return e.delAddress(n.id,n.is_default)}}},[e._v("删除")]):e._e()])])}))],2),e._v(" "),t("div",{staticClass:"pager"},[t("el-pagination",{attrs:{background:"","pager-count":5,total:e.total,"prev-text":"上一页","next-text":"下一页","current-page":e.currentPage,"page-size":e.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handlePageSizeChange,"current-change":e.handleCurrentPageChange}})],1)])])],1)}),[],!1,null,"1183a10c",null);t.default=component.exports}}]);