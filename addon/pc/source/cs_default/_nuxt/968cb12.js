(window.webpackJsonp=window.webpackJsonp||[]).push([[68],{546:function(e,t,r){"use strict";r.d(t,"f",(function(){return n})),r.d(t,"b",(function(){return d})),r.d(t,"e",(function(){return c})),r.d(t,"c",(function(){return l})),r.d(t,"a",(function(){return m})),r.d(t,"d",(function(){return v}));var o=r(1);function n(e){return Object(o.a)({url:"/api/ordercreate/payment",data:e,forceLogin:!0})}function d(e){return Object(o.a)({url:"/api/ordercreate/calculate",data:e,forceLogin:!0})}function c(e){return Object(o.a)({url:"/api/ordercreate/create",data:e,forceLogin:!0})}function l(e){return Object(o.a)({url:"/api/member/checkpaypassword",data:e,forceLogin:!0})}function m(){return Object(o.a)({url:"/api/pay/getBalanceConfig",data:"",forceLogin:!0})}function v(e){return Object(o.a)({url:"/api/ordercreate/getcouponlist",data:e,forceLogin:!0})}},553:function(e,t,r){"use strict";r.d(t,"g",(function(){return n})),r.d(t,"b",(function(){return d})),r.d(t,"a",(function(){return c})),r.d(t,"f",(function(){return l})),r.d(t,"c",(function(){return m})),r.d(t,"d",(function(){return v})),r.d(t,"e",(function(){return _}));var o=r(1);function n(e){return Object(o.a)({url:"/groupbuy/api/ordercreate/payment",data:e,forceLogin:!0})}function d(){return Object(o.a)({url:"/api/goodsevaluate/config",data:{},forceLogin:!0})}function c(e){return Object(o.a)({url:"/groupbuy/api/ordercreate/calculate",data:e,forceLogin:!0})}function l(e){return Object(o.a)({url:"/groupbuy/api/ordercreate/create",data:e,forceLogin:!0})}function m(e){return Object(o.a)({url:"/groupbuy/api/goods/page",data:e,forceLogin:!0})}function v(e){return Object(o.a)({url:"/groupbuy/api/goods/detail",data:e,forceLogin:!0})}function _(e){return Object(o.a)({url:"/groupbuy/api/goods/info",data:e,forceLogin:!0})}},646:function(e,t,r){},739:function(e,t,r){"use strict";r(646)},756:function(e,t,r){"use strict";r.r(t);r(56),r(317),r(315),r(25),r(29),r(30);var o=r(10),n=(r(31),r(7),r(18),r(24),r(23),r(92),r(546)),d=r(207),c=r(553),address=r(208),l=r(12);function m(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var v={name:"groupbuy_payment",components:{},middleware:"auth",data:function(){return{dialogVisible:!1,memberAddress:[],addressId:0,addressForm:{id:0,name:"",mobile:"",telephone:"",province_id:"",city_id:"",district_id:"",community_id:"",address:"",full_address:"",is_default:1,longitude:"",latitude:""},pickerValueArray:{},cityArr:{},districtArr:{},addressRules:{name:[{required:!0,message:"请输入收货人",trigger:"blur"},{min:1,max:20,message:"长度在 1 到 20 个字符",trigger:"blur"}],mobile:[{required:!0,validator:function(e,t,r){""===t?r(new Error("请输入手机号")):/^\d{11}$/.test(t)?r():r(new Error("手机号格式错误"))},trigger:"change"}],province:[{required:!0,message:"请选择省",trigger:"change"}],city:[{required:!0,message:"请选择市",trigger:"change"}],district:[{required:!0,message:"请选择区/县",trigger:"change"}],address:[{required:!0,message:"请输入详细地址",trigger:"change"}]},isSend:!1,orderCreateData:{is_balance:0,pay_password:"",is_invoice:0,invoice_type:1,invoice_title_type:1,is_tax_invoice:0,invoice_title:"",taxpayer_number:"",invoice_content:"",invoice_full_address:"",invoice_email:"",member_address:{mobile:""},delivery:{},order_key:"",store_id:0},orderPaymentData:{member_account:{balance:0,is_pay_password:0},delivery:{express_type:[]}},dialogStore:!1,storeList:{},sitePromotion:[],isSub:!1,dialogpay:!1,password:"",fullscreenLoading:!0,deliveryTime:!1,addressShow:!1,storeRadio:0,balance_show:1,addresNextType:!0,modules:[],promotionInfo:null,calculateData:null,storeConfig:null,storeId:0}},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(l.b)(["groupbuyOrderCreateData","defaultGoodsImage","city"])),mounted:function(){},created:function(){this.getMemberAddress(),this.getOrderPaymentData(),this.getBalanceConfig()},filters:{moneyFormat:function(e){return e||(e=0),parseFloat(e).toFixed(2)},promotion:function(data){var e="";return data&&Object.keys(data).forEach((function(t){e+=data[t].content+"　"})),e}},methods:{getBalanceConfig:function(){var e=this;Object(n.a)().then((function(t){var code=t.code,data=(t.message,t.data);code>=0&&(e.balance_show=data.balance_show)}))},getMemberAddress:function(){var e=this;Object(d.e)({page_size:0,type:1}).then((function(t){t.code,t.message;var data=t.data;if(data&&data.list){var r=e;e.memberAddress=data.list,data.list.forEach((function(e){1==e.is_default&&(r.addressId=e.id)}))}})).catch((function(t){t.code;var r=t.message;t.data;e.$message.error(r)}))},setMemberAddress:function(e){var t=this;this.addressId=e,Object(d.n)({id:e}).then((function(e){e.code,e.message,e.data;t.orderCalculate()})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))},deleteMemberAddress:function(e){var t=this;Object(d.i)({id:e}).then((function(e){e.code;var r=e.message;e.data?(t.$message({message:r,type:"success"}),t.getMemberAddress()):t.$message({message:r,type:"warning"})})).catch((function(e){t.$message.error(e.message)}))},addAddressShow:function(){this.dialogVisible=!0,this.addressForm.id=0,this.addressForm.name="",this.addressForm.mobile="",this.addressForm.telephone="",this.addressForm.province_id="",this.addressForm.city_id="",this.addressForm.district_id="",this.addressForm.community_id="",this.addressForm.address="",this.addressForm.full_address="",this.addressForm.is_default=1,this.addressForm.longitude="",this.addressForm.latitude="",this.cityArr={},this.districtArr={},this.getAddress(0)},getAddress:function(e){var t=this,r=0,o=this;switch(e){case 0:r=0;break;case 1:r=this.addressForm.province_id,o.cityArr={},o.districtArr={},this.addressForm.city_id="",this.addressForm.district_id="";break;case 2:r=this.addressForm.city_id,o.districtArr={},this.addressForm.district_id=""}Object(address.a)({pid:r}).then((function(r){r.code,r.message;var data=r.data;if(data)switch(e){case 0:o.pickerValueArray=data;break;case 1:o.cityArr=data;break;case 2:o.districtArr=data}0==data.length&&(t.addresNextType=!1)})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))},initAddress:function(e){var t=this,r=0,o=this;switch(e){case 0:r=0;break;case 1:r=this.addressForm.province_id,o.cityArr={},o.districtArr={};break;case 2:r=this.addressForm.city_id,o.districtArr={}}Object(address.a)({pid:r}).then((function(r){r.code,r.message;var data=r.data;if(data)switch(e){case 0:o.pickerValueArray=data;break;case 1:o.cityArr=data;break;case 2:o.districtArr=data}0==data.length&&(t.addresNextType=!1)})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))},addmemberAddress:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;if(t.isSend)return!1;if(t.addressForm.id){t.addressForm.full_address=t.$refs.province.selectedLabel+"-"+t.$refs.city.selectedLabel+"-"+t.$refs.district.selectedLabel;var r=t.addressForm;if(!r.province_id)return t.$message({message:"请选择省",type:"warning"}),!1;if(!r.city_id)return t.$message({message:"请选择市",type:"warning"}),!1;if(!r.district_id)return t.$message({message:"请选择区/县",type:"warning"}),!1;t.isSend=!0,t.setMemberAddress(r.id),r.url="edit",Object(d.m)(r).then((function(e){e.code;var r=e.message;e.data?(t.$message({message:r,type:"success"}),t.dialogVisible=!1,t.getMemberAddress(),t.getOrderPaymentData()):t.$message({message:r,type:"warning"}),t.isSend=!1})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))}else{t.addressForm.full_address=t.$refs.province.selectedLabel+"-"+t.$refs.city.selectedLabel+"-"+t.$refs.district.selectedLabel;var data={name:t.addressForm.name,mobile:t.addressForm.mobile,telephone:t.addressForm.telephone,province_id:t.addressForm.province_id,city_id:t.addressForm.city_id,district_id:t.addressForm.district_id,community_id:"",address:t.addressForm.address,full_address:t.addressForm.full_address,longitude:t.addressForm.longitude,latitude:t.addressForm.latitude,is_default:t.addressForm.is_default,url:"add"};if(!data.province_id||data.province_id<=0)return t.$message({message:"请选择省",type:"warning"}),!1;if(!data.city_id||data.city_id<=0)return t.$message({message:"请选择市",type:"warning"}),!1;if((!data.district_id||data.district_id<=0)&&1==t.addresNextType)return t.$message({message:"请选择区/县",type:"warning"}),!1;t.isSend=!0,Object(d.m)(data).then((function(e){e.code;var r=e.message,data=e.data;data?(t.setMemberAddress(data),t.$message({message:r,type:"success"}),t.dialogVisible=!1,t.getMemberAddress(),t.getOrderPaymentData()):t.$message({message:r,type:"warning"}),t.isSend=!1})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))}}))},editAddress:function(e){var t=this;Object(d.d)({id:e}).then((function(e){e.code,e.message;var data=e.data;t.addressForm={id:data.id,name:data.name,mobile:data.mobile,telephone:data.telephone,province_id:data.province_id,city_id:"",district_id:"",community_id:"",address:data.address,full_address:data.full_address,is_default:data.is_default,longitude:data.longitude,latitude:data.latitude},t.initAddress(0),t.initAddress(1),t.addressForm.city_id=data.city_id,t.initAddress(2),t.addressForm.district_id=data.district_id,t.dialogVisible=!0})).catch((function(e){e.code;var r=e.message;e.data;t.$message.error(r)}))},getOrderPaymentData:function(){var e=this;this.orderCreateData=this.groupbuyOrderCreateData,this.orderCreateData?(this.orderCreateData.web_city=this.city?this.city.id:0,Object(c.g)(this.orderCreateData).then((function(t){var code=t.code,data=(t.message,t.data);code>=0&&data?(data.delivery.express_type&&data.delivery.express_type.length&&(data.delivery.express_type=data.delivery.express_type.filter((function(e){return"local"!=e.name}))),e.orderPaymentData=data,e.handlePaymentData()):e.$message({message:"未获取到创建订单所需数据！",type:"warning",offset:225,duration:3e3,onClose:function(){return e.$router.go(-1),!1}})})).catch((function(t){t.code;var r=t.message;t.data;e.$message.error(r)}))):this.$message({message:"未获取到创建订单所需数据！",type:"warning",offset:225,duration:3e3,onClose:function(){return e.$router.go(-1),!1}})},handlePaymentData:function(){var e=this;this.orderCreateData.delivery={},this.orderCreateData.buyer_message="",this.orderCreateData.is_balance=0,this.orderCreateData.pay_password="",this.orderCreateData.is_invoice=0,this.orderCreateData.invoice_type=1,this.orderCreateData.invoice_title_type=1,this.orderCreateData.is_tax_invoice=0,this.orderCreateData.invoice_title="",this.orderCreateData.taxpayer_number="",this.orderCreateData.invoice_content="",this.orderCreateData.invoice_full_address="",this.orderCreateData.invoice_email="";var data=this.orderPaymentData;if(this.orderCreateData.order_key=data.order_key,data.delivery.express_type.length){var t=data.delivery.express_type[0];data.delivery.express_type.forEach((function(t){"store"==t.name&&(e.storeConfig=t)})),this.selectDeliveryType(t,!1,data.member_account)}if(1==data.is_virtual&&(this.orderCreateData.member_address={mobile:""}),this.modules=data.modules,this.promotionInfo=this.promotion(data),1==this.orderPaymentData.invoice.invoice_status){var r=this.orderPaymentData.invoice.invoice_content_array;r.length&&(this.orderCreateData.invoice_content=r[0])}this.orderCalculate()},promotion:function(data){if(data.groupbuy_info)return{title:"团购",content:"团购".concat(data.groupbuy_info.buy_num,'件起,享团购价<text class="color-base-text">').concat(data.groupbuy_info.groupbuy_price,"</text>元")}},clickType:function(e){this.orderCreateData.invoice_type=e,this.orderCalculate(),this.$forceUpdate()},clickTitleType:function(e){this.orderCreateData.invoice_title_type=e,this.orderCalculate(),this.$forceUpdate()},changeInvoiceContent:function(e){this.orderCreateData.invoice_content=e,this.$forceUpdate()},changeIsInvoice:function(){0==this.orderCreateData.is_invoice?this.orderCreateData.is_invoice=1:this.orderCreateData.is_invoice=0,this.orderCalculate(),this.$forceUpdate()},invoiceVerify:function(){if(!this.orderCreateData.invoice_title)return this.$message({message:"请填写发票抬头",type:"warning"}),!1;if(!this.orderCreateData.taxpayer_number&&2==this.orderCreateData.invoice_title_type)return this.$message({message:"请填写纳税人识别号",type:"warning"}),!1;if(1==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_full_address)return this.$message({message:"请填写发票邮寄地址",type:"warning"}),!1;if(2==this.orderCreateData.invoice_type&&!this.orderCreateData.invoice_email)return this.$message({message:"请填写邮箱",type:"warning"}),!1;if(2==this.orderCreateData.invoice_type){if(!/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.orderCreateData.invoice_email))return this.$message({message:"请填写正确的邮箱",type:"warning"}),!1}return!!this.orderCreateData.invoice_content||(this.$message({message:"请选择发票内容",type:"warning"}),!1)},orderCalculate:function(){var e=this;this.fullscreenLoading=!0;var data=this.$util.deepClone(this.orderCreateData);data.delivery=JSON.stringify(data.delivery),data.member_address=JSON.stringify(data.member_address),Object(c.a)(data).then((function(t){var code=t.code,r=t.message,data=t.data;code>=0&&data?(e.calculateData=data,e.calculateData.goods_list.forEach((function(e){e.sku_spec_format?e.sku_spec_format=JSON.parse(e.sku_spec_format):e.sku_spec_format=[]})),data.delivery&&"express"==data.delivery.delivery_type&&(e.orderCreateData.member_address=data.delivery.member_address),e.fullscreenLoading=!1):e.$message({message:r,type:"warning",offset:225,duration:3e3,onClose:function(){return e.$router.go(-1),!1}})})).catch((function(t){t.code;var r=t.message;t.data;e.$message.error(r),e.fullscreenLoading=!1}))},selectDeliveryType:function(data){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!this.orderCreateData.delivery||this.orderCreateData.delivery.delivery_type!=data.name){var r={delivery_type:data.name,delivery_type_name:data.title};"store"==data.name&&(data.store_list[0]&&(r.store_id=data.store_list[0].store_id),this.storeId=r.store_id?r.store_id:0,this.orderCreateData.store_id=r.store_id?r.store_id:0,this.orderCreateData.member_address=t?{name:t.nickname,mobile:t.mobile}:{name:this.orderPaymentData.member_account.nickname,mobile:this.orderPaymentData.member_account.mobile},data.store_list.forEach((function(e,i){data.store_list[i].store_address=e.full_address+e.address})),this.dialogStore=!0,this.storeList=data.store_list),this.$set(this.orderCreateData,"delivery",r),e&&this.orderCalculate(),this.$forceUpdate()}},selectStore:function(e){if(e){var t=e.store_id;this.dialogStore=!1,this.orderCreateData.delivery.store_id=t,this.orderCreateData.store_id=t,this.orderCreateData.delivery.store_name=e.store_name,this.storeRadio=e,this.orderCalculate(),this.$forceUpdate()}},useBalance:function(e){this.orderCreateData.is_balance?this.orderCreateData.is_balance=0:this.orderCreateData.is_balance=1,this.orderCalculate(),this.$forceUpdate()},orderCreate:function(){var e=this;if(this.verify()){if(this.isSub)return;if(1==this.orderCreateData.is_invoice&&!1===this.invoiceVerify())return;this.isSub=!0;var t=this.$loading({lock:!0,text:"订单提交中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),data=this.$util.deepClone(this.orderCreateData);data.delivery=JSON.stringify(data.delivery),data.member_address=JSON.stringify(data.member_address),data.forceLogin=!0,Object(c.f)(data).then((function(r){var code=r.code,o=r.message,data=r.data;t.close(),code>=0?(e.$store.dispatch("order/removeGroupbuyOrderCreateData",""),0==e.calculateData.pay_money?e.$router.push({path:"/pay/result",query:{code:data}}):e.$router.push({path:"/pay",query:{code:data}})):e.$message({message:o,type:"warning"})})).catch((function(r){t.close(),e.isSub=!1;r.code;var o=r.message;r.data;e.$message.error(o)}))}},verify:function(){if(1==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address.mobile.length)return this.$message({message:"请输入您的手机号码",type:"warning"}),!1;if(!this.$util.verifyMobile(this.orderCreateData.member_address.mobile))return this.$message({message:"请输入正确的手机号码",type:"warning"}),!1}if(0==this.orderPaymentData.is_virtual){if(!this.orderCreateData.member_address)return this.$message({message:"请先选择您的收货地址",type:"warning"}),!1;if(!this.orderCreateData.delivery||!this.orderCreateData.delivery.delivery_type)return this.$message({message:"商家未设置配送方式",type:"warning"}),!1;if("store"==this.orderCreateData.delivery.delivery_type&&!this.orderCreateData.delivery.store_id)return this.$message({message:"没有可提货的门店,请选择其他配送方式",type:"warning"}),!1}return!0},input:function(){var e=this;if(6==this.password.length){var t=this.$loading({lock:!0,text:"支付中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});Object(n.c)({pay_password:this.password}).then((function(r){var code=r.code,o=r.message;r.data;t.close(),code>=0?(e.orderCreateData.pay_password=e.password,e.orderCreate(),e.dialogpay=!1):e.$message({message:o,type:"warning"})})).catch((function(r){t.close();r.code;var o=r.message;r.data;e.$message.error(o)}))}},textarea:function(){this.$forceUpdate()},imageError:function(e){this.calculateData.goods_list[e].sku_image=this.defaultGoodsImage},setPayPassword:function(){this.$util.pushToTab("/member/security")}}},_={name:"groupbuy_payment",mixins:[v]},f=(r(739),r(6)),component=Object(f.a)(_,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.fullscreenLoading,expression:"fullscreenLoading"}],staticClass:"payment-wrap"},[1==e.orderPaymentData.is_virtual?t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v("购买虚拟类商品需填写您的手机号，以方便商家与您联系")]),e._v(" "),t("el-form",{ref:"form",staticClass:"mobile-wrap",attrs:{size:"mini","label-width":"80px"}},[t("el-form-item",{attrs:{label:"手机号码"}},[t("el-input",{attrs:{placeholder:"请输入您的手机号码",maxlength:"11"},model:{value:e.orderCreateData.member_address.mobile,callback:function(t){e.$set(e.orderCreateData.member_address,"mobile",t)},expression:"orderCreateData.member_address.mobile"}})],1)],1)],1):e._e(),e._v(" "),0==e.orderPaymentData.is_virtual?t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v("收货地址")]),e._v(" "),t("div",{staticClass:"address-list"},[t("div",{staticClass:"address-item",on:{click:e.addAddressShow}},[e._m(0)]),e._v(" "),e._l(e.memberAddress,(function(r,o){return o<3||e.addressShow&&o>=3?t("div",{key:r.id,staticClass:"address-item",class:e.addressId==r.id?"active":""},[t("div",{staticClass:"address-info"},[t("div",{staticClass:"options"},[t("div",{on:{click:function(t){return e.editAddress(r.id)}}},[e._v("编辑")]),e._v(" "),0==r.is_default?[t("el-popconfirm",{attrs:{title:"确定要删除该地址吗？"},on:{onConfirm:function(t){return e.deleteMemberAddress(r.id)}}},[t("div",{attrs:{slot:"reference"},slot:"reference"},[e._v("删除")])])]:e._e()],2),e._v(" "),t("div",{staticClass:"address-name"},[e._v(e._s(r.name))]),e._v(" "),t("div",{staticClass:"address-mobile",on:{click:function(t){return e.setMemberAddress(r.id)}}},[e._v(e._s(r.mobile))]),e._v(" "),t("div",{staticClass:"address-desc",on:{click:function(t){return e.setMemberAddress(r.id)}}},[e._v(e._s(r.full_address)+" "+e._s(r.address)+"\n          ")])])]):e._e()})),e._v(" "),e.memberAddress.length>3&&!e.addressShow?t("div",{staticClass:"el-button--text address-open",on:{click:function(t){e.addressShow=!0}}},[t("i",{staticClass:"el-icon-arrow-down"}),e._v("\n        更多收货地址\n      ")]):e._e(),e._v(" "),t("div",{staticClass:"clear"})],2)]):e._e(),e._v(" "),t("el-dialog",{attrs:{title:0==e.addressForm.id?"添加收货地址":"编辑收货地址",visible:e.dialogVisible,width:"32%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("el-form",{ref:"form",attrs:{rules:e.addressRules,model:e.addressForm,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("el-input",{attrs:{placeholder:"收货人姓名"},model:{value:e.addressForm.name,callback:function(t){e.$set(e.addressForm,"name",t)},expression:"addressForm.name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"手机",prop:"mobile"}},[t("el-input",{attrs:{maxlength:"11",placeholder:"收货人手机号"},model:{value:e.addressForm.mobile,callback:function(t){e.$set(e.addressForm,"mobile",t)},expression:"addressForm.mobile"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"电话"}},[t("el-input",{attrs:{placeholder:"收货人固定电话（选填）"},model:{value:e.addressForm.telephone,callback:function(t){e.$set(e.addressForm,"telephone",t)},expression:"addressForm.telephone"}})],1),e._v(" "),t("el-form-item",{staticClass:"area",attrs:{label:"地区",prop:"area"}},[t("el-row",{attrs:{gutter:10}},[t("el-col",{attrs:{span:7}},[t("el-select",{ref:"province",attrs:{prop:"province",placeholder:"请选择省"},on:{change:function(t){return e.getAddress(1)}},model:{value:e.addressForm.province_id,callback:function(t){e.$set(e.addressForm,"province_id",t)},expression:"addressForm.province_id"}},[t("el-option",{attrs:{label:"请选择省",value:"0"}}),e._v(" "),e._l(e.pickerValueArray,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),e._v(" "),t("el-col",{attrs:{span:7}},[t("el-select",{ref:"city",attrs:{prop:"city",placeholder:"请选择市"},on:{change:function(t){return e.getAddress(2)}},model:{value:e.addressForm.city_id,callback:function(t){e.$set(e.addressForm,"city_id",t)},expression:"addressForm.city_id"}},[t("el-option",{attrs:{label:"请选择市",value:"0"}}),e._v(" "),e._l(e.cityArr,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),e._v(" "),t("el-col",{attrs:{span:7}},[t("el-select",{ref:"district",attrs:{prop:"district",placeholder:"请选择区/县"},model:{value:e.addressForm.district_id,callback:function(t){e.$set(e.addressForm,"district_id",t)},expression:"addressForm.district_id"}},[t("el-option",{attrs:{label:"请选择区/县",value:"0"}}),e._v(" "),e._l(e.districtArr,(function(e){return t("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1)],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"定位小区、街道、写字楼"},model:{value:e.addressForm.address,callback:function(t){e.$set(e.addressForm,"address",t)},expression:"addressForm.address"}})],1)],1),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addmemberAddress("form")}}},[e._v("确 定")])],1)],1),e._v(" "),e.orderPaymentData.member_account.balance_total>0&&1==e.balance_show?t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v("是否使用余额")]),e._v(" "),t("div",{staticClass:"pay-type-list"},[t("div",{staticClass:"pay-type-item",class:e.orderCreateData.is_balance?"":"active",on:{click:function(t){return e.useBalance(0)}}},[e._v("不使用余额")]),e._v(" "),t("div",{staticClass:"pay-type-item",class:e.orderCreateData.is_balance?"active":"",on:{click:function(t){return e.useBalance(1)}}},[e._v("使用余额")]),e._v(" "),t("div",{staticClass:"clear"})])]):e._e(),e._v(" "),t("el-dialog",{attrs:{title:"使用余额",visible:e.dialogpay,width:"350px"},on:{"update:visible":function(t){e.dialogpay=t}}},[0==e.orderPaymentData.member_account.is_pay_password?[t("p",[e._v("为了您的账户安全,请您先设置的支付密码")]),e._v(" "),t("p",[e._v('可到"会员中心","账号安全","支付密码"中设置')]),e._v(" "),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogpay=!1}}},[e._v("暂不设置")]),e._v(" "),t("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.setPayPassword}},[e._v("立即设置")])],1)]:t("el-form",{ref:"ruleForm",attrs:{"status-icon":"","label-width":"100px"}},[t("el-form-item",{staticClass:"pay-password-item",attrs:{label:"支付密码"}},[t("el-input",{staticClass:"pay-password hide-password",attrs:{type:"password",maxlength:6}}),e._v(" "),t("el-input",{staticClass:"pay-password",attrs:{type:"password",maxlength:6},on:{input:e.input},model:{value:e.password,callback:function(t){e.password=t},expression:"password"}})],1),e._v(" "),t("p",{staticClass:"ns-text-color forget-password",on:{click:e.setPayPassword}},[e._v("忘记密码")])],1)],2),e._v(" "),e.orderPaymentData.delivery.express_type.length>0?t("div",{staticClass:"item-block padd-bom-20"},[t("div",{staticClass:"block-text"},[t("span",[e._v("配送方式")]),e._v(" "),"store"==e.orderCreateData.delivery.delivery_type?t("span",{staticClass:"distribution"},[e._v(e._s(e.orderCreateData.delivery.store_name))]):e._e()]),e._v(" "),e._l(e.orderPaymentData.delivery.express_type,(function(r,o){return t("div",{key:o,staticClass:"pay-type-item",class:r.name==e.orderCreateData.delivery.delivery_type?"active":"",on:{click:function(t){return e.selectDeliveryType(r)}}},[e._v("\n      "+e._s(r.title)+"\n    ")])}))],2):e._e(),e._v(" "),t("el-dialog",{attrs:{title:"选择门店",visible:e.dialogStore,width:"50%"},on:{"update:visible":function(t){e.dialogStore=t}}},[t("el-table",{ref:"singleTable",staticClass:"cursor-pointer",attrs:{data:e.storeList,"highlight-current-row":""},on:{"row-click":e.selectStore}},[t("el-table-column",{attrs:{label:"",width:"55"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-radio",{attrs:{label:r.row},model:{value:e.storeRadio,callback:function(t){e.storeRadio=t},expression:"storeRadio"}},[t("i")])]}}])}),e._v(" "),t("el-table-column",{attrs:{prop:"store_name",label:"名称",width:"160"}}),e._v(" "),t("el-table-column",{attrs:{prop:"store_address",label:"地址"}}),e._v(" "),t("el-table-column",{attrs:{prop:"open_date",label:"营业时间"}})],1)],1),e._v(" "),e.orderPaymentData.invoice&&1==e.orderPaymentData.invoice.invoice_status?t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v("发票信息")]),e._v(" "),t("div",{staticClass:"pay-type-list"},[t("div",{staticClass:"pay-type-item",class:0==e.orderCreateData.is_invoice?"active":"",on:{click:e.changeIsInvoice}},[e._v("无需发票")]),e._v(" "),t("div",{staticClass:"pay-type-item",class:1==e.orderCreateData.is_invoice?"active":"",on:{click:e.changeIsInvoice}},[e._v("需要发票")]),e._v(" "),t("div",{staticClass:"clear"})]),e._v(" "),1==e.orderCreateData.is_invoice?t("div",{staticClass:"invoice-information"},[t("div",{staticClass:"invoice-title"},[t("div",{staticClass:"invoice-type-box invoice-title-box"},[t("span",{staticClass:"invoice-name"},[e._v("发票类型：")]),e._v(" "),t("label",{staticClass:"invoice-to-type"},[t("i",{staticClass:"invoice-i-input",class:1==e.orderCreateData.invoice_type?"active":"",on:{click:function(t){return e.clickType(1)}}}),e._v(" "),t("span",[e._v("纸质")])]),e._v(" "),t("label",{staticClass:"invoice-to-type"},[t("i",{staticClass:"invoice-i-input",class:2==e.orderCreateData.invoice_type?"active":"",on:{click:function(t){return e.clickType(2)}}}),e._v(" "),t("span",[e._v("电子")])])]),e._v(" "),t("div",{staticClass:"invoice-type-box invoice-title-box"},[t("span",{staticClass:"invoice-name"},[e._v("抬头类型：")]),e._v(" "),t("label",{staticClass:"invoice-to-type"},[t("i",{staticClass:"invoice-i-input",class:1==e.orderCreateData.invoice_title_type?"active":"",on:{click:function(t){return e.clickTitleType(1)}}}),e._v(" "),t("span",[e._v("个人")])]),e._v(" "),t("label",{staticClass:"invoice-to-type"},[t("i",{staticClass:"invoice-i-input",class:2==e.orderCreateData.invoice_title_type?"active":"",on:{click:function(t){return e.clickTitleType(2)}}}),e._v(" "),t("span",[e._v("企业")])])])]),e._v(" "),t("div",{staticClass:"invoice-type-box"},[t("span",{staticClass:"invoice-name"},[e._v("发票信息：")]),e._v(" "),t("div",{staticClass:"invoice-box-form"},[t("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.orderCreateData.invoice_title,expression:"orderCreateData.invoice_title",modifiers:{trim:!0}}],attrs:{type:"text",placeholder:"请填写抬头名称"},domProps:{value:e.orderCreateData.invoice_title},on:{input:function(t){t.target.composing||e.$set(e.orderCreateData,"invoice_title",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}),e._v(" "),2==e.orderCreateData.invoice_title_type?t("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.orderCreateData.taxpayer_number,expression:"orderCreateData.taxpayer_number",modifiers:{trim:!0}}],attrs:{type:"text",placeholder:"请填写纳税人识别号"},domProps:{value:e.orderCreateData.taxpayer_number},on:{input:function(t){t.target.composing||e.$set(e.orderCreateData,"taxpayer_number",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}):e._e(),e._v(" "),t("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.orderCreateData.invoice_full_address,expression:"orderCreateData.invoice_full_address",modifiers:{trim:!0}},{name:"show",rawName:"v-show",value:1==e.orderCreateData.invoice_type,expression:"orderCreateData.invoice_type == 1"}],attrs:{type:"text",placeholder:"请填写邮寄地址"},domProps:{value:e.orderCreateData.invoice_full_address},on:{input:function(t){t.target.composing||e.$set(e.orderCreateData,"invoice_full_address",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}}),e._v(" "),t("input",{directives:[{name:"model",rawName:"v-model.trim",value:e.orderCreateData.invoice_email,expression:"orderCreateData.invoice_email",modifiers:{trim:!0}},{name:"show",rawName:"v-show",value:2==e.orderCreateData.invoice_type,expression:"orderCreateData.invoice_type == 2"}],attrs:{type:"text",placeholder:"请填写邮箱"},domProps:{value:e.orderCreateData.invoice_email},on:{input:function(t){t.target.composing||e.$set(e.orderCreateData,"invoice_email",t.target.value.trim())},blur:function(t){return e.$forceUpdate()}}})])]),e._v(" "),t("div",{staticClass:"invoice-condition"},[t("span",{staticClass:"invoice-name"},[e._v("发票内容：")]),e._v(" "),t("div",{staticClass:"invoice-box-form"},e._l(e.orderPaymentData.invoice.invoice_content_array,(function(r,o){return t("span",{key:o,staticClass:"option-item",class:{"color-base-bg active":r==e.orderCreateData.invoice_content},on:{click:function(t){return e.changeInvoiceContent(r)}}},[e._v("\n            "+e._s(r)+"\n          ")])})),0)]),e._v(" "),t("div",{staticClass:"invoice-tops"},[e._v("发票内容将以根据税法调整，具体请以展示为准，发票内容显示详细商品名 称及价格信息")])]):e._e()]):e._e(),e._v(" "),e._m(1),e._v(" "),t("div",[e.calculateData?t("div",{staticClass:"item-block"},[t("div",{staticClass:"goods-list"},[t("table",e._l(e.calculateData.goods_list,(function(r,o){return t("tr",{key:o},[t("td",{attrs:{width:"50%"}},[t("div",{staticClass:"goods-info"},[t("div",{staticClass:"goods-info-left"},[t("router-link",{attrs:{to:{path:"/sku/"+r.sku_id},target:"_blank"}},[t("img",{staticClass:"goods-img",attrs:{src:e.$img(r.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(o)}}})])],1),e._v(" "),t("div",{staticClass:"goods-info-right"},[t("router-link",{attrs:{to:{path:"/sku/"+r.sku_id},target:"_blank"}},[t("div",{staticClass:"goods-name"},[e._v(e._s(r.goods_name))])]),e._v(" "),r.sku_spec_format?t("div",{staticClass:"goods-spec"},e._l(r.sku_spec_format,(function(r,i){return t("span",{key:i},[e._v(e._s(r.spec_value_name))])})),0):e._e()],1)])]),e._v(" "),t("td",{staticClass:"goods-price",attrs:{width:"12.5%"}},[e._v("￥"+e._s(r.price))]),e._v(" "),t("td",{staticClass:"goods-num",attrs:{width:"12.5%"}},[e._v(e._s(r.num))]),e._v(" "),t("td",{staticClass:"goods-money",attrs:{width:"12.5%"}},[e._v("￥"+e._s((r.price*r.num).toFixed(2)))])])})),0)])]):e._e()]),e._v(" "),e.promotionInfo?t("div",{staticClass:"item-block"},[t("div",{staticClass:"block-text"},[e._v(e._s(e.promotionInfo.title))]),e._v(" "),t("div",{staticClass:"order-cell platform-coupon"},[t("div",{staticClass:"box ns-text-color",domProps:{innerHTML:e._s(e.promotionInfo.content)}})])]):e._e(),e._v(" "),t("div",{staticClass:"item-block padd-bom-10"},[t("div",{staticClass:"block-text"},[e._v("买家留言：")]),e._v(" "),t("el-input",{staticClass:"buyer-message",attrs:{rows:"3",type:"textarea",placeholder:"留言前建议先与商家协调一致",maxlength:"140","show-word-limit":"",resize:"none"},on:{input:e.textarea},model:{value:e.orderCreateData.buyer_message,callback:function(t){e.$set(e.orderCreateData,"buyer_message",t)},expression:"orderCreateData.buyer_message"}})],1),e._v(" "),e.calculateData?t("div",{staticClass:"item-block"},[t("div",{staticClass:"order-statistics"},[t("table",[t("tr",[t("td",{attrs:{align:"right"}},[e._v("商品金额：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.goods_money)))])]),e._v(" "),0==e.calculateData.is_virtual&&e.calculateData.delivery_money>0?t("tr",[t("td",{attrs:{align:"right"}},[e._v("运费：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.delivery_money)))])]):e._e(),e._v(" "),e.calculateData.invoice_money>0?t("tr",[t("td",{attrs:{align:"right"}},[e._v("发票税费"),t("span",{staticClass:"ns-text-color"},[e._v("("+e._s(e.calculateData.invoice.invoice_rate)+"%)")]),e._v("：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.invoice_money)))])]):e._e(),e._v(" "),e.calculateData.invoice_delivery_money>0?t("tr",[t("td",{attrs:{align:"right"}},[e._v("发票邮寄费：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.invoice_delivery_money)))])]):e._e(),e._v(" "),e.calculateData.promotion_money>0?t("tr",[t("td",{attrs:{align:"right"}},[e._v("优惠：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.promotion_money)))])]):e._e(),e._v(" "),e.calculateData.balance_money>0?t("tr",[t("td",{attrs:{align:"right"}},[e._v("使用余额：")]),e._v(" "),t("td",{attrs:{align:"left"}},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.balance_money)))])]):e._e()])]),e._v(" "),t("div",{staticClass:"clear"})]):e._e(),e._v(" "),e.calculateData?t("div",{staticClass:"item-block"},[t("div",{staticClass:"order-submit"},[t("div",{staticClass:"order-money"},[e._v("\n        共"+e._s(e.calculateData.goods_num)+"件，应付金额：\n        "),t("div",{staticClass:"ns-text-color"},[e._v("￥"+e._s(e._f("moneyFormat")(e.calculateData.pay_money)))])]),e._v(" "),t("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:e.orderCreate}},[e._v("订单结算")])],1),e._v(" "),t("div",{staticClass:"clear"})]):e._e()],1)}),[function(){var e=this._self._c;return e("div",{staticClass:"add-address"},[e("i",{staticClass:"el-icon-circle-plus-outline"}),this._v("\n          添加收货地址\n        ")])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"item-block"},[t("div",{staticClass:"goods-list"},[t("table",[t("tr",[t("td",{attrs:{width:"50%"}},[e._v("商品")]),e._v(" "),t("td",{attrs:{width:"12.5%"}},[e._v("价格")]),e._v(" "),t("td",{attrs:{width:"12.5%"}},[e._v("数量")]),e._v(" "),t("td",{attrs:{width:"12.5%"}},[e._v("小计")])])])])])}],!1,null,"044ca77b",null);t.default=component.exports}}]);