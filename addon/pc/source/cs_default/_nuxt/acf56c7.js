(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{541:function(e,r,t){"use strict";var o=t(204);r.a={methods:{orderPay:function(e){var r=this;0==e.adjust_money?Object(o.f)({order_ids:e.order_id}).then((function(e){e.code>=0?r.$router.push({path:"/pay",query:{code:e.data}}):r.$message({message:e.message,type:"warning"})})):this.$confirm("商家已将支付金额调整为"+e.pay_money+"元，是否继续支付？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.f)({order_ids:e.order_id}).then((function(e){e.code>=0?r.$router.push({path:"/pay",query:{code:e.data}}):r.$message({message:e.message,type:"warning"})}))}))},orderClose:function(e,r){var t=this;this.$confirm("您确定要关闭该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.b)({order_id:e}).then((function(e){t.$message({message:"订单关闭成功",type:"success"}),"function"==typeof r&&r()}))}))},orderDelivery:function(e,r){var t=this;this.$confirm("您确定已经收到货物了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.g)({order_id:e}).then((function(e){t.$message({message:"订单收货成功",type:"success"}),"function"==typeof r&&r()})).catch((function(e){t.$message({message:e.message,type:"warning"}),"function"==typeof r&&r()}))}))},orderVirtualDelivery:function(e,r){var t=this;this.$confirm("您确定要进行收货吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.a)({order_id:e}).then((function(e){t.$message({message:"订单收货成功",type:"success"}),"function"==typeof r&&r()}))}))}}}},610:function(e,r,t){},703:function(e,r,t){"use strict";t(610)},782:function(e,r,t){"use strict";t.r(r);t(56),t(315),t(24),t(25),t(23),t(7),t(29),t(18),t(30);var o=t(10),n=(t(73),t(204)),_=t(541),l=t(12);function d(object,e){var r=Object.keys(object);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(object);e&&(t=t.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),r.push.apply(r,t)}return r}var v={name:"order_detail_pickup",components:{},mixins:[_.a],data:function(){return{orderId:0,orderDetail:null,loading:!0,yes:!0}},created:function(){this.orderId=this.$route.query.order_id,this.getOrderDetail()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(r){Object(o.a)(e,r,source[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(source,r))}))}return e}({},Object(l.b)(["defaultGoodsImage"])),layout:"member",methods:{getOrderDetail:function(){var e=this;Object(n.c)({order_id:this.orderId}).then((function(r){r.code>=0?(e.orderDetail=r.data,""!=e.orderDetail.delivery_store_info&&(e.orderDetail.delivery_store_info=JSON.parse(e.orderDetail.delivery_store_info)),e.loading=!1):e.$message({message:"未获取到订单信息",type:"warning",duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})})).catch((function(r){e.loading=!1,e.$message.error({message:r.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})}))},operation:function(e){var r=this;switch(e){case"orderPay":this.orderPay(this.orderDetail);break;case"orderClose":this.orderClose(this.orderDetail.order_id,(function(){r.getOrderDetail()}));break;case"memberTakeDelivery":this.orderDelivery(this.orderDetail.order_id,(function(){r.getOrderDetail()}));break;case"trace":this.$router.push({path:"/order/logistics",query:{order_id:this.orderDetail.order_id}});break;case"memberOrderEvaluation":this.$router.push({path:"/order/evaluate",query:{order_id:this.orderDetail.order_id}});break;case"memberBatchRefund":this.$router.push({path:"/order/batchrefund",query:{order_id:this.orderId}});break;case"orderOfflinePay":this.$router.push({path:"/pay",query:{code:this.orderDetail.offline_pay_info.out_trade_no}})}},imageError:function(e){this.orderDetail.order_goods[e].sku_image=this.defaultGoodsImage}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}},c=v,m=(t(703),t(6)),component=Object(m.a)(c,(function(){var e=this,r=e._self._c;return r("div",{staticClass:"box"},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),r("el-card",{staticClass:"box-card order-detail"},[r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",{attrs:{to:{path:"/member/order_list"}}},[e._v("订单列表")]),e._v(" "),r("el-breadcrumb-item",[e._v("订单详情")])],1)],1),e._v(" "),r("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e.orderDetail?[r("div",{staticClass:"order-status"},[r("h4",[e._v("\n              订单状态：\n              "),r("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.order_status_name))])]),e._v(" "),0==e.orderDetail.order_status?r("div",{staticClass:"go-pay"},[r("p",[e._v("\n                需付款：\n                "),r("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])]):e._e(),e._v(" "),e.orderDetail.action.length>0?r("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?r("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(r){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e(),e._v(" "),e._l(e.orderDetail.action,(function(t,o){return r("el-button",{key:o,attrs:{type:"primary",size:"mini",plain:"orderPay"!=t.action},on:{click:function(r){return e.operation(t.action)}}},[e._v(e._s(t.title))])}))],2):0==e.orderDetail.action.length&&1==e.orderDetail.is_evaluate?r("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?r("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(r){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e()],1):e._e()]),e._v(" "),e.orderDetail.pay_status?r("div",{staticClass:"pickup-info"},[r("h4",[e._v("\n              自提点：\n              "),r("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.delivery_store_name))])]),e._v(" "),r("ul",[r("li",[e._v("\n                提货码：\n                "),r("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.delivery_code))])]),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.open_date?r("li",[e._v("营业时间："+e._s(e.orderDetail.delivery_store_info.open_date))]):e._e(),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.telphone?r("li",[e._v("联系方式："+e._s(e.orderDetail.delivery_store_info.telphone))]):e._e(),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.full_address?r("li",[e._v("详细地址："+e._s(e.orderDetail.delivery_store_info.full_address))]):e._e()]),e._v(" "),r("img",{attrs:{src:e.$img(e.orderDetail.pickup)}})]):e._e(),e._v(" "),r("nav",[r("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[e._v("商品信息")]),e._v(" "),r("li",[e._v("单价")]),e._v(" "),r("li",[e._v("数量")]),e._v(" "),r("li",[e._v("小计")]),e._v(" "),e.orderDetail.is_enable_refund?r("li",[e._v("操作")]):e._e()]),e._v(" "),r("div",{staticClass:"order-info"},[r("h4",[e._v("订单信息")]),e._v(" "),r("ul",[r("li",[e._v("订单类型："+e._s(e.orderDetail.order_type_name))]),e._v(" "),r("li",[e._v("订单编号："+e._s(e.orderDetail.order_no))]),e._v(" "),r("li",[e._v("订单交易号："+e._s(e.orderDetail.out_trade_no))]),e._v(" "),r("li",[e._v("配送方式："+e._s(e.orderDetail.delivery_type_name))]),e._v(" "),r("li",[e._v("创建时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.create_time)))]),e._v(" "),e.orderDetail.close_time>0?r("li",[e._v("关闭时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.close_time)))]):e._e(),e._v(" "),e.orderDetail.pay_status>0?[r("li",[e._v("支付方式："+e._s(e.orderDetail.pay_type_name))]),e._v(" "),r("li",[e._v("支付时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.pay_time)))])]:e._e(),e._v(" "),""!=e.orderDetail.promotion_type_name?r("li",[e._v("店铺活动："+e._s(e.orderDetail.promotion_type_name))]):e._e(),e._v(" "),""!=e.orderDetail.buyer_message?r("li",[e._v("买家留言："+e._s(e.orderDetail.buyer_message))]):e._e()],2)]),e._v(" "),"offlinepay"==e.orderDetail.pay_type&&e.orderDetail.offline_pay_info?r("div",{staticClass:"order-info"},[r("h4",[e._v("线下支付")]),e._v(" "),r("ul",["WAIT_AUDIT"==e.orderDetail.offline_pay_info.status_info.const?r("li",[e._v("\n                支付状态：审核中\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?r("li",[e._v("\n                支付状态：审核被拒\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?r("li",[e._v("\n                审核备注："+e._s(e.orderDetail.offline_pay_info.audit_remark)+"\n              ")]):e._e()])]):e._e(),e._v(" "),r("div",{staticClass:"take-delivery-info"},[r("h4",[e._v("收货信息")]),e._v(" "),r("ul",[r("li",[e._v("收货人："+e._s(e.orderDetail.name))]),e._v(" "),r("li",[e._v("手机号码："+e._s(e.orderDetail.mobile))]),e._v(" "),r("li",[e._v("收货地址："+e._s(e.orderDetail.full_address)+" "+e._s(e.orderDetail.address))])])]),e._v(" "),r("div",{staticClass:"list"},e._l(e.orderDetail.order_goods,(function(t,o){return r("ul",{key:o,staticClass:"item"},[r("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[r("div",{staticClass:"img-wrap",on:{click:function(r){return e.$util.pushToTab("/sku/"+t.sku_id)}}},[r("img",{attrs:{src:e.$img(t.sku_image,{size:"mid"})},on:{error:function(r){return e.imageError(o)}}})]),e._v(" "),r("div",{staticClass:"info-wrap"},[r("h5",{on:{click:function(r){return e.$util.pushToTab("/sku/"+t.sku_id)}}},[e._v(e._s(t.sku_name))])])]),e._v(" "),r("li",[r("span",[e._v("￥"+e._s(t.price))])]),e._v(" "),r("li",[r("span",[e._v(e._s(t.num))])]),e._v(" "),r("li",[r("span",[e._v("￥"+e._s((t.price*t.num).toFixed(2)))])]),e._v(" "),e.orderDetail.is_enable_refund?r("li",[0==t.refund_status||-1==t.refund_status?r("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(r){return e.$router.push({path:"/order/refund",query:{order_goods_id:t.order_goods_id}})}}},[e._v("退款")]):r("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(r){return e.$router.push({path:"/order/refund_detail",query:{order_goods_id:t.order_goods_id}})}}},[e._v("查看退款")])],1):e._e()])})),0),e._v(" "),r("ul",{staticClass:"total"},[r("li",[r("label",[e._v("商品金额：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.goods_money))])]),e._v(" "),r("li",[r("label",[e._v("运费：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.delivery_money))])]),e._v(" "),e.orderDetail.member_card_money>0?r("li",[r("label",[e._v("会员卡：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.member_card_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_money>0?r("li",[r("label",[e._v("税费：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.invoice_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_delivery_money>0?r("li",[r("label",[e._v("发票邮寄费：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.invoice_delivery_money))])]):e._e(),e._v(" "),0!=e.orderDetail.adjust_money?r("li",[r("label",[e._v("订单调整：")]),e._v(" "),r("span",[e.orderDetail.adjust_money<0?[e._v("-")]:[e._v("+")],e._v("\n\t\t\t\t\t\t\t\t￥"+e._s(e._f("abs")(e.orderDetail.adjust_money))+"\n\t\t\t\t\t\t\t")],2)]):e._e(),e._v(" "),e.orderDetail.promotion_money>0?r("li",[r("label",[e._v("优惠：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.promotion_money))])]):e._e(),e._v(" "),e.orderDetail.coupon_money>0?r("li",[r("label",[e._v("优惠券：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.coupon_money))])]):e._e(),e._v(" "),e.orderDetail.balance_money>0?r("li",[r("label",[e._v("使用余额：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.balance_money))])]):e._e(),e._v(" "),e.orderDetail.point_money>0?r("li",[r("label",[e._v("积分抵扣：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.point_money))])]):e._e(),e._v(" "),r("li",{staticClass:"pay-money"},[r("label",[e._v("实付款：")]),e._v(" "),r("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])])]:e._e()],2)])],1)}),[],!1,null,"36510428",null);r.default=component.exports}}]);