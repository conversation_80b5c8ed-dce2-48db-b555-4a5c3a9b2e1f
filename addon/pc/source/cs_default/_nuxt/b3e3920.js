(window.webpackJsonp=window.webpackJsonp||[]).push([[53],{622:function(t,e,o){},623:function(t,e,o){},714:function(t,e,o){"use strict";o(622)},715:function(t,e,o){"use strict";o(623)},793:function(t,e,o){"use strict";o.r(e);o(24),o(25),o(23),o(29),o(18),o(30);var n=o(10),r=(o(92),o(7),o(74),o(209),o(317),o(12)),l=o(204),d=o(33);function c(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var m={name:"evaluate",components:{},data:function(){return{loading:!0,value1:5,memberName:"",memberHeadimg:"",orderId:null,orderNo:"",isAnonymous:0,goodsList:[],goodsEvalList:[],imgList:[],isEvaluate:0,flag:!1,siteName:"",shop_deliverycredit:5,shop_desccredit:5,shop_servicecredit:5,uploadActionUrl:d.a.baseUrl+"/api/upload/evaluateimg",uploadData:{app_type:"pc",app_type_name:"PC"},dialogImageUrl:"",dialogVisible:!1,hide:[]}},created:function(){this.orderId=this.$route.query.order_id,this.getUserInfo(),this.orderId&&this.getOrderInfo()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),layout:"member",methods:{handleSuccess:function(t,e,o){var n=this.imgList[o];n=n.concat(t.data.pic_path),this.imgList[o]=[],this.$set(this.imgList,o,n),this.isEvaluate?this.goodsEvalList[o].again_images=this.imgList[o].toString():this.goodsEvalList[o].images=this.imgList[o].toString(),this.imgList[o].length>=6&&(this.hide[o]=!0)},handleRemove:function(t,e,o){var i=this.$util.inArray(t.response.data.pic_path,this.imgList[o]);this.imgList[o].splice(i,1),this.isEvaluate?this.goodsEvalList[o].again_images=this.imgList[o].toString():this.goodsEvalList[o].images=this.imgList[o].toString(),this.imgList[o].length<6&&(this.hide[o]=!1)},handleExceed:function(t,e){this.$message.warning("上传图片最大数量为6张")},handlePictureCardPreview:function(t){this.dialogImageUrl=t.url,this.dialogVisible=!0},getUserInfo:function(){var t=this;this.$store.dispatch("member/member_detail",{refresh:1}).then((function(e){t.memberName=e.data.nickname,t.memberHeadimg=e.data.headimg})).catch((function(e){t.$message.error(e.message)}))},getOrderInfo:function(){var t=this;Object(l.h)({order_id:this.orderId}).then((function(e){if(0==e.code)if(t.isEvaluate=e.data.evaluate_status,t.orderNo=e.data.list[0].order_no,t.goodsList=e.data.list,t.siteName=e.data.list[0].site_name,t.isEvaluate)for(var i=0;i<e.data.list.length;i++){t.imgList.push([]),t.hide.push(!1),t.goodsEvalList.push({order_goods_id:e.data.list[i].order_goods_id,goods_id:e.data.list[i].goods_id,sku_id:e.data.list[i].sku_id,again_content:"",again_images:"",site_id:e.data.list[i].site_id})}else for(var o=0;o<e.data.list.length;o++){t.imgList.push([]),t.goodsEvalList.push({content:"",images:"",scores:5,explain_type:1,order_goods_id:e.data.list[o].order_goods_id,goods_id:e.data.list[o].goods_id,sku_id:e.data.list[o].sku_id,sku_name:e.data.list[o].sku_name,sku_price:e.data.list[o].price,sku_image:e.data.list[o].sku_image,site_id:e.data.list[o].site_id})}t.loading=!1})).catch((function(e){t.$message.error(e.message),t.$router.push("/member/order_list"),t.loading=!1}))},setStar:function(t){this.goodsEvalList[t].scores>=4?this.goodsEvalList[t].explain_type=1:1<this.goodsEvalList[t].scores&&this.goodsEvalList[t].scores<4?this.goodsEvalList[t].explain_type=2:this.goodsEvalList[t].explain_type=3},imageError:function(t){this.goodsList[t].sku_image=this.defaultGoodsImage},save:function(){for(var t=this,i=0;i<this.goodsEvalList.length;i++)if(this.isEvaluate){if(!this.goodsEvalList[i].again_content.trim().length)return void this.$message({message:"商品的评价不能为空哦",type:"warning"})}else if(!this.goodsEvalList[i].content.trim().length)return void this.$message({message:"商品的评价不能为空哦",type:"warning"});var e=JSON.stringify(this.goodsEvalList),data={order_id:this.orderId,goods_evaluate:e,isEvaluate:this.isEvaluate};this.isEvaluate||(data.order_no=this.orderNo,data.member_name=this.memberName,data.member_headimg=this.memberHeadimg,data.is_anonymous=this.isAnonymous),this.flag||(this.flag=!0,Object(l.i)(data).then((function(e){0==e.code?t.$message({message:"评价成功",type:"success",duration:2e3,onClose:function(){t.$router.push({path:"/member/order_list"})}}):(t.$message({message:e.message,type:"warning"}),t.flag=!1)})).catch((function(e){t.$message.error(e.message),t.flag=!1})))},toGoodsDetail:function(t){this.$util.pushToTab("sku-"+t)}}},h=m,v=(o(714),o(715),o(6)),component=Object(v.a)(h,(function(){var t=this,e=t._self._c;return e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"ns-evalute"},[t._l(t.goodsList,(function(o,n){return e("div",{key:n,staticClass:"ns-eva-li"},[e("div",{staticClass:"ns-eva-good"},[e("el-image",{attrs:{fit:"scale-down",src:t.$img(o.sku_image,{size:"mid"})},on:{error:function(e){return t.imageError(n)},click:function(e){return t.toGoodsDetail(o.sku_id)}}}),t._v(" "),e("p",{staticClass:"ns-eva-good-name",attrs:{title:o.sku_name},on:{click:function(e){return t.toGoodsDetail(o.sku_id)}}},[t._v(t._s(o.sku_name))]),t._v(" "),e("p",[t._v("￥"+t._s(o.price))])],1),t._v(" "),e("div",{staticClass:"ns-eva-form"},[t.isEvaluate?t._e():e("div",{staticClass:"block"},[e("span",{staticClass:"demonstration"},[t._v("描述相符：")]),t._v(" "),e("el-rate",{on:{change:function(e){return t.setStar(n)}},model:{value:t.goodsEvalList[n].scores,callback:function(e){t.$set(t.goodsEvalList[n],"scores",e)},expression:"goodsEvalList[index].scores"}}),t._v(" "),e("div",{staticClass:"level"},[e("i",{staticClass:"iconfont",class:"1"==t.goodsEvalList[n].explain_type?"iconhaoping1 ns-text-color":"2"==t.goodsEvalList[n].explain_type?"iconzhongchaping ns-text-color":"3"==t.goodsEvalList[n].explain_type?"iconzhongchaping":""}),t._v(" "),e("span",[t._v("\n              "+t._s("1"==t.goodsEvalList[n].explain_type?"好评":"2"==t.goodsEvalList[n].explain_type?"中评":"3"==t.goodsEvalList[n].explain_type?"差评":"")+"\n            ")])])],1),t._v(" "),e("div",{staticClass:"ns-textarea"},[t.isEvaluate?e("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请在此处输入您的追评",maxlength:"200","show-word-limit":""},model:{value:t.goodsEvalList[n].again_content,callback:function(e){t.$set(t.goodsEvalList[n],"again_content",e)},expression:"goodsEvalList[index].again_content"}}):e("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请在此处输入您的评价",maxlength:"200","show-word-limit":""},model:{value:t.goodsEvalList[n].content,callback:function(e){t.$set(t.goodsEvalList[n],"content",e)},expression:"goodsEvalList[index].content"}})],1),t._v(" "),e("div",{staticClass:"upload-wrap"},[e("el-upload",{ref:"upload",refInFor:!0,class:{ishide:t.hide[n]},attrs:{action:t.uploadActionUrl,data:t.uploadData,"list-type":"picture-card","on-success":function(e,o){return t.handleSuccess(e,o,n)},"on-preview":t.handlePictureCardPreview,"on-remove":function(e,o){return t.handleRemove(e,o,n)},"on-exceed":t.handleExceed,multiple:"",drag:"",limit:6}},[e("i",{staticClass:"el-icon-plus"})]),t._v(" "),e("el-dialog",{attrs:{visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("img",{attrs:{width:"100%",src:t.dialogImageUrl,alt:""}})]),t._v(" "),e("div",{staticClass:"tips"},[t._v("共6张，还能上传"+t._s(t.imgList[n].length?6-t.imgList[n].length:6)+"张")])],1)])])})),t._v(" "),e("div",{staticClass:"save-btn-wrap"},[e("el-button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("提交")])],1)],2)}),[],!1,null,"1cd1c998",null);e.default=component.exports}}]);