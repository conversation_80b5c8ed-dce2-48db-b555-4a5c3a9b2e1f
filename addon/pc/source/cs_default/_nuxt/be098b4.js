(window.webpackJsonp=window.webpackJsonp||[]).push([[25],{535:function(t,e,r){"use strict";var n=r(3),o=r(35),l=r(14),c=r(8),path=r(319),v=r(5),f=r(113),m=r(16),d=r(205),_=r(63),h=r(112),I=r(318),y=r(4),N=r(93).f,w=r(57).f,O=r(26).f,k=r(320),j=r(316).trim,E="Number",T=c[E],C=path[E],S=T.prototype,x=c.TypeError,L=v("".slice),P=v("".charCodeAt),A=function(t){var e=I(t,"number");return"bigint"==typeof e?e:$(e)},$=function(t){var e,r,n,o,l,c,v,code,f=I(t,"number");if(h(f))throw x("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=j(f),43===(e=P(f,0))||45===e){if(88===(r=P(f,2))||120===r)return NaN}else if(48===e){switch(P(f,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+f}for(c=(l=L(f,2)).length,v=0;v<c;v++)if((code=P(l,v))<48||code>o)return NaN;return parseInt(l,n)}return+f},F=f(E,!T(" 0o1")||!T("0b1")||T("+0x1")),G=function(t){return _(S,t)&&y((function(){k(t)}))},D=function(t){var e=arguments.length<1?0:T(A(t));return G(this)?d(Object(e),this,D):e};D.prototype=S,F&&!o&&(S.constructor=D),n({global:!0,constructor:!0,wrap:!0,forced:F},{Number:D});var M=function(t,source){for(var e,r=l?N(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;r.length>n;n++)m(source,e=r[n])&&!m(t,e)&&O(t,e,w(source,e))};o&&C&&M(path[E],C),(F||o)&&M(path[E],T)},542:function(t,e,r){var n=r(3),o=r(548);n({target:"Number",stat:!0,forced:Number.parseInt!=o},{parseInt:o})},548:function(t,e,r){var n=r(8),o=r(4),l=r(5),c=r(19),v=r(316).trim,f=r(209),m=n.parseInt,d=n.Symbol,_=d&&d.iterator,h=/^[+-]?0x/i,I=l(h.exec),y=8!==m(f+"08")||22!==m(f+"0x16")||_&&!o((function(){m(Object(_))}));t.exports=y?function(t,e){var r=v(c(t));return m(r,e>>>0||(I(h,r)?16:10))}:m},560:function(t,e,r){},576:function(t,e,r){"use strict";r(560)},659:function(t,e,r){"use strict";r.r(e);r(321),r(542),r(535),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=(r(65),r(12));function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}function c(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var v={name:"floor-style-4",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:c(c({},Object(o.b)(["defaultGoodsImage"])),{},{goodsList:function(){var t=[];try{t=this.data.value.goodsList.value.list}catch(e){t=[]}return t},itemNum:function(){var t=[0,this.goodsList.length],e=t[0],r=t[1];return e=parseInt(r/3),parseInt(r%3)>0&&(e+=1),e}}),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage},itemList:function(t){if(t-=1,!this.goodsList.length)return[];var e=3*t,r=3*t+3;return this.goodsList.slice(e,r)}}},f=(r(576),r(6)),component=Object(f.a)(v,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-4"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))]),t._v(" "),e("div",{staticClass:"more",style:{color:t.data.value.more.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.more.value.link.url)}}},[e("span",[t._v(t._s(t.data.value.more.value.text))]),t._v(" "),e("i",{staticClass:"el-icon-arrow-right"})])]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[t.data.value.leftImg.value.url?e("div",{staticClass:"left-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}})]):t._e(),t._v(" "),e("el-carousel",{attrs:{trigger:"click",height:"324px","indicator-position":"none",arrow:"never"}},t._l(t.itemNum,(function(r){return e("el-carousel-item",{key:r},[e("ul",{staticClass:"goods-list"},t._l(t.itemList(r),(function(r,n){return e("li",{key:n,attrs:{title:r.goods_name},on:{click:function(e){return t.goSku(r.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(r.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("div",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v("￥"+t._s(r.discount_price))]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(r.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v("￥"+t._s(r.market_price))])]),t._v(" "),e("h3",{staticClass:"name"},[t._v(t._s(r.goods_name))]),t._v(" "),r.sale_num?e("div",{staticClass:"other-info"},[e("span",[t._v("已售"+t._s(r.sale_num)+"件")])]):t._e()])})),0)])})),1)],1),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"bdd97972",null);e.default=component.exports}}]);