(window.webpackJsonp=window.webpackJsonp||[]).push([[38],{607:function(t,e,n){},700:function(t,e,n){"use strict";n(607)},752:function(t,e,n){"use strict";n.r(e);n(73),n(7),n(18);var o=n(1);var c={name:"my_point",layout:"member",components:{},data:function(){return{pointInfo:{page:1,page_size:10,account_type:"point"},pointList:[],memberPoint:{point:0},total:0,loading:!0,yes:!0}},created:function(){this.getPointInfo(),this.getPointList()},mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},methods:{getPointInfo:function(){var t,e=this;(t={account_type:this.pointInfo.account_type},Object(o.a)({url:"/api/memberaccount/info",data:t,forceLogin:!0})).then((function(t){0==t.code&&t.data&&(e.memberPoint=t.data),e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error(t.message)}))},getPointList:function(){var t,e=this;(t=this.pointInfo,Object(o.a)({url:"/api/memberaccount/page",data:t,forceLogin:!0})).then((function(t){0==t.code&&t.data&&(e.pointList=t.data.list,e.total=t.data.count,e.pointList.forEach((function(t){t.time=e.$util.timeStampTurnTime(t.create_time),t.pointNum=t.account_data>0?"+"+parseInt(t.account_data):parseInt(t.account_data)})))})).catch((function(t){e.$message.error(t.message)}))},handlePageSizeChange:function(t){this.pointInfo.page_size=t,this.getPointList()},handleCurrentPageChange:function(t){this.pointInfo.page=t,this.getPointList()}}},r=(n(700),n(6)),component=Object(r.a)(c,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"my-point"},[e("div",{staticClass:"member-point"},[e("div",{staticClass:"title"},[t._v("我的积分")]),t._v(" "),e("div",{staticClass:"num"},[t._v(t._s(t.memberPoint.point?Math.ceil(t.memberPoint.point):0))])]),t._v(" "),e("div",{staticClass:"detail"},[e("el-table",{attrs:{data:t.pointList,border:""}},[e("el-table-column",{attrs:{prop:"type_name",label:"来源",width:"150"}}),t._v(" "),e("el-table-column",{attrs:{prop:"pointNum",label:"积分",width:"150"}}),t._v(" "),e("el-table-column",{attrs:{prop:"remark",label:"详细说明"}}),t._v(" "),e("el-table-column",{attrs:{prop:"time",label:"时间",width:"180"}})],1)],1),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.pointInfo.page,"page-size":t.pointInfo.page_size,"hide-on-single-page":""},on:{"update:currentPage":function(e){return t.$set(t.pointInfo,"page",e)},"update:current-page":function(e){return t.$set(t.pointInfo,"page",e)},"update:pageSize":function(e){return t.$set(t.pointInfo,"page_size",e)},"update:page-size":function(e){return t.$set(t.pointInfo,"page_size",e)},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)])])}),[],!1,null,"0c506c7c",null);e.default=component.exports}}]);