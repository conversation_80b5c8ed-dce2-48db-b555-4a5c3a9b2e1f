(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{559:function(t,e,o){},575:function(t,e,o){"use strict";o(559)},658:function(t,e,o){"use strict";o.r(e);o(321),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var r=o(10),l=o(12);function n(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var c={name:"floor-style-3",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?n(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):n(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(l.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageErrorRight:function(t){this.data.value.rightGoodsList.value.list[t].goods_image=this.defaultGoodsImage},imageErrorBottom:function(t){this.data.value.bottomGoodsList.value.list[t].goods_image=this.defaultGoodsImage}}},d=c,v=(o(575),o(6)),component=Object(v.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-3"},[e("div",{staticClass:"item-wrap"},[e("div",{staticClass:"head-wrap"},[t.data.value.title.value.text?e("div",{staticClass:"title-name"},[e("span",{style:{backgroundColor:t.data.value.title.value.color}}),t._v(" "),e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))])]):t._e(),t._v(" "),e("div",{staticClass:"category-wrap"},t._l(t.data.value.categoryList.value.list,(function(o,r){return e("li",{key:r},[e("router-link",{attrs:{target:"_blank",to:{path:"/goods/list",query:{category_id:o.category_id,level:o.level}}}},[t._v(t._s(o.category_name))])],1)})),0)]),t._v(" "),e("div",{staticClass:"body-wrap"},[e("div",{staticClass:"left-img-wrap"},[t.data.value.leftImg.value.url?e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}}):t._e()]),t._v(" "),e("ul",{staticClass:"right-goods-wrap"},t._l(t.data.value.rightGoodsList.value.list,(function(o,r){return e("li",{key:r,on:{click:function(e){return t.goSku(o.sku_id)}}},[e("h4",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"ns-text-color"},[t._v(t._s(o.introduction))]),t._v(" "),e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageErrorRight(r)}}})])])})),0),t._v(" "),e("ul",{staticClass:"bottom-goods-wrap"},t._l(t.data.value.bottomGoodsList.value.list,(function(o,r){return e("li",{key:r,on:{click:function(e){return t.goSku(o.sku_id)}}},[e("div",{staticClass:"info-wrap"},[e("h4",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"ns-text-color"},[t._v(t._s(o.introduction))])]),t._v(" "),e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageErrorBottom(r)}}})])])})),0)])])])}),[],!1,null,"d0ce147e",null);e.default=component.exports}}]);