(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{564:function(t,e,n){"use strict";n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return r}));var o=n(1);function c(t){return Object(o.a)({url:"/coupon/api/coupon/typepagelists",data:t})}function r(t){return Object(o.a)({url:"/coupon/api/coupon/receive",data:t,forceLogin:!0})}},591:function(t,e,n){},679:function(t,e,n){"use strict";n(591)},768:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(29),n(30);var o=n(10),c=(n(7),n(18),n(564)),r=n(12),l=n(27);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var _={name:"coupon",components:{},data:function(){return{couponList:[],total:0,currentPage:1,pageSize:9,couponBtnSwitch:!1,activeName:"first",loading:!0,loadingAd:!0,adList:[]}},created:function(){var t=this;this.addonIsExit&&1!=this.addonIsExit.coupon?this.$message({message:"优惠券插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}}):(this.getAdList(),this.getCanReceiveCouponQuery())},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["addonIsExit"])),watch:{addonIsExit:function(){var t=this;1!=this.addonIsExit.coupon&&this.$message({message:"优惠券插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}})}},head:function(){return{title:"领券专区-"+this.$store.state.site.siteInfo.site_name,meta:[{name:"description",content:this.$store.state.site.siteInfo.seo_description},{name:"keyword",content:this.$store.state.site.siteInfo.seo_keywords}]}},methods:{getAdList:function(){var t=this;Object(l.a)({keyword:"NS_PC_COUPON"}).then((function(e){t.adList=e.data.adv_list;for(var i=0;i<t.adList.length;i++)t.adList[i].adv_url&&(t.adList[i].adv_url=JSON.parse(t.adList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},handleClick:function(t,e){this.loading=!0},myCoupon:function(){this.$util.pushToTab("/member/coupon")},getCanReceiveCouponQuery:function(){var t=this;Object(c.b)({page:this.currentPage,page_size:this.pageSize,activeName:this.activeName}).then((function(e){t.couponList=e.data.list,t.total=e.data.count,t.couponList.forEach((function(t){t.count==t.lead_count?t.useState=2:0!=t.max_fetch&&t.member_coupon_num&&t.member_coupon_num>=t.max_fetch?t.useState=1:t.useState=0,t.received_type&&"expire"==t.received_type&&(t.useState=2)})),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},handlePageSizeChange:function(t){this.pageSize=t,this.loading=!0,this.getCanReceiveCouponQuery()},handleCurrentPageChange:function(t){this.currentPage=t,this.loading=!0,this.getCanReceiveCouponQuery()},couponTap:function(t,e){0==t.useState?this.receiveCoupon(t,e):this.toGoodsList(t)},receiveCoupon:function(t,e){var n=this;if(!this.couponBtnSwitch){this.couponBtnSwitch=!0;var data={site_id:t.site_id,activeName:this.activeName};data.coupon_type_id=t.coupon_type_id,Object(c.a)(data).then((function(e){e.data;var o=e.message;0==e.code?(o="领取成功",n.$message({message:o,type:"success"})):n.$message({message:o,type:"warning"});var c=n.couponList;if(1==e.data.is_exist)for(var i=0;i<c.length;i++)"first"==n.activeName?c[i].coupon_type_id==t.coupon_type_id&&(c[i].useState=1):c[i].platformcoupon_type_id==t.platformcoupon_type_id&&(c[i].useState=1);else for(var r=0;r<c.length;r++)"first"==n.activeName?c[r].coupon_type_id==t.coupon_type_id&&(c[r].received_type=e.data.type,c[r].useState=2):c[r].platformcoupon_type_id==t.platformcoupon_type_id&&(c[r].useState=2);n.couponBtnSwitch=!1,n.$forceUpdate()})).catch((function(t){"您尚未登录，请先进行登录"==t.message?n.$router.push("/auth/login"):n.$message.error(t.message),n.couponBtnSwitch=!1}))}},toGoodsList:function(t){"first"==this.activeName?1!=t.goods_type?this.$router.push({path:"/goods/list",query:{coupon:t.coupon_type_id}}):this.$router.push({path:"/goods/list"}):this.$router.push("/goods/list")}}},v=_,h=(n(679),n(6)),component=Object(h.a)(v,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"ns-coupon"},[t.adList.length?e("div",{staticClass:"ns-coupon-info"},[e("div",{staticClass:"ns-coupon-wrap"},[t._m(0),t._v(" "),t._m(1),t._v(" "),e("el-button",{on:{click:t.myCoupon}},[t._v("我的优惠券")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingAd,expression:"loadingAd"}],staticClass:"ns-coupon-img"},[e("el-carousel",{attrs:{height:"406px"}},t._l(t.adList,(function(n){return e("el-carousel-item",{key:n.adv_id},[e("el-image",{attrs:{src:t.$img(n.adv_image),fit:"cover"},on:{click:function(e){return t.$util.pushToTab(n.adv_url.url)}}})],1)})),1)],1)]):t._e(),t._v(" "),e("el-tabs",[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("ul",{staticClass:"ns-coupon-list"},t._l(t.couponList,(function(n,o){return e("li",{key:o,staticClass:"ns-bg-color ns-coupon-li",class:2==n.useState?"no-coupon":""},[e("div",{staticClass:"describe"},[[n.discount&&"0.00"!=n.discount?e("span",[t._v(t._s(n.discount)+"折")]):e("span",[t._v("￥"+t._s(n.money))])],t._v(" "),e("span",[t._v(t._s("first"==t.activeName?n.coupon_name:n.platformcoupon_name))]),t._v(" "),"first"==t.activeName?e("span",[t._v("\n              "+t._s(n.goods_type_name)+"\n            ")]):t._e(),t._v(" "),["0.00"==n.at_least?e("span",[t._v("无门槛优惠券")]):e("span",[t._v("满"+t._s(n.at_least)+"可使用")])],t._v(" "),[0==n.validity_type?e("span",{staticClass:"coupon-wrap-time"},[t._v("领取之日起"+t._s(n.fixed_term)+"日内有效")]):1==n.validity_type?e("span",{staticClass:"coupon-wrap-time"},[t._v("有效期至"+t._s(t.$timeStampTurnTime(n.end_time)))]):e("span",{staticClass:"coupon-wrap-time"},[t._v("长期有效")])]],2),t._v(" "),e("div",{staticClass:"receive"},[0==n.useState?e("a",{staticClass:"ns-text-color",on:{click:function(e){return t.couponTap(n,o)}}},[e("span",[t._v("立即领取")])]):t._e(),t._v(" "),1==n.useState?e("a",{staticClass:"ns-text-color",on:{click:function(e){return t.couponTap(n,o)}}},[e("span",[t._v("去使用")])]):t._e(),t._v(" "),n.received_type||2!=n.useState?t._e():e("a",{staticClass:"ns-text-color"},[e("span",[t._v("已抢光")])]),t._v(" "),"out"==n.received_type?e("a",{staticClass:"ns-text-color"},[e("span",[t._v("已抢光")])]):t._e(),t._v(" "),"expire"==n.received_type?e("a",{staticClass:"ns-text-color"},[e("span",[t._v("已过期")])]):t._e(),t._v(" "),"limit"==n.received_type?e("a",{staticClass:"ns-text-color"},[e("span",[t._v("已达上限")])]):t._e()])])})),0),t._v(" "),t.couponList.length<=0?e("div",{staticClass:"empty-wrap"},[e("div",{staticClass:"ns-text-align"},[t._v("暂无优惠券")])]):t._e(),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.currentPage,"page-size":t.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)])])],1)}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"coupon-name"},[e("span",{staticClass:"ns-text-color"},[t._v("优惠券中心")]),t._v(" "),e("span",{staticClass:"ns-text-color-gray text12"},[t._v("省钱更多，领券更多")])])},function(){var t=this,e=t._self._c;return e("ul",{staticClass:"coupon-type clear-float"},[e("li",[e("i"),t._v(" "),e("span",[t._v("限时抢券")])]),t._v(" "),e("li",[e("i"),t._v(" "),e("span",[t._v("叠加使用")])]),t._v(" "),e("li",[e("i"),t._v(" "),e("span",[t._v("种类多样")])])])}],!1,null,"60ee8d28",null);e.default=component.exports}}]);