(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{584:function(e,t,o){},585:function(e,t,o){},671:function(e,t,o){"use strict";o(584)},672:function(e,t,o){"use strict";o(585)},749:function(e,t,o){"use strict";o.r(t);o(31),o(73),o(64);var r=o(175),c=o(27),n=o(146),l={name:"login",layout:"login",mixins:[{data:function(){var e=function(e,t,o){if(!t)return o(new Error("手机号不能为空"));/^\d{11}$/.test(t)?o():o(new Error("请输入正确的手机号"))};return{qrcodeData:{time:0,timer:0},wx_key:"",expire_time:"",ischecked:!1,ischecked1:!1,activeName:"first",formData:{account:"",password:"",vercode:"",mobile:"",dynacode:"",key:"",checked:!1,autoLoginRange:7},captcha:{id:"",img:""},dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},isSub:!1,registerConfig:{is_enable:1,register:"",login:""},accountRules:{account:[{required:!0,message:"请输入登录账号",trigger:"blur"}],password:[{required:!0,message:"请输入登录密码",trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}]},mobileRules:{mobile:[{required:!0,validator:e,trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}],dynacode:[{required:!0,message:"请输入短信动态码",trigger:"blur"}]},wechatRules:{mobile:[{required:!0,validator:e,trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}],dynacode:[{required:!0,message:"请输入短信动态码",trigger:"blur"}]},codeRules:{mobile:[{required:!0,validator:e,trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}]},loadingAd:!0,adList:[],backgroundColor:"",img:"",third_party:0,wechatConfigStatus:0}},created:function(){this.ischecked=this.$route.params.third_party,this.ischecked&&this.weixinLogin(),this.getAdList(),this.getCaptcha(),this.getRegisterConfig(),this.getIsWechatLogin()},head:function(){return{title:"登录-"+this.$store.state.site.siteInfo.site_name}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&(clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1})},immediate:!0,deep:!0}},methods:{getIsWechatLogin:function(){var e=this;Object(n.b)().then((function(t){0==t.code&&(e.wechatConfigStatus=t.data.wechat_config_status)}))},getAdList:function(){var e=this;Object(c.a)({keyword:"NS_PC_LOGIN"}).then((function(t){if(0==t.code&&t.data.adv_list){e.adList=t.data.adv_list;for(var i=0;i<e.adList.length;i++)e.adList[i].adv_url&&(e.adList[i].adv_url=JSON.parse(e.adList[i].adv_url));e.backgroundColor=e.adList[0].background}e.loadingAd=!1})).catch((function(t){e.loadingAd=!1}))},handleClick:function(e,t){},handleChange:function(e,pre){this.backgroundColor=this.adList[e].background},accountLogin:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var data={username:t.formData.account,password:t.formData.password};""!=t.captcha.id&&(data.captcha_id=t.captcha.id,data.captcha_code=t.formData.vercode),t.formData.checked&&(data.autoLoginRange=t.formData.autoLoginRange),t.isSub||(t.isSub=!0,t.$store.dispatch("member/login",data).then((function(e){if(e.code>=0)if(t.$message({message:"登录成功！",type:"success"}),t.$route.query.redirect){t.$route.query.redirect,t.$route.query;t.$router.push(t.$route.query.redirect)}else t.$router.push({name:"member"});else t.isSub=!1,t.getCaptcha(),t.$message({message:e.message,type:"warning"})})).catch((function(e){t.isSub=!1,t.$message.error(e.message),t.getCaptcha()})))}))},mobileLogin:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var data={mobile:t.formData.mobile,key:t.formData.key,code:t.formData.dynacode};""!=t.captcha.id&&(data.captcha_id=t.captcha.id,data.captcha_code=t.formData.vercode),t.isSub||(t.isSub=!0,t.$store.dispatch("member/mobile_login",data).then((function(e){e.code>=0?(t.$message({message:"登录成功！",type:"success"}),t.$route.query.redirect?t.$router.push(t.$route.query.redirect):t.$router.push({name:"member"})):(t.isSub=!1,t.getCaptcha(),t.$message({message:e.message,type:"warning"}))})).catch((function(e){t.isSub=!1,t.$message.error(e.message),t.getCaptcha()})))}))},wechatLogin:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;var data={mobile:t.formData.mobile,key:t.formData.key,code:t.formData.dynacode};""!=t.captcha.id&&(data.captcha_id=t.captcha.id,data.captcha_code=t.formData.vercode),t.isSub||(t.isSub=!0,t.$store.dispatch("wechat/wechatLogin",data).then((function(e){e.code>=0?(t.$message({message:"登录成功！",type:"success"}),t.$route.query.redirect?t.$router.push(t.$route.query.redirect):t.$router.push({name:"member"})):(t.isSub=!1,t.getCaptcha(),t.$message({message:e.message,type:"warning"}))})).catch((function(e){t.isSub=!1,t.$message.error(e.message),t.getCaptcha()})))}))},weixinLogin:function(){var e=this;this.ischecked=!0,this.$store.dispatch("wechat/loginCode").then((function(t){t.code>=0&&(e.img=t.data.qrcode,e.wx_key=t.data.key,e.expire_time=t.data.expire_time,e.qrcodeData.timer=setInterval((function(){e.checkLogin()}),2e3))}))},checkLogin:function(){var e=this;if(this.qrcodeData.time+=2,this.qrcodeData.time>this.expire_time)clearInterval(this.qrcodeData.timer);else{var data={key:this.wx_key};this.$store.dispatch("wechat/checkLogin",data).then((function(t){t.code>=0&&(null!=t.data.token?(e.$message({message:"登录成功！",type:"success"}),e.$route.query.redirect?e.$router.push(e.$route.query.redirect):e.$router.push({name:"member"})):e.ischecked1=!0,clearInterval(e.qrcodeData.timer))}))}},closeWx:function(){this.ischecked=!1},closeWx1:function(){this.ischecked=!1,this.ischecked1=!1},getRegisterConfig:function(){var e=this;Object(r.f)().then((function(t){t.code>=0&&(e.registerConfig=t.data.value,-1!=e.registerConfig.login.indexOf("username")?e.activeName="first":e.activeName="second")}))},getCaptcha:function(){var e=this;Object(c.b)({captcha_id:this.captcha.id}).then((function(t){t.code>=0&&(e.captcha.id=t.data.id,e.captcha.img=t.data.img,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))})).catch((function(t){e.$message.error(t.message)}))},sendMobileCode:function(e){var t=this;120==this.dynacodeData.seconds&&(this.$refs[e].clearValidate("dynacode"),this.$refs[e].validateField("mobile",(function(e){if(e)return!1})),this.$refs[e].validateField("vercode",(function(e){if(e)return!1;Object(r.b)({mobile:t.formData.mobile,captcha_id:t.captcha.id,captcha_code:t.formData.vercode}).then((function(e){e.code>=0&&(t.formData.key=e.data.key,120==t.dynacodeData.seconds&&null==t.dynacodeData.timer&&(t.dynacodeData.timer=setInterval((function(){t.dynacodeData.seconds--,t.dynacodeData.codeText=t.dynacodeData.seconds+"s后可重新获取"}),1e3)))})).catch((function(e){t.$message.error(e.message)}))})))},sendWechatMobileCode:function(e){var t=this;120==this.dynacodeData.seconds&&(this.$refs[e].clearValidate("dynacode"),this.$refs[e].validateField("mobile",(function(e){if(e)return!1})),this.$refs[e].validateField("vercode",(function(e){if(e)return!1;Object(r.h)({mobile:t.formData.mobile,captcha_id:t.captcha.id,captcha_code:t.formData.vercode}).then((function(e){e.code>=0&&(t.formData.key=e.data.key,120==t.dynacodeData.seconds&&null==t.dynacodeData.timer&&(t.dynacodeData.timer=setInterval((function(){t.dynacodeData.seconds--,t.dynacodeData.codeText=t.dynacodeData.seconds+"s后可重新获取"}),1e3)))})).catch((function(e){t.$message.error(e.message)}))})))},keypress:function(e){var t=this;if(13==(e.all?e.keyCode:e.which))return"first"==t.activeName?t.accountLogin("ruleForm"):t.mobileLogin("mobileRuleForm"),!1}}}]},d=(o(671),o(672),o(6)),component=Object(d.a)(l,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingAd,expression:"loadingAd"}],staticClass:"ns-login-wrap",style:{background:e.backgroundColor},on:{keypress:e.keypress}},[t("div",{staticClass:"el-row-wrap el-row-wrap-login"},[t("el-row",[t("el-col",{attrs:{span:13}},[e.adList.length?t("el-carousel",{staticClass:"ns-login-bg",attrs:{height:"460px"},on:{change:e.handleChange}},e._l(e.adList,(function(o){return t("el-carousel-item",{key:o.adv_id},[t("el-image",{attrs:{src:e.$img(o.adv_image),fit:"cover"},on:{click:function(t){return e.$util.pushToTab(o.adv_url.url)}}})],1)})),1):e._e()],1),e._v(" "),t("el-col",{staticClass:"ns-login-form",staticStyle:{float:"right","margin-right":"70px"},attrs:{span:11}},[t("div",{staticClass:"grid-content bg-purple"},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[-1!=e.registerConfig.login.indexOf("username")?t("el-tab-pane",{attrs:{label:"账号登录",name:"first"}},["first"==e.activeName?t("el-form",{ref:"ruleForm",attrs:{model:e.formData,rules:e.accountRules}},[t("el-form-item",{attrs:{prop:"account"}},[t("el-input",{attrs:{placeholder:"请输入账号"},model:{value:e.formData.account,callback:function(t){e.$set(e.formData,"account",t)},expression:"formData.account"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-zhanghao"})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"password"}},[t("el-input",{attrs:{type:"password",autocomplete:"off",placeholder:"请输入登录密码"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-mima"})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"vercode"}},[t("el-input",{attrs:{autocomplete:"off",placeholder:"请输入验证码",maxlength:"4"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-yanzhengma"})]),e._v(" "),t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCaptcha}})])],2)],1),e._v(" "),t("el-form-item",[t("el-row",[t("el-col",{attrs:{span:12}},[t("el-checkbox",{model:{value:e.formData.checked,callback:function(t){e.$set(e.formData,"checked",t)},expression:"formData.checked"}},[e._v("七天自动登录")])],1),e._v(" "),t("el-col",{staticClass:"ns-forget-pass",attrs:{span:12}},[t("router-link",{attrs:{to:"/auth/find"}},[e._v("忘记密码")])],1)],1)],1),e._v(" "),t("el-form-item",{staticStyle:{"margin-bottom":"18px"}},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.accountLogin("ruleForm")}}},[e._v("登录")])],1),e._v(" "),t("el-form-item",[t("el-row",[t("el-col",{attrs:{span:24}},[t("div",{staticClass:"bg-purple-light",staticStyle:{padding:"0 0 10px 0"},style:e.wechatConfigStatus?"border-bottom: 1px solid #ebebeb":""},[e._v("\n                        没有账号？\n                        "),t("router-link",{attrs:{to:"/auth/register"}},[t("p",{staticStyle:{color:"#fd274a"}},[e._v("立即注册")])])],1)])],1),e._v(" "),e.wechatConfigStatus?t("el-col",{attrs:{span:18}},[t("div",{staticClass:"go-wx-login iconfont icon-weixin-copy",staticStyle:{"margin-left":"100px","margin-top":"20px",position:"relative"},on:{click:function(t){return e.weixinLogin()}}},[t("p",{staticStyle:{"font-size":"14px","text-indent":"10px",position:"absolute",top:"1px"}},[e._v("使用微信扫码登录")])])]):e._e()],1)],1):e._e()],1):e._e(),e._v(" "),-1!=e.registerConfig.login.indexOf("mobile")?t("el-tab-pane",{attrs:{label:"手机动态码登录",name:"second"}},["second"==e.activeName?t("el-form",{ref:"mobileRuleForm",staticClass:"ns-login-mobile",attrs:{model:e.formData,rules:e.mobileRules}},[t("el-form-item",{attrs:{prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-shouji-copy"})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"vercode"}},[t("el-input",{attrs:{autocomplete:"off",placeholder:"请输入验证码",maxlength:"4"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-yanzhengma"})]),e._v(" "),t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCaptcha}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"dynacode"}},[t("el-input",{attrs:{maxlength:"4",placeholder:"请输入短信动态码"},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-dongtaima"})]),e._v(" "),t("template",{slot:"append"},[t("div",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"ns-text-color":"ns-text-color-gray",on:{click:function(t){return e.sendMobileCode("mobileRuleForm")}}},[e._v("\n                        "+e._s(e.dynacodeData.codeText)+"\n                      ")])])],2)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.mobileLogin("mobileRuleForm")}}},[e._v("登录")])],1),e._v(" "),t("el-form-item",[t("el-row",[t("el-col",{attrs:{span:24}},[t("div",{staticClass:"bg-purple-light",staticStyle:{padding:"0 0 10px 0"},style:e.wechatConfigStatus?"border-bottom: 1px solid #ebebeb":""},[t("router-link",{attrs:{to:"/auth/register"}},[e._v("立即注册")]),e._v(" "),t("i",{staticClass:"iconfont icon-arrow-right"})],1)])],1),e._v(" "),e.wechatConfigStatus?t("el-col",{attrs:{span:18}},[t("div",{staticClass:"go-wx-login iconfont icon-weixin-copy",staticStyle:{"margin-left":"100px","margin-top":"20px",position:"relative"},on:{click:function(t){return e.weixinLogin()}}},[t("p",{staticStyle:{"font-size":"14px","text-indent":"10px",position:"absolute",top:"1px"}},[e._v("使用微信扫码登录")])])]):e._e()],1)],1):e._e()],1):e._e()],1)],1)]),e._v(" "),t("div",{staticClass:"wx-login",class:1==e.ischecked?"wx-login-display":""},[t("p",{staticClass:"wx-login-title"},[e._v("微信扫码登录")]),e._v(" "),t("div",{staticClass:"qrcode"},[t("img",{attrs:{src:e.img}})]),e._v(" "),t("div",{staticStyle:{display:"flex"}},[t("p",{staticClass:"iconfont icon-arrowLeft",on:{click:function(t){return e.closeWx()}}}),e._v(" "),t("p",{staticClass:"wx-login-footer",on:{click:function(t){return e.closeWx()}}},[e._v("使用账号密码登录")])])]),e._v(" "),t("div",{staticClass:"wx-login1",class:1==e.ischecked1?"wx-login-display1":""},[t("p",{staticClass:"wx-login-title1"},[e._v("扫码成功")]),e._v(" "),t("el-col",{staticClass:"ns-login-form ns-login-form2",attrs:{span:11}},[t("div",{staticClass:"grid-content bg-purple"},[t("el-form",{ref:"wechatRuleForm",attrs:{model:e.formData,rules:e.wechatRules}},[t("el-form-item",{attrs:{prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-shouji-copy"})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"vercode"}},[t("el-input",{attrs:{autocomplete:"off",placeholder:"请输入验证码",maxlength:"4"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-yanzhengma"})]),e._v(" "),t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCaptcha}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{prop:"dynacode"}},[t("el-input",{attrs:{maxlength:"4",placeholder:"请输入短信动态码"},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"iconfont icon-dongtaima"})]),e._v(" "),t("template",{slot:"append"},[t("div",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"ns-text-color":"ns-text-color-gray",on:{click:function(t){return e.sendWechatMobileCode("wechatRuleForm")}}},[e._v("\n                      "+e._s(e.dynacodeData.codeText)+"\n                    ")])])],2)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.wechatLogin("wechatRuleForm")}}},[e._v("确定")])],1),e._v(" "),t("el-form-item",[t("el-row",[t("el-col",{attrs:{span:12}},[t("div",{staticClass:"go-wx-login iconfont",on:{click:function(t){return e.closeWx1()}}},[t("p",[e._v("使用其他方式登录")])])]),e._v(" "),t("el-col",{attrs:{span:12}},[t("div",{staticClass:"bg-purple-light"},[t("router-link",{attrs:{to:"/auth/register"}},[e._v("立即注册")]),e._v(" "),t("i",{staticClass:"iconfont icon-arrow-right"})],1)])],1)],1)],1)],1)])],1)],1)],1)])}),[],!1,null,"3dad5b02",null);t.default=component.exports}}]);