(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{572:function(t,e,n){"use strict";n.d(e,"d",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return d})),n.d(e,"b",(function(){return l}));var r=n(1);function o(t){return Object(r.a)({url:"/bundling/api/ordercreate/payment",data:t,forceLogin:!0})}function c(t){return Object(r.a)({url:"/bundling/api/ordercreate/calculate",data:t,forceLogin:!0})}function d(t){return Object(r.a)({url:"/bundling/api/ordercreate/create",data:t,forceLogin:!0})}function l(t){return Object(r.a)({url:"/bundling/api/bundling/detail",data:t})}},648:function(t,e,n){},741:function(t,e,n){"use strict";n(648)},809:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),o=(n(73),n(315),n(572)),c=n(12);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var l={name:"combo",components:{},data:function(){return{id:0,goodsList:[],num:1,packagePrice:0,saveThePrice:0,isDisabled:!1,loading:!0}},created:function(){this.id=this.$route.params.id,this.getDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["defaultGoodsImage"])),watch:{$route:function(t){this.id=t.query.id,this.getDetail()}},methods:{getDetail:function(){var t=this;Object(o.b)({bl_id:this.id}).then((function(e){e.data&&(t.goodsList=e.data,t.changeNum()),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},changeNum:function(t,e){var n=this;setTimeout((function(){var t=0;0==n.num.length&&(n.num=1),(n.num<=0||isNaN(n.num))&&(n.num=1),n.num=parseInt(n.num);for(var r=0,i=0;i<n.goodsList.bundling_goods.length;i++)r+=parseFloat(n.goodsList.bundling_goods[i].price),n.goodsList.bundling_goods[i].stock<n.num&&t++;n.isDisabled=t>0,n.saveThePrice=((r-n.goodsList.bl_price)*n.num).toFixed(2),n.packagePrice=(n.goodsList.bl_price*n.num).toFixed(2),e&&e()}),0)},comboBuy:function(){if(!this.isDisabled){var data={bl_id:this.id,num:this.num};this.$store.dispatch("order/setComboOrderCreateData",data),this.$router.push({path:"/promotion/combo/payment"})}},imageError:function(t){this.goodsList.bundling_goods[t].sku_image=this.defaultGoodsImage}}},m=l,v=(n(741),n(6)),component=Object(v.a)(m,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"combo-detail"},[t._m(0),t._v(" "),t._l(t.goodsList.bundling_goods,(function(n,r){return e("div",{key:n.sku_item,staticClass:"item-wrap"},[e("div",{staticClass:"item"},[e("div",{staticClass:"info"},[e("div",{staticClass:"img-wrap",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+n.sku_id})}}},[e("img",{attrs:{src:t.$img(n.sku_image,{size:"mid"})},on:{error:function(e){return t.imageError(r)}}})]),t._v(" "),e("div",{staticClass:"name"},[t._v("\n          "+t._s(n.sku_name)+"\n          "),t.num>n.stock?e("p",[t._v("库存不足，剩余："+t._s(n.stock)+"件")]):t._e()])]),t._v(" "),e("div",{staticClass:"price-wrap"},[e("div",{staticClass:"price"},[t._v(t._s(n.price))]),t._v(" "),e("div",{staticClass:"num"},[t._v("x1")])])])])})),t._v(" "),e("div",{staticClass:"combo-bottom"},[e("div",{staticClass:"num"},[e("p",[t._v("购买数量:")]),t._v(" "),e("el-input",{attrs:{type:"number"},on:{change:function(e){return t.changeNum(!1)}},model:{value:t.num,callback:function(e){t.num=e},expression:"num"}})],1),t._v(" "),e("div",{staticClass:"bottom-right"},[e("div",{staticClass:"price"},[e("div",{staticClass:"save-price"},[t._v("为您节省:￥"+t._s(t.saveThePrice))]),t._v(" "),e("div",{staticClass:"old-price"},[t._v("\n          套餐价:\n          "),e("p",[t._v("￥"+t._s(t.packagePrice))])])]),t._v(" "),e("el-button",{attrs:{type:"primary",disabled:!!t.isDisabled},on:{click:t.comboBuy}},[t._v("立即购买")])],1)])],2)}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"combo-title"},[e("div",{staticClass:"title-goods"},[t._v("商品信息")]),t._v(" "),e("div",{staticClass:"title-orther"},[e("div",{staticClass:"title-price"},[t._v("价格")]),t._v(" "),e("div",{staticClass:"title-num"},[t._v("数量")])])])}],!1,null,"f0e98030",null);e.default=component.exports}}]);