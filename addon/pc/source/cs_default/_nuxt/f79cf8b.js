(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{541:function(e,t,r){"use strict";var o=r(204);t.a={methods:{orderPay:function(e){var t=this;0==e.adjust_money?Object(o.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})})):this.$confirm("商家已将支付金额调整为"+e.pay_money+"元，是否继续支付？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})}))}))},orderClose:function(e,t){var r=this;this.$confirm("您确定要关闭该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.b)({order_id:e}).then((function(e){r.$message({message:"订单关闭成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelivery:function(e,t){var r=this;this.$confirm("您确定已经收到货物了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.h)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()})).catch((function(e){r.$message({message:e.message,type:"warning"}),"function"==typeof t&&t()}))}))},orderVirtualDelivery:function(e,t){var r=this;this.$confirm("您确定要进行收货吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.a)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelete:function(e,t){var r=this;this.$confirm("您确定要删除该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.c)({order_id:e}).then((function(e){r.$message({message:"订单删除成功",type:"success"}),"function"==typeof t&&t()}))}))}}}},610:function(e,t,r){},703:function(e,t,r){"use strict";r(610)},782:function(e,t,r){"use strict";r.r(t);r(56),r(315),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var o=r(10),n=(r(73),r(204)),_=r(541),l=r(12);function d(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var c={name:"order_detail_pickup",components:{},mixins:[_.a],data:function(){return{orderId:0,orderDetail:null,loading:!0,yes:!0}},created:function(){this.orderId=this.$route.query.order_id,this.getOrderDetail()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(l.b)(["defaultGoodsImage"])),layout:"member",methods:{getOrderDetail:function(){var e=this;Object(n.d)({order_id:this.orderId}).then((function(t){t.code>=0?(e.orderDetail=t.data,""!=e.orderDetail.delivery_store_info&&(e.orderDetail.delivery_store_info=JSON.parse(e.orderDetail.delivery_store_info)),e.loading=!1):e.$message({message:"未获取到订单信息",type:"warning",duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})})).catch((function(t){e.loading=!1,e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})}))},operation:function(e){var t=this;switch(e){case"orderPay":this.orderPay(this.orderDetail);break;case"orderClose":this.orderClose(this.orderDetail.order_id,(function(){t.getOrderDetail()}));break;case"memberTakeDelivery":this.orderDelivery(this.orderDetail.order_id,(function(){t.getOrderDetail()}));break;case"trace":this.$router.push({path:"/order/logistics",query:{order_id:this.orderDetail.order_id}});break;case"memberOrderEvaluation":this.$router.push({path:"/order/evaluate",query:{order_id:this.orderDetail.order_id}});break;case"memberBatchRefund":this.$router.push({path:"/order/batchrefund",query:{order_id:this.orderId}});break;case"orderOfflinePay":this.$router.push({path:"/pay",query:{code:this.orderDetail.offline_pay_info.out_trade_no}})}},imageError:function(e){this.orderDetail.order_goods[e].sku_image=this.defaultGoodsImage}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}},v=c,m=(r(703),r(6)),component=Object(m.a)(v,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card order-detail"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/member/order_list"}}},[e._v("订单列表")]),e._v(" "),t("el-breadcrumb-item",[e._v("订单详情")])],1)],1),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e.orderDetail?[t("div",{staticClass:"order-status"},[t("h4",[e._v("\n              订单状态：\n              "),t("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.order_status_name))])]),e._v(" "),0==e.orderDetail.order_status?t("div",{staticClass:"go-pay"},[t("p",[e._v("\n                需付款：\n                "),t("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])]):e._e(),e._v(" "),e.orderDetail.action.length>0?t("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e(),e._v(" "),e._l(e.orderDetail.action,(function(r,o){return t("el-button",{key:o,attrs:{type:"primary",size:"mini",plain:"orderPay"!=r.action},on:{click:function(t){return e.operation(r.action)}}},[e._v(e._s(r.title))])}))],2):0==e.orderDetail.action.length&&1==e.orderDetail.is_evaluate?t("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e()],1):e._e()]),e._v(" "),e.orderDetail.pay_status?t("div",{staticClass:"pickup-info"},[t("h4",[e._v("\n              自提点：\n              "),t("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.delivery_store_name))])]),e._v(" "),t("ul",[t("li",[e._v("\n                提货码：\n                "),t("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.delivery_code))])]),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.open_date?t("li",[e._v("营业时间："+e._s(e.orderDetail.delivery_store_info.open_date))]):e._e(),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.telphone?t("li",[e._v("联系方式："+e._s(e.orderDetail.delivery_store_info.telphone))]):e._e(),e._v(" "),e.orderDetail.delivery_store_info&&e.orderDetail.delivery_store_info.full_address?t("li",[e._v("详细地址："+e._s(e.orderDetail.delivery_store_info.full_address))]):e._e()]),e._v(" "),t("img",{attrs:{src:e.$img(e.orderDetail.pickup)}})]):e._e(),e._v(" "),t("nav",[t("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[e._v("商品信息")]),e._v(" "),t("li",[e._v("单价")]),e._v(" "),t("li",[e._v("数量")]),e._v(" "),t("li",[e._v("小计")]),e._v(" "),e.orderDetail.is_enable_refund?t("li",[e._v("操作")]):e._e()]),e._v(" "),t("div",{staticClass:"order-info"},[t("h4",[e._v("订单信息")]),e._v(" "),t("ul",[t("li",[e._v("订单类型："+e._s(e.orderDetail.order_type_name))]),e._v(" "),t("li",[e._v("订单编号："+e._s(e.orderDetail.order_no))]),e._v(" "),t("li",[e._v("订单交易号："+e._s(e.orderDetail.out_trade_no))]),e._v(" "),t("li",[e._v("配送方式："+e._s(e.orderDetail.delivery_type_name))]),e._v(" "),t("li",[e._v("创建时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.create_time)))]),e._v(" "),e.orderDetail.close_time>0?t("li",[e._v("关闭时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.close_time)))]):e._e(),e._v(" "),e.orderDetail.pay_status>0?[t("li",[e._v("支付方式："+e._s(e.orderDetail.pay_type_name))]),e._v(" "),t("li",[e._v("支付时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.pay_time)))])]:e._e(),e._v(" "),""!=e.orderDetail.promotion_type_name?t("li",[e._v("店铺活动："+e._s(e.orderDetail.promotion_type_name))]):e._e(),e._v(" "),""!=e.orderDetail.buyer_message?t("li",[e._v("买家留言："+e._s(e.orderDetail.buyer_message))]):e._e()],2)]),e._v(" "),"offlinepay"==e.orderDetail.pay_type&&e.orderDetail.offline_pay_info?t("div",{staticClass:"order-info"},[t("h4",[e._v("线下支付")]),e._v(" "),t("ul",["WAIT_AUDIT"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                支付状态：审核中\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                支付状态：审核被拒\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                审核备注："+e._s(e.orderDetail.offline_pay_info.audit_remark)+"\n              ")]):e._e()])]):e._e(),e._v(" "),t("div",{staticClass:"take-delivery-info"},[t("h4",[e._v("收货信息")]),e._v(" "),t("ul",[t("li",[e._v("收货人："+e._s(e.orderDetail.name))]),e._v(" "),t("li",[e._v("手机号码："+e._s(e.orderDetail.mobile))]),e._v(" "),t("li",[e._v("收货地址："+e._s(e.orderDetail.full_address)+" "+e._s(e.orderDetail.address))])])]),e._v(" "),t("div",{staticClass:"list"},e._l(e.orderDetail.order_goods,(function(r,o){return t("ul",{key:o,staticClass:"item"},[t("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[t("div",{staticClass:"img-wrap",on:{click:function(t){return e.$util.pushToTab("/sku/"+r.sku_id)}}},[t("img",{attrs:{src:e.$img(r.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(o)}}})]),e._v(" "),t("div",{staticClass:"info-wrap"},[t("h5",{on:{click:function(t){return e.$util.pushToTab("/sku/"+r.sku_id)}}},[e._v(e._s(r.sku_name))])])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s(r.price))])]),e._v(" "),t("li",[t("span",[e._v(e._s(r.num))])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s((r.price*r.num).toFixed(2)))])]),e._v(" "),e.orderDetail.is_enable_refund?t("li",[0==r.refund_status||-1==r.refund_status?t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.$router.push({path:"/order/refund",query:{order_goods_id:r.order_goods_id}})}}},[e._v("退款")]):t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.$router.push({path:"/order/refund_detail",query:{order_goods_id:r.order_goods_id}})}}},[e._v("查看退款")])],1):e._e()])})),0),e._v(" "),t("ul",{staticClass:"total"},[t("li",[t("label",[e._v("商品金额：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.goods_money))])]),e._v(" "),t("li",[t("label",[e._v("运费：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.delivery_money))])]),e._v(" "),e.orderDetail.member_card_money>0?t("li",[t("label",[e._v("会员卡：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.member_card_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_money>0?t("li",[t("label",[e._v("税费：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.invoice_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_delivery_money>0?t("li",[t("label",[e._v("发票邮寄费：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.invoice_delivery_money))])]):e._e(),e._v(" "),0!=e.orderDetail.adjust_money?t("li",[t("label",[e._v("订单调整：")]),e._v(" "),t("span",[e.orderDetail.adjust_money<0?[e._v("-")]:[e._v("+")],e._v("\n\t\t\t\t\t\t\t\t￥"+e._s(e._f("abs")(e.orderDetail.adjust_money))+"\n\t\t\t\t\t\t\t")],2)]):e._e(),e._v(" "),e.orderDetail.promotion_money>0?t("li",[t("label",[e._v("优惠：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.promotion_money))])]):e._e(),e._v(" "),e.orderDetail.coupon_money>0?t("li",[t("label",[e._v("优惠券：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.coupon_money))])]):e._e(),e._v(" "),e.orderDetail.balance_money>0?t("li",[t("label",[e._v("使用余额：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.balance_money))])]):e._e(),e._v(" "),e.orderDetail.point_money>0?t("li",[t("label",[e._v("积分抵扣：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.point_money))])]):e._e(),e._v(" "),t("li",{staticClass:"pay-money"},[t("label",[e._v("实付款：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])])]:e._e()],2)])],1)}),[],!1,null,"36510428",null);t.default=component.exports}}]);