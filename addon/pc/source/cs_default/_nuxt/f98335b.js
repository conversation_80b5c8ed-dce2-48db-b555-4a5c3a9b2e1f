(window.webpackJsonp=window.webpackJsonp||[]).push([[48],{550:function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return c})),r.d(t,"a",(function(){return l}));var n=r(1);function o(e){return Object(n.a)({url:"/api/member/info",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/member/modifynickname",data:e,forceLogin:!0})}function l(e){return Object(n.a)({url:"/api/member/modifyheadimg",data:e,forceLogin:!0})}},551:function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"h",(function(){return c})),r.d(t,"g",(function(){return l})),r.d(t,"a",(function(){return m})),r.d(t,"b",(function(){return d})),r.d(t,"i",(function(){return f})),r.d(t,"c",(function(){return h})),r.d(t,"e",(function(){return y})),r.d(t,"f",(function(){return w}));var n=r(1);function o(e){return Object(n.a)({url:"/api/member/modifypassword",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/member/bindmobliecode",data:e,forceLogin:!0})}function l(e){return Object(n.a)({url:"/api/member/modifymobile",data:e,forceLogin:!0})}function m(e){return Object(n.a)({url:"/api/member/checkemail",data:e,forceLogin:!0})}function d(e){return Object(n.a)({url:"/api/member/modifyemail",data:e,forceLogin:!0})}function f(e){return Object(n.a)({url:"/api/member/verifypaypwdcode",data:e,forceLogin:!0})}function h(e){return Object(n.a)({url:"/api/member/modifypaypassword",data:e,forceLogin:!0})}function y(e){return Object(n.a)({url:"/api/member/paypwdcode",data:e,forceLogin:!0})}function w(e){return Object(n.a)({url:"/api/member/pwdmobliecode",data:e,forceLogin:!0})}},616:function(e,t,r){"use strict";r.r(t);var n=r(21),o=(r(95),r(31),r(73),r(64),r(551)),c=r(550),l=r(27);t.default={name:"security",components:{},data:function(){var e=this;return{type:"all",passWordForm:{oldPass:"",pass:"",checkPass:""},emailForm:{email:"",code:"",emailDynacode:"",emailCodeText:"",key:"",currEmail:""},passWordRules:{oldPass:[{required:!0,message:"请输入原密码",trigger:"blur"}],pass:[{required:!0,validator:function(t,r,n){""===r?n(new Error("请输入新密码")):r==e.passWordForm.oldPass?n(new Error("新密码不能与原密码相同！")):(""!==e.passWordForm.checkPass&&e.$refs.passWordRef.validateField("checkPass"),n())},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,r,n){""===r?n(new Error("请再次输入密码")):r!==e.passWordForm.pass?n(new Error("两次输入密码不一致!")):n()},trigger:"blur"}]},emailRules:{email:[{required:!0,message:"请输入正确的邮箱",trigger:"blur"},{validator:function(e,t,r){if(/^\w+@\w+(\.\w+)+$/.test(t))return r();r(new Error("请输入正确的的邮箱"))},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}],emailDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}]},captcha:{id:"",img:""},seconds:120,timer:null,isSend:!1,isMobileSend:!1,tellForm:{tell:"",code:"",tellDynacode:"",tellCodeText:"",key:"",currTell:""},tellRules:{tell:[{required:!0,message:"请输入正确的手机号",trigger:"blur"},{validator:function(e,t,r){if(/^1[3|4|5|6|7|8|9][0-9]{9}$/.test(t))return r();r(new Error("请输入正确的手机号"))},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}],tellDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}]},isClick:!0,payCodeText:"获取验证码",step:0,payCode:"",payPassword:"",payRepassword:"",payKey:"",payInput:"",palceText:"输入短信验证码",memberInfo:{},tellPassForm:{code:"",tellPassCodeText:"",key:"",tellPassDynacode:"",pass:"",checkPass:""},tellPassRules:{code:[{required:!0,message:"请输入验证码",trigger:"blur"}],tellPassDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}],pass:[{required:!0,validator:function(t,r,n){""===r?n(new Error("请输入新密码")):r==e.tellPassForm.oldPass?n(new Error("新密码不能与原密码相同！")):(""!==e.tellPassForm.checkPass&&e.$refs.tellPassRef.validateField("checkPass"),n())},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,r,n){""===r?n(new Error("请再次输入密码")):r!==e.tellPassForm.pass?n(new Error("两次输入密码不一致!")):n()},trigger:"blur"}]},loading:!0,yes:!0}},created:function(){this.getcaptcha(),this.seconds=120,this.tellForm.tellCodeText="获取动态码",this.emailForm.emailCodeText="获取动态码",this.tellPassForm.tellPassCodeText="获取动态码",this.isSend=!1,this.isMobileSend=!1,clearInterval(this.timer),this.getInfo()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getInfo:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(c.b)().then((function(t){0==t.code&&(e.memberInfo=t.data,e.emailForm.currEmail=t.data.email,e.tellForm.currTell=t.data.mobile),e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error(t.message)}));case 2:case"end":return t.stop()}}),t)})))()},edit:function(e){var t=this;return Object(n.a)(regeneratorRuntime.mark((function r(){return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.getInfo();case 2:"payPassWord"==e?t.tellForm.currTell?t.type=e:t.$confirm("你还未绑定手机号，请先绑定手机号？","提示信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(e){"confirm"?t.type="tell":t.type="all"})):t.type=e;case 3:case"end":return r.stop()}}),r)})))()},getcaptcha:function(){var e=this;Object(l.b)({captcha_id:this.captcha.id}).then((function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))})).catch((function(t){e.$message.error(t.message)}))},save:function(){var e=this;this.$refs.passWordRef.validate((function(t){if(!t)return!1;Object(o.d)({new_password:e.passWordForm.pass,old_password:e.passWordForm.oldPass}).then((function(t){e.$message({message:"修改密码成功",type:"success"}),e.type="all",e.$store.dispatch("member/member_detail",{refresh:1}),e.passWordForm.pass="",e.passWordForm.oldPass="",e.passWordForm.checkPass=""})).catch((function(t){e.$message.error(t.message)}))}))},getCheckEmail:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(o.a)({email:e.emailForm.email}).then((function(t){return 0==t.code||(e.$message({message:t.message,type:"success"}),!1)})).catch((function(t){e.$message.error(t.message)}));case 2:return r=t.sent,t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)})))()},bindEmail:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$refs.emailRef.validate((function(t){if(!t)return!1;Object(o.b)({email:e.emailForm.email,captcha_id:e.captcha.id,captcha_code:e.emailForm.code,code:e.emailForm.emailDynacode,key:e.emailForm.key}).then((function(t){0==t.code&&(e.$message({message:"邮箱绑定成功",type:"success"}),e.type="all",e.emailForm.email="",e.emailForm.code="",e.emailForm.emailDynacode="",clearInterval(e.timer),e.getcaptcha())})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)}))}));case 1:case"end":return t.stop()}}),t)})))()},gettellCode:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.isMobileSend){t.next=6;break}return e.isMobileSend=!0,t.next=4,Object(o.h)({mobile:e.tellForm.tell,captcha_id:e.captcha.id,captcha_code:e.tellForm.code}).then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.tellForm.tellCodeText="已发送("+e.seconds+"s)"}),1e3)),e.tellForm.key=data.key):(e.$message({message:t.message,type:"warning"}),e.isMobileSend=!1)})).catch((function(t){e.getcaptcha(),e.$message.error(t.message),"当前手机号已存在"==t.message&&(e.isMobileSend=!1)}));case 4:t.next=7;break;case 6:e.$message({message:"请勿重复点击",type:"warning"});case 7:case"end":return t.stop()}}),t)})))()},bindtell:function(){var e=this;this.$refs.tellRef.validate((function(t){if(!t)return!1;Object(o.g)({mobile:e.tellForm.tell,captcha_id:e.captcha.id,captcha_code:e.tellForm.code,code:e.tellForm.tellDynacode,key:e.tellForm.key}).then((function(t){0==t.code&&(e.$message({message:"手机号绑定成功",type:"success"}),e.type="all",e.tellForm.email="",e.tellForm.code="",e.tellForm.emailDynacode="",clearInterval(e.timer),e.getcaptcha())})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)}))}))},input:function(e){this.isClick=!1,0==this.step&&4==e.length?this.payCode=e:1==this.step&&6==e.length?this.payPassword=e:6==e.length&&(this.payRepassword=e)},sendMobileCode:function(){var e=this;this.isSend||Object(o.e)().then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.payCodeText="已发送("+e.seconds+"s)"}),1e3)),e.payKey=data.key):(e.$message({message:t.message,type:"warning"}),e.isSend=!1)})).catch((function(t){e.$message.error(t.message)}))},bindPayPwd:function(){var e=this;clearInterval(this.timer);if(0==this.step)Object(o.i)({code:this.payCode,key:this.payKey}).then((function(t){0==t.code&&(e.$refs.input.clear(),e.step=1,e.palceText="请设置支付密码")})).catch((function(t){e.$message.error(t.message)}));else if(1==this.step)/^[0-9]*$/.test(this.$refs.input.value)?(this.$refs.input.clear(),this.step=2,this.palceText="请再次输入"):(this.$message.error("请输入数字"),this.step=1,this.$refs.input.clear());else if(this.payPassword==this.payRepassword){if(this.isSub)return;this.isSub=!0,Object(o.c)({key:this.payKey,code:this.payCode,password:this.payPassword}).then((function(t){t.code>=0&&(e.$message({message:"修改支付密码成功",type:"success"}),e.type="all",e.step=0,e.$refs.input.clear(),clearInterval(e.timer))})).catch((function(t){e.$message.error(t.message)}))}else this.$message.error("两次密码输入不一样"),this.initInfo()},initInfo:function(){this.step=1,this.palceText="请设置支付密码",this.password="",this.repassword="",this.oldpassword="",this.isSub=!1,this.$refs.input.clear()},getTellPassCode:function(){var e=this;this.isSend?this.$message({message:"请勿重复点击",type:"warning"}):(this.isSend=!0,Object(o.f)({captcha_id:this.captcha.id,captcha_code:this.tellPassForm.code}).then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.tellPassForm.tellPassCodeText="已发送("+e.seconds+"s)"}),1e3)),e.tellPassForm.key=data.key):(e.$message({message:t.message,type:"warning"}),e.isSend=!1)})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)})))},tellPassSave:function(){var e=this;this.$refs.tellPassRef.validate((function(t){if(!t)return!1;Object(o.d)({new_password:e.tellPassForm.pass,code:e.tellPassForm.tellPassDynacode,key:e.tellPassForm.key}).then((function(t){e.$message({message:"修改密码成功",type:"success"}),e.type="all",e.$store.dispatch("member/member_detail",{refresh:1}),e.tellPassForm.pass="",e.tellPassForm.checkPass="",e.tellPassForm.key="",e.tellPassForm.tellPassDynacode=""})).catch((function(t){e.$message.error(t.message)}))}))}},filters:{mobile:function(e){return e.substring(0,3)+"****"+e.substring(7)}}}}}]);