(window.webpackJsonp=window.webpackJsonp||[]).push([[67],{545:function(t,e,n){t.exports=n.p+"img/goods_empty.288af96.png"},553:function(t,e,n){"use strict";n.d(e,"g",(function(){return o})),n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return d})),n.d(e,"f",(function(){return l})),n.d(e,"c",(function(){return f})),n.d(e,"d",(function(){return h})),n.d(e,"e",(function(){return v}));var r=n(1);function o(t){return Object(r.a)({url:"/groupbuy/api/ordercreate/payment",data:t,forceLogin:!0})}function c(){return Object(r.a)({url:"/api/goodsevaluate/config",data:{},forceLogin:!0})}function d(t){return Object(r.a)({url:"/groupbuy/api/ordercreate/calculate",data:t,forceLogin:!0})}function l(t){return Object(r.a)({url:"/groupbuy/api/ordercreate/create",data:t,forceLogin:!0})}function f(t){return Object(r.a)({url:"/groupbuy/api/goods/page",data:t,forceLogin:!0})}function h(t){return Object(r.a)({url:"/groupbuy/api/goods/detail",data:t,forceLogin:!0})}function v(t){return Object(r.a)({url:"/groupbuy/api/goods/info",data:t,forceLogin:!0})}},635:function(t,e,n){},636:function(t,e,n){},728:function(t,e,n){"use strict";n(635)},729:function(t,e,n){"use strict";n(636)},802:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),o=n(553),c=n(12),d=n(27);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var f={name:"groupbuy",data:function(){return{loading:!0,goodsList:[],total:0,currentPage:1,pageSize:25,loadingAd:!0,adList:[]}},created:function(){var t=this;this.addonIsExit&&1!=this.addonIsExit.groupbuy?this.$message({message:"团购插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}}):(this.getAdList(),this.getGoodsList())},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["defaultGoodsImage","addonIsExit"])),head:function(){return{title:"团购专区-"+this.$store.state.site.siteInfo.site_name,meta:[{name:"description",content:this.$store.state.site.siteInfo.seo_description},{name:"keyword",content:this.$store.state.site.siteInfo.seo_keywords}]}},watch:{addonIsExit:function(){var t=this;1!=this.addonIsExit.groupbuy&&this.$message({message:"团购插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}})}},methods:{getAdList:function(){var t=this;Object(d.a)({keyword:"NS_PC_GROUPBUY"}).then((function(e){t.adList=e.data.adv_list;for(var i=0;i<t.adList.length;i++)t.adList[i].adv_url&&(t.adList[i].adv_url=JSON.parse(t.adList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},getGoodsList:function(){var t=this;Object(o.c)({page_size:this.pageSize,page:this.currentPage}).then((function(e){t.goodsList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},handlePageSizeChange:function(t){this.pageSize=t,this.refresh()},handleCurrentPageChange:function(t){this.currentPage=t,this.refresh()},refresh:function(){this.loading=!0,this.getGoodsList()},imageError:function(t){this.goodsList[t].goods_image=this.defaultGoodsImage}}},h=f,v=(n(728),n(729),n(6)),component=Object(v.a)(h,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"ns-groupbuy"},[e("div",{staticClass:"ns-groupbuy-head"},[t.adList.length?e("el-carousel",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingAd,expression:"loadingAd"}],attrs:{height:"420px"}},t._l(t.adList,(function(n){return e("el-carousel-item",{key:n.adv_id},[e("el-image",{attrs:{src:t.$img(n.adv_image),fit:"cover"},on:{click:function(e){return t.$util.pushToTab(n.adv_url.url)}}})],1)})),1):t._e()],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"ns-groupbuy-box"},[t.goodsList.length?e("div",{staticClass:"ns-groupbuy-title"},[e("span",{staticClass:"groupbuy-title-left"},[t._v("团购专区")])]):t._e(),t._v(" "),t.goodsList.length?e("div",{staticClass:"goods-list"},t._l(t.goodsList,(function(n,r){return e("div",{key:n.groupbuy_id,staticClass:"goods",on:{click:function(e){return t.$router.push("/promotion/groupbuy/"+n.groupbuy_id)}}},[e("div",{staticClass:"img"},[e("el-image",{attrs:{fit:"scale-down",src:t.$img(n.goods_image,{size:"mid"}),lazy:""},on:{error:function(e){return t.imageError(r)}}})],1),t._v(" "),e("div",{staticClass:"name"},[e("p",{attrs:{title:n.goods_name}},[t._v(t._s(n.goods_name))])]),t._v(" "),e("div",{staticClass:"price"},[e("div",{staticClass:"curr-price"},[e("span",[t._v("团购价")]),t._v(" "),e("span",[t._v("￥")]),t._v(" "),e("span",{staticClass:"main_price"},[t._v(t._s(n.groupbuy_price))])]),t._v(" "),e("span",{staticClass:"primary_price"},[t._v("￥"+t._s(n.price))])]),t._v(" "),e("el-button",[t._v("立即拼购")])],1)})),0):t.loading?t._e():e("div",{staticClass:"empty-wrap"},[e("img",{attrs:{src:n(545)}}),t._v(" "),e("span",[t._v("暂无正在进行团购的商品，"),e("router-link",{staticClass:"ns-text-color",attrs:{to:"/"}},[t._v("去首页")]),t._v("看看吧")],1)]),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.currentPage,"page-size":t.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)])])}),[],!1,null,"9a042050",null);e.default=component.exports}}]);