(window.webpackJsonp=window.webpackJsonp||[]).push([[61],{552:function(e,t,r){"use strict";r.d(t,"c",(function(){return o})),r.d(t,"b",(function(){return c})),r.d(t,"a",(function(){return l})),r.d(t,"d",(function(){return f}));var n=r(1);function o(e){return Object(n.a)({url:"/api/verify/verifyInfo",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/verify/verify",data:e,forceLogin:!0})}function l(e){return Object(n.a)({url:"/api/verify/getVerifyType",data:e})}function f(e){return Object(n.a)({url:"/api/verify/lists",data:e,forceLogin:!0})}},633:function(e,t,r){},726:function(e,t,r){"use strict";r(633)},800:function(e,t,r){"use strict";r.r(t);r(56),r(25),r(23),r(29),r(30);var n=r(10),o=(r(73),r(7),r(18),r(24),r(12)),c=r(552);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var f={name:"verification_list",components:{},data:function(){return{orderType:"",loading:!0,typeList:[],verifyList:[],currentPage:1,pageSize:10,total:0,yes:!0}},created:function(){this.getVerifyType()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(o.b)(["defaultGoodsImage"])),layout:"member",mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{handlePageSizeChange:function(e){this.pageSize=e,this.refresh()},handleCurrentPageChange:function(e){this.currentPage=e,this.refresh()},refresh:function(){this.loading=!0,this.getVerifyType()},handleClick:function(e,t){this.refresh()},getVerifyType:function(){var e=this;Object(c.a)().then((function(t){if(t.code>=0){if(e.typeList=[],e.verifyList=[],Object.keys(t.data).forEach((function(r){e.typeList.push({type:r,name:t.data[r].name})})),0==e.orderType)for(var i=0;i<e.typeList.length;i++)0==i&&(e.orderType=e.typeList[i].type);e.getVerifyList(e.orderType)}}))},getVerifyList:function(e){var t=this;Object(c.d)({verify_type:e,page:this.currentPage,page_size:this.pageSize}).then((function(e){t.verifyList=e.data.list,t.total=e.data.count,t.loading=!1})).catch((function(e){t.$message.error(e.message),t.loading=!1}))},imageError:function(e,t){this.verifyList[e].item_array[t].img=this.defaultGoodsImage},toVerifyDetail:function(code){this.$router.push({path:"/order/verification_detail",query:{code:code}})}}},d=f,v=(r(726),r(6)),component=Object(v.a)(d,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card order-list"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("核销记录")])]),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.orderType,callback:function(t){e.orderType=t},expression:"orderType"}},e._l(e.typeList,(function(e,r){return t("el-tab-pane",{key:r,attrs:{label:e.name,name:e.type}})})),1),e._v(" "),t("div",[t("nav",[t("li",[e._v("商品信息")]),e._v(" "),t("li",[e._v("单价")]),e._v(" "),t("li",[e._v("数量")])]),e._v(" "),e.verifyList.length>0?t("div",{staticClass:"list"},e._l(e.verifyList,(function(r,n){return t("div",{key:n,staticClass:"item"},[t("div",{staticClass:"head"},[t("span",{staticClass:"create-time"},[e._v(e._s(e.$util.timeStampTurnTime(r.create_time)))]),e._v(" "),t("router-link",{attrs:{to:"/shop-"+r.site_id,target:"_blank"}},[e._v(e._s(r.site_name))]),e._v(" "),t("span",{staticClass:"order-type"},[e._v(e._s(r.order_type_name))]),e._v(" "),t("span",{staticClass:"order-type"},[e._v("核销员："+e._s(r.verifier_name))])],1),e._v(" "),e._l(r.item_array,(function(o,c){return t("ul",{key:c},[t("li",[t("div",{staticClass:"img-wrap",on:{click:function(t){return e.toVerifyDetail(r.verify_code)}}},[t("img",{attrs:{src:e.$img(o.img)},on:{error:function(t){return e.imageError(n,c)}}})]),e._v(" "),t("div",{staticClass:"info-wrap"},[t("h5",{on:{click:function(t){return e.toVerifyDetail(r.verify_code)}}},[e._v(e._s(o.name))])])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s(o.price))])]),e._v(" "),t("li",[t("span",[e._v(e._s(o.num))])])])}))],2)})),0):e.loading||0!=e.verifyList.length?e._e():t("div",{staticClass:"empty-wrap"},[e._v("暂无相关订单")])])],1),e._v(" "),t("div",{staticClass:"pager"},[t("el-pagination",{attrs:{background:"","pager-count":5,total:e.total,"prev-text":"上一页","next-text":"下一页","current-page":e.currentPage,"page-size":e.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handlePageSizeChange,"current-change":e.handleCurrentPageChange}})],1)])],1)}),[],!1,null,"42133458",null);t.default=component.exports}}]);