(window.webpackJsonp=window.webpackJsonp||[]).push([[70],{535:function(t,e,n){"use strict";var r=n(3),o=n(35),c=n(14),l=n(8),path=n(319),d=n(5),h=n(113),m=n(16),f=n(205),_=n(63),v=n(112),k=n(318),T=n(4),w=n(93).f,y=n(57).f,x=n(26).f,L=n(320),I=n(316).trim,C="Number",N=l[C],S=path[C],O=N.prototype,P=l.TypeError,E=d("".slice),j=d("".charCodeAt),$=function(t){var e=k(t,"number");return"bigint"==typeof e?e:M(e)},M=function(t){var e,n,r,o,c,l,d,code,h=k(t,"number");if(v(h))throw P("Cannot convert a Symbol value to a number");if("string"==typeof h&&h.length>2)if(h=I(h),43===(e=j(h,0))||45===e){if(88===(n=j(h,2))||120===n)return NaN}else if(48===e){switch(j(h,1)){case 66:case 98:r=2,o=49;break;case 79:case 111:r=8,o=55;break;default:return+h}for(l=(c=E(h,2)).length,d=0;d<l;d++)if((code=j(c,d))<48||code>o)return NaN;return parseInt(c,r)}return+h},z=h(C,!N(" 0o1")||!N("0b1")||N("+0x1")),D=function(t){return _(O,t)&&T((function(){L(t)}))},A=function(t){var e=arguments.length<1?0:N($(t));return D(this)?f(Object(e),this,A):e};A.prototype=O,z&&!o&&(O.constructor=A),r({global:!0,constructor:!0,wrap:!0,forced:z},{Number:A});var G=function(t,source){for(var e,n=c?w(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),r=0;n.length>r;r++)m(source,e=n[r])&&!m(t,e)&&x(t,e,y(source,e))};o&&S&&G(path[C],S),(z||o)&&G(path[C],N)},538:function(t,e,n){t.exports=n(543)},543:function(t,e,n){"use strict";n.r(e);n(535),n(7),n(74),n(73),n(315);var r={replace:!0,data:function(){return{tipShow:!0,msTime:{show:!1,day:"",hour:"",minutes:"",seconds:""},star:"",end:"",current:""}},watch:{currentTime:function(t,e){this.gogogo()}},props:{tipText:{type:String,default:"距离开始"},tipTextEnd:{type:String,default:"距离结束"},id:{type:String,default:"1"},currentTime:{type:Number},startTime:{type:Number},endTime:{type:Number},endText:{type:String,default:"已结束"},dayTxt:{type:String,default:":"},hourTxt:{type:String,default:":"},minutesTxt:{type:String,default:":"},secondsTxt:{type:String,default:":"},secondsFixed:{type:Boolean,default:!1}},mounted:function(){console.log(this),this.gogogo()},methods:{gogogo:function(){var t=this;10==this.startTime.toString().length?this.star=1e3*this.startTime:this.star=this.startTime,10==this.endTime.toString().length?this.end=1e3*this.endTime:this.end=this.endTime,this.currentTime?10==this.currentTime.toString().length?this.current=1e3*this.currentTime:this.current=this.currentTime:this.current=(new Date).getTime(),this.end<this.current?(this.msTime.show=!1,this.end_message()):this.current<this.star?(this.$set(this,"tipShow",!0),setTimeout((function(){t.runTime(t.star,t.current,t.start_message)}),1)):(this.end>this.current&&this.star<this.current||this.star==this.current)&&(this.$set(this,"tipShow",!1),this.msTime.show=!0,this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1))},runTime:function(t,e,n,r){var o=this,c=this.msTime,l=t-e;if(l>0){this.msTime.show=!0,c.day=Math.floor(l/864e5),l-=864e5*c.day,c.hour=Math.floor(l/36e5),l-=36e5*c.hour,c.minutes=Math.floor(l/6e4),l-=6e4*c.minutes,c.seconds=Math.floor(l/1e3).toFixed(0),l-=1e3*c.seconds,c.hour<10&&(c.hour="0"+c.hour),c.minutes<10&&(c.minutes="0"+c.minutes),c.seconds<10&&(c.seconds="0"+c.seconds);var d=Date.now(),h=Date.now();setTimeout((function(){r?o.runTime(o.end,e+=1e3,n,!0):o.runTime(o.star,e+=1e3,n)}),1e3-(h-d))}else n()},start_message:function(){var t=this;this.$set(this,"tipShow",!1),this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1)},end_message:function(){this.msTime.show=!1,this.currentTime<=0||this.$emit("end_callback",this.msTime.show)}}},o=n(6),component=Object(o.a)(r,(function(){var t=this,e=t._self._c;return e("div",[t.msTime.show?e("p",[t.msTime.day>0?e("span",[e("span",[t._v(t._s(t.msTime.day))]),e("i",[t._v(t._s(t.dayTxt))])]):t._e(),t._v(" "),e("span",[t._v(t._s(t.msTime.hour))]),e("i",[t._v(t._s(t.hourTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.minutes))]),e("i",[t._v(t._s(t.minutesTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.seconds))]),e("i",[t._v(t._s(t.secondsTxt))])]):t._e()])}),[],!1,null,null,null);e.default=component.exports},545:function(t,e,n){t.exports=n.p+"img/goods_empty.288af96.png"},547:function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"d",(function(){return c})),n.d(e,"h",(function(){return l})),n.d(e,"g",(function(){return d})),n.d(e,"f",(function(){return h})),n.d(e,"a",(function(){return m})),n.d(e,"b",(function(){return f})),n.d(e,"e",(function(){return _}));var r=n(1);function o(t){return Object(r.a)({url:"/seckill/api/seckillgoods/page",data:t})}function c(t){return Object(r.a)({url:"/seckill/api/seckillgoods/detail",data:t})}function l(t){return Object(r.a)({url:"/seckill/api/seckill/lists",data:t})}function d(t){return Object(r.a)({url:"/seckill/api/seckillgoods/info",data:t})}function h(t){return Object(r.a)({url:"/seckill/api/ordercreate/payment",data:t,forceLogin:!0})}function m(t){return Object(r.a)({url:"/seckill/api/ordercreate/calculate",data:t,forceLogin:!0})}function f(){return Object(r.a)({url:"/api/goodsevaluate/config",data:{},forceLogin:!0})}function _(t){return Object(r.a)({url:"/seckill/api/ordercreate/create",data:t,forceLogin:!0})}},637:function(t,e,n){},638:function(t,e,n){},730:function(t,e,n){"use strict";n(637)},731:function(t,e,n){"use strict";n(638)},754:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(29),n(30);var r=n(10),o=(n(56),n(7),n(18),n(65),n(547)),c=n(12),l=n(27),d=n(538);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var m={name:"groupbuy",components:{CountDown:n.n(d).a},data:function(){return{loading:!0,timeList:[],seckillId:null,seckillName:null,seckillIndex:null,goodsList:[],index:null,siteId:0,total:0,currentPage:1,pageSize:25,loadingAd:!0,adList:[],seckillTimeMachine:{currentTime:0,startTime:0,endTime:0},seckillText:"距离结束仅剩",thumbPosition:0,moveThumbLeft:!1,moveThumbRight:!1,shouType:!0,isNoClick:!1,key:0}},watch:{seckillId:function(t,e){t&&t!=e&&this.refresh()},addonIsExit:function(){var t=this;1!=this.addonIsExit.seckill&&this.$message({message:"秒杀插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}})}},created:function(){var t=this;this.addonIsExit&&1!=this.addonIsExit.seckill?this.$message({message:"秒杀插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}}):(this.getAdList(),this.getTimeList())},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({isTrue:function(){var t=0;return this.timeList&&this.timeList[this.index]&&(t=this.timeList[this.index].isNow),t}},Object(c.b)(["defaultGoodsImage","addonIsExit"])),methods:{handleSelected:function(i,t){this.key=i;var text=this.timeList[i].name;i<this.index?this.$message.warning(text+"秒杀已结束"):i>this.index?(this.shouType=!1,this.seckillId=t.id,this.isNoClick=!0,this.seckillName=t.name,this.getTimeList()):(this.seckillId=t.id,this.shouType=!0,this.isNoClick=!1,this.seckillName=t.name,this.getTimeList())},changeThumbImg:function(t){var e=this.$refs.seckillTime.clientWidth,n=document.querySelector(".seckill-time-ul").style.left.indexOf("px");document.querySelector(".seckill-time-ul").style.left.substring(0,n);if(!(this.timeList.length<4)){var r=this.timeList.length%4,o=302.5;if(0==r)r=this.timeList.length-4;else if(0!=r&&1!=r&&r<2)return;if("prev"==t)0!=this.thumbPosition&&Math.round(this.thumbPosition,2)!=o&&o<Math.abs(this.thumbPosition)?this.thumbPosition+=o:this.thumbPosition=0;else if("next"==t&&Math.round(this.thumbPosition,2)!=-Math.round(o*r,2)){var c=this.timeList.length*o-e;Math.abs(this.thumbPosition)-c>=0?this.thumbPosition=-c:Math.abs(this.thumbPosition)-c<-150?this.thumbPosition-=o:this.thumbPosition=-c}}},countDownS_cb:function(){},countDownE_cb:function(){this.seckillText="活动已结束"},getAdList:function(){var t=this;Object(l.a)({keyword:"NS_PC_SECKILL"}).then((function(e){t.adList=e.data.adv_list;for(var i=0;i<t.adList.length;i++)t.adList[i].adv_url&&(t.adList[i].adv_url=JSON.parse(t.adList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},getTimeList:function(){var t=this;Object(o.h)().then((function(e){var data=e.data;if(data){var time=new Date(1e3*e.timestamp),n=60*time.getHours()*60+60*time.getMinutes()+time.getSeconds();if(data.list.forEach((function(r,o){if(r.seckill_start_time<=n&&n<r.seckill_end_time){if(r.isNow=!0,1==t.shouType){t.seckillId=r.id,t.seckillName=r.name,t.index=o,t.seckillIndex=o;var c=parseInt(time.getTime()/1e3)+(r.seckill_end_time-n);t.seckillTimeMachine={currentTime:e.timestamp,startTime:e.timestamp,endTime:c}}}else r.isNow=!1;r.seckill_end_time_show=r.seckill_end_time_show.slice(0,5),r.seckill_start_time_show=r.seckill_start_time_show.slice(0,5)})),t.timeList=data.list,!t.seckillId)for(var i=0;i<data.list.length;i++)(n<data.list[i].seckill_start_time&&0==i||n<data.list[i].seckill_start_time&&n>data.list[i-1].seckill_end_time&&0!=i||i==data.list.length-1&&n>data.list[i].seckill_end_time)&&(t.seckillId=data.list[i].id,t.index=i,t.seckillIndex=i)}})).catch((function(e){t.$message.error(e.message)}))},getGoodsList:function(){var t=this;Object(o.c)({page_size:this.pageSize,page:this.currentPage,seckill_time_id:this.seckillId,site_id:this.siteId}).then((function(e){t.goodsList=e.data.list,t.goodsList.forEach((function(t){t.goods_image=t.goods_image.split(",")[0]})),t.total=e.data.count,t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},toGoodsDetail:function(t,e){var time=new Date,n=60*time.getHours()*60+60*time.getMinutes()+time.getSeconds();this.timeList[this.key].seckill_start_time<=n&&n<this.timeList[this.key].seckill_end_time?this.timeList[this.key].isNow=!0:this.timeList[this.key].isNow=!1,this.timeList[this.key].isNow?this.$router.push("/promotion/seckill/"+t):this.$message.error("秒杀活动还未开启，敬请期待！")},handlePageSizeChange:function(t){this.pageSize=t,this.refresh()},handleCurrentPageChange:function(t){this.currentPage=t,this.refresh()},refresh:function(){this.loading=!0,this.getGoodsList()},imageError:function(t){this.goodsList[t].goods_image=this.defaultGoodsImage}},head:function(){return{title:"秒杀专区-"+this.$store.state.site.siteInfo.site_name,meta:[{name:"description",content:this.$store.state.site.siteInfo.seo_description},{name:"keyword",content:this.$store.state.site.siteInfo.seo_keywords}]}}},f={name:"seckill",components:{},mixins:[m]},_=(n(730),n(731),n(6)),component=Object(_.a)(f,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"ns-seckill"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingAd,expression:"loadingAd"}],staticClass:"ns-seckill-head"},[t.adList.length?e("el-carousel",{attrs:{height:"420px","indicator-position":"none"}},t._l(t.adList,(function(n){return e("el-carousel-item",{key:n.adv_id},[e("el-image",{attrs:{src:t.$img(n.adv_image)},on:{click:function(e){return t.$util.pushToTab(n.adv_url.url)}}})],1)})),1):t._e(),t._v(" "),t.timeList.length>0?e("div",{staticClass:"ns-seckill-time-box"},[t.timeList.length>4?e("span",{staticClass:"left-btn el-icon-arrow-left",on:{click:function(e){return t.changeThumbImg("prev")}}}):t._e(),t._v(" "),t.timeList.length>4?e("span",{staticClass:"right-btn el-icon-arrow-right",on:{click:function(e){return t.changeThumbImg("next")}}}):t._e(),t._v(" "),e("div",{ref:"seckillTime",staticClass:"ns-seckill-time-list"},[e("ul",{staticClass:"seckill-time-ul",style:{left:t.thumbPosition+"px"}},t._l(t.timeList,(function(n,r){return e("li",{key:r,staticClass:"seckill-time-li",class:{"selected-tab":t.seckillId==n.id},attrs:{slot:"label"},on:{click:function(e){return t.handleSelected(r,n)}},slot:"label"},[e("div",[t._v(t._s(n.seckill_start_time_show+" - "+n.seckill_end_time_show))]),t._v(" "),"today"==n.type?e("div",[n.isNow?n.isNow?e("p",{staticClass:"em font-size-tag"},[t._v("抢购中")]):t._e():e("p",{staticClass:"em font-size-tag"},[t._v("即将开始")])]):e("div",[e("p",[t._v("敬请期待")])])])})),0)])]):t._e()],1),t._v(" "),t.timeList.length>0&&t.goodsList.length>0?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"seckillGoods",staticClass:"ns-seckill-box"},[e("div",{staticClass:"ns-seckill-title"},[t._m(0),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:t.seckillIndex==t.index&&t.isTrue&&0==t.isNoClick,expression:"seckillIndex == index && isTrue && isNoClick == false"}],staticClass:"ns-seckill-right"},[e("span",[t._v(t._s(t.seckillText))]),t._v(" "),e("count-down",{staticClass:"count-down",attrs:{currentTime:t.seckillTimeMachine.currentTime,startTime:t.seckillTimeMachine.startTime,endTime:t.seckillTimeMachine.endTime,dayTxt:"：",hourTxt:"：",minutesTxt:"：",secondsTxt:""},on:{start_callback:function(e){return t.countDownS_cb()},end_callback:function(e){return t.countDownE_cb()}}})],1)]),t._v(" "),e("div",[e("div",{staticClass:"goods-list"},t._l(t.goodsList,(function(n,r){return e("div",{key:r,staticClass:"goods",on:{click:function(e){return t.toGoodsDetail(n.id)}}},[e("div",{staticClass:"img"},[e("el-image",{attrs:{fit:"scale-down",src:t.$img(n.goods_image,{size:"mid"}),lazy:""},on:{error:function(e){return t.imageError(t.index)}}})],1),t._v(" "),e("div",{staticClass:"name"},[e("p",{attrs:{title:n.goods_name}},[t._v(t._s(n.goods_name))])]),t._v(" "),e("div",{staticClass:"price"},[e("div",{staticClass:"curr-price"},[e("span",[t._v("秒杀价")]),t._v(" "),e("span",[t._v("￥")]),t._v(" "),e("span",{staticClass:"main_price"},[t._v(t._s(n.seckill_price))])]),t._v(" "),e("span",{staticClass:"primary_price"},[t._v("￥"+t._s(n.price))])]),t._v(" "),t.seckillIndex==t.index&&t.timeList[t.index].isNow&&1==t.shouType?e("el-button",[t._v("立即抢购")]):t._e()],1)})),0)]),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.currentPage,"page-size":t.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)]):t.loading?t._e():e("div",{staticClass:"empty-wrap"},[e("img",{attrs:{src:n(545)}}),t._v(" "),e("span",[t._v("暂无正在进行秒杀的商品，"),e("router-link",{staticClass:"ns-text-color",attrs:{to:"/"}},[t._v("去首页")]),t._v("看看吧")],1)])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"seckill-title-left"},[e("span",{staticClass:"name"},[t._v("限时秒杀")]),t._v(" "),e("span",{staticClass:"desc"},[t._v("限时秒杀，每款限购一件")])])}],!1,null,"03310974",null);e.default=component.exports}}]);