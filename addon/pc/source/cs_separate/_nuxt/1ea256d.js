(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{536:function(e,t,o){},539:function(e,t,o){"use strict";o(536)},544:function(e,t,o){"use strict";o.r(t);o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=(o(535),o(12)),c=o(206);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}var d={name:"goods_recommend",props:{page:{type:[Number,String],default:1},pageSize:{type:[Number,String],default:5}},data:function(){return{loading:!0,list:[]}},created:function(){this.getGoodsRecommend()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(r.b)(["defaultGoodsImage"])),methods:{getGoodsRecommend:function(){var e=this;Object(c.e)({page:this.page,page_size:this.pageSize}).then((function(t){0==t.code&&(e.list=t.data.list),e.loading=!1})).catch((function(t){e.loading=!1}))},imageError:function(e){this.list[e].sku_image=this.defaultGoodsImage}}},_=d,v=(o(539),o(6)),component=Object(v.a)(_,(function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"goods-recommend"},[t("h4",[e._v("商品精选")]),e._v(" "),e.list.length?t("ul",e._l(e.list,(function(o,n){return t("li",{key:n,on:{click:function(t){return e.$util.pushToTab({path:"/sku/"+o.sku_id})}}},[t("div",{staticClass:"img-wrap"},[t("img",{attrs:{src:e.$img(o.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(n)}}})]),e._v(" "),t("div",{staticClass:"price"},[e._v("￥"+e._s(o.discount_price))]),e._v(" "),t("p",{staticClass:"sku-name"},[e._v(e._s(o.goods_name))]),e._v(" "),t("div",{staticClass:"info-wrap"})])})),0):e._e()])}),[],!1,null,"a68a46cc",null);t.default=component.exports},553:function(e,t,o){"use strict";o.d(t,"g",(function(){return r})),o.d(t,"b",(function(){return c})),o.d(t,"a",(function(){return l})),o.d(t,"f",(function(){return d})),o.d(t,"c",(function(){return _})),o.d(t,"d",(function(){return v})),o.d(t,"e",(function(){return m}));var n=o(1);function r(e){return Object(n.a)({url:"/groupbuy/api/ordercreate/payment",data:e,forceLogin:!0})}function c(){return Object(n.a)({url:"/api/goodsevaluate/config",data:{},forceLogin:!0})}function l(e){return Object(n.a)({url:"/groupbuy/api/ordercreate/calculate",data:e,forceLogin:!0})}function d(e){return Object(n.a)({url:"/groupbuy/api/ordercreate/create",data:e,forceLogin:!0})}function _(e){return Object(n.a)({url:"/groupbuy/api/goods/page",data:e,forceLogin:!0})}function v(e){return Object(n.a)({url:"/groupbuy/api/goods/detail",data:e,forceLogin:!0})}function m(e){return Object(n.a)({url:"/groupbuy/api/goods/info",data:e,forceLogin:!0})}},555:function(e,t,o){"use strict";o.d(t,"c",(function(){return r})),o.d(t,"a",(function(){return c})),o.d(t,"b",(function(){return l}));var n=o(1);function r(e){return Object(n.a)({url:"/api/goodscollect/iscollect",data:e,forceLogin:!0})}function c(e){return Object(n.a)({url:"/api/goodscollect/add",data:e,forceLogin:!0})}function l(e){return Object(n.a)({url:"/api/goodscollect/delete",data:e,forceLogin:!0})}},556:function(e,t,o){"use strict";o.d(t,"b",(function(){return r})),o.d(t,"a",(function(){return c}));var n=o(1);function r(e){return Object(n.a)({url:"/api/goodsevaluate/page",data:e})}function c(e){return Object(n.a)({url:"/api/goodsevaluate/getgoodsevaluate",data:e})}},650:function(e,t,o){},743:function(e,t,o){"use strict";o(650)},758:function(e,t,o){"use strict";o.r(t);o(56);var n=o(577),r=(o(24),o(25),o(29),o(30),o(10)),c=(o(23),o(7),o(317),o(92),o(18),o(31),o(64),o(73),o(206)),l=o(152),d=o(553),_=o(555),v=o(556),m=o(12),f=o(538),h=o.n(f),address=o(208);o(27);function k(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}var y={data:function(){return{id:0,skuId:0,loading:!0,picZoomUrl:"",thumbPosition:0,moveThumbLeft:!1,moveThumbRight:!1,goodsSkuDetail:{video_url:""},groupbuyText:"距离结束仅剩",groupbuyTimeMachine:{currentTime:0,startTime:0,endTime:0},qrcode:"",specDisabled:!1,specBtnRepeat:!1,btnSwitch:!1,shopInfo:{},whetherCollection:0,score:0,currentPage:1,pageSize:25,total:0,evaluaType:0,evaluteCount:{},goodsEvaluateList:[],evaluate_show:!1,service:null,number:1,limitNumber:0,tabName:"detail",playerOptions:{playbackRates:[.5,1,1.5,2,3],autoplay:!1,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,sources:[{type:"video/mp4",src:""}],poster:"",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!0,fullscreenToggle:!0}},switchMedia:"img",hasFollow:!1,kefuConfig:{system:"",open_pc:"",open_url:""},provinceArr:{},cityArr:{},districtArr:{},currTabAddres:"province",hideRegion:!1,selectedAddress:{},service_list:[],serverType:"disable",serverThird:"",categoryNameArr:[]}},components:{CountDown:h.a},created:function(){var e=this;this.id=this.$route.params.id,this.addonIsExit&&1!=this.addonIsExit.groupbuy?this.$message({message:"团购插件未安装",type:"warning",duration:2e3,onClose:function(){e.$route.push("/")}}):(this.getGoodsSkuDetail(),this.shopServiceOpen(),this.getGoodsEvaluate(),this.goodsEvaluCount())},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?k(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):k(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(m.b)(["token","siteInfo","defaultHeadImage","defaultShopImage","addonIsExit","locationRegion"])),watch:{$route:function(e){var t=this;this.id=e.query.id,this.addonIsExit&&1!=this.addonIsExit.groupbuy?this.$message({message:"团购插件未安装",type:"warning",duration:2e3,onClose:function(){t.$route.push("/")}}):(this.getGoodsSkuDetail(),this.shopServiceOpen())},addonIsExit:function(){var e=this;1!=this.addonIsExit.groupbuy&&this.$message({message:"团购插件未安装",type:"warning",duration:2e3,onClose:function(){e.$route.push("/")}})}},methods:{shopServiceOpen:function(){},tabChange:function(e,t){},bundlingChange:function(e,t){},getGoodsSkuDetail:function(){var e=this;Object(d.d)({groupbuy_id:this.id}).then((function(t){var data=t.data;if(null!=data.goods_sku_detail){e.goodsSkuDetail=data.goods_sku_detail;var o=data.goods_sku_detail.category_id.split(",");if(o=o.filter((function(e){return e&&e.trim()})),e.categorySearch(o[o.length-1]),e.service_list=data.goods_sku_detail.goods_service,e.skuId=e.goodsSkuDetail.sku_id,e.number=e.goodsSkuDetail.buy_num,e.limitNumber=e.goodsSkuDetail.buy_num,e.goodsSkuDetail.end_time-t.timestamp>0?e.groupbuyTimeMachine={currentTime:t.timestamp,startTime:t.timestamp,endTime:e.goodsSkuDetail.end_time}:e.$message({message:"活动已结束",type:"warning",duration:2e3,onClose:function(){e.$router.push("/sku/"+e.goodsSkuDetail.sku_id)}}),e.goodsSkuDetail.sku_images?e.goodsSkuDetail.sku_images=e.goodsSkuDetail.sku_images.split(","):e.goodsSkuDetail.sku_images=[],e.goodsSkuDetail.goods_spec_format&&e.goodsSkuDetail.goods_image&&(e.goodsSkuDetail.goods_image=e.goodsSkuDetail.goods_image.split(","),e.goodsSkuDetail.sku_images=e.goodsSkuDetail.sku_images.concat(e.goodsSkuDetail.goods_image)),e.goodsSkuDetail.video_url&&(e.switchMedia="video",e.playerOptions.poster=e.$img(e.goodsSkuDetail.sku_images[0]),e.playerOptions.sources[0].src=e.$img(e.goodsSkuDetail.video_url)),e.picZoomUrl=e.goodsSkuDetail.sku_images[0],e.goodsSkuDetail.unit=e.goodsSkuDetail.unit||"件",e.goodsSkuDetail.sku_spec_format&&(e.goodsSkuDetail.sku_spec_format=JSON.parse(e.goodsSkuDetail.sku_spec_format)),e.goodsSkuDetail.goods_attr_format){var n=JSON.parse(e.goodsSkuDetail.goods_attr_format);e.goodsSkuDetail.goods_attr_format=e.$util.unique(n,"attr_id");for(var i=0;i<e.goodsSkuDetail.goods_attr_format.length;i++)for(var r=0;r<n.length;r++)e.goodsSkuDetail.goods_attr_format[i].attr_id==n[r].attr_id&&e.goodsSkuDetail.goods_attr_format[i].attr_value_id!=n[r].attr_value_id&&(e.goodsSkuDetail.goods_attr_format[i].attr_value_name+="、"+n[r].attr_value_name)}e.goodsSkuDetail.goods_spec_format&&(e.goodsSkuDetail.goods_spec_format=JSON.parse(e.goodsSkuDetail.goods_spec_format)),window.document.title="".concat(e.goodsSkuDetail.sku_name," - ").concat(e.siteInfo.site_name),e.loading=!1}else e.$router.push("/")})).then((function(t){""!=e.token&&e.getWhetherCollection(),e.modifyGoodsInfo(),e.getGoodsQrcode(),e.getEvaluateConfig(),e.getAddress("province",null,!0),e.locationRegion||e.$store.commit("app/SET_LOCATION_REGION",{level_1:{id:11e4,pid:0,name:"北京市",shortname:"北京",longitude:"116.40529",latitude:"39.904987",level:1,sort:1,status:1,default_data:1},level_2:{id:110100,pid:11e4,name:"北京市",shortname:"北京",longitude:"116.40529",latitude:"39.904987",level:2,sort:1,status:1,default_data:1},level_3:{id:110101,pid:110100,name:"东城区",shortname:"东城",longitude:"116.418755",latitude:"39.917545",level:3,sort:3,status:1,default_data:1}}),e.selectedAddress=e.locationRegion,e.provinceId=e.selectedAddress.level_1.id,e.getAddress("city",null,!0,(function(){e.cityId=e.selectedAddress.level_2.id,e.cityId&&e.getAddress("district",null,!0)}))})).catch((function(t){e.loading=!1,e.$router.push("/")}))},categorySearch:function(e){var t=this;Object(l.b)({category_id:e}).then((function(e){if(0==e.code&&e.data){t.categoryNameArr=[];try{e.data.category_full_name.split("$_SPLIT_$").forEach((function(o,n){var r={};r.name=o,r.category_id=e.data["category_id_"+(n+1)],t.categoryNameArr.push(r)}))}catch(e){t.categoryNameArr=[]}}}))},getEvaluateConfig:function(){var e=this;Object(d.b)().then((function(t){if(0==t.code){var data=t.data;e.evaluateConfig=data,1==e.evaluateConfig.evaluate_show&&(e.evaluate_show=!0,e.getGoodsEvaluate())}}))},service_link:function(){this.token?this.$refs.servicerMessage.show():this.$message({message:"您还未登录",type:"warning"})},changeThumbImg:function(e){if(!(this.goodsSkuDetail.sku_images.length<4)){var t=this.goodsSkuDetail.sku_images.length%4;if(0==t)t=this.goodsSkuDetail.sku_images.length-4;else if(0!=t&&1!=t&&t<2)return;"prev"==e?0!=this.thumbPosition&&94!=Math.round(this.thumbPosition,2)&&(this.thumbPosition+=94):"next"==e&&Math.round(this.thumbPosition,2)!=-Math.round(94*t,2)&&(this.thumbPosition-=94)}},getWhetherCollection:function(){var e=this;Object(_.c)({goods_id:this.goodsSkuDetail.goods_id}).then((function(t){e.whetherCollection=t.data}))},editCollection:function(){var e=this;0==this.whetherCollection?Object(_.a)({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id}).then((function(t){t.data>0&&(e.whetherCollection=1,e.goodsSkuDetail.collect_num++)})):Object(_.b)({goods_id:this.goodsSkuDetail.goods_id}).then((function(t){t.data>0&&(e.whetherCollection=0,e.goodsSkuDetail.collect_num--)}))},getAftersale:function(){var e=this;Object(c.b)({}).then((function(t){if(0==t.code&&t.data){t.data.content;t.data.content&&(e.service=t.data.content)}}))},modifyGoodsInfo:function(){Object(c.i)({sku_id:this.skuId}),Object(c.a)({sku_id:this.skuId,goods_id:this.goodsSkuDetail.goods_id})},getGoodsQrcode:function(){var e=this;Object(c.d)({sku_id:this.skuId}).then((function(t){var data=t.data;data.path.h5.img&&(e.qrcode=e.$img(data.path.h5.img))}))},goodsEvaluCount:function(){var e=this;Object(v.a)({goods_id:this.id}).then((function(t){0==t.code&&t.data&&(e.evaluteCount=t.data)}))},getGoodsEvaluate:function(){var e=this;Object(v.b)({page:this.currentPage,page_size:this.pageSize,goods_id:this.id,explain_type:0==this.evaluaType?"":this.evaluaType}).then((function(t){var o=[];t.message;0==t.code&&t.data&&(o=t.data.list,e.total=t.data.count);for(var i=0;i<o.length;i++){if(1==o[i].explain_type?o[i].star=5:2==o[i].explain_type?o[i].star=3:3==o[i].explain_type&&(o[i].star=1),o[i].images){o[i].images=o[i].images.split(","),o[i].imagesFormat=[];for(var n=0;n<o[i].images.length;n++)o[i].imagesFormat.push(e.$img(o[i].images[n]))}if(o[i].again_images){o[i].again_images=o[i].again_images.split(","),o[i].againImagesFormat=[];for(var r=0;r<o[i].again_images.length;r++)o[i].againImagesFormat.push(e.$img(o[i].again_images[r]))}1==o[i].is_anonymous&&(o[i].member_name=o[i].member_name.replace(o[i].member_name.substring(1,o[i].member_name.length-1),"***"))}e.goodsEvaluateList=o}))},imageErrorEvaluate:function(e){this.goodsEvaluateList[e].member_headimg=this.defaultHeadImage},handlePageSizeChange:function(e){this.pageSize=e,this.getGoodsEvaluate()},handleCurrentPageChange:function(e){this.currentPage=e,this.getGoodsEvaluate()},changeSpec:function(e,t){var o=this;if(!this.specDisabled){this.specBtnRepeat=!1,this.skuId=e;for(var i=0;i<this.goodsSkuDetail.goods_spec_format.length;i++)for(var n=this.goodsSkuDetail.goods_spec_format[i],r=0;r<n.value.length;r++)t==this.goodsSkuDetail.goods_spec_format[i].value[r].spec_id&&(this.goodsSkuDetail.goods_spec_format[i].value[r].selected=!1);Object(d.e)({sku_id:this.skuId,id:this.goodsSkuDetail.groupbuy_id}).then((function(e){var data=e.data;null!=data?(data.sku_images=data.sku_images.split(","),o.picZoomUrl=data.sku_images[0],o.playerOptions.poster=o.$img(data.sku_images[0]),data.sku_spec_format&&(data.sku_spec_format=JSON.parse(data.sku_spec_format)),data.goods_spec_format&&(data.goods_spec_format=JSON.parse(data.goods_spec_format)),o.keyInput(!0),data.end_time-e.timestamp>0?o.groupbuyTimeMachine={currentTime:e.timestamp,startTime:e.timestamp,endTime:data.end_time}:o.$message({message:"活动已结束",type:"warning",duration:2e3,onClose:function(){o.$router.push("/sku/"+o.goodsSkuDetail.sku_id)}}),o.specBtnRepeat=!1,Object.assign(o.goodsSkuDetail,data)):o.$router.push("/")}))}},changeNum:function(e){if(0!=this.goodsSkuDetail.stock){var t=this.goodsSkuDetail.stock,o=this.goodsSkuDetail.buy_num;t=(this.goodsSkuDetail.buy_num,this.goodsSkuDetail.stock,this.goodsSkuDetail.stock),"+"==e?this.number<t&&this.number++:"-"==e&&this.number>o&&(this.number-=1)}},blur:function(){var e=this,t=parseInt(this.number);this.number=0,setTimeout((function(){e.number=t}),0)},keyInput:function(e,t){var o=this;setTimeout((function(){0!=o.goodsSkuDetail.stock?(e&&0==o.number.length&&(o.number=1),e&&(o.number<=0||isNaN(o.number))&&(o.number=1),o.number<o.goodsSkuDetail.buy_num&&(o.number=o.goodsSkuDetail.buy_num),e&&(o.number=parseInt(o.number)),t&&t()):o.number=0}),0)},onPlayerPlay:function(e){},onPlayerPause:function(e){},onPlayerEnded:function(e){},onPlayerWaiting:function(e){},onPlayerPlaying:function(e){},onPlayerLoadeddata:function(e){},onPlayerTimeupdate:function(e){},onPlayerCanplay:function(e){},onPlayerCanplaythrough:function(e){},playerStateChanged:function(e){},playerReadied:function(e){},buyNow:function(){var e=this;this.keyInput(!0,(function(){if(0!=e.goodsSkuDetail.stock)if(0!=e.number.length&&0!=e.number){var data={groupbuy_id:e.goodsSkuDetail.groupbuy_id,sku_id:e.skuId,num:e.number};e.$store.dispatch("order/setGroupbuyOrderCreateData",data),e.$router.push({path:"/promotion/groupbuy/payment"})}else e.$message({message:"购买数量不能为0",type:"warning"});else e.$message({message:"商品已售罄",type:"warning"})}))},countDownS_cb:function(){},countDownE_cb:function(){var e=this;this.groupbuyText="活动已结束",this.$message({message:"团购活动已结束",type:"warning",duration:2e3,onClose:function(){e.$router.push("/sku/"+e.goodsSkuDetail.sku_id)}})},imageErrorSpec:function(e){this.goodsSkuDetail.sku_images[e]=this.defaultGoodsImage,this.picZoomUrl=this.defaultGoodsImage},getAddress:function(e,t,o,n){var r=this,c=0;switch(e){case"province":c=0;break;case"city":t&&(this.provinceId=t.id),c=this.provinceId,this.cityArr={},this.districtArr={};break;case"district":t&&(this.cityId=t.id),c=this.cityId,this.districtArr={}}if(t){if(t.level<=2)for(var i=t.level;i<=3;i++)delete this.selectedAddress["level_"+i];this.selectedAddress["level_"+t.level]=t}if(o||this.$store.commit("app/SET_LOCATION_REGION",this.selectedAddress),this.$forceUpdate(),"community"==e)return this.hideRegion=!0,void setTimeout((function(){r.hideRegion=!1}),10);Object(address.a)({pid:c}).then((function(t){t.code;var data=t.data;if(data){switch(e){case"province":r.provinceArr=data;break;case"city":r.cityArr=data;break;case"district":r.districtArr=data}r.currTabAddres=e,n&&n()}}))}}},C=o(544),S=o(217),D={name:"groupbuy_detail",components:{PicZoom:n.a,GoodsRecommend:C.default,servicerMessage:S.default},mixins:[y]},w=(o(743),o(6)),component=Object(w.a)(D,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"goods-detail-wrap"},[e.categoryNameArr?t("div",{staticClass:"detail-nav-wrap"},[e.categoryNameArr.length?t("div",{staticClass:"detail-nav"},[e._l(e.categoryNameArr,(function(o,n){return[t("router-link",{attrs:{to:{path:"/goods/list",query:{category_id:o.category_id,level:n}}}},[e._v("\n          "+e._s(o.name)+"\n        ")]),e._v(" "),t("span",{staticClass:"iconfont icon-arrow-right"})]})),e._v(" "),t("span",{staticClass:"goods-name"},[e._v(e._s(e.goodsSkuDetail.goods_name))])],2):e._e()]):e._e(),e._v(" "),t("div",{staticClass:"detail-main"},[t("div",{staticClass:"goods-detail"},[t("div",{staticClass:"preview-wrap"},[""!=e.goodsSkuDetail.video_url?t("div",{staticClass:"video-player-wrap",class:{show:"video"==e.switchMedia}},[""!=e.goodsSkuDetail.video_url?t("video-player",{ref:"videoPlayer",attrs:{playsinline:!0,options:e.playerOptions},on:{play:function(t){return e.onPlayerPlay(t)},pause:function(t){return e.onPlayerPause(t)},ended:function(t){return e.onPlayerEnded(t)},waiting:function(t){return e.onPlayerWaiting(t)},playing:function(t){return e.onPlayerPlaying(t)},loadeddata:function(t){return e.onPlayerLoadeddata(t)},timeupdate:function(t){return e.onPlayerTimeupdate(t)},canplay:function(t){return e.onPlayerCanplay(t)},canplaythrough:function(t){return e.onPlayerCanplaythrough(t)},statechanged:function(t){return e.playerStateChanged(t)},ready:e.playerReadied}}):e._e(),e._v(" "),""!=e.goodsSkuDetail.video_url?t("div",{staticClass:"media-mode"},[t("span",{class:{"ns-bg-color":"video"==e.switchMedia},on:{click:function(t){e.switchMedia="video"}}},[e._v("视频")]),e._v(" "),t("span",{class:{"ns-bg-color":"img"==e.switchMedia},on:{click:function(t){e.switchMedia="img"}}},[e._v("图片")])]):e._e()],1):e._e(),e._v(" "),t("div",{staticClass:"magnifier-wrap"},[t("pic-zoom",{ref:"PicZoom",attrs:{url:e.$img(e.picZoomUrl),scale:2}})],1),e._v(" "),t("div",{staticClass:"spec-items"},[t("span",{staticClass:"left-btn iconfont icon-weibiaoti35",class:{move:e.moveThumbLeft},on:{click:function(t){return e.changeThumbImg("prev")}}}),e._v(" "),t("span",{staticClass:"right-btn iconfont icon-weibiaoti35",class:{move:e.moveThumbRight},on:{click:function(t){return e.changeThumbImg("next")}}}),e._v(" "),t("ul",{style:{top:42+e.thumbPosition+"px"}},e._l(e.goodsSkuDetail.sku_images,(function(o,n){return t("li",{key:n,class:{selected:e.picZoomUrl==o},on:{mousemove:function(t){e.picZoomUrl=o}}},[t("img",{attrs:{src:e.$img(o,{size:"small"})},on:{error:function(t){return e.imageErrorSpec(n)}}})])})),0)])]),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"basic-info-wrap"},[t("h1",[e._v(e._s(e.goodsSkuDetail.sku_name))]),e._v(" "),e.goodsSkuDetail.introduction?t("p",{staticClass:"desc ns-text-color"},[e._v(e._s(e.goodsSkuDetail.introduction))]):e._e(),e._v(" "),e.groupbuyTimeMachine.currentTime?t("div",{staticClass:"discount-banner ns-bg-color"},[e._m(0),e._v(" "),t("div",{staticClass:"surplus-time"},[t("span",[e._v(e._s(e.groupbuyText))]),e._v(" "),t("count-down",{staticClass:"count-down",attrs:{currentTime:e.groupbuyTimeMachine.currentTime,startTime:e.groupbuyTimeMachine.startTime,endTime:e.groupbuyTimeMachine.endTime,dayTxt:"天",hourTxt:"小时",minutesTxt:"分钟",secondsTxt:"秒"},on:{start_callback:function(t){return e.countDownS_cb()},end_callback:function(t){return e.countDownE_cb()}}})],1)]):e._e(),e._v(" "),t("div",{staticClass:"item-block"},[t("div",{staticClass:"promotion-price"},[t("dl",{staticClass:"item-line"},[t("dt",{staticClass:"ns-text-color-gray"},[e._v("团购价")]),e._v(" "),t("dd",[t("em",{staticClass:"yuan ns-text-color"},[e._v("¥")]),e._v(" "),t("span",{staticClass:"price ns-text-color"},[e._v(e._s(e.goodsSkuDetail.groupbuy_price))])])]),e._v(" "),t("dl",{staticClass:"item-line"},[t("dt",{staticClass:"ns-text-color-gray"},[e._v("原价")]),e._v(" "),t("dd",[t("em",{staticClass:"market-yuan"},[e._v("¥")]),e._v(" "),t("span",{staticClass:"market-price"},[e._v(e._s(e.goodsSkuDetail.price))])])]),e._v(" "),0==e.goodsSkuDetail.is_virtual?t("dl",{staticClass:"item-line"},[t("dt",[e._v("运费")]),e._v(" "),t("dd",[e.goodsSkuDetail.is_free_shipping?t("i",{staticClass:"i-activity-flag ns-text-color ns-border-color"},[e._v("快递免邮")]):t("i",{staticClass:"i-activity-flag ns-text-color ns-border-color"},[e._v("快递不免邮")])])]):e._e()])]),e._v(" "),0==e.goodsSkuDetail.is_virtual?t("dl",{staticClass:"item-line delivery"},[t("dt",[e._v("配送至")]),e._v(" "),t("dd",[t("div",{staticClass:"region-selected ns-border-color-gray"},[t("span",[e.selectedAddress.level_1?[e._l(e.selectedAddress,(function(t){return[e._v(e._s(t.name))]}))]:[e._v("请选择配送地址")]],2),e._v(" "),t("i",{staticClass:"el-icon-arrow-down"})]),e._v(" "),t("div",{staticClass:"region-list ns-border-color-gray",class:{hide:e.hideRegion}},[t("ul",{staticClass:"nav-tabs"},[t("li",{class:{active:"province"==e.currTabAddres},on:{click:function(t){e.currTabAddres="province"}}},[t("div",[t("span",[e._v(e._s(e.selectedAddress.level_1?e.selectedAddress.level_1.name:"请选择省"))]),e._v(" "),t("i",{staticClass:"el-icon-arrow-down"})])]),e._v(" "),t("li",{class:{active:"city"==e.currTabAddres},on:{click:function(t){e.currTabAddres="city"}}},[t("div",[t("span",[e._v(e._s(e.selectedAddress.level_2?e.selectedAddress.level_2.name:"请选择市"))]),e._v(" "),t("i",{staticClass:"el-icon-arrow-down"})])]),e._v(" "),t("li",{class:{active:"district"==e.currTabAddres},on:{click:function(t){e.currTabAddres="district"}}},[t("div",[t("span",[e._v(e._s(e.selectedAddress.level_3?e.selectedAddress.level_3.name:"请选择区/县"))]),e._v(" "),t("i",{staticClass:"el-icon-arrow-down"})])])]),e._v(" "),t("div",{staticClass:"tab-content"},[t("div",{staticClass:"tab-pane",class:{active:"province"==e.currTabAddres}},[t("ul",{staticClass:"province"},e._l(e.provinceArr,(function(o,n){return t("li",{key:n,class:{selected:e.selectedAddress["level_"+o.level]&&e.selectedAddress["level_"+o.level].id==o.id}},[t("span",{on:{click:function(t){return e.getAddress("city",o)}}},[e._v(e._s(o.name))])])})),0)]),e._v(" "),t("div",{staticClass:"tab-pane",class:{active:"city"==e.currTabAddres}},[t("ul",{staticClass:"city"},e._l(e.cityArr,(function(o,n){return t("li",{key:n,class:{selected:e.selectedAddress["level_"+o.level]&&e.selectedAddress["level_"+o.level].id==o.id}},[t("span",{on:{click:function(t){return e.getAddress("district",o)}}},[e._v(e._s(o.name))])])})),0)]),e._v(" "),t("div",{staticClass:"tab-pane",class:{active:"district"==e.currTabAddres}},[t("ul",{staticClass:"district"},e._l(e.districtArr,(function(o,n){return t("li",{key:n,class:{selected:e.selectedAddress["level_"+o.level]&&e.selectedAddress["level_"+o.level].id==o.id}},[t("span",{on:{click:function(t){return e.getAddress("community",o)}}},[e._v(e._s(o.name))])])})),0)])])])])]):e._e(),e._v(" "),t("dl",{staticClass:"item-line service"},[t("dt",[e._v("服务")]),e._v(" "),t("dd",[t("span",[e._v("\n              由\n              "),t("span",{staticClass:"ns-text-color"},[e._v(e._s(e.siteInfo.site_name))]),e._v("\n              发货并提供售后服务\n            ")])])]),e._v(" "),t("hr",{staticClass:"divider"}),e._v(" "),e.goodsSkuDetail.goods_spec_format?t("div",{staticClass:"sku-list"},e._l(e.goodsSkuDetail.goods_spec_format,(function(o,n){return t("dl",{key:n,staticClass:"item-line"},[t("dt",[e._v(e._s(o.spec_name))]),e._v(" "),t("dd",[t("ul",e._l(o.value,(function(o,n){return t("li",{key:n},[t("div",{class:{"selected ns-border-color":o.selected||e.skuId==o.sku_id,disabled:o.disabled||!o.selected&&e.specDisabled},on:{click:function(t){return e.changeSpec(o.sku_id,o.spec_id)}}},[o.image?t("img",{attrs:{src:e.$img(o.image,{size:"small"})}}):e._e(),e._v(" "),t("span",[e._v(e._s(o.spec_value_name))]),e._v(" "),t("i",{staticClass:"iconfont iconduigou1 ns-text-color"})])])})),0)])])})),0):e._e(),e._v(" "),t("div",{staticClass:"buy-number"},[t("dl",{staticClass:"item-line"},[t("dt",[e._v("数量")]),e._v(" "),t("dd",[t("div",{staticClass:"num-wrap"},[t("div",{staticClass:"operation"},[t("span",{staticClass:"decrease el-icon-minus",on:{click:function(t){return e.changeNum("-")}}}),e._v(" "),t("el-input",{attrs:{placeholder:"0"},on:{input:function(t){return e.keyInput()}},model:{value:e.number,callback:function(t){e.number=t},expression:"number"}}),e._v(" "),t("span",{staticClass:"increase el-icon-plus",on:{click:function(t){return e.changeNum("+")}}})],1)]),e._v(" "),t("span",{staticClass:"unit"},[e._v(e._s(e.goodsSkuDetail.unit))]),e._v(" "),t("span",{staticClass:"inventory"},[e._v("库存"+e._s(e.goodsSkuDetail.stock)+e._s(e.goodsSkuDetail.unit))]),e._v(" "),e.limitNumber>0?t("em",[e._v("("+e._s(e.limitNumber)+e._s(e.goodsSkuDetail.unit)+"起购)")]):e._e()])])]),e._v(" "),t("dl",{staticClass:"item-line buy-btn"},[t("dt"),e._v(" "),1==e.goodsSkuDetail.goods_state?t("dd",[e.goodsSkuDetail.goods_stock<e.goodsSkuDetail.buy_num&&!e.goodsSkuDetail.sku_spec_format?[t("el-button",{attrs:{type:"info",plain:"",disabled:""}},[e._v("库存不足")])]:[t("el-button",{attrs:{type:"primary",plain:""},on:{click:e.buyNow}},[e._v("立即抢购")])],e._v(" "),t("div",{staticClass:"go-phone icon-item",on:{click:e.editCollection}},[t("span",{class:["iconfont",1==e.whetherCollection?"icon-_shouzang2 selected":"icon-shouzang"]}),e._v(" "),t("span",[e._v("收藏")])])],2):t("dd",[[t("el-button",{attrs:{type:"info",plain:"",disabled:""}},[e._v("该商品已下架")])],e._v(" "),t("div",{staticClass:"go-phone icon-item",on:{click:e.editCollection}},[t("span",{class:["iconfont",1==e.whetherCollection?"icon-_shouzang2 selected":"icon-shouzang"]}),e._v(" "),t("span",[e._v("收藏")])])],2)]),e._v(" "),t("dl",{directives:[{name:"show",rawName:"v-show",value:e.service_list.length,expression:"service_list.length"}],staticClass:"item-line merchant-service"},[t("dt",[e._v("商品服务")]),e._v(" "),t("div",e._l(e.service_list,(function(o){return t("dd",{staticClass:"service-li"},[t("i",{staticClass:"el-icon-success"}),e._v(" "),t("span",{staticClass:"ns-text-color-gray",attrs:{title:o.service_name}},[e._v(e._s(o.service_name))])])})),0)])]),e._v(" "),t("div",{staticClass:"detail-wrap"},[t("el-tabs",{staticClass:"goods-tab",attrs:{type:"card"},on:{"tab-click":e.tabChange},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},[t("el-tab-pane",{attrs:{label:"商品详情",name:"detail"}},[t("div",{domProps:{innerHTML:e._s(e.goodsSkuDetail.goods_content)}})]),e._v(" "),t("el-tab-pane",{attrs:{label:"商品属性",name:"attr"}},[t("ul",{staticClass:"attr-list"},[e.goodsSkuDetail.goods_attr_format&&e.goodsSkuDetail.goods_attr_format.length>0?e._l(e.goodsSkuDetail.goods_attr_format,(function(o,n){return t("li",{key:n},[e._v(e._s(o.attr_name)+"："+e._s(o.attr_value_name))])})):e._e()],2)]),e._v(" "),e.evaluate_show?t("el-tab-pane",{staticClass:"evaluate",attrs:{label:e.goodsEvaluateList.length?"商品评价("+e.goodsEvaluateList.length+")":"商品评价",name:"evaluate"}},[e.goodsEvaluateList.length?[t("nav",[t("li",{class:0==e.evaluaType?"selected":"",on:{click:function(t){return e.evaluationType(0)}}},[e._v("全部评价("+e._s(e.evaluteCount.total)+")")]),e._v(" "),t("li",{class:1==e.evaluaType?"selected":"",on:{click:function(t){return e.evaluationType(1)}}},[e._v("好评("+e._s(e.evaluteCount.haoping)+")")]),e._v(" "),t("li",{class:2==e.evaluaType?"selected":"",on:{click:function(t){return e.evaluationType(2)}}},[e._v("中评("+e._s(e.evaluteCount.zhongping)+")")]),e._v(" "),t("li",{class:3==e.evaluaType?"selected":"",on:{click:function(t){return e.evaluationType(3)}}},[e._v("差评("+e._s(e.evaluteCount.chaping)+")")])]),e._v(" "),t("ul",{staticClass:"list"},e._l(e.goodsEvaluateList,(function(o,n){return t("li",{key:n},[t("div",{staticClass:"member-info"},[t("img",{staticClass:"avatar",attrs:{src:e.$img(o.member_headimg)},on:{error:function(t){return e.imageErrorEvaluate(n)}}}),e._v(" "),t("span",[e._v(e._s(o.member_name))])]),e._v(" "),t("div",{staticClass:"info-wrap"},[t("el-rate",{attrs:{disabled:""},model:{value:o.star,callback:function(t){e.$set(o,"star",t)},expression:"item.star"}}),e._v(" "),t("p",{staticClass:"content"},[e._v(e._s(o.content))]),e._v(" "),o.images?t("div",{staticClass:"img-list"},e._l(o.images,(function(img,n){return t("el-image",{key:n,attrs:{src:e.$img(img),"preview-src-list":o.imagesFormat}})})),1):e._e(),e._v(" "),t("div",{staticClass:"sku-info"},[t("span",[e._v(e._s(o.sku_name))]),e._v(" "),t("span",{staticClass:"create-time"},[e._v(e._s(e.$util.timeStampTurnTime(o.create_time)))])]),e._v(" "),""!=o.explain_first?t("div",{staticClass:"evaluation-reply"},[e._v("店家回复："+e._s(o.explain_first))]):e._e(),e._v(" "),""!=o.again_content?[t("div",{staticClass:"review-evaluation"},[t("span",[e._v("追加评价")]),e._v(" "),t("span",{staticClass:"review-time"},[e._v(e._s(e.$util.timeStampTurnTime(o.again_time)))])]),e._v(" "),t("p",{staticClass:"content"},[e._v(e._s(o.again_content))]),e._v(" "),t("div",{staticClass:"img-list"},e._l(o.again_images,(function(n,r){return t("el-image",{key:r,attrs:{src:e.$img(n),"preview-src-list":o.againImagesFormat}})})),1),e._v(" "),""!=o.again_explain?t("div",{staticClass:"evaluation-reply"},[e._v("店家回复："+e._s(o.again_explain)+"\n                      ")]):e._e()]:e._e()],2)])})),0),e._v(" "),t("div",{staticClass:"pager"},[t("el-pagination",{attrs:{background:"","pager-count":5,total:e.total,"prev-text":"上一页","next-text":"下一页","current-page":e.currentPage,"page-size":e.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handlePageSizeChange,"current-change":e.handleCurrentPageChange}})],1)]:t("div",{staticClass:"empty"},[e._v("该商品暂无评价哦")])],2):e._e(),e._v(" "),e.service?[1==e.service.is_display?t("el-tab-pane",{staticClass:"after-sale",attrs:{label:"售后保障",name:"after_sale"}},[t("div",{domProps:{innerHTML:e._s(e.service.content)}})]):e._e()]:e._e()],2)],1),e._v(" "),t("servicerMessage",{ref:"servicerMessage",staticClass:"kefu",attrs:{shop:{shop_id:e.shopInfo.site_id,logo:e.shopInfo.logo,shop_name:e.shopInfo.site_name}}})],1)])])}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"activity-name"},[t("i",{staticClass:"discount-icon iconfont iconicon_naozhong"}),e._v(" "),t("span",[e._v("团购")])])}],!1,null,null,null);t.default=component.exports}}]);