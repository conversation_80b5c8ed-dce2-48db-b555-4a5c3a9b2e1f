(window.webpackJsonp=window.webpackJsonp||[]).push([[42],{535:function(e,t,r){"use strict";var o=r(3),n=r(35),_=r(14),l=r(8),path=r(319),d=r(5),c=r(113),v=r(16),m=r(205),f=r(63),h=r(112),y=r(318),D=r(4),T=r(93).f,w=r(57).f,A=r(26).f,x=r(320),O=r(316).trim,C="Number",S=l[C],k=path[C],j=S.prototype,I=l.TypeError,E=d("".slice),B=d("".charCodeAt),$=function(e){var t=y(e,"number");return"bigint"==typeof t?t:N(t)},N=function(e){var t,r,o,n,_,l,d,code,c=y(e,"number");if(h(c))throw I("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=O(c),43===(t=B(c,0))||45===t){if(88===(r=B(c,2))||120===r)return NaN}else if(48===t){switch(B(c,1)){case 66:case 98:o=2,n=49;break;case 79:case 111:o=8,n=55;break;default:return+c}for(l=(_=E(c,2)).length,d=0;d<l;d++)if((code=B(_,d))<48||code>n)return NaN;return parseInt(_,o)}return+c},P=c(C,!S(" 0o1")||!S("0b1")||S("+0x1")),U=function(e){return f(j,e)&&D((function(){x(e)}))},M=function(e){var t=arguments.length<1?0:S($(e));return U(this)?m(Object(t),this,M):t};M.prototype=j,P&&!n&&(j.constructor=M),o({global:!0,constructor:!0,wrap:!0,forced:P},{Number:M});var F=function(e,source){for(var t,r=_?T(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;r.length>o;o++)v(source,t=r[o])&&!v(e,t)&&A(e,t,w(source,t))};n&&k&&F(path[C],k),(P||n)&&F(path[C],S)},538:function(e,t,r){e.exports=r(543)},541:function(e,t,r){"use strict";var o=r(204);t.a={methods:{orderPay:function(e){var t=this;0==e.adjust_money?Object(o.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})})):this.$confirm("商家已将支付金额调整为"+e.pay_money+"元，是否继续支付？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})}))}))},orderClose:function(e,t){var r=this;this.$confirm("您确定要关闭该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.b)({order_id:e}).then((function(e){r.$message({message:"订单关闭成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelivery:function(e,t){var r=this;this.$confirm("您确定已经收到货物了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.h)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()})).catch((function(e){r.$message({message:e.message,type:"warning"}),"function"==typeof t&&t()}))}))},orderVirtualDelivery:function(e,t){var r=this;this.$confirm("您确定要进行收货吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.a)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelete:function(e,t){var r=this;this.$confirm("您确定要删除该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(o.c)({order_id:e}).then((function(e){r.$message({message:"订单删除成功",type:"success"}),"function"==typeof t&&t()}))}))}}}},543:function(e,t,r){"use strict";r.r(t);r(535),r(7),r(74),r(73),r(315);var o={replace:!0,data:function(){return{tipShow:!0,msTime:{show:!1,day:"",hour:"",minutes:"",seconds:""},star:"",end:"",current:""}},watch:{currentTime:function(e,t){this.gogogo()}},props:{tipText:{type:String,default:"距离开始"},tipTextEnd:{type:String,default:"距离结束"},id:{type:String,default:"1"},currentTime:{type:Number},startTime:{type:Number},endTime:{type:Number},endText:{type:String,default:"已结束"},dayTxt:{type:String,default:":"},hourTxt:{type:String,default:":"},minutesTxt:{type:String,default:":"},secondsTxt:{type:String,default:":"},secondsFixed:{type:Boolean,default:!1}},mounted:function(){console.log(this),this.gogogo()},methods:{gogogo:function(){var e=this;10==this.startTime.toString().length?this.star=1e3*this.startTime:this.star=this.startTime,10==this.endTime.toString().length?this.end=1e3*this.endTime:this.end=this.endTime,this.currentTime?10==this.currentTime.toString().length?this.current=1e3*this.currentTime:this.current=this.currentTime:this.current=(new Date).getTime(),this.end<this.current?(this.msTime.show=!1,this.end_message()):this.current<this.star?(this.$set(this,"tipShow",!0),setTimeout((function(){e.runTime(e.star,e.current,e.start_message)}),1)):(this.end>this.current&&this.star<this.current||this.star==this.current)&&(this.$set(this,"tipShow",!1),this.msTime.show=!0,this.$emit("start_callback",this.msTime.show),setTimeout((function(){e.runTime(e.end,e.star,e.end_message,!0)}),1))},runTime:function(e,t,r,o){var n=this,_=this.msTime,l=e-t;if(l>0){this.msTime.show=!0,_.day=Math.floor(l/864e5),l-=864e5*_.day,_.hour=Math.floor(l/36e5),l-=36e5*_.hour,_.minutes=Math.floor(l/6e4),l-=6e4*_.minutes,_.seconds=Math.floor(l/1e3).toFixed(0),l-=1e3*_.seconds,_.hour<10&&(_.hour="0"+_.hour),_.minutes<10&&(_.minutes="0"+_.minutes),_.seconds<10&&(_.seconds="0"+_.seconds);var d=Date.now(),c=Date.now();setTimeout((function(){o?n.runTime(n.end,t+=1e3,r,!0):n.runTime(n.star,t+=1e3,r)}),1e3-(c-d))}else r()},start_message:function(){var e=this;this.$set(this,"tipShow",!1),this.$emit("start_callback",this.msTime.show),setTimeout((function(){e.runTime(e.end,e.star,e.end_message,!0)}),1)},end_message:function(){this.msTime.show=!1,this.currentTime<=0||this.$emit("end_callback",this.msTime.show)}}},n=r(6),component=Object(n.a)(o,(function(){var e=this,t=e._self._c;return t("div",[e.msTime.show?t("p",[e.msTime.day>0?t("span",[t("span",[e._v(e._s(e.msTime.day))]),t("i",[e._v(e._s(e.dayTxt))])]):e._e(),e._v(" "),t("span",[e._v(e._s(e.msTime.hour))]),t("i",[e._v(e._s(e.hourTxt))]),e._v(" "),t("span",[e._v(e._s(e.msTime.minutes))]),t("i",[e._v(e._s(e.minutesTxt))]),e._v(" "),t("span",[e._v(e._s(e.msTime.seconds))]),t("i",[e._v(e._s(e.secondsTxt))])]):e._e()])}),[],!1,null,null,null);t.default=component.exports},570:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAC8UlEQVRIibWWe2iOURzHP3tbmPnjyeMPJ9RcMjRFLrlluU0WVij/iFxyjfLPCv+MjD/4Q9swKUmjKcXIrSmSRJYsuY6i2Ik666QhzOjo99Tj9Tx7n5f51tvvfc57nt+n857fLedA9RESaDQwBygBxgA+kAIs8Ay4DDRao+9kcpWb4fdSYBswLW39C9AB9AUmy2eX56vHQKU1+lScw1TMugdcAi4KrAUoB2YC+UAvIE/sRGAT0ASMAk56vmryfDUsKbAIeALMA14Di4DhwD7gOvApfFJr9D1r9CFr9ASgWMDjgOeerxZmAhYCD4H+QA1QAJyN+3vSZY2+KeDtQA7Q4PlqfhywD3BLvu8BNnfhe6X83iMGvBdYK48XPF+NiAKeB/oBLmx3ZDjMMaBK7jDutEflbpFY+A04FZgBvAfWZ4A5tYrt7GqTu1vgLjDE89WqMHC/2BUJYE4/Eu5zWiK2xvNVjgMOBiZJRF7JwlEiWaPfyHW5NCp1wKXyYlV3w0Kqla/LHXCuPNzOwkGO2PaE+1vEzk5JbXS6nwUwuMO8hPtfAd9cKcyVMubq4tcsgD3Ftnm+6khLrxPW6A3hzdboDs9XL4CRQfHuMrwj1OxeBlRELkYWAznUr27RLlUmlQW4JGZ/pA+XDsDQAPgUGC89rzkhsDNwbI1OX4/SIKA38DEVyr0pCWF/o6BV3XDAennY+B+By8TWOeAjyZOiUIp0mzxf5Ut3cWoIwrlcbH13A12aiK2wRn8OgOfkpIUJWlNieb4qk4mhzRq9k7SELRG7G1jXDbBiOYhTWbAeBraGoLX/EkSerxa7iJTHLdboYJL4Y6ZpBIIZ5CBwTYIpKWig56s64IwsbbVGV4f3RM2lbhwYCxwHZslQ5Ryclnt+Gaq7Kc9XBTIeur9tjay/BVZbo6+mO48bhB9IirgiXCldO+jcrpq4Zu3sgIhaWuHiwBr9Pcpxpsn7sAxVLtIWANNldHRTQqB30ksb3Ehpjf4Q6w34CexCxYf8/YyRAAAAAElFTkSuQmCC"},611:function(e,t,r){},704:function(e,t,r){"use strict";r(611)},783:function(e,t,r){"use strict";r.r(t);r(315),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var o=r(10),n=(r(73),r(204)),_=r(541),l=r(12),d=r(538);function c(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var v={name:"order_detail_virtual",components:{CountDown:r.n(d).a},mixins:[_.a],data:function(){return{orderId:0,orderDetail:null,loading:!0,yes:!0}},created:function(){this.orderId=this.$route.query.order_id,this.getOrderDetail()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(l.b)(["token","defaultGoodsImage"])),layout:"member",methods:{countDownS_cb:function(){},countDownE_cb:function(){},getOrderDetail:function(){var e=this;Object(n.d)({order_id:this.orderId}).then((function(t){if(t.code>=0){var r=Date.parse(new Date)/1e3;t.data.currentTime=r,t.data.startTime=r,t.data.endTime=t.data.create_time+t.data.auto_close,e.orderDetail=t.data,""!=e.orderDetail.delivery_store_info&&(e.orderDetail.delivery_store_info=JSON.parse(e.orderDetail.delivery_store_info)),e.loading=!1}else e.$message({message:"未获取到订单信息",type:"warning",duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})})).catch((function(t){e.loading=!1,e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})}))},operation:function(e){var t=this;switch(e){case"orderPay":this.orderPay(this.orderDetail);break;case"orderClose":this.orderClose(this.orderDetail.order_id,(function(){t.getOrderDetail()}));break;case"memberOrderEvaluation":this.$util.pushToTab({path:"/order/evaluate",query:{order_id:this.orderDetail.order_id}});break;case"orderOfflinePay":this.$router.push({path:"/pay",query:{code:this.orderDetail.offline_pay_info.out_trade_no}})}},imageError:function(e){this.orderDetail.order_goods[e].sku_image=this.defaultGoodsImage}},filters:{abs:function(e){return Math.abs(parseFloat(e)).toFixed(2)}}},m=v,f=(r(704),r(6)),component=Object(f.a)(m,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card order-detail"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/member/order_list"}}},[e._v("订单列表")]),e._v(" "),t("el-breadcrumb-item",[e._v("订单详情")])],1)],1),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e.orderDetail?[t("div",{staticClass:"order-status"},[t("h4",{staticStyle:{position:"relative"}},[e._v("\n              订单状态：\n              "),t("span",{staticClass:"ns-text-color"},[e._v(e._s(e.orderDetail.order_status_name))]),e._v(" "),0==e.orderDetail.order_status?t("div",{staticClass:"edit-time"},[t("img",{staticStyle:{width:"15px",height:"15px","margin-right":"6px"},attrs:{src:r(570)}}),e._v("距离订单自动关闭，剩余\n                "),t("count-down",{staticClass:"count-down",staticStyle:{color:"#f00","margin-left":"10px"},attrs:{currentTime:e.orderDetail.currentTime,startTime:e.orderDetail.startTime,endTime:e.orderDetail.endTime,dayTxt:":",hourTxt:":",minutesTxt:":",secondsTxt:""},on:{start_callback:function(t){return e.countDownS_cb()},end_callback:function(t){return e.countDownE_cb()}}})],1):e._e()]),e._v(" "),0==e.orderDetail.order_status?t("div",{staticClass:"go-pay"},[t("p",[e._v("\n                需付款：\n                "),t("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])]):e._e(),e._v(" "),e.orderDetail.action.length>0?t("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e(),e._v(" "),e._l(e.orderDetail.action,(function(r,o){return t("el-button",{key:o,attrs:{type:"primary",size:"mini",plain:"orderPay"!=r.action},on:{click:function(t){return e.operation(r.action)}}},[e._v(e._s(r.title))])}))],2):0==e.orderDetail.action.length&&1==e.orderDetail.is_evaluate?t("div",{staticClass:"operation"},[1==e.orderDetail.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation")}}},[0==e.orderDetail.evaluate_status?[e._v("评价")]:1==e.orderDetail.evaluate_status?[e._v("追评")]:e._e()],2):e._e()],1):e._e()]),e._v(" "),e.orderDetail.virtual_goods?t("div",{staticClass:"verify-code-wrap"},[2==e.orderDetail.goods_class?[t("h4",[e._v("核销码")]),e._v(" "),t("div",{staticClass:"virtual-code"},[t("img",{attrs:{src:e.$img(e.orderDetail.virtualgoods)}}),e._v(" "),t("div",{staticClass:"tips"},[e._v("请将二维码出示给核销员")]),e._v(" "),t("div",[e._v("核销码："+e._s(e.orderDetail.virtual_code))])]),e._v(" "),t("h4",[e._v("核销信息")]),e._v(" "),t("ul",[t("li",[e._v("核销次数：剩余"+e._s(e.orderDetail.virtual_goods.total_verify_num-e.orderDetail.virtual_goods.verify_num)+"次/共"+e._s(e.orderDetail.virtual_goods.total_verify_num)+"次")]),e._v(" "),t("li",[e._v("有效期：\n                  "),e.orderDetail.virtual_goods.expire_time>0?t("span",[e._v("\t"+e._s(e.$util.timeStampTurnTime(e.orderDetail.virtual_goods.expire_time)))]):t("span",[e._v("永久有效")])])]),e._v(" "),e.orderDetail.virtual_goods.verify_record.length?[t("h4",[e._v("核销记录")]),e._v(" "),e._l(e.orderDetail.virtual_goods.verify_record,(function(r,o){return t("ul",{key:o},[t("li",[e._v("核销人："+e._s(r.verifier_name))]),e._v(" "),t("li",[e._v("核销时间："+e._s(e.$util.timeStampTurnTime(r.verify_time)))])])}))]:e._e()]:e._e(),e._v(" "),3==e.orderDetail.goods_class?[t("h4",[e._v("卡密信息")]),e._v(" "),e._l(e.orderDetail.virtual_goods,(function(r,o){return t("ul",{key:o},[t("li",[t("span",[e._v("卡号："+e._s(r.card_info.cardno))])]),e._v(" "),t("li",[t("span",[e._v("密码："+e._s(r.card_info.password))])])])}))]:e._e()],2):e._e(),e._v(" "),t("div",{staticClass:"order-info"},[t("h4",[e._v("订单信息")]),e._v(" "),t("ul",[t("li",[e._v("订单类型："+e._s(e.orderDetail.order_type_name))]),e._v(" "),t("li",[e._v("订单编号："+e._s(e.orderDetail.order_no))]),e._v(" "),t("li",[e._v("订单交易号："+e._s(e.orderDetail.out_trade_no))]),e._v(" "),t("li",[e._v("创建时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.create_time)))]),e._v(" "),e.orderDetail.close_time>0?t("li",[e._v("关闭时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.close_time)))]):e._e(),e._v(" "),e.orderDetail.pay_status>0?[t("li",[e._v("支付方式："+e._s(e.orderDetail.pay_type_name))]),e._v(" "),t("li",[e._v("支付时间："+e._s(e.$util.timeStampTurnTime(e.orderDetail.pay_time)))])]:e._e(),e._v(" "),""!=e.orderDetail.promotion_type_name?t("li",[e._v("店铺活动："+e._s(e.orderDetail.promotion_type_name))]):e._e(),e._v(" "),""!=e.orderDetail.buyer_message?t("li",[e._v("买家留言："+e._s(e.orderDetail.buyer_message))]):e._e()],2)]),e._v(" "),"offlinepay"==e.orderDetail.pay_type&&e.orderDetail.offline_pay_info?t("div",{staticClass:"order-info"},[t("h4",[e._v("线下支付")]),e._v(" "),t("ul",["WAIT_AUDIT"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                支付状态：审核中\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                支付状态：审核被拒\n              ")]):e._e(),e._v(" "),"AUDIT_REFUSE"==e.orderDetail.offline_pay_info.status_info.const?t("li",[e._v("\n                审核备注："+e._s(e.orderDetail.offline_pay_info.audit_remark)+"\n              ")]):e._e()])]):e._e(),e._v(" "),1==e.orderDetail.is_invoice?t("div",{staticClass:"take-delivery-info"},[t("h4",[e._v("发票信息")]),e._v(" "),t("ul",[t("li",[e._v("发票类型："+e._s(1==e.orderDetail.invoice_type?"纸质发票":"电子发票"))]),e._v(" "),t("li",[e._v("发票抬头类型："+e._s(1==e.orderDetail.invoice_title_type?"个人":"企业"))]),e._v(" "),t("li",[e._v("发票抬头："+e._s(e.orderDetail.invoice_title))]),e._v(" "),t("li",[e._v("发票内容："+e._s(e.orderDetail.invoice_content))]),e._v(" "),1==e.orderDetail.invoice_type?t("li",[e._v("发票邮寄地址地址："+e._s(e.orderDetail.invoice_full_address))]):t("li",[e._v("发票接收邮箱："+e._s(e.orderDetail.invoice_email))])])]):e._e(),e._v(" "),t("nav",[t("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[e._v("商品信息")]),e._v(" "),t("li",[e._v("单价")]),e._v(" "),t("li",[e._v("数量")]),e._v(" "),t("li",[e._v("小计")]),e._v(" "),e.orderDetail.is_enable_refund?t("li",[e._v("操作")]):e._e()]),e._v(" "),t("div",{staticClass:"list"},e._l(e.orderDetail.order_goods,(function(r,o){return t("ul",{key:o,staticClass:"item"},[t("li",{class:{"no-operation":!e.orderDetail.is_enable_refund}},[t("div",{staticClass:"img-wrap",on:{click:function(t){return e.$util.pushToTab("/sku/"+r.sku_id)}}},[t("img",{attrs:{src:e.$img(r.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(o)}}})]),e._v(" "),t("div",{staticClass:"info-wrap"},[t("h5",{on:{click:function(t){return e.$util.pushToTab("/sku/"+r.sku_id)}}},[e._v(e._s(r.sku_name))])])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s(r.price))])]),e._v(" "),t("li",[t("span",[e._v(e._s(r.num))])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s((r.price*r.num).toFixed(2)))])]),e._v(" "),e.orderDetail.is_enable_refund?t("li",[0==r.refund_status||-1==r.refund_status?t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.$router.push({path:"/order/refund",query:{order_goods_id:r.order_goods_id}})}}},[e._v("退款")]):t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.$router.push({path:"/order/refund_detail",query:{order_goods_id:r.order_goods_id}})}}},[e._v("查看退款")])],1):e._e()])})),0),e._v(" "),t("ul",{staticClass:"total"},[t("li",[t("label",[e._v("商品金额：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.goods_money))])]),e._v(" "),e.orderDetail.member_card_money>0?t("li",[t("label",[e._v("会员卡：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.member_card_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_money>0?t("li",[t("label",[e._v("税费：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.invoice_money))])]):e._e(),e._v(" "),e.orderDetail.invoice_delivery_money>0?t("li",[t("label",[e._v("发票邮寄费：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.invoice_delivery_money))])]):e._e(),e._v(" "),0!=e.orderDetail.adjust_money?t("li",[t("label",[e._v("订单调整：")]),e._v(" "),t("span",[e.orderDetail.adjust_money<0?[e._v("-")]:[e._v("+")],e._v("\n\t\t\t\t\t\t\t\t￥"+e._s(e._f("abs")(e.orderDetail.adjust_money))+"\n\t\t\t\t\t\t\t")],2)]):e._e(),e._v(" "),e.orderDetail.promotion_money>0?t("li",[t("label",[e._v("优惠：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.promotion_money))])]):e._e(),e._v(" "),e.orderDetail.coupon_money>0?t("li",[t("label",[e._v("优惠券：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.coupon_money))])]):e._e(),e._v(" "),e.orderDetail.point_money>0?t("li",[t("label",[e._v("积分抵扣：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.point_money))])]):e._e(),e._v(" "),e.orderDetail.balance_money>0?t("li",[t("label",[e._v("使用余额：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.balance_money))])]):e._e(),e._v(" "),t("li",{staticClass:"pay-money"},[t("label",[e._v("实付款：")]),e._v(" "),t("span",[e._v("￥"+e._s(e.orderDetail.pay_money))])])])]:e._e()],2)])],1)}),[],!1,null,"59a32e08",null);t.default=component.exports}}]);