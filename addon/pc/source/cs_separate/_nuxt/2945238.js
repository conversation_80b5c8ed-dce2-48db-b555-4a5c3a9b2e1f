(window.webpackJsonp=window.webpackJsonp||[]).push([[15],{554:function(t,e,n){"use strict";n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return o}));var r=n(1);function c(t){return Object(r.a)({url:"/api/notice/page",data:t})}function o(t){return Object(r.a)({url:"/api/notice/info",data:t})}},644:function(t,e,n){},737:function(t,e,n){"use strict";n(644)},808:function(t,e,n){"use strict";n.r(e);var r=n(554),c={name:"notice",components:{},data:function(){return{queryInfo:{page:1,page_size:10,receiving_type:"web"},noticeList:[],total:0,loading:!0}},head:function(){return{title:"公告列表-"+this.$store.state.site.siteInfo.site_name}},created:function(){this.getList()},methods:{toDetail:function(t){this.$router.push({path:"/cms/notice/detail",query:{id:t}})},getList:function(){var t=this;Object(r.b)(this.queryInfo).then((function(e){0==e.code&&e.data&&(t.noticeList=e.data.list,t.total=e.data.count),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},handlePageSizeChange:function(t){this.queryInfo.page_size=t,this.getList()},handleCurrentPageChange:function(t){this.queryInfo.page=t,this.getList()}}},o=(n(737),n(6)),component=Object(o.a)(c,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"notice-wrap"},[e("el-breadcrumb",{staticClass:"path",attrs:{separator:"/"}},[e("el-breadcrumb-item",{staticClass:"path-home",attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{staticClass:"path-help"},[t._v("公告")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"notice"},[e("div",{staticClass:"menu"},[e("div",{staticClass:"title"},[t._v("最新公告")]),t._v(" "),t._l(t.noticeList,(function(n){return e("div",{key:n.id,staticClass:"item",on:{click:function(e){return t.toDetail(n.id)}}},[e("div",{staticClass:"item-name"},[t._v(t._s(n.title))])])}))],2),t._v(" "),e("div",{staticClass:"list-wrap"},[e("div",{staticClass:"notice-title"},[t._v("商城公告")]),t._v(" "),t._l(t.noticeList,(function(n){return e("div",{key:n.id,staticClass:"list",on:{click:function(e){return t.toDetail(n.id)}}},[e("div",{staticClass:"item"},[t._v(t._s(n.title))]),t._v(" "),e("div",{staticClass:"info"},[e("div",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(n.create_time)))])])])}))],2)]),t._v(" "),e("div",{staticClass:"pager"},[e("el-pagination",{attrs:{background:"","pager-count":5,total:t.total,"prev-text":"上一页","next-text":"下一页","current-page":t.queryInfo.page,"page-size":t.queryInfo.page_size,"hide-on-single-page":""},on:{"update:currentPage":function(e){return t.$set(t.queryInfo,"page",e)},"update:current-page":function(e){return t.$set(t.queryInfo,"page",e)},"update:pageSize":function(e){return t.$set(t.queryInfo,"page_size",e)},"update:page-size":function(e){return t.$set(t.queryInfo,"page_size",e)},"size-change":t.handlePageSizeChange,"current-change":t.handleCurrentPageChange}})],1)],1)}),[],!1,null,"000c5148",null);e.default=component.exports}}]);