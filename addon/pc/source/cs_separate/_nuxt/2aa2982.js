(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{540:function(t,e,o){"use strict";o.d(e,"h",(function(){return n})),o.d(e,"i",(function(){return c})),o.d(e,"g",(function(){return l})),o.d(e,"f",(function(){return d})),o.d(e,"e",(function(){return m})),o.d(e,"a",(function(){return _})),o.d(e,"d",(function(){return v})),o.d(e,"b",(function(){return f})),o.d(e,"c",(function(){return h})),o.d(e,"j",(function(){return y}));var r=o(1);function n(t){return Object(r.a)({url:"/api/orderrefund/refundData",data:t,forceLogin:!0})}function c(t){return Object(r.a)({url:"/api/orderrefund/refundDataBatch",data:t,forceLogin:!0})}function l(t){return Object(r.a)({url:"/api/orderrefund/refund",data:t,forceLogin:!0})}function d(t){return Object(r.a)({url:"/api/orderrefund/detail",data:t,forceLogin:!0})}function m(t){return Object(r.a)({url:"/api/orderrefund/delivery",data:t,forceLogin:!0})}function _(t){return Object(r.a)({url:"/api/orderrefund/cancel",data:t,forceLogin:!0})}function v(t){return Object(r.a)({url:"/api/ordercomplain/detail",data:t,forceLogin:!0})}function f(t){return Object(r.a)({url:"/api/ordercomplain/complain",data:t,forceLogin:!0})}function h(t){return Object(r.a)({url:"/api/ordercomplain/cancel",data:t,forceLogin:!0})}function y(t){return Object(r.a)({url:"/api/orderrefund/lists",data:t,forceLogin:!0})}},621:function(t,e,o){},713:function(t,e,o){"use strict";o(621)},792:function(t,e,o){"use strict";o.r(e);o(24),o(25),o(23),o(7),o(29),o(18),o(30);var r=o(10),n=(o(73),o(12)),c=o(540);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var d={name:"refund",components:{},data:function(){return{orderGoodsId:0,complainData:{order_goods_info:{sku_image:"",sku_name:""}},detail:{sku_image:""},complainReason:"",complainRemark:"",action:"",actionOpen:!1,loading:!1,yes:!0}},created:function(){this.loading=!0,this.$route.query.order_goods_id&&(this.orderGoodsId=this.$route.query.order_goods_id),this.getComplainData()},mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(n.b)(["defaultGoodsImage"])),layout:"member",methods:{selectRefundType:function(t){this.refund_type=t},getComplainData:function(){var t=this;this.loading=!0,Object(c.d)({order_goods_id:this.orderGoodsId}).then((function(e){var code=e.code;e.message,e.data;code>=0?(t.complainData=e.data,t.detail=t.complainData.complain_info,t.loading=!1):t.$message({message:"未获取到该订单项退款信息",type:"warning",duration:2e3,onClose:function(){t.$router.push({path:"/member/order_list"})}})})).catch((function(e){t.loading=!1,t.$message.error({message:e.message,duration:2e3,onClose:function(){t.$router.push({path:"/member/order_list"})}})}))},submit:function(){var t=this;if(this.verify()){if(this.isSub)return;this.isSub=!0;var e={order_goods_id:this.orderGoodsId,complain_reason:this.complainReason,complain_remark:this.complainRemark};Object(c.b)(e).then((function(e){var code=e.code,o=e.message;e.data;code>=0?(t.$message({message:o,type:"success"}),t.getComplainData(),t.$forceUpdate(),t.action=""):(t.isSub=!1,t.$message({message:o,type:"warning"}))})).catch((function(e){t.$message.error({message:e.message,duration:2e3,onClose:function(){t.$router.push({path:"/member/order_list"})}})}))}},verify:function(){return""!=this.complainReason||(this.$message({message:"请选择退款原因",type:"warning"}),!1)},refundAction:function(t){switch(t){case"complainCancel":this.cancelRefund(this.detail.order_goods_id);break;case"complainApply":this.action="apply"}},cancelRefund:function(t){var e=this;this.$confirm("撤销之后本次申请将会关闭,如后续仍有问题可再次发起申请","提示",{confirmButtonText:"确认撤销",cancelButtonText:"暂不撤销",type:"warning"}).then((function(){e.isSub||(e.isSub=!0,Object(c.c)({order_goods_id:t}).then((function(t){var code=t.code,o=t.message;t.data;code>=0?e.$message({message:"撤销成功",type:"success",duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}}):e.$message({message:o,type:"warning"})})).catch((function(t){e.$message.error({message:t.message,duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})})))}))}}},m=d,_=(o(713),o(6)),component=Object(_.a)(m,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[t.complainData.complain_info&&"apply"!=t.action?e("div",[e("div",{staticClass:"item-block"},[e("div",{staticClass:"block-text"},[t._v(t._s(t.detail.complain_status_name))])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"action-box"},[e("span",{staticClass:"media-left"},[t._v("协商记录")]),t._v(" "),e("div",{staticClass:"media-right"},[e("div",{staticClass:"el-button--text",on:{click:function(e){t.actionOpen?t.actionOpen=!1:t.actionOpen=!0}}},[t._v("\n              协商记录\n              "),e("i",{staticClass:"el-icon-arrow-down",class:t.actionOpen?"rotate":""})])]),t._v(" "),e("div",{staticClass:"clear"})]),t._v(" "),t.actionOpen?e("div",[e("el-timeline",t._l(t.detail.refund_log_list,(function(o,r){return e("el-timeline-item",{key:r,class:{buyer:1==o.action_way,seller:2==o.action_way,platform:3==o.action_way},attrs:{timestamp:t.$util.timeStampTurnTime(o.action_time),placement:"top"}},[e("div",[1==o.action_way?e("h4",[t._v("买家")]):2==o.action_way?e("h4",[t._v("卖家")]):3==o.action_way?e("h4",[t._v("平台")]):t._e(),t._v(" "),e("p",[t._v(t._s(o.action))])])])})),1)],1):t._e()]),t._v(" "),t._m(0),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[e("div",{staticClass:"goods-info"},[e("div",{staticClass:"goods-info-left"},[e("router-link",{attrs:{to:{path:"/sku/"+t.detail.sku_id},target:"_blank"}},[e("img",{staticClass:"goods-img",attrs:{src:t.$img(t.detail.sku_image,{size:"mid"})},on:{error:function(e){t.detail.sku_image=t.defaultGoodsImage}}})])],1),t._v(" "),e("div",{staticClass:"goods-info-right"},[e("router-link",{attrs:{to:{path:"/sku/"+t.detail.sku_id},target:"_blank"}},[e("div",{staticClass:"goods-name"},[t._v(t._s(t.detail.sku_name))])])],1)])]),t._v(" "),e("td",{staticClass:"goods-num",attrs:{width:"12.5%"}},[t._v(t._s(t.detail.num))]),t._v(" "),e("td",{staticClass:"goods-money",attrs:{width:"12.5%"}},[t._v("￥"+t._s(t.detail.complain_apply_money))])])])])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"order-statistics"},[e("table",[e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款原因：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.complainReason))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款金额：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v("￥"+t._s(t.detail.complain_apply_money))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款编号：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.detail.complain_no))])]),t._v(" "),e("tr",[e("td",{attrs:{align:"right"}},[t._v("申请时间：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.complain_apply_time)))])]),t._v(" "),t.detail.complain_time?e("tr",[e("td",{attrs:{align:"right"}},[t._v("退款时间：")]),t._v(" "),e("td",{attrs:{align:"left"}},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.complain_time)))])]):t._e()])]),t._v(" "),e("div",{staticClass:"clear"})]),t._v(" "),t.detail.complain_action.length?e("div",{staticClass:"item-block"},[t._l(t.detail.complain_action,(function(o,r){return e("div",{key:r,staticClass:"order-submit"},[e("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:function(e){return t.refundAction(o.event)}}},[t._v(t._s(o.title))])],1)})),t._v(" "),e("div",{staticClass:"clear"})],2):t._e()]):e("div",[e("el-card",{staticClass:"box-card order-list"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("el-breadcrumb",{attrs:{separator:"/"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/member/activist"}}},[t._v("退款/售后")]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/order/refund_detail?order_goods_id="+t.orderGoodsId}}},[t._v("退款详情\n            ")]),t._v(" "),e("el-breadcrumb-item",[t._v("平台维权")])],1)],1),t._v(" "),e("div",[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[t._v("商品")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("数量")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("金额")])])])])]),t._v(" "),e("div",[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[e("div",{staticClass:"goods-info"},[e("div",{staticClass:"goods-info-left"},[e("router-link",{attrs:{to:{path:"/sku/"+t.complainData.order_goods_info.sku_id},target:"_blank"}},[e("img",{staticClass:"goods-img",attrs:{src:t.$img(t.complainData.order_goods_info.sku_image,{size:"mid"})},on:{error:function(e){t.complainData.order_goods_info.sku_image=t.defaultGoodsImage}}})])],1),t._v(" "),e("div",{staticClass:"goods-info-right"},[e("router-link",{attrs:{to:{path:"/sku/"+t.complainData.order_goods_info.sku_id},target:"_blank"}},[e("div",{staticClass:"goods-name"},[t._v(t._s(t.complainData.order_goods_info.sku_name))])])],1)])]),t._v(" "),e("td",{staticClass:"goods-num",attrs:{width:"12.5%"}},[t._v(t._s(t.complainData.order_goods_info.num))]),t._v(" "),e("td",{staticClass:"goods-money",attrs:{width:"12.5%"}},[t._v("￥"+t._s(t.complainData.order_goods_info.goods_money))])])])])])]),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"block-text"}),t._v(" "),e("el-form",{ref:"form",staticClass:"refund-form",attrs:{"label-width":"80px"}},[e("el-form-item",{attrs:{label:"退款金额"}},[e("el-input",{attrs:{disabled:"",value:t.complainData.refund_money}})],1),t._v(" "),e("el-form-item",{attrs:{label:"退款原因"}},[e("el-select",{attrs:{placeholder:"请选择"},model:{value:t.complainReason,callback:function(e){t.complainReason=e},expression:"complainReason"}},t._l(t.complainData.refund_reason_type,(function(t,o){return e("el-option",{key:o,attrs:{label:t,value:t}})})),1)],1),t._v(" "),e("el-form-item",{attrs:{label:"退款说明"}},[e("el-input",{attrs:{maxlength:"140","show-word-limit":"",resize:"none",rows:"5",placeholder:"请输入退款说明（选填）",type:"textarea"},model:{value:t.complainRemark,callback:function(e){t.complainRemark=e},expression:"complainRemark"}})],1)],1)],1),t._v(" "),e("div",{staticClass:"item-block"},[e("div",{staticClass:"order-submit"},[e("el-button",{staticClass:"el-button--primary",attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交")])],1),t._v(" "),e("div",{staticClass:"clear"})])],1)])])}),[function(){var t=this,e=t._self._c;return e("div",{staticClass:"item-block"},[e("div",{staticClass:"goods-list"},[e("table",[e("tr",[e("td",{attrs:{width:"62.5%"}},[t._v("商品")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("数量")]),t._v(" "),e("td",{attrs:{width:"12.5%"}},[t._v("退款金额")])])])])])}],!1,null,"2255d742",null);e.default=component.exports}}]);