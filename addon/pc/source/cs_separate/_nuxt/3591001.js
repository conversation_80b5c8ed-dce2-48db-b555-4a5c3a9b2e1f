(window.webpackJsonp=window.webpackJsonp||[]).push([[21,22,23,24,25],{535:function(t,e,o){"use strict";var n=o(3),r=o(35),l=o(14),c=o(8),path=o(319),d=o(5),f=o(113),h=o(16),m=o(205),v=o(63),_=o(112),y=o(318),w=o(4),T=o(93).f,O=o(57).f,k=o(26).f,S=o(320),x=o(316).trim,L="Number",C=c[L],j=path[L],P=C.prototype,I=c.TypeError,E=d("".slice),$=d("".charCodeAt),N=function(t){var e=y(t,"number");return"bigint"==typeof e?e:A(e)},A=function(t){var e,o,n,r,l,c,d,code,f=y(t,"number");if(_(f))throw I("Cannot convert a Symbol value to a number");if("string"==typeof f&&f.length>2)if(f=x(f),43===(e=$(f,0))||45===e){if(88===(o=$(f,2))||120===o)return NaN}else if(48===e){switch($(f,1)){case 66:case 98:n=2,r=49;break;case 79:case 111:n=8,r=55;break;default:return+f}for(c=(l=E(f,2)).length,d=0;d<c;d++)if((code=$(l,d))<48||code>r)return NaN;return parseInt(l,n)}return+f},D=f(L,!C(" 0o1")||!C("0b1")||C("+0x1")),F=function(t){return v(P,t)&&w((function(){S(t)}))},M=function(t){var e=arguments.length<1?0:C(N(t));return F(this)?m(Object(e),this,M):e};M.prototype=P,D&&!r&&(P.constructor=M),n({global:!0,constructor:!0,wrap:!0,forced:D},{Number:M});var R=function(t,source){for(var e,o=l?T(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;o.length>n;n++)h(source,e=o[n])&&!h(t,e)&&k(t,e,O(source,e))};r&&j&&R(path[L],j),(D||r)&&R(path[L],C)},538:function(t,e,o){t.exports=o(543)},542:function(t,e,o){var n=o(3),r=o(548);n({target:"Number",stat:!0,forced:Number.parseInt!=r},{parseInt:r})},543:function(t,e,o){"use strict";o.r(e);o(535),o(7),o(74),o(73),o(315);var n={replace:!0,data:function(){return{tipShow:!0,msTime:{show:!1,day:"",hour:"",minutes:"",seconds:""},star:"",end:"",current:""}},watch:{currentTime:function(t,e){this.gogogo()}},props:{tipText:{type:String,default:"距离开始"},tipTextEnd:{type:String,default:"距离结束"},id:{type:String,default:"1"},currentTime:{type:Number},startTime:{type:Number},endTime:{type:Number},endText:{type:String,default:"已结束"},dayTxt:{type:String,default:":"},hourTxt:{type:String,default:":"},minutesTxt:{type:String,default:":"},secondsTxt:{type:String,default:":"},secondsFixed:{type:Boolean,default:!1}},mounted:function(){console.log(this),this.gogogo()},methods:{gogogo:function(){var t=this;10==this.startTime.toString().length?this.star=1e3*this.startTime:this.star=this.startTime,10==this.endTime.toString().length?this.end=1e3*this.endTime:this.end=this.endTime,this.currentTime?10==this.currentTime.toString().length?this.current=1e3*this.currentTime:this.current=this.currentTime:this.current=(new Date).getTime(),this.end<this.current?(this.msTime.show=!1,this.end_message()):this.current<this.star?(this.$set(this,"tipShow",!0),setTimeout((function(){t.runTime(t.star,t.current,t.start_message)}),1)):(this.end>this.current&&this.star<this.current||this.star==this.current)&&(this.$set(this,"tipShow",!1),this.msTime.show=!0,this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1))},runTime:function(t,e,o,n){var r=this,l=this.msTime,c=t-e;if(c>0){this.msTime.show=!0,l.day=Math.floor(c/864e5),c-=864e5*l.day,l.hour=Math.floor(c/36e5),c-=36e5*l.hour,l.minutes=Math.floor(c/6e4),c-=6e4*l.minutes,l.seconds=Math.floor(c/1e3).toFixed(0),c-=1e3*l.seconds,l.hour<10&&(l.hour="0"+l.hour),l.minutes<10&&(l.minutes="0"+l.minutes),l.seconds<10&&(l.seconds="0"+l.seconds);var d=Date.now(),f=Date.now();setTimeout((function(){n?r.runTime(r.end,e+=1e3,o,!0):r.runTime(r.star,e+=1e3,o)}),1e3-(f-d))}else o()},start_message:function(){var t=this;this.$set(this,"tipShow",!1),this.$emit("start_callback",this.msTime.show),setTimeout((function(){t.runTime(t.end,t.star,t.end_message,!0)}),1)},end_message:function(){this.msTime.show=!1,this.currentTime<=0||this.$emit("end_callback",this.msTime.show)}}},r=o(6),component=Object(r.a)(n,(function(){var t=this,e=t._self._c;return e("div",[t.msTime.show?e("p",[t.msTime.day>0?e("span",[e("span",[t._v(t._s(t.msTime.day))]),e("i",[t._v(t._s(t.dayTxt))])]):t._e(),t._v(" "),e("span",[t._v(t._s(t.msTime.hour))]),e("i",[t._v(t._s(t.hourTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.minutes))]),e("i",[t._v(t._s(t.minutesTxt))]),t._v(" "),e("span",[t._v(t._s(t.msTime.seconds))]),e("i",[t._v(t._s(t.secondsTxt))])]):t._e()])}),[],!1,null,null,null);e.default=component.exports},547:function(t,e,o){"use strict";o.d(e,"c",(function(){return r})),o.d(e,"d",(function(){return l})),o.d(e,"h",(function(){return c})),o.d(e,"g",(function(){return d})),o.d(e,"f",(function(){return f})),o.d(e,"a",(function(){return h})),o.d(e,"b",(function(){return m})),o.d(e,"e",(function(){return v}));var n=o(1);function r(t){return Object(n.a)({url:"/seckill/api/seckillgoods/page",data:t})}function l(t){return Object(n.a)({url:"/seckill/api/seckillgoods/detail",data:t})}function c(t){return Object(n.a)({url:"/seckill/api/seckill/lists",data:t})}function d(t){return Object(n.a)({url:"/seckill/api/seckillgoods/info",data:t})}function f(t){return Object(n.a)({url:"/seckill/api/ordercreate/payment",data:t,forceLogin:!0})}function h(t){return Object(n.a)({url:"/seckill/api/ordercreate/calculate",data:t,forceLogin:!0})}function m(){return Object(n.a)({url:"/api/goodsevaluate/config",data:{},forceLogin:!0})}function v(t){return Object(n.a)({url:"/seckill/api/ordercreate/create",data:t,forceLogin:!0})}},548:function(t,e,o){var n=o(8),r=o(4),l=o(5),c=o(19),d=o(316).trim,f=o(209),h=n.parseInt,m=n.Symbol,v=m&&m.iterator,_=/^[+-]?0x/i,y=l(_.exec),w=8!==h(f+"08")||22!==h(f+"0x16")||v&&!r((function(){h(Object(v))}));t.exports=w?function(t,e){var o=d(c(t));return h(o,e>>>0||(y(_,o)?16:10))}:h},554:function(t,e,o){"use strict";o.d(e,"b",(function(){return r})),o.d(e,"a",(function(){return l}));var n=o(1);function r(t){return Object(n.a)({url:"/api/notice/page",data:t})}function l(t){return Object(n.a)({url:"/api/notice/info",data:t})}},557:function(t,e,o){},558:function(t,e,o){},559:function(t,e,o){},560:function(t,e,o){},573:function(t,e,o){"use strict";o(557)},574:function(t,e,o){"use strict";o(558)},575:function(t,e,o){"use strict";o(559)},576:function(t,e,o){"use strict";o(560)},654:function(t,e,o){},656:function(t,e,o){"use strict";o.r(e);o(321),o(542),o(535),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=o(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var c={name:"floor-style-1",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage}}},d=c,f=(o(573),o(6)),component=Object(f.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-1"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))])]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[t.data.value.leftImg.value.url?e("div",{staticClass:"left-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}})]):t._e(),t._v(" "),e("ul",{staticClass:"goods-list"},t._l(t.data.value.goodsList.value.list,(function(o,n){return e("li",{key:n,attrs:{title:o.goods_name},on:{click:function(e){return t.goSku(o.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("h3",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"desc"},[t._v(t._s(o.introduction))]),t._v(" "),e("p",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v(t._s(o.discount_price)+"元")]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(o.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v(t._s(o.market_price)+"元")])])])})),0)]),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"e9a00f52",null);e.default=component.exports},657:function(t,e,o){"use strict";o.r(e);o(321),o(542),o(535),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=o(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var c={name:"floor-style-2",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$router.push("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage}}},d=c,f=(o(574),o(6)),component=Object(f.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-2"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{textAlign:t.data.value.title.value.textAlign,color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))]),t._v(" "),t.data.value.subTitle.value.text?e("p",{style:{color:t.data.value.subTitle.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.subTitle.value.link.url)}}},[t._v(t._s(t.data.value.subTitle.value.text))]):t._e()]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[e("ul",{staticClass:"goods-list"},t._l(t.data.value.goodsList.value.list,(function(o,n){return e("li",{key:n,attrs:{title:o.goods_name},on:{click:function(e){return t.goSku(o.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:o.goods_image?t.$img(o.goods_image.split(",")[0],{size:"mid"}):t.defaultGoodsImage},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("h3",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"desc"},[t._v(t._s(o.introduction))]),t._v(" "),e("p",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v(t._s(o.discount_price)+"元")]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(o.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v(t._s(o.market_price)+"元")])])])})),0)]),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"cf1bbd00",null);e.default=component.exports},658:function(t,e,o){"use strict";o.r(e);o(321),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=o(12);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var c={name:"floor-style-3",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageErrorRight:function(t){this.data.value.rightGoodsList.value.list[t].goods_image=this.defaultGoodsImage},imageErrorBottom:function(t){this.data.value.bottomGoodsList.value.list[t].goods_image=this.defaultGoodsImage}}},d=c,f=(o(575),o(6)),component=Object(f.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-3"},[e("div",{staticClass:"item-wrap"},[e("div",{staticClass:"head-wrap"},[t.data.value.title.value.text?e("div",{staticClass:"title-name"},[e("span",{style:{backgroundColor:t.data.value.title.value.color}}),t._v(" "),e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))])]):t._e(),t._v(" "),e("div",{staticClass:"category-wrap"},t._l(t.data.value.categoryList.value.list,(function(o,n){return e("li",{key:n},[e("router-link",{attrs:{target:"_blank",to:{path:"/goods/list",query:{category_id:o.category_id,level:o.level}}}},[t._v(t._s(o.category_name))])],1)})),0)]),t._v(" "),e("div",{staticClass:"body-wrap"},[e("div",{staticClass:"left-img-wrap"},[t.data.value.leftImg.value.url?e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}}):t._e()]),t._v(" "),e("ul",{staticClass:"right-goods-wrap"},t._l(t.data.value.rightGoodsList.value.list,(function(o,n){return e("li",{key:n,on:{click:function(e){return t.goSku(o.sku_id)}}},[e("h4",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"ns-text-color"},[t._v(t._s(o.introduction))]),t._v(" "),e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageErrorRight(n)}}})])])})),0),t._v(" "),e("ul",{staticClass:"bottom-goods-wrap"},t._l(t.data.value.bottomGoodsList.value.list,(function(o,n){return e("li",{key:n,on:{click:function(e){return t.goSku(o.sku_id)}}},[e("div",{staticClass:"info-wrap"},[e("h4",[t._v(t._s(o.goods_name))]),t._v(" "),e("p",{staticClass:"ns-text-color"},[t._v(t._s(o.introduction))])]),t._v(" "),e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageErrorBottom(n)}}})])])})),0)])])])}),[],!1,null,"d0ce147e",null);e.default=component.exports},659:function(t,e,o){"use strict";o.r(e);o(321),o(542),o(535),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(10),r=(o(65),o(12));function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function c(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var d={name:"floor-style-4",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:c(c({},Object(r.b)(["defaultGoodsImage"])),{},{goodsList:function(){var t=[];try{t=this.data.value.goodsList.value.list}catch(e){t=[]}return t},itemNum:function(){var t=[0,this.goodsList.length],e=t[0],o=t[1];return e=parseInt(o/3),parseInt(o%3)>0&&(e+=1),e}}),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage},itemList:function(t){if(t-=1,!this.goodsList.length)return[];var e=3*t,o=3*t+3;return this.goodsList.slice(e,o)}}},f=(o(576),o(6)),component=Object(f.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-4"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))]),t._v(" "),e("div",{staticClass:"more",style:{color:t.data.value.more.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.more.value.link.url)}}},[e("span",[t._v(t._s(t.data.value.more.value.text))]),t._v(" "),e("i",{staticClass:"el-icon-arrow-right"})])]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[t.data.value.leftImg.value.url?e("div",{staticClass:"left-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}})]):t._e(),t._v(" "),e("el-carousel",{attrs:{trigger:"click",height:"324px","indicator-position":"none",arrow:"never"}},t._l(t.itemNum,(function(o){return e("el-carousel-item",{key:o},[e("ul",{staticClass:"goods-list"},t._l(t.itemList(o),(function(o,n){return e("li",{key:n,attrs:{title:o.goods_name},on:{click:function(e){return t.goSku(o.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("div",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v("￥"+t._s(o.discount_price))]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(o.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v("￥"+t._s(o.market_price))])]),t._v(" "),e("h3",{staticClass:"name"},[t._v(t._s(o.goods_name))]),t._v(" "),o.sale_num?e("div",{staticClass:"other-info"},[e("span",[t._v("已售"+t._s(o.sale_num)+"件")])]):t._e()])})),0)])})),1)],1),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"bdd97972",null);e.default=component.exports},746:function(t,e,o){"undefined"!=typeof self&&self,t.exports=function(t){function i(o){if(e[o])return e[o].exports;var n=e[o]={i:o,l:!1,exports:{}};return t[o].call(n.exports,n,n.exports,i),n.l=!0,n.exports}var e={};return i.m=t,i.c=e,i.d=function(t,e,o){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:o})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},i.p="",i(i.s=1)}([function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),e(4)();var o=e(5),n=e(6);i.default={name:"vue-seamless-scroll",data:function(){return{xPos:0,yPos:0,delay:0,copyHtml:"",height:0,width:0,realBoxWidth:0}},props:{data:{type:Array,default:function(){return[]}},classOption:{type:Object,default:function(){return{}}}},computed:{leftSwitchState:function(){return this.xPos<0},rightSwitchState:function(){return Math.abs(this.xPos)<this.realBoxWidth-this.width},leftSwitchClass:function(){return this.leftSwitchState?"":this.options.switchDisabledClass},rightSwitchClass:function(){return this.rightSwitchState?"":this.options.switchDisabledClass},leftSwitch:function(){return{position:"absolute",margin:this.height/2+"px 0 0 -"+this.options.switchOffset+"px",transform:"translate(-100%,-50%)"}},rightSwitch:function(){return{position:"absolute",margin:this.height/2+"px 0 0 "+(this.width+this.options.switchOffset)+"px",transform:"translateY(-50%)"}},float:function(){return this.isHorizontal?{float:"left",overflow:"hidden"}:{overflow:"hidden"}},pos:function(){return{transform:"translate("+this.xPos+"px,"+this.yPos+"px)",transition:"all "+this.ease+" "+this.delay+"ms",overflow:"hidden"}},defaultOption:function(){return{step:1,limitMoveNum:5,hoverStop:!0,direction:1,openTouch:!0,singleHeight:0,singleWidth:0,waitTime:1e3,switchOffset:30,autoPlay:!0,navigation:!1,switchSingleStep:134,switchDelay:400,switchDisabledClass:"disabled",isSingleRemUnit:!1}},options:function(){return n({},this.defaultOption,this.classOption)},navigation:function(){return this.options.navigation},autoPlay:function(){return!this.navigation&&this.options.autoPlay},scrollSwitch:function(){return this.data.length>=this.options.limitMoveNum},hoverStopSwitch:function(){return this.options.hoverStop&&this.autoPlay&&this.scrollSwitch},canTouchScroll:function(){return this.options.openTouch},isHorizontal:function(){return this.options.direction>1},baseFontSize:function(){return this.options.isSingleRemUnit?parseInt(window.getComputedStyle(document.documentElement,null).fontSize):1},realSingleStopWidth:function(){return this.options.singleWidth*this.baseFontSize},realSingleStopHeight:function(){return this.options.singleHeight*this.baseFontSize},step:function(){var t=this.options.step;return this.isHorizontal?this.realSingleStopWidth:this.realSingleStopHeight,t}},methods:{reset:function(){this._cancle(),this._initMove()},leftSwitchClick:function(){if(this.leftSwitchState)return Math.abs(this.xPos)<this.options.switchSingleStep?void(this.xPos=0):void(this.xPos+=this.options.switchSingleStep)},rightSwitchClick:function(){if(this.rightSwitchState)return this.realBoxWidth-this.width+this.xPos<this.options.switchSingleStep?void(this.xPos=this.width-this.realBoxWidth):void(this.xPos-=this.options.switchSingleStep)},_cancle:function(){cancelAnimationFrame(this.reqFrame||"")},touchStart:function(t){var i=this;if(this.canTouchScroll){var e=void 0,o=t.targetTouches[0],n=this.options,s=n.waitTime,r=n.singleHeight,a=n.singleWidth;this.startPos={x:o.pageX,y:o.pageY},this.startPosY=this.yPos,this.startPosX=this.xPos,r&&a?(e&&clearTimeout(e),e=setTimeout((function(){i._cancle()}),s+20)):this._cancle()}},touchMove:function(t){if(!(!this.canTouchScroll||t.targetTouches.length>1||t.scale&&1!==t.scale)){var i=t.targetTouches[0],e=this.options.direction;this.endPos={x:i.pageX-this.startPos.x,y:i.pageY-this.startPos.y},event.preventDefault();var o=Math.abs(this.endPos.x)<Math.abs(this.endPos.y)?1:0;1===o&&e<2?this.yPos=this.startPosY+this.endPos.y:0===o&&e>1&&(this.xPos=this.startPosX+this.endPos.x)}},touchEnd:function(){var t=this;if(this.canTouchScroll){var i=void 0,e=this.options.direction;if(this.delay=50,1===e)this.yPos>0&&(this.yPos=0);else if(0===e){var o=this.realBoxHeight/2*-1;this.yPos<o&&(this.yPos=o)}else if(2===e)this.xPos>0&&(this.xPos=0);else if(3===e){var n=-1*this.realBoxWidth;this.xPos<n&&(this.xPos=n)}i&&clearTimeout(i),i=setTimeout((function(){t.delay=0,t._move()}),this.delay)}},enter:function(){this.hoverStopSwitch&&this._stopMove()},leave:function(){this.hoverStopSwitch&&this._startMove()},_move:function(){this.isHover||(this._cancle(),this.reqFrame=requestAnimationFrame(function(){var t=this,i=this.realBoxHeight/2,e=this.realBoxWidth/2,o=this.options,n=o.direction,s=o.waitTime,r=this.step;1===n?(Math.abs(this.yPos)>=i&&(this.$emit("ScrollEnd"),this.yPos=0),this.yPos-=r):0===n?(this.yPos>=0&&(this.$emit("ScrollEnd"),this.yPos=-1*i),this.yPos+=r):2===n?(Math.abs(this.xPos)>=e&&(this.$emit("ScrollEnd"),this.xPos=0),this.xPos-=r):3===n&&(this.xPos>=0&&(this.$emit("ScrollEnd"),this.xPos=-1*e),this.xPos+=r),this.singleWaitTime&&clearTimeout(this.singleWaitTime),this.realSingleStopHeight?Math.abs(this.yPos)%this.realSingleStopHeight<r?this.singleWaitTime=setTimeout((function(){t._move()}),s):this._move():this.realSingleStopWidth&&Math.abs(this.xPos)%this.realSingleStopWidth<r?this.singleWaitTime=setTimeout((function(){t._move()}),s):this._move()}.bind(this)))},_initMove:function(){var t=this;this.$nextTick((function(){var i=t.options.switchDelay,e=t.autoPlay,o=t.isHorizontal;if(t._dataWarm(t.data),t.copyHtml="",o){t.height=t.$refs.wrap.offsetHeight,t.width=t.$refs.wrap.offsetWidth;var n=t.$refs.slotList.offsetWidth;e&&(n=2*n+1),t.$refs.realBox.style.width=n+"px",t.realBoxWidth=n}if(!e)return t.ease="linear",void(t.delay=i);t.ease="ease-in",t.delay=0,t.scrollSwitch?(t.copyHtml=t.$refs.slotList.innerHTML,setTimeout((function(){t.realBoxHeight=t.$refs.realBox.offsetHeight,t._move()}),0)):(t._cancle(),t.yPos=t.xPos=0)}))},_dataWarm:function(t){t.length},_startMove:function(){this.isHover=!1,this._move()},_stopMove:function(){this.isHover=!0,this.singleWaitTime&&clearTimeout(this.singleWaitTime),this._cancle()}},mounted:function(){this._initMove()},watch:{data:function(t,i){this._dataWarm(t),o(t,i)||this.reset()},autoPlay:function(t){t?this.reset():this._stopMove()}},beforeCreate:function(){this.reqFrame=null,this.singleWaitTime=null,this.isHover=!1,this.ease="ease-in"},beforeDestroy:function(){this._cancle(),clearTimeout(this.singleWaitTime)}}},function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var o=function(t){return t&&t.__esModule?t:{default:t}}(e(2));o.default.install=function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.component(i.componentName||o.default.name,o.default)},"undefined"!=typeof window&&window.Vue&&Vue.component(o.default.name,o.default),i.default=o.default},function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var o=e(0),n=e.n(o);for(var s in o)"default"!==s&&function(t){e.d(i,t,(function(){return o[t]}))}(s);var r=e(7),l=e(3)(n.a,r.a,!1,null,null,null);i.default=l.exports},function(t,i){t.exports=function(t,i,e,o,n,s){var r,a=t=t||{},l=typeof t.default;"object"!==l&&"function"!==l||(r=t,a=t.default);var c,d="function"==typeof a?a.options:a;if(i&&(d.render=i.render,d.staticRenderFns=i.staticRenderFns,d._compiled=!0),e&&(d.functional=!0),n&&(d._scopeId=n),s?(c=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},d._ssrRegister=c):o&&(c=o),c){var u=d.functional,f=u?d.render:d.beforeCreate;u?(d._injectStyles=c,d.render=function(t,i){return c.call(i),f(t,i)}):d.beforeCreate=f?[].concat(f,c):[c]}return{esModule:r,exports:a,options:d}}},function(t,i){var e=function(){window.cancelAnimationFrame=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame||function(t){return window.clearTimeout(t)},window.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)}};t.exports=e},function(t,i){var e=function(t,i){if(t===i)return!0;if(t.length!==i.length)return!1;for(var e=0;e<t.length;++e)if(t[e]!==i[e])return!1;return!0};t.exports=e},function(t,i){function e(){Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)});var t=void 0,i=void 0,n=void 0,s=void 0,r=void 0,a=void 0,l=1,c=arguments[0]||{},d=!1,u=arguments.length;if("boolean"==typeof c&&(d=c,c=arguments[1]||{},l++),"object"!==(void 0===c?"undefined":o(c))&&"function"!=typeof c&&(c={}),l===u)return c;for(;l<u;l++)if(null!=(i=arguments[l]))for(t in i)n=c[t],s=i[t],r=Array.isArray(s),d&&s&&("object"===(void 0===s?"undefined":o(s))||r)?(r?(r=!1,a=n&&Array.isArray(n)?n:[]):a=n&&"object"===(void 0===n?"undefined":o(n))?n:{},c[t]=e(d,a,s)):void 0!==s&&(c[t]=s);return c}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};t.exports=e},function(t,i,e){"use strict";var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{ref:"wrap"},[t.navigation?e("div",{class:t.leftSwitchClass,style:t.leftSwitch,on:{click:t.leftSwitchClick}},[t._t("left-switch")],2):t._e(),t._v(" "),t.navigation?e("div",{class:t.rightSwitchClass,style:t.rightSwitch,on:{click:t.rightSwitchClick}},[t._t("right-switch")],2):t._e(),t._v(" "),e("div",{ref:"realBox",style:t.pos,on:{mouseenter:t.enter,mouseleave:t.leave,touchstart:t.touchStart,touchmove:t.touchMove,touchend:t.touchEnd}},[e("div",{ref:"slotList",style:t.float},[t._t("default")],2),t._v(" "),e("div",{style:t.float,domProps:{innerHTML:t._s(t.copyHtml)}})])])},s={render:o,staticRenderFns:[]};i.a=s}]).default},747:function(t,e,o){"use strict";o(654)},761:function(t,e,o){"use strict";o.r(e);o(321),o(24),o(25),o(23),o(7),o(29),o(18),o(30);var n=o(21),r=o(10),l=(o(95),o(27)),c=o(656),d=o(657),f=o(658),h=o(659),m=(o(554),o(270)),v=o(12),_=o(547),y=o(538);function w(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function T(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?w(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):w(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var O={name:"index",components:{CountDown:o.n(y).a},data:function(){return{loadingAd:!0,loadingFloor:!0,adList:[],adLeftList:[],adRightList:[],adCenterList:[],floorList:[],floatLayer:{is_show:!1,link:{url:""}},isSub:!1,siteId:0,listData:[],seckillTimeMachine:{currentTime:0,startTime:0,endTime:0},seckillText:"距离结束",backgroundColor:"",keyword:"",defaultSearchWords:"",isShow:!1}},watch:{addonIsExit:{handler:function(){this.addonIsExit&&1==this.addonIsExit.seckill&&this.getTimeList()},deep:!0}},created:function(){this.getAdList(),this.getBigAdList(),this.getSmallAdList(),this.getCategoryBelowList(),this.getFloors(),this.getFloatLayer(),this.addonIsExit&&1==this.addonIsExit.seckill&&this.getTimeList()},mounted:function(){window.addEventListener("scroll",this.handleScroll)},computed:T(T({},Object(v.b)(["defaultHeadImage","addonIsExit","defaultGoodsImage","member","siteInfo","cartCount"])),{},{optionLeft:function(){return{direction:2,limitMoveNum:2}},indexFloatLayerNum:function(){var t=localStorage.getItem("indexFloatLayerNum")||0;return parseInt(t)}}),methods:{countDownS_cb:function(){},countDownE_cb:function(){this.seckillText="活动已结束"},getAdList:function(){var t=this;Object(l.a)({keyword:"NS_PC_INDEX"}).then((function(e){t.adList=e.data.adv_list,t.$store.dispatch("app/is_show",{is_show:t.adList.length}).then((function(t){}));for(var i=0;i<t.adList.length;i++)t.adList[i].adv_url&&(t.adList[i].adv_url=JSON.parse(t.adList[i].adv_url));t.backgroundColor=t.adList[0].background,t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},handleChange:function(t,pre){this.backgroundColor=this.adList[t].background},getBigAdList:function(){var t=this;Object(l.a)({keyword:"NS_PC_INDEX_MID_LEFT"}).then((function(e){t.adLeftList=e.data.adv_list;for(var i=0;i<t.adLeftList.length;i++)t.adLeftList[i].adv_url&&(t.adLeftList[i].adv_url=JSON.parse(t.adLeftList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},getSmallAdList:function(){var t=this;Object(l.a)({keyword:"NS_PC_INDEX_MID_RIGHT"}).then((function(e){t.adRightList=e.data.adv_list;for(var i=0;i<t.adRightList.length;i++)t.adRightList[i].adv_url&&(t.adRightList[i].adv_url=JSON.parse(t.adRightList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},getCategoryBelowList:function(){var t=this;Object(l.a)({keyword:"NS_PC_INDEX_CATEGORY_BELOW"}).then((function(e){t.adCenterList=e.data.adv_list;for(var i=0;i<t.adCenterList.length;i++)t.adCenterList[i].adv_url&&(t.adCenterList[i].adv_url=JSON.parse(t.adCenterList[i].adv_url));t.loadingAd=!1})).catch((function(e){t.loadingAd=!1}))},getTimeList:function(){var t=this;Object(_.h)().then((function(e){if(0==e.code&&e.data){var time=new Date(1e3*e.timestamp),o=60*time.getHours()*60+60*time.getMinutes()+time.getSeconds();e.data.list.forEach((function(n,r){if(n.seckill_start_time<=o&&o<n.seckill_end_time){var l=n.id;t.getGoodsList(l);var c=parseInt(time.getTime()/1e3)+(n.seckill_end_time-o);t.seckillTimeMachine={currentTime:e.timestamp,startTime:e.timestamp,endTime:c}}}))}}))},getGoodsList:function(t){var e=this;Object(_.c)({page_size:0,seckill_time_id:t,site_id:this.siteId}).then((function(t){0==t.code&&t.data.list&&(e.listData=t.data.list)}))},imageError:function(t){this.listData[t].sku_image=this.defaultGoodsImage},adLeftImageError:function(t){this.adLeftList[t].adv_image=this.defaultGoodsImage},adRightImageError:function(t){this.adRightList[t].adv_image=this.defaultGoodsImage},adCenterImageError:function(t){this.adCenterList[t].adv_image=this.defaultGoodsImage},getFloors:function(){var t=this;Object(m.c)().then((function(e){t.floorList=e.data}))},getFloatLayer:function(){var t=this;Object(m.b)().then((function(e){if(0==e.code&&e.data)if(t.floatLayer=e.data,1==t.floatLayer.is_show){if(t.floatLayer.link=JSON.parse(t.floatLayer.url),!t.floatLayer.img_url)return;parseInt(t.floatLayer.number)>=1?t.indexFloatLayerNum>=parseInt(t.floatLayer.number)?t.floatLayer.is_show_type=!1:t.floatLayer.is_show_type=!0:0==parseInt(t.floatLayer.number)&&(t.floatLayer.is_show_type=!0)}else t.floatLayer.is_show_type=!1}))},closeFloat:function(){if(0==parseInt(this.floatLayer.number))this.$store.commit("app/SET_FLOAT_LAYER",0);else if(parseInt(this.floatLayer.number)>=1&&this.indexFloatLayerNum!=parseInt(this.floatLayer.number)){var t=this.indexFloatLayerNum+1;this.$store.commit("app/SET_FLOAT_LAYER",t)}else this.indexFloatLayerNum==parseInt(this.floatLayer.number)&&this.$store.commit("app/SET_FLOAT_LAYER",this.floatLayer.number);this.floatLayer.is_show_type=!1,this.$forceUpdate()},handleScroll:function(){var t=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop;this.isShow=t>=680}},destroyed:function(){console.log("// 离开该页面需要移除这个监听的事件，不然会报错"),window.removeEventListener("scroll",this.handleScroll)}},k=o(61),S=o(746),x=o.n(S);function L(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var C={components:{floorStyle1:c.default,floorStyle2:d.default,floorStyle3:f.default,floorStyle4:h.default,NsHeaderMid:k.a,vueSeamlessScroll:x.a},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?L(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):L(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(v.b)(["siteInfo"])),mixins:[O],fetch:function(t){return Object(n.a)(regeneratorRuntime.mark((function e(){var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.store,t.params,e.next=3,o.dispatch("site/siteInfo");case 3:case"end":return e.stop()}}),e)})))()},head:function(){return{title:this.siteInfo.seo_title?this.siteInfo.seo_title:this.siteInfo.site_title,meta:[{name:"description",content:this.siteInfo.seo_description},{name:"keyword",content:this.siteInfo.seo_keywords},{property:"og:title",content:this.siteInfo.seo_title},{property:"og:description",content:this.siteInfo.seo_description},{property:"og:type",content:"website"}]}}},j=C,P=(o(747),o(6)),component=Object(P.a)(j,(function(){var t=this,e=t._self._c;return e("div",[t.adList.length?e("div",{staticClass:"index-wrap",style:{background:t.backgroundColor}},[e("div",{staticClass:"index"},[e("div",{staticClass:"banner"},[e("el-carousel",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingAd,expression:"loadingAd"}],attrs:{height:"500px",arrow:"never"},on:{change:t.handleChange}},t._l(t.adList,(function(o){return e("el-carousel-item",{key:o.adv_id},[e("el-image",{attrs:{src:t.$img(o.adv_image),fit:"cover"},on:{click:function(e){return t.$util.pushToTab(o.adv_url.url)}}})],1)})),1)],1)])]):t._e(),t._v(" "),e("div",{staticClass:"index-content-wrap"},[t.adCenterList.length?e("ul",{staticClass:"adv-middle"},t._l(t.adCenterList,(function(o,n){return e("li",{key:n,staticClass:"adv-middle-item"},[e("el-image",{attrs:{src:t.$img(o.adv_image),fit:"cover"},on:{error:function(e){return t.adCenterImageError(n)},click:function(e){return t.$util.pushToTab(o.adv_url.url)}}})],1)})),0):t._e(),t._v(" "),t.adLeftList.length>0||t.adRightList.length>0?e("div",{staticClass:"content-div"},[e("div",{staticClass:"ad-wrap"},[t.adLeftList.length>0?e("div",{staticClass:"ad-big"},t._l(t.adLeftList,(function(o,n){return e("div",{key:n,staticClass:"ad-big-img"},[e("el-image",{attrs:{src:t.$img(o.adv_image),fit:"cover"},on:{error:function(e){return t.adLeftImageError(n)},click:function(e){return t.$util.pushToTab(o.adv_url.url)}}})],1)})),0):t._e(),t._v(" "),t.adRightList.length>0?e("div",{staticClass:"ad-small"},t._l(t.adRightList,(function(o,n){return e("div",{key:n,staticClass:"ad-small-img"},[e("el-image",{attrs:{src:t.$img(o.adv_image),fit:"cover"},on:{error:function(e){return t.adRightImageError(n)},click:function(e){return t.$util.pushToTab(o.adv_url.url)}}})],1)})),0):t._e()])]):t._e(),t._v(" "),1==t.addonIsExit.seckill&&t.listData.length>0?e("div",{staticClass:"content-div"},[e("div",{staticClass:"seckill-wrap"},[e("div",{staticClass:"seckill-time"},[e("div",{staticClass:"seckill-time-left"},[e("i",{staticClass:"iconfont icon-miaosha1 ns-text-color"}),t._v(" "),e("span",{staticClass:"seckill-time-title ns-text-color"},[t._v("限时秒杀")]),t._v(" "),e("span",[t._v(t._s(t.seckillText))]),t._v(" "),e("count-down",{staticClass:"count-down",attrs:{currentTime:t.seckillTimeMachine.currentTime,startTime:t.seckillTimeMachine.startTime,endTime:t.seckillTimeMachine.endTime,dayTxt:"：",hourTxt:"：",minutesTxt:"：",secondsTxt:""},on:{start_callback:function(e){return t.countDownS_cb()},end_callback:function(e){return t.countDownE_cb()}}})],1),t._v(" "),e("div",{staticClass:"seckill-time-right",on:{click:function(e){return t.$router.push("/promotion/seckill")}}},[e("span",[t._v("更多商品")]),t._v(" "),e("i",{staticClass:"iconfont icon-arrow-right"})])]),t._v(" "),e("div",{staticClass:"seckill-content"},[e("vue-seamless-scroll",{staticClass:"seamless-warp2",attrs:{data:t.listData,"class-option":t.optionLeft}},[e("ul",{staticClass:"item",style:{width:250*t.listData.length+"px"}},t._l(t.listData,(function(o,n){return e("li",{key:n},[e("div",{staticClass:"seckill-goods",on:{click:function(e){return t.$router.push("/promotion/seckill/"+o.id)}}},[e("div",{staticClass:"seckill-goods-img"},[e("img",{attrs:{src:t.$img(o.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("p",[t._v(t._s(o.goods_name))]),t._v(" "),e("div",{staticClass:"seckill-price-wrap"},[e("p",{staticClass:"ns-text-color"},[t._v("\n                      ￥\n                      "),e("span",[t._v(t._s(o.seckill_price))])]),t._v(" "),e("p",{staticClass:"primary-price"},[t._v("￥"+t._s(o.price))])])])])})),0)])],1)])]):t._e(),t._v(" "),e("div",{staticClass:"content-div"},[e("div",{staticClass:"floor"},t._l(t.floorList,(function(o,n){return e("div",{key:n,staticClass:"floor_item"},["floor-style-1"==o.block_name?e("floor-style-1",{attrs:{data:o}}):t._e(),t._v(" "),"floor-style-2"==o.block_name?e("floor-style-2",{attrs:{data:o}}):t._e(),t._v(" "),"floor-style-3"==o.block_name?e("floor-style-3",{attrs:{data:o}}):t._e(),t._v(" "),"floor-style-4"==o.block_name?e("floor-style-4",{attrs:{data:o}}):t._e()],1)})),0)]),t._v(" "),t.floatLayer.is_show_type?e("div",{staticClass:"floatLayer-wrap"},[e("div",{staticClass:"floatLayer"},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{src:t.$img(t.floatLayer.img_url)},on:{click:function(e){return t.$util.pushToTab(t.floatLayer.link.url)}}})]),t._v(" "),e("i",{staticClass:"el-icon-circle-close",on:{click:t.closeFloat}})])]):t._e(),t._v(" "),e("div",{staticClass:"fixed-box",style:{display:t.isShow?"block":"none"}},[e("ns-header-mid")],1)])])}),[],!1,null,"7c5aac62",null);e.default=component.exports}}]);