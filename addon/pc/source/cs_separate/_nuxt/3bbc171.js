(window.webpackJsonp=window.webpackJsonp||[]).push([[14],{554:function(t,e,n){"use strict";n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return o}));var r=n(1);function c(t){return Object(r.a)({url:"/api/notice/page",data:t})}function o(t){return Object(r.a)({url:"/api/notice/info",data:t})}},643:function(t,e,n){},736:function(t,e,n){"use strict";n(643)},807:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),c=(n(92),n(12)),o=n(554);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var d={name:"notice_detail",components:{},data:function(){return{info:{},loading:!0}},created:function(){this.id=this.$route.query.id,this.getDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["siteInfo"])),watch:{$route:function(t){this.id=t.query.id,this.getDetail()}},methods:{getDetail:function(){var t=this;Object(o.a)({id:this.id}).then((function(e){e.data?(t.info=e.data,t.loading=!1,window.document.title="".concat(t.info.title," - ").concat(t.siteInfo.site_name)):t.$router.push({path:"/cms/notice/list"})})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))}}},f=d,m=(n(736),n(6)),component=Object(m.a)(f,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"notice-wrap"},[e("el-breadcrumb",{staticClass:"path",attrs:{separator:"/"}},[e("el-breadcrumb-item",{staticClass:"path-home",attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/cms/notice/list"}}},[t._v("公告列表")]),t._v(" "),e("el-breadcrumb-item",{staticClass:"path-help"},[t._v("公告详情")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"notice-detil"},[e("div",{staticClass:"notice-info"},[e("div",{staticClass:"title"},[t._v(t._s(t.info.title))]),t._v(" "),e("div",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(t.info.create_time)))])]),t._v(" "),e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.info.content)}})])],1)}),[],!1,null,"322898bc",null);e.default=component.exports}}]);