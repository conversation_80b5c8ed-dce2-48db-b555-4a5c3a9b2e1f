(window.webpackJsonp=window.webpackJsonp||[]).push([[54],{624:function(e,t,r){},716:function(e,t,r){"use strict";r(624)},794:function(e,t,r){"use strict";r.r(t);r(24),r(25),r(23),r(29),r(30);var n=r(10),c=(r(73),r(7),r(18),r(204)),o=r(12);function l(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var d={name:"logistics",components:{},data:function(){return{orderId:0,loading:!0,activeParcel:"parcel_0",packageList:[],yes:!0}},created:function(){this.orderId=this.$route.query.order_id,this.orderId||this.$router.push({path:"/member/order_list"}),this.getOrderPackageInfo()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(o.b)(["defaultGoodsImage"])),layout:"member",mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getOrderPackageInfo:function(){var e=this;Object(c.f)({order_id:this.orderId}).then((function(t){t.code>=0?(e.packageList=t.data,e.packageList.forEach((function(e){e.trace.list&&(e.trace.list=e.trace.list.reverse())})),e.loading=!1):e.$message({message:"未获取到订单包裹信息！",type:"warning",duration:2e3,onClose:function(){e.$router.push({path:"/member/order_list"})}})})).catch((function(t){e.loading=!1}))},imageError:function(e,t){this.packageList[e].goods_list[t].sku_image=this.defaultGoodsImage}}},m=d,v=(r(716),r(6)),component=Object(v.a)(m,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card logistics"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",{attrs:{to:{path:"/member/order_list"}}},[e._v("订单列表")]),e._v(" "),t("el-breadcrumb-item",[e._v("物流详情")])],1)],1),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-tabs",{model:{value:e.activeParcel,callback:function(t){e.activeParcel=t},expression:"activeParcel"}},e._l(e.packageList,(function(r,n){return t("el-tab-pane",{key:n,attrs:{label:r.package_name,name:"parcel_"+n}},[r.trace.success&&r.trace.list.length>0?t("div",{staticClass:"trace"},[t("el-timeline",e._l(r.trace.list,(function(r,n){return t("el-timeline-item",{key:n,attrs:{timestamp:r.datetime,placement:"top",type:0==n?"primary":""}},[t("p",[e._v(e._s(r.remark))])])})),1)],1):t("div",{staticClass:"trace"},[t("p",{staticClass:"empty-wrap"},[e._v(e._s(r.trace.reason))])]),e._v(" "),t("ul",{staticClass:"info-wrap"},[t("li",[t("label",[e._v("运单号码：")]),e._v(" "),t("span",[e._v(e._s(r.delivery_no))])]),e._v(" "),t("li",[t("label",[e._v("物流公司：")]),e._v(" "),t("span",[e._v(e._s(r.express_company_name))])])]),e._v(" "),t("ul",{staticClass:"goods-wrap"},e._l(r.goods_list,(function(r,c){return t("li",{key:c,on:{click:function(t){return e.$util.pushToTab("/sku/"+r.sku_id)}}},[t("div",{staticClass:"img-wrap"},[t("img",{attrs:{src:e.$img(r.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(n,c)}}})]),e._v(" "),t("p",{staticClass:"sku-name"},[e._v(e._s(r.sku_name)+" x "+e._s(r.num))])])})),0)])})),1)],1)])],1)}),[],!1,null,"444ed289",null);t.default=component.exports}}]);