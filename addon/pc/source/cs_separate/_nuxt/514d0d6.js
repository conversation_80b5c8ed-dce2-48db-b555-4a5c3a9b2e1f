(window.webpackJsonp=window.webpackJsonp||[]).push([[5],{535:function(t,e,r){"use strict";var n=r(3),o=r(35),c=r(14),f=r(8),path=r(319),l=r(5),d=r(113),m=r(16),v=r(205),h=r(63),_=r(112),O=r(318),N=r(4),y=r(93).f,I=r(57).f,w=r(26).f,E=r(320),j=r(316).trim,S="Number",k=f[S],P=path[S],T=k.prototype,A=f.TypeError,C=l("".slice),G=l("".charCodeAt),F=function(t){var e=O(t,"number");return"bigint"==typeof e?e:z(e)},z=function(t){var e,r,n,o,c,f,l,code,d=O(t,"number");if(_(d))throw A("Cannot convert a Symbol value to a number");if("string"==typeof d&&d.length>2)if(d=j(d),43===(e=G(d,0))||45===e){if(88===(r=G(d,2))||120===r)return NaN}else if(48===e){switch(G(d,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+d}for(f=(c=C(d,2)).length,l=0;l<f;l++)if((code=G(c,l))<48||code>o)return NaN;return parseInt(c,n)}return+d},D=d(S,!k(" 0o1")||!k("0b1")||k("+0x1")),M=function(t){return h(T,t)&&N((function(){E(t)}))},R=function(t){var e=arguments.length<1?0:k(F(t));return M(this)?v(Object(e),this,R):e};R.prototype=T,D&&!o&&(T.constructor=R),n({global:!0,constructor:!0,wrap:!0,forced:D},{Number:R});var V=function(t,source){for(var e,r=c?y(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;r.length>n;n++)m(source,e=r[n])&&!m(t,e)&&w(t,e,I(source,e))};o&&P&&V(path[S],P),(D||o)&&V(path[S],k)},536:function(t,e,r){},539:function(t,e,r){"use strict";r(536)},544:function(t,e,r){"use strict";r.r(e);r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=(r(535),r(12)),c=r(206);function f(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var l={name:"goods_recommend",props:{page:{type:[Number,String],default:1},pageSize:{type:[Number,String],default:5}},data:function(){return{loading:!0,list:[]}},created:function(){this.getGoodsRecommend()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?f(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):f(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(o.b)(["defaultGoodsImage"])),methods:{getGoodsRecommend:function(){var t=this;Object(c.e)({page:this.page,page_size:this.pageSize}).then((function(e){0==e.code&&(t.list=e.data.list),t.loading=!1})).catch((function(e){t.loading=!1}))},imageError:function(t){this.list[t].sku_image=this.defaultGoodsImage}}},d=l,m=(r(539),r(6)),component=Object(m.a)(d,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"goods-recommend"},[e("h4",[t._v("商品精选")]),t._v(" "),t.list.length?e("ul",t._l(t.list,(function(r,n){return e("li",{key:n,on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+r.sku_id})}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{src:t.$img(r.sku_image,{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("div",{staticClass:"price"},[t._v("￥"+t._s(r.discount_price))]),t._v(" "),e("p",{staticClass:"sku-name"},[t._v(t._s(r.goods_name))]),t._v(" "),e("div",{staticClass:"info-wrap"})])})),0):t._e()])}),[],!1,null,"a68a46cc",null);e.default=component.exports}}]);