(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{535:function(e,t,r){"use strict";var n=r(3),o=r(35),c=r(14),d=r(8),path=r(319),l=r(5),h=r(113),m=r(16),f=r(205),_=r(63),v=r(112),y=r(318),T=r(4),w=r(93).f,A=r(57).f,x=r(26).f,k=r(320),S=r(316).trim,O="Number",C=d[O],D=path[O],j=C.prototype,B=d.TypeError,E=l("".slice),I=l("".charCodeAt),P=function(e){var t=y(e,"number");return"bigint"==typeof t?t:N(t)},N=function(e){var t,r,n,o,c,d,l,code,h=y(e,"number");if(v(h))throw B("Cannot convert a Symbol value to a number");if("string"==typeof h&&h.length>2)if(h=S(h),43===(t=I(h,0))||45===t){if(88===(r=I(h,2))||120===r)return NaN}else if(48===t){switch(I(h,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+h}for(d=(c=E(h,2)).length,l=0;l<d;l++)if((code=I(c,l))<48||code>o)return NaN;return parseInt(c,n)}return+h},$=h(O,!C(" 0o1")||!C("0b1")||C("+0x1")),L=function(e){return _(j,e)&&T((function(){k(e)}))},z=function(e){var t=arguments.length<1?0:C(P(e));return L(this)?f(Object(t),this,z):t};z.prototype=j,$&&!o&&(j.constructor=z),n({global:!0,constructor:!0,wrap:!0,forced:$},{Number:z});var M=function(e,source){for(var t,r=c?w(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;r.length>n;n++)m(source,t=r[n])&&!m(e,t)&&x(e,t,A(source,t))};o&&D&&M(path[O],D),($||o)&&M(path[O],C)},538:function(e,t,r){e.exports=r(543)},541:function(e,t,r){"use strict";var n=r(204);t.a={methods:{orderPay:function(e){var t=this;0==e.adjust_money?Object(n.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})})):this.$confirm("商家已将支付金额调整为"+e.pay_money+"元，是否继续支付？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(n.g)({order_ids:e.order_id}).then((function(e){e.code>=0?t.$router.push({path:"/pay",query:{code:e.data}}):t.$message({message:e.message,type:"warning"})}))}))},orderClose:function(e,t){var r=this;this.$confirm("您确定要关闭该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(n.b)({order_id:e}).then((function(e){r.$message({message:"订单关闭成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelivery:function(e,t){var r=this;this.$confirm("您确定已经收到货物了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(n.h)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()})).catch((function(e){r.$message({message:e.message,type:"warning"}),"function"==typeof t&&t()}))}))},orderVirtualDelivery:function(e,t){var r=this;this.$confirm("您确定要进行收货吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(n.a)({order_id:e}).then((function(e){r.$message({message:"订单收货成功",type:"success"}),"function"==typeof t&&t()}))}))},orderDelete:function(e,t){var r=this;this.$confirm("您确定要删除该订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(n.c)({order_id:e}).then((function(e){r.$message({message:"订单删除成功",type:"success"}),"function"==typeof t&&t()}))}))}}}},543:function(e,t,r){"use strict";r.r(t);r(535),r(7),r(74),r(73),r(315);var n={replace:!0,data:function(){return{tipShow:!0,msTime:{show:!1,day:"",hour:"",minutes:"",seconds:""},star:"",end:"",current:""}},watch:{currentTime:function(e,t){this.gogogo()}},props:{tipText:{type:String,default:"距离开始"},tipTextEnd:{type:String,default:"距离结束"},id:{type:String,default:"1"},currentTime:{type:Number},startTime:{type:Number},endTime:{type:Number},endText:{type:String,default:"已结束"},dayTxt:{type:String,default:":"},hourTxt:{type:String,default:":"},minutesTxt:{type:String,default:":"},secondsTxt:{type:String,default:":"},secondsFixed:{type:Boolean,default:!1}},mounted:function(){console.log(this),this.gogogo()},methods:{gogogo:function(){var e=this;10==this.startTime.toString().length?this.star=1e3*this.startTime:this.star=this.startTime,10==this.endTime.toString().length?this.end=1e3*this.endTime:this.end=this.endTime,this.currentTime?10==this.currentTime.toString().length?this.current=1e3*this.currentTime:this.current=this.currentTime:this.current=(new Date).getTime(),this.end<this.current?(this.msTime.show=!1,this.end_message()):this.current<this.star?(this.$set(this,"tipShow",!0),setTimeout((function(){e.runTime(e.star,e.current,e.start_message)}),1)):(this.end>this.current&&this.star<this.current||this.star==this.current)&&(this.$set(this,"tipShow",!1),this.msTime.show=!0,this.$emit("start_callback",this.msTime.show),setTimeout((function(){e.runTime(e.end,e.star,e.end_message,!0)}),1))},runTime:function(e,t,r,n){var o=this,c=this.msTime,d=e-t;if(d>0){this.msTime.show=!0,c.day=Math.floor(d/864e5),d-=864e5*c.day,c.hour=Math.floor(d/36e5),d-=36e5*c.hour,c.minutes=Math.floor(d/6e4),d-=6e4*c.minutes,c.seconds=Math.floor(d/1e3).toFixed(0),d-=1e3*c.seconds,c.hour<10&&(c.hour="0"+c.hour),c.minutes<10&&(c.minutes="0"+c.minutes),c.seconds<10&&(c.seconds="0"+c.seconds);var l=Date.now(),h=Date.now();setTimeout((function(){n?o.runTime(o.end,t+=1e3,r,!0):o.runTime(o.star,t+=1e3,r)}),1e3-(h-l))}else r()},start_message:function(){var e=this;this.$set(this,"tipShow",!1),this.$emit("start_callback",this.msTime.show),setTimeout((function(){e.runTime(e.end,e.star,e.end_message,!0)}),1)},end_message:function(){this.msTime.show=!1,this.currentTime<=0||this.$emit("end_callback",this.msTime.show)}}},o=r(6),component=Object(o.a)(n,(function(){var e=this,t=e._self._c;return t("div",[e.msTime.show?t("p",[e.msTime.day>0?t("span",[t("span",[e._v(e._s(e.msTime.day))]),t("i",[e._v(e._s(e.dayTxt))])]):e._e(),e._v(" "),t("span",[e._v(e._s(e.msTime.hour))]),t("i",[e._v(e._s(e.hourTxt))]),e._v(" "),t("span",[e._v(e._s(e.msTime.minutes))]),t("i",[e._v(e._s(e.minutesTxt))]),e._v(" "),t("span",[e._v(e._s(e.msTime.seconds))]),t("i",[e._v(e._s(e.secondsTxt))])]):e._e()])}),[],!1,null,null,null);t.default=component.exports},570:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAC8UlEQVRIibWWe2iOURzHP3tbmPnjyeMPJ9RcMjRFLrlluU0WVij/iFxyjfLPCv+MjD/4Q9swKUmjKcXIrSmSRJYsuY6i2Ik666QhzOjo99Tj9Tx7n5f51tvvfc57nt+n857fLedA9RESaDQwBygBxgA+kAIs8Ay4DDRao+9kcpWb4fdSYBswLW39C9AB9AUmy2eX56vHQKU1+lScw1TMugdcAi4KrAUoB2YC+UAvIE/sRGAT0ASMAk56vmryfDUsKbAIeALMA14Di4DhwD7gOvApfFJr9D1r9CFr9ASgWMDjgOeerxZmAhYCD4H+QA1QAJyN+3vSZY2+KeDtQA7Q4PlqfhywD3BLvu8BNnfhe6X83iMGvBdYK48XPF+NiAKeB/oBLmx3ZDjMMaBK7jDutEflbpFY+A04FZgBvAfWZ4A5tYrt7GqTu1vgLjDE89WqMHC/2BUJYE4/Eu5zWiK2xvNVjgMOBiZJRF7JwlEiWaPfyHW5NCp1wKXyYlV3w0Kqla/LHXCuPNzOwkGO2PaE+1vEzk5JbXS6nwUwuMO8hPtfAd9cKcyVMubq4tcsgD3Ftnm+6khLrxPW6A3hzdboDs9XL4CRQfHuMrwj1OxeBlRELkYWAznUr27RLlUmlQW4JGZ/pA+XDsDQAPgUGC89rzkhsDNwbI1OX4/SIKA38DEVyr0pCWF/o6BV3XDAennY+B+By8TWOeAjyZOiUIp0mzxf5Ut3cWoIwrlcbH13A12aiK2wRn8OgOfkpIUJWlNieb4qk4mhzRq9k7SELRG7G1jXDbBiOYhTWbAeBraGoLX/EkSerxa7iJTHLdboYJL4Y6ZpBIIZ5CBwTYIpKWig56s64IwsbbVGV4f3RM2lbhwYCxwHZslQ5Ryclnt+Gaq7Kc9XBTIeur9tjay/BVZbo6+mO48bhB9IirgiXCldO+jcrpq4Zu3sgIhaWuHiwBr9Pcpxpsn7sAxVLtIWANNldHRTQqB30ksb3Ehpjf4Q6w34CexCxYf8/YyRAAAAAElFTkSuQmCC"},612:function(e,t,r){},705:function(e,t,r){"use strict";r(612)},784:function(e,t,r){"use strict";r.r(t);r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=(r(73),r(56),r(12)),c=r(204),d=r(541),l=r(538);function h(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}var m={name:"order_list",components:{CountDown:r.n(l).a},layout:"member",data:function(){return{orderStatus:"all",loading:!0,orderList:[],currentPage:1,pageSize:10,total:0,yes:!0}},mixins:[d.a],created:function(){this.orderStatus=this.$route.query.status||"all",this.getOrderList()},computed:function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},Object(o.b)(["defaultGoodsImage"])),mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{countDownS_cb:function(){},countDownE_cb:function(){},handleClick:function(e,t){this.currentPage=1,this.orderStatus=e.name,this.refresh()},getOrderList:function(){var e=this;Object(c.e)({page:this.currentPage,page_size:this.pageSize,order_status:this.orderStatus}).then((function(t){var r=[],n=0;if(0==t.code&&t.data){n=t.data.auto_close;for(var o=Date.parse(new Date)/1e3,i=0;i<t.data.list.length;i++)t.data.list[i].currentTime=o,t.data.list[i].startTime=o,t.data.list[i].endTime=t.data.list[i].create_time+n;r=t.data.list,e.total=t.data.count}e.orderList=r,e.loading=!1})).catch((function(t){e.loading=!1}))},handlePageSizeChange:function(e){this.pageSize=e,this.refresh()},handleCurrentPageChange:function(e){this.currentPage=e,this.refresh()},refresh:function(){this.loading=!0,this.getOrderList()},operation:function(e,t){var r=this;this.status;switch(e){case"orderPay":this.orderPay(t);break;case"orderClose":this.orderClose(t.order_id,(function(){r.refresh()}));break;case"memberTakeDelivery":this.orderDelivery(t.order_id,(function(){r.refresh()}));break;case"trace":this.$router.push({path:"/order/logistics",query:{order_id:t.order_id}});break;case"memberOrderEvaluation":this.$router.push({path:"/order/evaluate",query:{order_id:t.order_id}});break;case"memberVirtualTakeDelivery":this.orderVirtualDelivery(t.order_id,(function(){r.refresh()}));break;case"orderOfflinePay":this.$router.push({path:"/pay",query:{code:t.offline_pay_info.out_trade_no}});break;case"orderDelete":this.orderDelete(t.order_id,(function(){r.refresh()}))}},orderDetail:function(data){switch(parseInt(data.order_type)){case 2:this.$router.push({path:"/member/order_detail_pickup",query:{order_id:data.order_id}});break;case 3:this.$router.push({path:"/member/order_detail_local_delivery",query:{order_id:data.order_id}});break;case 4:this.$router.push({path:"/member/order_detail_virtual",query:{order_id:data.order_id}});break;default:this.$router.push({path:"/member/order_detail",query:{order_id:data.order_id}})}},imageError:function(e,t){this.orderList[e].order_goods[t].sku_image=this.defaultGoodsImage}}},f=m,_=(r(705),r(6)),component=Object(_.a)(f,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("el-card",{staticClass:"box-card order-list"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("我的订单")])]),e._v(" "),t("el-tabs",{on:{"tab-click":e.handleClick},model:{value:e.orderStatus,callback:function(t){e.orderStatus=t},expression:"orderStatus"}},[t("el-tab-pane",{attrs:{label:"全部订单",name:"all"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"待付款",name:"waitpay"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"待发货",name:"waitsend"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"待收货",name:"waitconfirm"}}),e._v(" "),t("el-tab-pane",{attrs:{label:"待评价",name:"waitrate"}})],1),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("nav",[t("li",[e._v("商品信息")]),e._v(" "),t("li",[e._v("单价")]),e._v(" "),t("li",[e._v("数量")]),e._v(" "),t("li",[e._v("实付款")]),e._v(" "),t("li",[e._v("订单状态")]),e._v(" "),t("li",[e._v("操作")])]),e._v(" "),e.orderList.length>0?t("div",{staticClass:"list"},e._l(e.orderList,(function(n,o){return t("div",{key:o,staticClass:"item"},[t("div",{staticClass:"head"},[t("span",{staticClass:"create-time"},[e._v(e._s(e.$util.timeStampTurnTime(n.create_time)))]),e._v(" "),t("span",{staticClass:"order-no"},[e._v("订单号："+e._s(n.order_no))]),e._v(" "),t("span",{staticClass:"order-type"},[e._v(e._s(n.order_type_name))])]),e._v(" "),e._l(n.order_goods,(function(c,d){return t("ul",{key:d},[t("li",[t("div",{staticClass:"img-wrap",on:{click:function(t){return e.$router.push("/sku/"+c.sku_id)}}},[t("img",{attrs:{src:e.$img(c.sku_image,{size:"mid"})},on:{error:function(t){return e.imageError(o,d)}}})]),e._v(" "),t("div",{staticClass:"info-wrap",on:{click:function(t){return e.$router.push("/sku/"+c.sku_id)}}},[t("h5",[e._v(e._s(c.sku_name))])]),e._v(" "),0==n.order_status?t("div",{staticClass:"order-time"},[t("img",{staticStyle:{width:"15px",height:"15px","margin-right":"6px"},attrs:{src:r(570)}}),e._v("距离订单自动关闭，剩余\n                "),t("count-down",{staticClass:"count-down",staticStyle:{color:"#f00","margin-left":"10px"},attrs:{currentTime:n.currentTime,startTime:n.startTime,endTime:n.endTime,dayTxt:":",hourTxt:":",minutesTxt:":",secondsTxt:""},on:{start_callback:function(t){return e.countDownS_cb()},end_callback:function(t){return e.countDownE_cb()}}})],1):e._e()]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s(c.price))])]),e._v(" "),t("li",[t("span",[e._v(e._s(c.num))])]),e._v(" "),t("li",[t("span",[e._v("￥"+e._s(n.pay_money))])]),e._v(" "),0==d?[t("li",[t("span",{staticClass:"ns-text-color"},[e._v(e._s(n.order_status_name))]),e._v(" "),t("el-link",{attrs:{underline:!1},on:{click:function(t){return e.orderDetail(n)}}},[e._v("订单详情")])],1),e._v(" "),t("li",[n.action.length>0?[1==n.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation",n)}}},[0==n.evaluate_status?[e._v("评价")]:1==n.evaluate_status?[e._v("追评")]:e._e()],2):e._e(),e._v(" "),e._l(n.action,(function(r,o){return t("el-button",{key:o,attrs:{type:"primary",size:"mini",plain:"orderPay"!=r.action},on:{click:function(t){return e.operation(r.action,n)}}},[e._v("\n                    "+e._s(r.title)+"\n                  ")])}))]:0==n.action.length&&1==n.is_evaluate?[1==n.is_evaluate?t("el-button",{attrs:{type:"primary",size:"mini",plain:""},on:{click:function(t){return e.operation("memberOrderEvaluation",n)}}},[0==n.evaluate_status?[e._v("评价")]:1==n.evaluate_status?[e._v("追评")]:e._e()],2):e._e()]:e._e()],2)]:e._e()],2)}))],2)})),0):e.loading||0!=e.orderList.length?e._e():t("div",{staticClass:"empty-wrap"},[e._v("暂无相关订单")])]),e._v(" "),t("div",{staticClass:"pager"},[t("el-pagination",{attrs:{background:"","pager-count":5,total:e.total,"prev-text":"上一页","next-text":"下一页","current-page":e.currentPage,"page-size":e.pageSize,"hide-on-single-page":""},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handlePageSizeChange,"current-change":e.handleCurrentPageChange}})],1)],1)],1)}),[],!1,null,"1cc679ec",null);t.default=component.exports}}]);