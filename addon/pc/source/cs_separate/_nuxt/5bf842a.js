(window.webpackJsonp=window.webpackJsonp||[]).push([[22],{535:function(t,e,r){"use strict";var n=r(3),o=r(35),c=r(14),l=r(8),path=r(319),f=r(5),v=r(113),m=r(16),d=r(205),_=r(63),I=r(112),h=r(318),N=r(4),O=r(93).f,y=r(57).f,w=r(26).f,k=r(320),j=r(316).trim,E="Number",T=l[E],C=path[E],S=T.prototype,P=l.TypeError,x=f("".slice),A=f("".charCodeAt),$=function(t){var e=h(t,"number");return"bigint"==typeof e?e:F(e)},F=function(t){var e,r,n,o,c,l,f,code,v=h(t,"number");if(I(v))throw P("Cannot convert a Symbol value to a number");if("string"==typeof v&&v.length>2)if(v=j(v),43===(e=A(v,0))||45===e){if(88===(r=A(v,2))||120===r)return NaN}else if(48===e){switch(A(v,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+v}for(l=(c=x(v,2)).length,f=0;f<l;f++)if((code=A(c,f))<48||code>o)return NaN;return parseInt(c,n)}return+v},G=v(E,!T(" 0o1")||!T("0b1")||T("+0x1")),L=function(t){return _(S,t)&&N((function(){k(t)}))},D=function(t){var e=arguments.length<1?0:T($(t));return L(this)?d(Object(e),this,D):e};D.prototype=S,G&&!o&&(S.constructor=D),n({global:!0,constructor:!0,wrap:!0,forced:G},{Number:D});var M=function(t,source){for(var e,r=c?O(source):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),n=0;r.length>n;n++)m(source,e=r[n])&&!m(t,e)&&w(t,e,y(source,e))};o&&C&&M(path[E],C),(G||o)&&M(path[E],T)},542:function(t,e,r){var n=r(3),o=r(548);n({target:"Number",stat:!0,forced:Number.parseInt!=o},{parseInt:o})},548:function(t,e,r){var n=r(8),o=r(4),c=r(5),l=r(19),f=r(316).trim,v=r(210),m=n.parseInt,d=n.Symbol,_=d&&d.iterator,I=/^[+-]?0x/i,h=c(I.exec),N=8!==m(v+"08")||22!==m(v+"0x16")||_&&!o((function(){m(Object(_))}));t.exports=N?function(t,e){var r=f(l(t));return m(r,e>>>0||(h(I,r)?16:10))}:m},557:function(t,e,r){},573:function(t,e,r){"use strict";r(557)},656:function(t,e,r){"use strict";r.r(e);r(321),r(542),r(535),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var n=r(10),o=r(12);function c(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var l={name:"floor-style-1",props:{data:{type:Object}},data:function(){return{}},created:function(){},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(o.b)(["defaultGoodsImage"])),methods:{goSku:function(t){this.$util.pushToTab("/sku/"+t)},imageError:function(t){this.data.value.goodsList.value.list[t].goods_image=this.defaultGoodsImage}}},f=l,v=(r(573),r(6)),component=Object(v.a)(f,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"floor-style-1"},[t.data.value.title.value.text?e("div",{staticClass:"head-wrap"},[e("h2",{style:{color:t.data.value.title.value.color},on:{click:function(e){return t.$util.pushToTab(t.data.value.title.value.link.url)}}},[t._v(t._s(t.data.value.title.value.text))])]):t._e(),t._v(" "),e("div",{staticClass:"body-wrap"},[t.data.value.leftImg.value.url?e("div",{staticClass:"left-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.leftImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.leftImg.value.link.url)}}})]):t._e(),t._v(" "),e("ul",{staticClass:"goods-list"},t._l(t.data.value.goodsList.value.list,(function(r,n){return e("li",{key:n,attrs:{title:r.goods_name},on:{click:function(e){return t.goSku(r.sku_id)}}},[e("div",{staticClass:"img-wrap"},[e("img",{attrs:{alt:"商品图片",src:t.$img(r.goods_image.split(",")[0],{size:"mid"})},on:{error:function(e){return t.imageError(n)}}})]),t._v(" "),e("h3",[t._v(t._s(r.goods_name))]),t._v(" "),e("p",{staticClass:"desc"},[t._v(t._s(r.introduction))]),t._v(" "),e("p",{staticClass:"price"},[e("span",{staticClass:"num"},[t._v(t._s(r.discount_price)+"元")]),t._v(" "),e("del",{directives:[{name:"show",rawName:"v-show",value:Number.parseInt(r.market_price),expression:"Number.parseInt(item.market_price)"}]},[t._v(t._s(r.market_price)+"元")])])])})),0)]),t._v(" "),t.data.value.bottomImg.value.url?e("div",{staticClass:"bottom-wrap"},[e("img",{attrs:{src:t.$img(t.data.value.bottomImg.value.url)},on:{click:function(e){return t.$util.pushToTab(t.data.value.bottomImg.value.link.url)}}})]):t._e()])}),[],!1,null,"e9a00f52",null);e.default=component.exports}}]);