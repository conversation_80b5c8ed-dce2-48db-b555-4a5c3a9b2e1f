(window.webpackJsonp=window.webpackJsonp||[]).push([[17],{545:function(t,e,c){t.exports=c.p+"img/goods_empty.288af96.png"},589:function(t,e,c){},677:function(t,e,c){"use strict";c(589)},750:function(t,e,c){"use strict";c.r(e);c(25),c(23),c(29),c(30);var n=c(10),r=(c(7),c(18),c(24),c(315),c(74),c(209),c(12)),o=c(62);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(object);t&&(c=c.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,c)}return e}var d={data:function(){return{cartList:[],checkAll:!1,totalPrice:"0.00",totalCount:0,invalidGoods:[],loading:!0,modifyNum:1}},created:function(){this.getCartList()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(r.b)(["defaultGoodsImage"])),middleware:"auth",methods:{getCartList:function(){var t=this;Object(o.b)({}).then((function(e){e.code>=0&&e.data.length&&t.handleCartList(e.data),t.loading=!1})).catch((function(e){t.loading=!1}))},handleCartList:function(data){var t=this;this.invalidGoods=[],this.cartList=[];var e={};data.forEach((function(c,n){1==c.goods_state?(c.checked=!0,null!=e["site_"+c.site_id]?e["site_"+c.site_id].cartList.push(c):e["site_"+c.site_id]={siteId:c.site_id,siteName:c.site_name,checked:!0,cartList:[c]}):t.invalidGoods.push(c)})),this.invalidGoods.forEach((function(t){t.sku_spec_format?t.sku_spec_format=JSON.parse(t.sku_spec_format):t.sku_spec_format=[]})),Object.keys(e).forEach((function(c){t.cartList.push(e[c])})),this.calculationTotalPrice(),this.cartList.forEach((function(t){t.cartList.forEach((function(t){t.sku_spec_format?t.sku_spec_format=JSON.parse(t.sku_spec_format):t.sku_spec_format=[]}))}))},singleElection:function(t,e){this.calculationTotalPrice()},siteAllElection:function(t){var e=this;this.cartList[t].cartList.forEach((function(c){c.checked=e.cartList[t].checked})),this.calculationTotalPrice()},allElection:function(){var t=this;this.cartList.length&&this.cartList.forEach((function(e){e.checked=t.checkAll,e.cartList.forEach((function(e){e.checked=t.checkAll}))})),this.calculationTotalPrice()},calculationTotalPrice:function(){if(this.cartList.length){var t=0,e=0,c=0;this.cartList.forEach((function(n){var r=0;n.cartList.forEach((function(c){c.checked&&(r+=1,e+=1,t+=c.discount_price*c.num)})),n.cartList.length==r?(n.checked=!0,c+=1):n.checked=!1})),this.totalPrice=t.toFixed(2),this.totalCount=e,this.checkAll=this.cartList.length==c}else this.totalPrice="0.00",this.totalCount=0;this.modifyNum=1},deleteCart:function(t,e){var c=this;this.$confirm("确定要删除该商品吗？","提示信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c.$store.dispatch("cart/delete_cart",{cart_id:c.cartList[t].cartList[e].cart_id.toString()}).then((function(n){n.code>=0?(c.cartList[t].cartList.splice(e,1),0==c.cartList[t].cartList.length&&c.cartList.splice(t,1),c.calculationTotalPrice(),c.$message({type:"success",message:"删除成功"})):c.$message({message:n.message,type:"warning"})})).catch((function(t){c.$message.error(t.message)}))}))},deleteCartSelected:function(){var t=this,e=[],c=[];this.cartList.forEach((function(t,n){t.cartList.forEach((function(r,o){r.checked&&(e.push(r.cart_id),c.push({siteIndex:n,cartIndex:o,siteId:t.siteId,cartId:r.cart_id}))}))})),0!=e.length?this.$confirm("确定要删除选择的商品吗？","提示信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$store.dispatch("cart/delete_cart",{cart_id:e.toString()}).then((function(e){e.code>=0?(c.forEach((function(e){t.cartList.forEach((function(c,n){c.cartList.forEach((function(r,o){e.cartId==r.cart_id&&c.cartList.splice(o,1),0==c.cartList.length&&t.cartList.splice(n,1)}))}))})),t.calculationTotalPrice(),t.$message({type:"success",message:"删除成功"})):t.$message({message:e.message,type:"warning"})})).catch((function(e){t.$message.error(e.message)}))})):this.$message({message:"请选择要删除的商品",type:"warning"})},clearInvalidGoods:function(){var t=this;this.$confirm("确认要清空这些商品吗？","提示信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=[];t.invalidGoods.forEach((function(t){e.push(t.cart_id)})),e.length&&t.$store.dispatch("cart/delete_cart",{cart_id:e.toString()}).then((function(e){e.code>=0?(t.invalidGoods=[],t.$message({type:"success",message:"删除成功"})):t.$message({message:e.message,type:"warning"})})).catch((function(e){t.$message.error(e.message)}))}))},cartNumChange:function(t,e){var c=this;(t<1||!t)&&(t=1),this.modifyNum=0,this.$store.dispatch("cart/edit_cart_num",{num:t,cart_id:this.cartList[e.siteIndex].cartList[e.cartIndex].cart_id}).then((function(n){n.code>=0?(c.cartList[e.siteIndex].cartList[e.cartIndex].num=t,c.calculationTotalPrice()):(c.$message({message:n.message,type:"warning"}),c.modifyNum=1)})).catch((function(t){c.$message.error(t.message),c.modifyNum=1}))},settlement:function(){if(this.totalCount>0){var t=[];if(this.cartList.forEach((function(e){e.cartList.forEach((function(e){e.checked&&t.push(e.cart_id)}))})),t.length>100)return void this.$message({message:"购物车最多支持100个商品一起购买",type:"warning"});var data={cart_ids:t.toString()};this.$store.dispatch("order/setOrderCreateData",data),this.$router.push({path:"/order/payment"})}},imageError:function(t,e){this.cartList[t].cartList[e].sku_image=this.defaultGoodsImage},imageErrorInvalid:function(t){this.invalidGoods[t].sku_image=this.defaultGoodsImage}}},_={name:"cart",mixins:[d]},h=(c(677),c(6)),component=Object(h.a)(_,(function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"cart"},[t.cartList.length||t.invalidGoods.length?[e("nav",[e("li",[e("el-checkbox",{on:{change:t.allElection},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}})],1),t._v(" "),e("li",[t._v("商品信息")]),t._v(" "),e("li",[t._v("价格")]),t._v(" "),e("li",[t._v("数量")]),t._v(" "),e("li",[t._v("小计")]),t._v(" "),e("li",[t._v("操作")])]),t._v(" "),t._l(t.cartList,(function(c,n){return e("div",{key:n,staticClass:"list"},[e("div",{staticClass:"item"},t._l(c.cartList,(function(c,r){return e("ul",{key:r},[e("li",[e("el-checkbox",{on:{change:function(e){return t.singleElection(n,r)}},model:{value:c.checked,callback:function(e){t.$set(c,"checked",e)},expression:"item.checked"}})],1),t._v(" "),e("li",{staticClass:"goods-info-wrap",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+c.sku_id})}}},[e("div",{staticClass:"img-wrap"},[e("img",{staticClass:"img-thumbnail",attrs:{src:t.$img(c.sku_image,{size:"mid"})},on:{error:function(e){return t.imageError(n,r)}}})]),t._v(" "),e("div",{staticClass:"info-wrap"},[e("h5",[t._v(t._s(c.sku_name))]),t._v(" "),c.sku_spec_format?t._l(c.sku_spec_format,(function(n,i){return e("span",{key:i},[t._v("\n                    "+t._s(n.spec_name)+"："+t._s(n.spec_value_name)+" "+t._s(i<c.sku_spec_format.length-1?"；":"")+"\n                  ")])})):t._e()],2)]),t._v(" "),e("li",[e("span",[t._v("￥"+t._s(c.discount_price))])]),t._v(" "),e("li",[e("el-input-number",{attrs:{step:t.modifyNum,size:"mini",min:c.min_buy>0?c.min_buy:1,max:c.max_buy>0&&c.max_buy<c.stock?c.max_buy:c.stock},on:{change:function(e){return t.cartNumChange(e,{siteIndex:n,cartIndex:r})}},model:{value:c.num,callback:function(e){t.$set(c,"num",e)},expression:"item.num"}})],1),t._v(" "),e("li",[c.discount_price*c.num?e("strong",{staticClass:"subtotal ns-text-color"},[t._v("￥"+t._s(t.$util.filterPrice(c.discount_price*c.num)))]):e("strong",{staticClass:"subtotal ns-text-color"},[t._v("￥0")])]),t._v(" "),e("li",[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.deleteCart(n,r)}}},[t._v("删除")])],1)])})),0)])})),t._v(" "),t.invalidGoods.length?e("div",{staticClass:"lose-list"},[e("div",{staticClass:"head"},[t._v("\n          失效商品\n          "),e("span",{staticClass:"ns-text-color"},[t._v(t._s(t.invalidGoods.length))]),t._v("\n          件\n        ")]),t._v(" "),t._l(t.invalidGoods,(function(c,n){return e("ul",{key:n},[e("li",[e("el-tag",{attrs:{size:"small",type:"info"}},[t._v("失效")])],1),t._v(" "),e("li",{staticClass:"goods-info-wrap"},[e("div",{staticClass:"img-wrap"},[e("img",{staticClass:"img-thumbnail",attrs:{src:t.$img(c.sku_image,{size:"mid"})},on:{error:function(e){return t.imageErrorInvalid(n)}}})]),t._v(" "),e("div",{staticClass:"info-wrap"},[e("h5",[t._v(t._s(c.sku_name))]),t._v(" "),c.sku_spec_format?t._l(c.sku_spec_format,(function(n,i){return e("span",{key:i},[t._v("\n                  "+t._s(n.spec_name)+"："+t._s(n.spec_value_name)+t._s(i<c.sku_spec_format.length-1?"；":"")+"\n                ")])})):t._e()],2)]),t._v(" "),e("li",[e("span",[t._v("￥"+t._s(c.discount_price))])]),t._v(" "),e("li",[t._v(t._s(c.num))]),t._v(" "),e("li",[e("strong",{staticClass:"subtotal"},[t._v("￥"+t._s(t.$util.filterPrice(c.discount_price*c.num)))])])])}))],2):t._e(),t._v(" "),e("footer",[e("el-checkbox",{on:{change:t.allElection},model:{value:t.checkAll,callback:function(e){t.checkAll=e},expression:"checkAll"}},[t._v("全选")]),t._v(" "),e("ul",{staticClass:"operation"},[e("li",[e("el-button",{attrs:{type:"text"},on:{click:t.deleteCartSelected}},[t._v("删除")])],1),t._v(" "),e("li",[0!=t.invalidGoods.length?e("el-button",{attrs:{type:"text"},on:{click:t.clearInvalidGoods}},[t._v("清除失效宝贝")]):t._e()],1)]),t._v(" "),e("div",{staticClass:"sum-wrap"},[e("div",{staticClass:"selected-sum"},[e("span",[t._v("已选商品")]),t._v(" "),e("em",{staticClass:"total-count"},[t._v(t._s(t.totalCount))]),t._v(" "),e("span",[t._v("件")])]),t._v(" "),e("div",{staticClass:"price-wrap"},[e("span",[t._v("合计（不含运费）：")]),t._v(" "),e("strong",{staticClass:"ns-text-color"},[t._v("￥"+t._s(t.totalPrice))])]),t._v(" "),0!=t.totalCount?e("el-button",{attrs:{type:"primary"},on:{click:t.settlement}},[t._v("结算")]):e("el-button",{attrs:{type:"info",disabled:""},on:{click:t.settlement}},[t._v("结算")])],1)],1)]:t.loading||t.cartList.length&&t.invalidGoods.length?t._e():e("div",{staticClass:"empty-wrap"},[e("img",{attrs:{src:c(545)}}),t._v(" "),e("router-link",{attrs:{to:"/"}},[t._v("您的购物车是空的，赶快去逛逛，挑选商品吧！")])],1)],2)}),[],!1,null,"12619470",null);e.default=component.exports}}]);