(window.webpackJsonp=window.webpackJsonp||[]).push([[47,48],{550:function(e,t,r){"use strict";r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return n})),r.d(t,"a",(function(){return c}));var l=r(1);function o(e){return Object(l.a)({url:"/api/member/info",data:e,forceLogin:!0})}function n(e){return Object(l.a)({url:"/api/member/modifynickname",data:e,forceLogin:!0})}function c(e){return Object(l.a)({url:"/api/member/modifyheadimg",data:e,forceLogin:!0})}},551:function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"h",(function(){return n})),r.d(t,"g",(function(){return c})),r.d(t,"a",(function(){return m})),r.d(t,"b",(function(){return d})),r.d(t,"i",(function(){return f})),r.d(t,"c",(function(){return h})),r.d(t,"e",(function(){return v})),r.d(t,"f",(function(){return y}));var l=r(1);function o(e){return Object(l.a)({url:"/api/member/modifypassword",data:e,forceLogin:!0})}function n(e){return Object(l.a)({url:"/api/member/bindmobliecode",data:e,forceLogin:!0})}function c(e){return Object(l.a)({url:"/api/member/modifymobile",data:e,forceLogin:!0})}function m(e){return Object(l.a)({url:"/api/member/checkemail",data:e,forceLogin:!0})}function d(e){return Object(l.a)({url:"/api/member/modifyemail",data:e,forceLogin:!0})}function f(e){return Object(l.a)({url:"/api/member/verifypaypwdcode",data:e,forceLogin:!0})}function h(e){return Object(l.a)({url:"/api/member/modifypaypassword",data:e,forceLogin:!0})}function v(e){return Object(l.a)({url:"/api/member/paypwdcode",data:e,forceLogin:!0})}function y(e){return Object(l.a)({url:"/api/member/pwdmobliecode",data:e,forceLogin:!0})}},616:function(e,t,r){"use strict";r.r(t);var l=r(21),o=(r(95),r(31),r(73),r(64),r(551)),n=r(550),c=r(27);t.default={name:"security",components:{},data:function(){var e=this;return{type:"all",passWordForm:{oldPass:"",pass:"",checkPass:""},emailForm:{email:"",code:"",emailDynacode:"",emailCodeText:"",key:"",currEmail:""},passWordRules:{oldPass:[{required:!0,message:"请输入原密码",trigger:"blur"}],pass:[{required:!0,validator:function(t,r,l){""===r?l(new Error("请输入新密码")):r==e.passWordForm.oldPass?l(new Error("新密码不能与原密码相同！")):(""!==e.passWordForm.checkPass&&e.$refs.passWordRef.validateField("checkPass"),l())},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,r,l){""===r?l(new Error("请再次输入密码")):r!==e.passWordForm.pass?l(new Error("两次输入密码不一致!")):l()},trigger:"blur"}]},emailRules:{email:[{required:!0,message:"请输入正确的邮箱",trigger:"blur"},{validator:function(e,t,r){if(/^\w+@\w+(\.\w+)+$/.test(t))return r();r(new Error("请输入正确的的邮箱"))},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}],emailDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}]},captcha:{id:"",img:""},seconds:120,timer:null,isSend:!1,isMobileSend:!1,tellForm:{tell:"",code:"",tellDynacode:"",tellCodeText:"",key:"",currTell:""},tellRules:{tell:[{required:!0,message:"请输入正确的手机号",trigger:"blur"},{validator:function(e,t,r){if(/^1[3|4|5|6|7|8|9][0-9]{9}$/.test(t))return r();r(new Error("请输入正确的手机号"))},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}],tellDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}]},isClick:!0,payCodeText:"获取验证码",step:0,payCode:"",payPassword:"",payRepassword:"",payKey:"",payInput:"",palceText:"输入短信验证码",memberInfo:{},tellPassForm:{code:"",tellPassCodeText:"",key:"",tellPassDynacode:"",pass:"",checkPass:""},tellPassRules:{code:[{required:!0,message:"请输入验证码",trigger:"blur"}],tellPassDynacode:[{required:!0,message:"请输入动态验证码",trigger:"blur"}],pass:[{required:!0,validator:function(t,r,l){""===r?l(new Error("请输入新密码")):r==e.tellPassForm.oldPass?l(new Error("新密码不能与原密码相同！")):(""!==e.tellPassForm.checkPass&&e.$refs.tellPassRef.validateField("checkPass"),l())},trigger:"blur"}],checkPass:[{required:!0,validator:function(t,r,l){""===r?l(new Error("请再次输入密码")):r!==e.tellPassForm.pass?l(new Error("两次输入密码不一致!")):l()},trigger:"blur"}]},loading:!0,yes:!0}},created:function(){this.getcaptcha(),this.seconds=120,this.tellForm.tellCodeText="获取动态码",this.emailForm.emailCodeText="获取动态码",this.tellPassForm.tellPassCodeText="获取动态码",this.isSend=!1,this.isMobileSend=!1,clearInterval(this.timer),this.getInfo()},mounted:function(){var e=this;setTimeout((function(){e.yes=!1}),300)},methods:{getInfo:function(){var e=this;return Object(l.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(n.b)().then((function(t){0==t.code&&(e.memberInfo=t.data,e.emailForm.currEmail=t.data.email,e.tellForm.currTell=t.data.mobile),e.loading=!1})).catch((function(t){e.loading=!1,e.$message.error(t.message)}));case 2:case"end":return t.stop()}}),t)})))()},edit:function(e){var t=this;return Object(l.a)(regeneratorRuntime.mark((function r(){return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,t.getInfo();case 2:"payPassWord"==e?t.tellForm.currTell?t.type=e:t.$confirm("你还未绑定手机号，请先绑定手机号？","提示信息",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(e){"confirm"?t.type="tell":t.type="all"})):t.type=e;case 3:case"end":return r.stop()}}),r)})))()},getcaptcha:function(){var e=this;Object(c.b)({captcha_id:this.captcha.id}).then((function(t){t.code>=0&&(e.captcha=t.data,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))})).catch((function(t){e.$message.error(t.message)}))},save:function(){var e=this;this.$refs.passWordRef.validate((function(t){if(!t)return!1;Object(o.d)({new_password:e.passWordForm.pass,old_password:e.passWordForm.oldPass}).then((function(t){e.$message({message:"修改密码成功",type:"success"}),e.type="all",e.$store.dispatch("member/member_detail",{refresh:1}),e.passWordForm.pass="",e.passWordForm.oldPass="",e.passWordForm.checkPass=""})).catch((function(t){e.$message.error(t.message)}))}))},getCheckEmail:function(){var e=this;return Object(l.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(o.a)({email:e.emailForm.email}).then((function(t){return 0==t.code||(e.$message({message:t.message,type:"success"}),!1)})).catch((function(t){e.$message.error(t.message)}));case 2:return r=t.sent,t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)})))()},bindEmail:function(){var e=this;return Object(l.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$refs.emailRef.validate((function(t){if(!t)return!1;Object(o.b)({email:e.emailForm.email,captcha_id:e.captcha.id,captcha_code:e.emailForm.code,code:e.emailForm.emailDynacode,key:e.emailForm.key}).then((function(t){0==t.code&&(e.$message({message:"邮箱绑定成功",type:"success"}),e.type="all",e.emailForm.email="",e.emailForm.code="",e.emailForm.emailDynacode="",clearInterval(e.timer),e.getcaptcha())})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)}))}));case 1:case"end":return t.stop()}}),t)})))()},gettellCode:function(){var e=this;return Object(l.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.isMobileSend){t.next=6;break}return e.isMobileSend=!0,t.next=4,Object(o.h)({mobile:e.tellForm.tell,captcha_id:e.captcha.id,captcha_code:e.tellForm.code}).then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.tellForm.tellCodeText="已发送("+e.seconds+"s)"}),1e3)),e.tellForm.key=data.key):(e.$message({message:t.message,type:"warning"}),e.isMobileSend=!1)})).catch((function(t){e.getcaptcha(),e.$message.error(t.message),"当前手机号已存在"==t.message&&(e.isMobileSend=!1)}));case 4:t.next=7;break;case 6:e.$message({message:"请勿重复点击",type:"warning"});case 7:case"end":return t.stop()}}),t)})))()},bindtell:function(){var e=this;this.$refs.tellRef.validate((function(t){if(!t)return!1;Object(o.g)({mobile:e.tellForm.tell,captcha_id:e.captcha.id,captcha_code:e.tellForm.code,code:e.tellForm.tellDynacode,key:e.tellForm.key}).then((function(t){0==t.code&&(e.$message({message:"手机号绑定成功",type:"success"}),e.type="all",e.tellForm.email="",e.tellForm.code="",e.tellForm.emailDynacode="",clearInterval(e.timer),e.getcaptcha())})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)}))}))},input:function(e){this.isClick=!1,0==this.step&&4==e.length?this.payCode=e:1==this.step&&6==e.length?this.payPassword=e:6==e.length&&(this.payRepassword=e)},sendMobileCode:function(){var e=this;this.isSend||Object(o.e)().then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.payCodeText="已发送("+e.seconds+"s)"}),1e3)),e.payKey=data.key):(e.$message({message:t.message,type:"warning"}),e.isSend=!1)})).catch((function(t){e.$message.error(t.message)}))},bindPayPwd:function(){var e=this;clearInterval(this.timer);if(0==this.step)Object(o.i)({code:this.payCode,key:this.payKey}).then((function(t){0==t.code&&(e.$refs.input.clear(),e.step=1,e.palceText="请设置支付密码")})).catch((function(t){e.$message.error(t.message)}));else if(1==this.step)/^[0-9]*$/.test(this.$refs.input.value)?(this.$refs.input.clear(),this.step=2,this.palceText="请再次输入"):(this.$message.error("请输入数字"),this.step=1,this.$refs.input.clear());else if(this.payPassword==this.payRepassword){if(this.isSub)return;this.isSub=!0,Object(o.c)({key:this.payKey,code:this.payCode,password:this.payPassword}).then((function(t){t.code>=0&&(e.$message({message:"修改支付密码成功",type:"success"}),e.type="all",e.step=0,e.$refs.input.clear(),clearInterval(e.timer))})).catch((function(t){e.$message.error(t.message)}))}else this.$message.error("两次密码输入不一样"),this.initInfo()},initInfo:function(){this.step=1,this.palceText="请设置支付密码",this.password="",this.repassword="",this.oldpassword="",this.isSub=!1,this.$refs.input.clear()},getTellPassCode:function(){var e=this;this.isSend?this.$message({message:"请勿重复点击",type:"warning"}):(this.isSend=!0,Object(o.f)({captcha_id:this.captcha.id,captcha_code:this.tellPassForm.code}).then((function(t){var data=t.data;data.key?(120==e.seconds&&null==e.timer&&(e.timer=setInterval((function(){e.seconds--,e.tellPassForm.tellPassCodeText="已发送("+e.seconds+"s)"}),1e3)),e.tellPassForm.key=data.key):(e.$message({message:t.message,type:"warning"}),e.isSend=!1)})).catch((function(t){e.getcaptcha(),e.$message.error(t.message)})))},tellPassSave:function(){var e=this;this.$refs.tellPassRef.validate((function(t){if(!t)return!1;Object(o.d)({new_password:e.tellPassForm.pass,code:e.tellPassForm.tellPassDynacode,key:e.tellPassForm.key}).then((function(t){e.$message({message:"修改密码成功",type:"success"}),e.type="all",e.$store.dispatch("member/member_detail",{refresh:1}),e.tellPassForm.pass="",e.tellPassForm.checkPass="",e.tellPassForm.key="",e.tellPassForm.tellPassDynacode=""})).catch((function(t){e.$message.error(t.message)}))}))}},filters:{mobile:function(e){return e.substring(0,3)+"****"+e.substring(7)}}}},617:function(e,t,r){},709:function(e,t,r){"use strict";r(617)},788:function(e,t,r){"use strict";r.r(t);var l={name:"security",layout:"member",mixins:[r(616).default]},o=(r(709),r(6)),component=Object(o.a)(l,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"box"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.yes,expression:"yes"}],staticClass:"null-page"}),e._v(" "),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"security"},["all"==e.type?t("div",{staticClass:"item-wrap"},[t("div",{staticClass:"item"},[e._m(0),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(t){return e.edit("password")}}},[e._v("修改")])],1)]),e._v(" "),t("div",{staticClass:"item"},[e._m(1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(t){return e.edit("tell")}}},[e._v("修改")])],1)])]):e._e(),e._v(" "),"password"==e.type?t("div",{staticClass:"edit"},[t("div",{staticClass:"title"},[e._v("修改登录密码")]),e._v(" "),e.memberInfo.password?t("div",[t("div",{staticClass:"pass-form"},[t("el-form",{ref:"passWordRef",attrs:{model:e.passWordForm,rules:e.passWordRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"原密码",prop:"oldPass"}},[t("el-input",{attrs:{type:"password",placeholder:"当前密码"},model:{value:e.passWordForm.oldPass,callback:function(t){e.$set(e.passWordForm,"oldPass",t)},expression:"passWordForm.oldPass"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"新密码",prop:"pass"}},[t("el-input",{attrs:{type:"password",placeholder:"新密码"},model:{value:e.passWordForm.pass,callback:function(t){e.$set(e.passWordForm,"pass",t)},expression:"passWordForm.pass"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"确认密码",prop:"checkPass"}},[t("el-input",{attrs:{type:"password",placeholder:"请确认新密码"},model:{value:e.passWordForm.checkPass,callback:function(t){e.$set(e.passWordForm,"checkPass",t)},expression:"passWordForm.checkPass"}})],1)],1)],1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")]),e._v(" "),t("el-button",{on:{click:function(t){e.type="all"}}},[e._v("取消")])],1)]):t("div",{staticClass:"tell-pass"},[t("el-form",{ref:"tellPassRef",attrs:{model:e.tellPassForm,rules:e.tellPassRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"验证码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入验证码",maxlength:"4"},model:{value:e.tellPassForm.code,callback:function(t){e.$set(e.tellPassForm,"code",t)},expression:"tellPassForm.code"}},[t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getcaptcha}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{label:"动态码",prop:"tellPassDynacode"}},[t("el-input",{attrs:{placeholder:"请输入动态码"},model:{value:e.tellPassForm.tellPassDynacode,callback:function(t){e.$set(e.tellPassForm,"tellPassDynacode",t)},expression:"tellPassForm.tellPassDynacode"}},[t("template",{slot:"append"},[t("el-button",{attrs:{type:"primary"},on:{click:e.getTellPassCode}},[e._v(e._s(e.tellPassForm.tellPassCodeText))])],1)],2)],1),e._v(" "),t("p",{staticClass:"tell-code"},[e._v("点击“获取动态码”，将会向您已绑定的手机号"+e._s(e._f("mobile")(e.memberInfo.mobile))+"发送验证码")]),e._v(" "),t("el-form-item",{attrs:{label:"新密码",prop:"pass"}},[t("el-input",{attrs:{type:"password",placeholder:"新密码"},model:{value:e.tellPassForm.pass,callback:function(t){e.$set(e.tellPassForm,"pass",t)},expression:"tellPassForm.pass"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"确认密码",prop:"checkPass"}},[t("el-input",{attrs:{type:"password",placeholder:"请确认新密码"},model:{value:e.tellPassForm.checkPass,callback:function(t){e.$set(e.tellPassForm,"checkPass",t)},expression:"tellPassForm.checkPass"}})],1)],1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary"},on:{click:e.tellPassSave}},[e._v("保存")]),e._v(" "),t("el-button",{on:{click:function(t){e.type="all"}}},[e._v("取消")])],1)],1)]):e._e(),e._v(" "),"email"==e.type?t("div",{staticClass:"edit"},[t("div",{staticClass:"title"},[e._v("绑定邮箱")]),e._v(" "),t("div",{staticClass:"pass-form"},[t("el-form",{ref:"emailRef",attrs:{model:e.emailForm,rules:e.emailRules,"label-width":"100px"}},[e.emailForm.currEmail?t("el-form-item",{attrs:{label:"当前邮箱",prop:"email"}},[t("p",[e._v(e._s(e.emailForm.currEmail))])]):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[t("el-input",{attrs:{type:"email",placeholder:"请输入邮箱"},model:{value:e.emailForm.email,callback:function(t){e.$set(e.emailForm,"email",t)},expression:"emailForm.email"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"验证码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入验证码",maxlength:"4"},model:{value:e.emailForm.code,callback:function(t){e.$set(e.emailForm,"code",t)},expression:"emailForm.code"}},[t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getcaptcha}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{label:"动态码",prop:"emailDynacode"}},[t("el-input",{attrs:{placeholder:"请输入动态码"},model:{value:e.emailForm.emailDynacode,callback:function(t){e.$set(e.emailForm,"emailDynacode",t)},expression:"emailForm.emailDynacode"}},[t("template",{slot:"append"},[t("el-button",{attrs:{type:"primary"},on:{click:e.getEmailCode}},[e._v(e._s(e.emailForm.emailCodeText))])],1)],2)],1)],1)],1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary"},on:{click:e.bindEmail}},[e._v("保存")]),e._v(" "),t("el-button",{on:{click:function(t){e.type="all"}}},[e._v("取消")])],1)]):e._e(),e._v(" "),"tell"==e.type?t("div",{staticClass:"edit"},[t("div",{staticClass:"title"},[e._v("绑定手机号")]),e._v(" "),t("div",{staticClass:"pass-form"},[t("el-form",{ref:"tellRef",attrs:{model:e.tellForm,rules:e.tellRules,"label-width":"100px"}},[e.tellForm.currTell?t("el-form-item",{attrs:{label:"当前手机号",prop:"email"}},[t("p",[e._v(e._s(e.tellForm.currTell))])]):e._e(),e._v(" "),t("el-form-item",{attrs:{label:"手机号",prop:"tell"}},[t("el-input",{attrs:{type:"tell",placeholder:"请输入手机号"},model:{value:e.tellForm.tell,callback:function(t){e.$set(e.tellForm,"tell",t)},expression:"tellForm.tell"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"验证码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入验证码",maxlength:"4"},model:{value:e.tellForm.code,callback:function(t){e.$set(e.tellForm,"code",t)},expression:"tellForm.code"}},[t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getcaptcha}})])],2)],1),e._v(" "),t("el-form-item",{attrs:{label:"动态码",prop:"tellDynacode"}},[t("el-input",{attrs:{placeholder:"请输入动态码"},model:{value:e.tellForm.tellDynacode,callback:function(t){e.$set(e.tellForm,"tellDynacode",t)},expression:"tellForm.tellDynacode"}},[t("template",{slot:"append"},[t("el-button",{attrs:{type:"primary"},on:{click:e.gettellCode}},[e._v(e._s(e.tellForm.tellCodeText))])],1)],2)],1)],1)],1),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary"},on:{click:e.bindtell}},[e._v("保存")]),e._v(" "),t("el-button",{on:{click:function(t){e.type="all"}}},[e._v("取消")])],1)]):e._e(),e._v(" "),"payPassWord"==e.type?t("div",{staticClass:"edit-pay"},[t("div",{staticClass:"title"},[e._v("绑定支付密码")]),e._v(" "),t("div",{staticClass:"container"},[0!=e.step?t("div",{staticClass:"name"},[e._v("请输入6位支付密码，建议不要使用重复或连续数字")]):e.isSend?t("div",{staticClass:"name"},[e._v("验证码已发送至"+e._s(e._f("mobile")(e.tellForm.currTell)))]):e._e(),e._v(" "),t("div",{staticClass:"password-wrap"},[0==e.step?t("el-input",{ref:"input",attrs:{maxlength:0==e.step?4:6,"auto-focus":!0,placeholder:e.palceText},on:{change:e.input},model:{value:e.payInput,callback:function(t){e.payInput=t},expression:"payInput"}}):t("el-input",{ref:"input",attrs:{maxlength:0==e.step?4:6,"auto-focus":!0,type:"password",placeholder:e.palceText},on:{change:e.input},model:{value:e.payInput,callback:function(t){e.payInput=t},expression:"payInput"}}),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:0==e.step,expression:"step == 0"}],staticClass:"dynacode",on:{click:e.sendMobileCode}},[e._v(e._s(e.payCodeText))])],1)]),e._v(" "),t("div",{staticClass:"btn"},[t("el-button",{attrs:{type:"primary",disabled:e.isClick},on:{click:e.bindPayPwd}},[e._v("保存")]),e._v(" "),t("el-button",{on:{click:function(t){e.type="all"}}},[e._v("取消")])],1)]):e._e()])])}),[function(){var e=this,t=e._self._c;return t("div",{staticClass:"item-content"},[t("i",{staticClass:"iconfont icon-xiugaidenglumima"}),e._v(" "),t("div",{staticClass:"name-wrap"},[t("div",{staticClass:"name"},[e._v("登录密码")]),e._v(" "),t("div",{staticClass:"info"},[e._v("互联网账号存在被盗风险，建议您定期更改密码以保护账户安全")])])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"item-content"},[t("i",{staticClass:"iconfont icon-shoujiyanzheng"}),e._v(" "),t("div",{staticClass:"name-wrap"},[t("div",{staticClass:"name"},[e._v("手机验证")]),e._v(" "),t("div",{staticClass:"info"},[e._v("验证后，可用于快速找回登录密码及支付密码，接收账户余额变动提醒")])])])}],!1,null,"682f61f0",null);t.default=component.exports}}]);