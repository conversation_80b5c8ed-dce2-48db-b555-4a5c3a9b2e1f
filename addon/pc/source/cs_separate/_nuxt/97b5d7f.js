(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{571:function(t,e,n){"use strict";n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return l}));var r=n(1);function c(t){return Object(r.a)({url:"/api/article/page",data:t})}function o(t){return Object(r.a)({url:"/api/article/category",data:t})}function l(t){return Object(r.a)({url:"/api/article/info",data:t})}},639:function(t,e,n){},732:function(t,e,n){"use strict";n(639)},803:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),c=(n(92),n(12)),article=n(571);function o(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var l={name:"article_detail",data:function(){return{info:{},loading:!0}},created:function(){this.id=this.$route.query.id,this.getDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?o(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):o(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["siteInfo"])),watch:{$route:function(t){this.id=t.query.id,this.getDetail()}},methods:{getDetail:function(){var t=this;Object(article.b)({article_id:this.id}).then((function(e){e.data?(t.info=e.data,t.loading=!1,window.document.title="".concat(t.info.article_title," - ").concat(t.siteInfo.site_name)):t.$router.push({path:"/cms/article/list"})})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))}}},d=l,f=(n(732),n(6)),component=Object(f.a)(d,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"article-wrap"},[e("el-breadcrumb",{staticClass:"path",attrs:{separator:"/"}},[e("el-breadcrumb-item",{staticClass:"path-home",attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/cms/article/list"}}},[t._v("文章列表")]),t._v(" "),e("el-breadcrumb-item",{staticClass:"path-help"},[t._v("文章详情")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"article-detil"},[e("div",{staticClass:"article-info"},[e("div",{staticClass:"title"},[t._v(t._s(t.info.article_title))]),t._v(" "),e("div",{staticClass:"flex-wrap"},[e("div",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(t.info.create_time)))]),t._v(" "),1==t.info.is_show_read_num?e("div",{staticClass:"num-wrap"},[e("img",{attrs:{src:t.$img("public/static/img/read.png")}}),t._v("\n          "+t._s(t.info.initial_read_num+t.info.read_num)+"\n        ")]):t._e(),t._v(" "),1==t.info.is_show_dianzan_num?e("div",{staticClass:"num-wrap"},[e("img",{attrs:{src:t.$img("public/static/img/dianzan.png")}}),t._v(" "),e("span",[t._v(t._s(t.info.initial_dianzan_num+t.info.dianzan_num))])]):t._e()])]),t._v(" "),e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.info.article_content)}})])],1)}),[],!1,null,"de941858",null);e.default=component.exports}}]);