(window.webpackJsonp=window.webpackJsonp||[]).push([[36],{561:function(t,e,r){"use strict";r.d(e,"d",(function(){return n})),r.d(e,"e",(function(){return c})),r.d(e,"a",(function(){return d})),r.d(e,"b",(function(){return l})),r.d(e,"c",(function(){return m}));var o=r(1);function n(t){return Object(o.a)({url:"/api/member/info",data:t,forceLogin:!0})}function c(t){return Object(o.a)({url:"/api/order/num",data:t,forceLogin:!0})}function d(t){return Object(o.a)({url:"/coupon/api/coupon/num",data:t,forceLogin:!0})}function l(t){return Object(o.a)({url:"/api/goodsbrowse/page",data:t,forceLogin:!0})}function m(t){return Object(o.a)({url:"/api/memberlevel/lists",data:t,forceLogin:!0})}},562:function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"a",(function(){return c}));var o=r(1);function n(t){return Object(o.a)({url:"/api/goodscollect/page",data:t,forceLogin:!0})}function c(t){return Object(o.a)({url:"/api/goodscollect/delete",data:t,forceLogin:!0})}},579:function(t,e,r){},661:function(t,e,r){t.exports=r.p+"img/coupon.182640e.png"},662:function(t,e,r){t.exports=r.p+"img/point.b7ac8d4.png"},663:function(t,e,r){t.exports=r.p+"img/balance.cded328.png"},664:function(t,e,r){t.exports=r.p+"img/member-empty.bc7a8ca.png"},665:function(t,e,r){"use strict";var o=r(3),n=r(115).findIndex,c=r(211),d="findIndex",l=!0;d in[]&&Array(1)[d]((function(){l=!1})),o({target:"Array",proto:!0,forced:l},{findIndex:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),c(d)},666:function(t,e,r){"use strict";r(579)},763:function(t,e,r){"use strict";r.r(e);r(315),r(24),r(25),r(23),r(7),r(29),r(18),r(30);var o=r(10),n=(r(73),r(665),r(561)),c=r(562),d=r(204),l=r(12);function m(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var v={name:"member",components:{},layout:"member",data:function(){return{couponNum:0,orderNum:{waitPay:0,readyDelivery:0,waitDelivery:0,refunding:0},orderList:[],orderStatus:"all",footInfo:{page:1,page_size:6},total:0,footList:[],currentPage:1,loading:!0,goodsTotal:0,state:"",growth:"",levelList:[],member_level:{},progress:0,yes:!0}},created:function(){this.getCouponNum(),this.getOrderNum(),this.getOrderList(),this.getFootprint(),this.getGoodsCollect(),this.$forceUpdate()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(l.b)(["defaultHeadImage","defaultGoodsImage","member"])),watch:{member:{handler:function(){this.member&&this.getLevelList()},immediate:!0,deep:!0}},mounted:function(){var t=this;setTimeout((function(){t.yes=!1}),300)},methods:{getLevelList:function(){var t=this;Object(n.c)().then((function(e){if(e.data&&0==e.code){t.levelList=e.data;var r=t.levelList.findIndex((function(e){return e.level_id==t.member.member_level}));t.levelList.length>r+1?t.member.growth>t.levelList[r+1].growth?t.progress=100:t.progress=t.member.growth/t.levelList[r+1].growth*100:t.progress=100}else t.$message.error(err.message)}))},getCouponNum:function(){var t=this;Object(n.a)().then((function(e){t.couponNum=e.data}))},getOrderNum:function(){var t=this;Object(n.e)({order_status:"waitpay,waitsend,waitconfirm,waitrate,refunding"}).then((function(e){0==e.code&&(t.orderNum.waitPay=e.data.waitpay,t.orderNum.readyDelivery=e.data.waitsend,t.orderNum.waitDelivery=e.data.waitconfirm,t.orderNum.waitEvaluate=e.data.waitrate,t.orderNum.refunding=e.data.refunding)}))},getOrderList:function(){var t=this;Object(d.d)({order_status:this.orderStatus,page:1,page_size:3}).then((function(e){0==e.code&&e.data&&(t.orderList=e.data.list),t.loading=!1})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},getFootprint:function(){var t=this;Object(n.b)(this.footInfo).then((function(e){0==e.code&&e.data&&(t.footList=e.data.list,t.total=e.data.count)}))},orderDetail:function(data){switch(parseInt(data.order_type)){case 2:this.$router.push({path:"/member/order_detail_pickup",query:{order_id:data.order_id}});break;case 3:this.$router.push({path:"/member/order_detail_local_delivery",query:{order_id:data.order_id}});break;case 4:this.$router.push({path:"/member/order_detail_virtual",query:{order_id:data.order_id}});break;default:this.$router.push({path:"/member/order_detail",query:{order_id:data.order_id}})}},imageErrorOrder:function(t,e){this.orderList[t].order_goods[e].sku_image=this.defaultGoodsImage},imageErrorFoot:function(t){this.footList[t].sku_image=this.defaultGoodsImage},getGoodsCollect:function(){var t=this;Object(c.b)().then((function(e){t.goodsTotal=e.data.count})).catch((function(e){t.loading=!1,console.log(e.message)}))}}},_=v,f=(r(666),r(6)),component=Object(f.a)(_,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"box"},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.yes,expression:"yes"}],staticClass:"null-page"}),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"member-index"},[e("div",{staticClass:"member-top"},[e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"info-top"},[e("div",{staticClass:"avtar"},[e("router-link",{attrs:{to:"/member/info"}},[t.member.headimg?e("img",{attrs:{src:t.$img(t.member.headimg)},on:{error:function(e){t.member.headimg=t.defaultHeadImage}}}):e("img",{attrs:{src:t.$img(t.defaultHeadImage)}})])],1),t._v(" "),e("div",{staticClass:"member-wrap"},[t.member?[t.member.nickname?e("div",{staticClass:"name member-name"},[e("router-link",{attrs:{to:"/member/info"}},[t._v(t._s(t.member.nickname))])],1):t._e(),t._v(" "),t.member.member_level_name?e("div",{staticClass:"level"},[t._v(t._s(t.member.member_level_name))]):t._e(),t._v(" "),e("div",{staticClass:"growth"},[t._v("\n                成长值：\n                "),e("el-progress",{attrs:{"text-inside":!0,"stroke-width":10,percentage:t.progress,"show-text":!1}}),t._v(" "),e("div")],1)]:e("div",{staticClass:"no-login name"},[t._v("未登录")])],2)]),t._v(" "),e("div",{staticClass:"account"},[e("div",{staticClass:"content"},[e("div",{staticClass:"item"},[e("router-link",{staticClass:"item-content",attrs:{to:"/member/coupon"}},[e("img",{attrs:{src:r(661),alt:""}}),t._v(" "),e("div",{staticClass:"name"},[t._v("优惠券")]),t._v(" "),t.member.member_id&&t.couponNum?e("div",{staticClass:"num"},[t._v(t._s(t.couponNum))]):e("div",{staticClass:"num"},[t._v("0")])])],1),t._v(" "),e("div",{staticClass:"item"},[e("router-link",{staticClass:"item-content",attrs:{to:"/member/my_point"}},[e("img",{attrs:{src:r(662),alt:""}}),t._v(" "),e("div",{staticClass:"name"},[t._v("积分")]),t._v(" "),t.member.point?e("div",{staticClass:"num"},[t._v(t._s(t.member.point))]):e("div",{staticClass:"num"},[t._v("0")])])],1),t._v(" "),e("div",{staticClass:"item"},[e("router-link",{staticClass:"item-content",attrs:{to:"/member/account"}},[e("img",{attrs:{src:r(663),alt:""}}),t._v(" "),e("div",{staticClass:"name"},[t._v("余额")]),t._v(" "),t.member.balance||t.member.balance_money?e("div",{staticClass:"num"},[t._v(t._s((parseFloat(t.member.balance)+parseFloat(t.member.balance_money)).toFixed(2)))]):e("div",{staticClass:"num"},[t._v("0")])])],1)])])]),t._v(" "),e("div",{staticClass:"collection"},[e("router-link",{staticClass:"item-content",attrs:{to:"/member/collection"}},[e("div",{staticClass:"title"},[t._v("我的关注")]),t._v(" "),e("div",{staticClass:"xian"}),t._v(" "),e("div",{staticClass:"item-wrap"},[e("div",{staticClass:"item"},[e("div",{staticClass:"num"},[t._v(t._s(t.goodsTotal))]),t._v(" "),e("div",{staticClass:"collect"},[t._v("商品关注")])])])])],1)]),t._v(" "),e("div",{staticClass:"member-bottom"},[e("div",{staticClass:"my-order"},[e("div",{staticClass:"order-title"},[t._v("我的订单")]),t._v(" "),e("div",{staticClass:"xian"}),t._v(" "),e("div",{staticClass:"order-item"},[e("router-link",{staticClass:"item",attrs:{to:"/member/order_list?status=waitpay"}},[e("i",{staticClass:"iconfont icon-daifukuan"}),t._v(" "),t.orderNum.waitPay?e("div",{staticClass:"order-num"},[t._v(t._s(t.orderNum.waitPay))]):t._e(),t._v(" "),e("div",{staticClass:"name"},[t._v("待付款")])]),t._v(" "),e("router-link",{staticClass:"item",attrs:{to:"/member/order_list?status=waitsend"}},[e("i",{staticClass:"iconfont icon-daifahuo"}),t._v(" "),t.orderNum.readyDelivery?e("div",{staticClass:"order-num"},[t._v(t._s(t.orderNum.readyDelivery))]):t._e(),t._v(" "),e("div",{staticClass:"name"},[t._v("待发货")])]),t._v(" "),e("router-link",{staticClass:"item",attrs:{to:"/member/order_list?status=waitconfirm"}},[e("i",{staticClass:"iconfont icon-tubiaolunkuo-"}),t._v(" "),t.orderNum.waitDelivery?e("div",{staticClass:"order-num"},[t._v(t._s(t.orderNum.waitDelivery))]):t._e(),t._v(" "),e("div",{staticClass:"name"},[t._v("待收货")])]),t._v(" "),e("router-link",{staticClass:"item",attrs:{to:"/member/order_list?status=waitrate"}},[e("i",{staticClass:"iconfont icon-daipingjia"}),t._v(" "),t.orderNum.waitEvaluate?e("div",{staticClass:"order-num"},[t._v(t._s(t.orderNum.waitEvaluate))]):t._e(),t._v(" "),e("div",{staticClass:"name"},[t._v("待评价")])]),t._v(" "),e("router-link",{staticClass:"item",attrs:{to:"/member/activist"}},[e("i",{staticClass:"iconfont icon-shouhou"}),t._v(" "),t.orderNum.refunding?e("div",{staticClass:"order-num"},[t._v(t._s(t.orderNum.refunding))]):t._e(),t._v(" "),e("div",{staticClass:"name"},[t._v("退款/售后")])])],1),t._v(" "),t.orderList.length?e("div",t._l(t.orderList,(function(r,o){return e("div",{key:o,staticClass:"order-goods-wrap"},t._l(r.order_goods,(function(n,c){return e("div",{key:c,staticClass:"order-goods"},[e("div",{staticClass:"goods-item"},[e("div",{staticClass:"goods-img",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+n.sku_id})}}},[e("img",{attrs:{src:t.$img(n.sku_image,{size:"mid"})},on:{error:function(e){return t.imageErrorOrder(o,c)}}})]),t._v(" "),e("div",{staticClass:"info-wrap"},[e("div",{staticClass:"goods-name",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+n.sku_id})}}},[t._v(t._s(n.sku_name))]),t._v(" "),e("div",{staticClass:"price"},[t._v("￥"+t._s(n.price))])]),t._v(" "),e("div",{staticClass:"payment"},[t._v(t._s(r.order_status_name))]),t._v(" "),e("div",{staticClass:"goods-detail",on:{click:function(e){return t.orderDetail(r)}}},[e("p",[t._v("查看详情")])])])])})),0)})),0):e("div",{staticClass:"empty"},[e("img",{attrs:{src:r(664),alt:""}}),t._v(" "),e("div",[e("router-link",{attrs:{to:"/"}},[t._v("您买的东西太少了，这里都空空的，快去挑选合适的商品吧！")])],1)])]),t._v(" "),e("div",{staticClass:"bottom-right"},[e("div",{staticClass:"my-foot"},[e("div",{staticClass:"title"},[t._v("我的足迹")]),t._v(" "),e("div",{staticClass:"xian"}),t._v(" "),t._l(t.footList,(function(r,o){return e("div",{key:r.goods_id,staticClass:"foot-content"},[e("div",{staticClass:"foot-item",on:{click:function(e){return t.$util.pushToTab({path:"/sku/"+r.sku_id})}}},[e("div",{staticClass:"foot-img"},[e("img",{attrs:{src:t.$img(r.sku_image,{size:"mid"})},on:{error:function(e){return t.imageErrorFoot(o)}}})]),t._v(" "),e("div",{staticClass:"foot-info"},[e("div",{staticClass:"foot-name"},[t._v(t._s(r.goods_name))]),t._v(" "),e("div",{staticClass:"foot-price"},[t._v("￥"+t._s(r.discount_price))])])])])}))],2)])])])])}),[],!1,null,"5cf9a5c9",null);e.default=component.exports}}]);