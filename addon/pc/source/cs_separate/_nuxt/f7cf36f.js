(window.webpackJsonp=window.webpackJsonp||[]).push([[6],{582:function(e,t,r){},583:function(e,t,r){},669:function(e,t,r){"use strict";r(582)},670:function(e,t,r){"use strict";r(583)},748:function(e,t,r){"use strict";r.r(t);r(317),r(31),r(64),r(73);var o=r(175),n=r(27),c={name:"find_pass",layout:"login",mixins:[{data:function(){var e=this;return{formData:{mobile:"",vercode:"",dynacode:"",pass:"",repass:"",key:""},step:1,activeName:"first",isShowPhone:"",captcha:{id:"",img:""},dynacodeData:{seconds:120,timer:null,codeText:"获取动态码",isSend:!1},registerConfig:{},rules:{mobile:[{required:!0,validator:function(e,t,r){if(!t)return r(new Error("手机号不能为空"));/^\d{11}$/.test(t)?r():r(new Error("请输入正确的手机号"))},trigger:"blur"}],vercode:[{required:!0,message:"请输入验证码",trigger:"blur"}],dynacode:[{required:!0,message:"请输入短信动态码",trigger:"blur"}],pass:[{required:!0,validator:function(t,r,o){var n=e.registerConfig;if(!r)return o(new Error("请输入新的登录密码"));if(n.pwd_len>0){if(r.length<n.pwd_len)return o(new Error("密码长度不能小于"+n.pwd_len+"位"));o()}else if(""!=n.pwd_complexity){var c="密码需包含",l="";if(-1!=n.pwd_complexity.indexOf("number")?(l+="(?=.*?[0-9])",c+="数字"):-1!=n.pwd_complexity.indexOf("letter")?(l+="(?=.*?[a-z])",c+="、小写字母"):-1!=n.pwd_complexity.indexOf("upper_case")?(l+="(?=.*?[A-Z])",c+="、大写字母"):-1!=n.pwd_complexity.indexOf("symbol")?(l+="(?=.*?[#?!@$%^&*-])",c+="、特殊字符"):(l+="",c+=""),l.test(r))return o(new Error(c));o()}},trigger:"blur"}],repass:[{required:!0,validator:function(t,r,o){if(!r)return o(new Error("请输入确认密码"));r!==e.formData.pass?o(new Error("两次密码输入不一致")):o()},trigger:"blur"}]}}},created:function(){this.getCaptcha(),this.getRegisterConfig()},head:function(){return{title:"找回密码-"+this.$store.state.site.siteInfo.site_name}},watch:{"dynacodeData.seconds":{handler:function(e,t){0==e&&(clearInterval(this.dynacodeData.timer),this.dynacodeData={seconds:120,timer:null,codeText:"获取动态码",isSend:!1})},immediate:!0,deep:!0}},methods:{nextStep:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(o.d)({mobile:t.formData.mobile}).then((function(e){-1==e.code?t.step=2:t.$message({message:e.message,type:"warning"})})).catch((function(e){0==e.code?t.$message({message:"该手机号未注册",type:"warning"}):t.$message.error(e.message)}))}))},nextStepToSetPass:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;t.step=3}))},resetPass:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(o.e)({password:t.formData.pass,code:t.formData.dynacode,key:t.formData.key,mobile:t.formData.mobile}).then((function(e){e.code>=0&&(t.step=4,t.$message({message:e.message,type:"success"}))})).catch((function(e){t.$message.error(e.message)}))}))},getCaptcha:function(){var e=this;Object(n.b)({captcha_id:this.captcha.id}).then((function(t){t.code>=0&&(e.captcha.id=t.data.id,e.captcha.img=t.data.img,e.captcha.img=e.captcha.img.replace(/\r\n/g,""))})).catch((function(t){e.$message.error(t.message)}))},sendMobileCode:function(e){var t=this;120==this.dynacodeData.seconds&&(this.$refs[e].clearValidate("dynacode"),this.$refs[e].validateField("vercode",(function(e){if(e)return!1;t.isSend||(t.isSend=!0,Object(o.g)({captcha_id:t.captcha.id,captcha_code:t.formData.vercode,mobile:t.formData.mobile}).then((function(e){e.code>=0&&(t.formData.key=e.data.key,120==t.dynacodeData.seconds&&null==t.dynacodeData.timer?t.dynacodeData.timer=setInterval((function(){t.dynacodeData.seconds--,t.dynacodeData.codeText=t.dynacodeData.seconds+"s后可重新获取"}),1e3):(t.$message({message:e.message,type:"warning"}),t.isSend=!1,t.getCaptcha()))})).catch((function(e){t.isSend=!1,t.getCaptcha(),t.$message.error(e.message)})))})))},getRegisterConfig:function(){var e=this;Object(o.f)().then((function(t){t.code>=0&&(e.registerConfig=t.data.value)}))}}}]},l=(r(669),r(670),r(6)),component=Object(l.a)(c,(function(){var e=this,t=e._self._c;return t("div",{staticClass:"el-row-wrap find-pass"},[t("ul",[e._m(0),e._v(" "),t("li",{attrs:{id:"current2"}},[t("span",{staticClass:"line-1",class:e.step>=2?"ns-border-color":"ns-border-color-gray"}),e._v(" "),t("div",[t("p",{class:e.step>=2?"ns-bg-color":"ns-bg-color-gray"},[e._v("2")]),e._v(" "),t("p",{class:e.step>=2?"ns-text-color":"ns-text-color-gray"},[e._v("验证身份")])]),e._v(" "),t("span",{staticClass:"line-2",class:e.step>=2?"ns-border-color":"ns-border-color-gray"})]),e._v(" "),t("li",{attrs:{id:"current3"}},[t("span",{staticClass:"line-1",class:e.step>=3?"ns-border-color":"ns-border-color-gray"}),e._v(" "),t("div",[t("p",{class:e.step>=3?"ns-bg-color":"ns-bg-color-gray"},[e._v("3")]),e._v(" "),t("p",{class:e.step>=3?"ns-text-color":"ns-text-color-gray"},[e._v("重置密码")])]),e._v(" "),t("span",{staticClass:"line-2",class:e.step>=3?"ns-border-color":"ns-border-color-gray"})]),e._v(" "),t("li",{attrs:{id:"current4"}},[t("span",{staticClass:"line-1",class:e.step>=4?"ns-border-color":"ns-border-color-gray"}),e._v(" "),t("div",[t("p",{class:e.step>=4?"ns-bg-color":"ns-bg-color-gray"},[e._v("4")]),e._v(" "),t("p",{class:e.step>=4?"ns-text-color":"ns-text-color-gray"},[e._v("完成")])])])]),e._v(" "),t("el-row",[t("el-col",{attrs:{span:12,offset:6}},[t("div",{staticClass:"grid-content bg-purple"},[t("el-form",{ref:"ruleForm",staticClass:"ns-forget-pass-form",attrs:{model:e.formData,rules:e.rules}},[t("div",{staticClass:"ns-forget-pass"},[1==e.step?[t("el-form-item",{key:"1",attrs:{prop:"mobile"}},[t("el-input",{attrs:{placeholder:"请输入注册手机号"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"el-icon-mobile-phone"})])],2)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.nextStep("ruleForm")}}},[e._v("下一步")])],1)]:2==e.step?[t("el-form-item",{key:"2",attrs:{prop:"vercode"}},[t("el-input",{attrs:{autocomplete:"off",placeholder:"请输入验证码",maxlength:"4"},model:{value:e.formData.vercode,callback:function(t){e.$set(e.formData,"vercode",t)},expression:"formData.vercode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"el-icon-picture-outline"})]),e._v(" "),t("template",{slot:"append"},[t("img",{staticClass:"captcha",attrs:{src:e.captcha.img,mode:""},on:{click:e.getCaptcha}})])],2)],1),e._v(" "),t("el-form-item",{key:"3",attrs:{prop:"dynacode"}},[t("el-input",{attrs:{maxlength:"4",autocomplete:"off",placeholder:"请输入短信动态码"},model:{value:e.formData.dynacode,callback:function(t){e.$set(e.formData,"dynacode",t)},expression:"formData.dynacode"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"el-icon-mobile"})]),e._v(" "),t("template",{slot:"append"},[t("div",{staticClass:"dynacode",class:120==e.dynacodeData.seconds?"ns-text-color":"ns-text-color-gray",on:{click:function(t){return e.sendMobileCode("ruleForm")}}},[e._v("\n                      "+e._s(e.dynacodeData.codeText)+"\n                    ")])])],2)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.nextStepToSetPass("ruleForm")}}},[e._v("下一步")])],1)]:3==e.step?[t("el-form-item",{key:"4",attrs:{prop:"pass"}},[t("el-input",{attrs:{type:"password",autocomplete:"off",placeholder:"请输入新的登录密码"},model:{value:e.formData.pass,callback:function(t){e.$set(e.formData,"pass","string"==typeof t?t.trim():t)},expression:"formData.pass"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"el-icon-lock"})])],2)],1),e._v(" "),t("el-form-item",{key:"5",attrs:{prop:"repass"}},[t("el-input",{attrs:{type:"password",autocomplete:"off",placeholder:"请再次输入新密码"},model:{value:e.formData.repass,callback:function(t){e.$set(e.formData,"repass","string"==typeof t?t.trim():t)},expression:"formData.repass"}},[t("template",{slot:"prepend"},[t("i",{staticClass:"el-icon-lock"})])],2)],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.resetPass("ruleForm")}}},[e._v("重置密码")])],1)]:4==e.step?[t("span",{staticClass:"ns-reset-success"},[e._v("重置密码成功")]),e._v(" "),t("el-form-item",[t("router-link",{attrs:{to:"/auth/login"}},[t("el-button",{attrs:{type:"primary"}},[e._v("重新登录")])],1)],1)]:e._e()],2)])],1)])],1)],1)}),[function(){var e=this,t=e._self._c;return t("li",[t("div",[t("p",{staticClass:"ns-bg-color"},[e._v("1")]),e._v(" "),t("p",{staticClass:"ns-text-color"},[e._v("输入手机号")])]),e._v(" "),t("span",{staticClass:"line-2 ns-border-color"})])}],!1,null,"7721ab4c",null);t.default=component.exports}}]);