(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{641:function(t,e,n){},734:function(t,e,n){"use strict";n(641)},805:function(t,e,n){"use strict";n.r(e);n(24),n(25),n(23),n(7),n(29),n(18),n(30);var r=n(10),c=(n(92),n(12)),o=n(216);function l(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var d={name:"help_detail",components:{},data:function(){return{detail:[],loading:!0}},created:function(){this.id=this.$route.query.id,this.getDetail()},computed:function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?l(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):l(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},Object(c.b)(["siteInfo"])),watch:{$route:function(t){this.id=t.query.id,this.getDetail()}},methods:{getDetail:function(){var t=this;Object(o.a)({id:this.id}).then((function(e){0==e.code&&(e.data?(t.loading=!1,t.detail=e.data,window.document.title="".concat(t.detail.title," - ").concat(t.siteInfo.site_name)):t.$router.push({path:"/cms/help/list"}))})).catch((function(e){t.loading=!1,t.$message.error(e.message)}))},toLink:function(){this.detail.link_address&&window.open(this.detail.link_address)}}},h=d,m=(n(734),n(6)),component=Object(m.a)(h,(function(){var t=this,e=t._self._c;return e("div",{staticClass:"detail-wrap"},[e("el-breadcrumb",{staticClass:"path",attrs:{separator:"/"}},[e("el-breadcrumb-item",{staticClass:"path-home",attrs:{to:{path:"/"}}},[t._v("首页")]),t._v(" "),e("el-breadcrumb-item",{attrs:{to:{path:"/cms/help/list"}}},[t._v("帮助列表")]),t._v(" "),e("el-breadcrumb-item",{staticClass:"path-help"},[t._v("帮助详情")])],1),t._v(" "),e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"help-detail"},[e("div",{staticClass:"title",on:{click:t.toLink}},[t._v(t._s(t.detail.title))]),t._v(" "),e("div",{staticClass:"info"},[e("div",{staticClass:"time"},[t._v(t._s(t.$util.timeStampTurnTime(t.detail.create_time)))])]),t._v(" "),e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.detail.content)}})])],1)}),[],!1,null,"0a2130a8",null);e.default=component.exports}}]);