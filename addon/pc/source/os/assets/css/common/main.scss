// 参考文档：https://element.faas.ele.me/#/zh-CN/component/color#fu-zhu-se

//主色调，红色：#FF0036，绿色 #4CAF50，蓝色：#03A9F4，黄色：#FF9800，粉色：#FF547B，棕色：#C3A769，浅绿色：#65C4AA，黑色：#333333，紫色：#B323B4，淡粉色：#FF8B8B,element UI：#409eff
$base-color: #ff547b;
$base-color-success: #4caf50;
$base-color-warning: #e6a23c;
$base-color-danger: #f56c6c;
$base-color-info: #909399;

/* 文字基本颜色 */
$ns-text-color-black: #303133; //基本色

/* 文字尺寸 */
$ns-font-size-sm: 12px;
$ns-font-size-base: 14px;
$ns-font-size-lg: 16px;

$width: 1210px;

// 小
.ns-font-size-sm {
  font-size: $ns-font-size-sm;
}

// 标准
.ns-font-size-base {
  font-size: $ns-font-size-base;
}

// 标准
.ns-font-size-lg {
  font-size: $ns-font-size-lg;
}

//文字颜色
.ns-text-color {
  color: $base-color !important;
}

.ns-text-color-black {
  color: $ns-text-color-black !important;
}

//边框
.ns-border-color {
  border-color: $base-color !important;
}

//背景色
.ns-bg-color {
  background-color: $base-color !important;
}

body {
  line-height: 1.8;
  font-family: 'Helvetica Neue', Helvetica, 'Microsoft Yahei', 'PingFang SC', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', sans-serif;
  background-color: #f9f9f9;
  font-size: $ns-font-size-base;
  color: $ns-text-color-black;
}

#__nuxt {
  background-color: #f9f9f9;
}

#nprogress .bar {
  background: $base-color !important; //自定义颜色
}

html,
body,
dl,
dt,
dd,
ol,
ul,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ol,
ul,
li {
  list-style: none;
}

i,
em {
  font-style: normal;
}

// 去掉a标签的默认样式
a {
  text-decoration: none;
  color: $ns-text-color-black;

  &:hover {
    color: $base-color;
  }
}

.el-main {
  border-top: solid 1px #f2f2f2;
  overflow: initial;
  // width: $width;
  margin: 0 auto;
  padding: 0;
}

.el-button:focus,
.el-button:hover {
  color: $base-color;
  border-color: lighten($base-color, 20%);
  background-color: lighten($base-color, 30%);
}

// 改变按钮默认样式
.el-button--primary,
.el-button--primary:focus,
.el-button--primary:hover,
.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  background-color: $base-color;
  border-color: $base-color;
  color: #fff;
}

.el-button--primary.is-plain {
  color: $base-color;
  background-color: transparent;
  border-color: $base-color;
}

img {
  border-style: none;
  max-width: 100%;
  vertical-align: middle;
}

// 居中
.ns-text-align {
  text-align: center;
}

// 分页
.el-pagination {
  text-align: right;

  .el-pagination__rightwrapper {
    margin-left: 20px;
  }
}

/* 单行超出隐藏 */
.using-hidden {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  white-space: break-spaces;
}

/* 多行超出隐藏 */
.multi-hidden {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.el-breadcrumb__inner a:hover,
.el-breadcrumb__inner.is-link:hover {
  color: $base-color;
}

.el-tabs__item:hover,
.el-tabs__item.is-active {
  color: $base-color;
}

.el-tabs__active-bar {
  background-color: $base-color;
}

.el-pager li.active {
  color: $base-color;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: $base-color;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: $base-color;
}

.el-menu-item.is-active {
  color: $base-color;
}

.el-progress-bar__inner {
  background-color: $base-color;
}

.el-link.el-link--default:hover {
  color: $base-color;
}

.el-button--text {
  color: $base-color;
}

.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: $base-color;
}

.el-loading-spinner .path {
  stroke: $base-color;
}

.el-loading-spinner i {
  color: $base-color;
}

.el-loading-spinner .el-loading-text {
  color: $base-color;
}

.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $base-color;
  border-color: $base-color;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: $base-color;
}

.el-radio__input.is-checked .el-radio__inner {
  border-color: $base-color;
  background: $base-color;
}
.el-textarea__inner:focus{
  border-color: $base-color;
}
