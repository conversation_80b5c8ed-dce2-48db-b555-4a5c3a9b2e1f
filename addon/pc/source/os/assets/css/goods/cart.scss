.cart {
	margin: 20px auto;
  width: $width;

	nav {
		overflow: hidden;
		padding: 10px;
		background: #fff;
		margin-bottom: 10px;
		li {
			float: left;
			&:nth-child(1) {
				width: 3%;
			}
			&:nth-child(2) {
				width: 55%;
			}
			&:nth-child(3) {
				width: 10%;
			}
			&:nth-child(4) {
				width: 15%;
			}
			&:nth-child(5) {
				width: 10%;
			}
			&:nth-child(6) {
				width: 7%;
			}
		}
	}
	.list {
		.item {
			margin-bottom: 20px;
			.head {
				padding: 10px;
				background: #fff;
				border-bottom: 1px solid #eeeeee;
				a {
					margin: 0 10px 0 20px;
				}
			}

			ul {
				overflow: hidden;
				padding: 10px;
				background: #fff;
				li {
					float: left;
					line-height: 60px;
					&:nth-child(1) {
						width: 3%;
						margin-left: 20px;
					}
					&:nth-child(2) {
						width: 53.3%;
						line-height: inherit;
						.img-wrap {
							width: 60px;
							height: 60px;
							float: left;
							margin-right: 10px;
							cursor: pointer;
						}
						.info-wrap {
							margin-left: 70px;
							cursor: pointer;
							h5 {
								font-weight: normal;
								font-size: $ns-font-size-base;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								overflow: hidden;
								margin-right: 10px;
								&:hover {
									color: $base-color;
								}
							}
							span {
								font-size: $ns-font-size-sm;
								color: #9a9a9a;
							}
						}
					}
					&:nth-child(3) {
						width: 10%;
					}
					&:nth-child(4) {
						width: 15%;
						.el-input-number--mini {
							width: 100px;
						}
					}
					&:nth-child(5) {
						width: 10%;
					}
					&:nth-child(6) {
						width: 7%;
					}
				}
			}
		}
	}

	.lose-list {
		margin-bottom: 20px;
		.head {
			padding: 10px;
			background: #fff;
			border-bottom: 1px solid #eeeeee;
			span {
				margin: 0 5px;
				font-weight: bold;
				font-size: 16px;
			}
		}

		ul {
			overflow: hidden;
			padding: 10px;
			background: #f0f0f0;
			li {
				float: left;
				line-height: 60px;
				color: #9a9a9a;
				&:nth-child(1) {
					width: 3%;
					margin-left: 5px;
					margin-right: 15px;
				}
				&:nth-child(2) {
					width: 53.3%;
					line-height: inherit;
					.img-wrap {
						width: 60px;
						height: 60px;
						float: left;
						margin-right: 10px;
					}
					.info-wrap {
						margin-left: 70px;
						cursor: pointer;
						h5 {
							font-weight: normal;
							font-size: $ns-font-size-base;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							margin-right: 10px;
						}
						span {
							font-size: $ns-font-size-sm;
						}
					}
				}
				&:nth-child(3) {
					width: 10%;
				}
				&:nth-child(4) {
					width: 15%;
					.el-input-number--mini {
						width: 100px;
					}
				}
				&:nth-child(5) {
					width: 10%;
				}
				&:nth-child(6) {
					width: 7%;
				}
			}
		}
	}

	footer {
		background-color: #fff;
		margin-bottom: 20px;
		padding: 10px;
		.operation {
			display: inline-block;
			vertical-align: middle;
			margin-left: 20px;
			li {
				float: left;
				margin-right: 30px;
			}
		}
		.sum-wrap {
			float: right;
			.selected-sum {
				display: inline-block;
				margin-right: 20px;
				.total-count {
					padding: 0 5px;
					font-size: 16px;
					color: $base-color;
					font-weight: bold;
				}
			}
			.price-wrap {
				display: inline-block;
				margin-right: 20px;
				span {
					font-size: $ns-font-size-sm;
				}
				strong {
					font-size: 18px;
				}
			}
		}
	}
}
.el-button--text {
	color: #333;
	&:hover {
		color: $base-color;
	}
}
.empty-wrap {
	padding: 50px 0 60px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	img{
	  width: 400px;
	  height: 244px;
	}
	a{
	  font-size: 14px;
	  color: #4A4A4A;
    &:hover{
      color: $base-color;
    }
	}
}
