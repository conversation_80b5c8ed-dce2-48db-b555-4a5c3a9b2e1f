.el-main {
  // width: auto !important;
  // background-color: #fff;
  .detail-nav-wrap {
    background-color: #f8f8f8;
    .detail-nav {
      margin: auto;
      width: 1210px;
      height: 54px;
      display: flex;
      align-items: center;
      .iconfont {
        margin: 0 8px;
      }
      span {
        color: #666;
        &:last-of-type {
          color: $base-color;
        }
      }
      .goods-name {
        display: inline-block;
        width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.detail-main {
  background: #fff;
  padding-top: 20px;
}
.goods-detail {
  overflow: hidden;
  margin: 0 auto;
  width: 1210px;

  .preview-wrap {
    display: flex;
    float: left;
    .video-player-wrap {
      position: absolute;
      z-index: 1;
      .video-player {
        visibility: hidden;
        > div {
          width: 460px;
          height: 460px;
          display: none;
        }
        .vjs-control-bar,
        .vjs-big-play-button {
          visibility: hidden;
          transition: none;
        }
        .vjs-big-play-button {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .media-mode {
        position: absolute;
        width: 460px;
        z-index: 5;
        bottom: 0;
        top: 400px;
        text-align: center;
        line-height: 25px;

        span {
          background: rgba(100, 100, 100, 0.6);
          color: #fff;
          font-size: 12px;
          line-height: 25px;
          border-radius: 10px;
          padding: 0 15px;
          display: inline-block;
          cursor: pointer;

          &:last-child {
            margin-left: 20px;
          }
        }
      }
      &.show {
        .video-player,
        .vjs-control-bar,
        .vjs-big-play-button {
          visibility: visible;
        }

        .video-player {
          > div {
            display: block;
          }
        }
      }
    }
    .magnifier-wrap {
      width: 460px;
      height: 460px;
      border: 1px solid #d8d8d8;
      box-sizing: border-box;
      .magnifier-box .mouse-cover {
        background-color: #fede4f;
        opacity: 0.4;
      }
    }

    .spec-items {
      margin-left: 10px;
      position: relative;
      width: 84px;
      height: 460px;
      overflow: hidden;
      > span {
        cursor: pointer;
        left: 0;
        top: 0;
        width: 84px;
        height: 42px;
        line-height: 42px;
        text-align: center;
        position: absolute;
        background: #fff;
        color: #838383;
        z-index: 1;
        font-size: 25px;
        &:first-of-type {
          transform: rotate(180deg);
        }
        &:last-of-type {
          top: calc(100% - 42px);
        }
      }
      ul {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 500%;
        position: absolute;
        top: 48px;
        li {
          width: 84px;
          height: 84px;
          overflow: hidden;
          margin-bottom: 10px;
          border: 1px solid #dadada;
          cursor: pointer;
          opacity: 0.5;
          box-sizing: border-box;
          &:hover,
          &.selected {
            opacity: 1;
            border-color: $base-color;
          }
        }
      }
    }
    .share-collect {
      padding: 10px 0 20px;
      cursor: pointer;
      text-align: right;
      color: #838383;
      display: flex;
      justify-content: space-between;
      align-items: center;
      a {
        color: #838383;
      }
      i,
      span {
        vertical-align: middle;
      }
      i {
        margin-right: 5px;
      }
    }
  }
  .basic-info-wrap {
    float: left;
    width: calc(100% - 589px);
    margin-left: 30px;
    h1 {
      font-size: 20px;
      font-weight: normal;
      color: #333;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 5px;
    }
    .desc {
      font-size: 12px;
      color: #999;
      margin: 5px 0 12px;
    }
    .divider {
      margin: 10px 0;
      border: 0;
      border-top: 1px dotted #e5e5e5;
    }
    .discount-banner {
      background: url(~/assets/images/goods/discount_bg.png) no-repeat;
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .activity-name {
        color: #fff;
        font-size: 14px;
        height: 40px;

        i {
          display: inline-block;
          line-height: 40px;
        }
        span {
          display: inline-block;
          line-height: 40px;
          vertical-align: top;
        }
      }
      .discount-icon {
        margin: 0 8px 0 10px;
        color: #fff;
        font-size: 20px;
      }
      .surplus-time {
        color: #fff;
        font-size: 14px;
        float: right;
        height: 35px;
        line-height: 35px;
        padding-right: 5px;
        text-align: right;
        > span {
          margin-right: 5px;
        }
        i {
          font-style: normal;
          padding: 2px;
          width: 20px;
          height: 20px;
          margin: 0 5px;
          border-radius: 3px;
        }
        .count-down {
          display: inline-block;
        }
      }
    }
    .item-block {
      background-color: #f8f8f8;
      padding: 15px 0;
      position: relative;
      background-size: cover;
      min-height: 62px;
      margin-bottom: 20px;
      .statistical {
        position: absolute;
        right: 20px;
        top: 16px;
        li {
          display: inline-block;
          text-align: center;
          position: relative;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          &:first-child {
            margin-right: 40px;
          }

          p {
            color: #838383;
          }

          span {
            color: #383838;
            font-size: 16px;
          }
        }
      }
    }
    .item-line {
      font-size: 14px;
      min-height: 35px;
      line-height: 35px;
      display: flex;
      padding-left: 20px;
      margin-bottom: 10px;
      dt {
        display: inline-block;
        width: 50px;
        height: 35px;
        line-height: 35px;
        text-align: justify;
        font-size: 12px;
        margin-right: 16px;
        color: #222;
        flex-shrink: 0;
        &::after {
          display: inline-block;
          width: 100%;
          content: '';
          height: 0;
        }
      }
      > * {
        height: inherit;
        line-height: inherit;
        font-size: 14px;
      }
      > dd {
        // display: flex;
        // align-items: center;
        height: 35px;
      }
      &:last-of-type {
        margin-bottom: 0;
      }
      &:first-of-type > dd {
        align-items: baseline;
      }
      .yuan {
        font-size: 18px;
      }
      .price {
        font-size: 30px;
        font-weight: bold;
      }
      .market-yuan {
        font-size: 14px;
        text-decoration: line-through;
      }
      .member_price {
        font-size: 14px;
        margin-left: 5px;
      }
      .market-price {
        font-size: 14px;
        margin-left: 5px;
        text-decoration: line-through;
      }
      &.promotion-price .label {
        line-height: initial;
        font-size: 18px;
        background: transparent;
        padding: 0;
        vertical-align: text-bottom;
      }
      &.coupon-list {
        align-items: baseline;
        > div {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          dd {
            margin-right: 20px;
          }
        }
        p {
          display: inline-block;
          position: relative;
          line-height: 18px;
        }
        span {
          display: inline-block;
          padding: 0 10px;
          margin-right: 10px;
          cursor: pointer;
          border: 1px solid $base-color;
          height: 32px;
          line-height: 32px;
          color: $base-color;
          font-size: 14px;
          box-sizing: border-box;
        }
        label {
          line-height: 16px;
          position: absolute;
          left: 10px;
          top: 1px;
        }
      }
      &.manjian {
        dd{
            height: inherit;
        }
      }
      &.mansong{
        dd{
          height: inherit;
        }
      }
      &.delivery {
        position: relative;
        i {
          margin-left: 5px;
        }
        dd {
          align-items: center;
        }
        .region-selected {
          position: relative;
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          margin: 0 5px 0 0;
          width: auto;
          padding: 0 10px;
          height: 32px;
          line-height: 32px;
          border: 1px solid #e5e5e5;
          z-index: 2;
          font-size: 14px;
        }
        .region-list {
          width: 466px;
          position: absolute;
          z-index: 10;
          top: 35px;
          left: 82px;
          background: #fff;
          border: 1px solid #e5e5e5;
          padding: 15px;
          line-height: 18px;
          height: auto;
          display: none;
          font-size: 14px;
          &.hide {
            display: none !important;
          }
          .nav-tabs {
            border-bottom: 2px solid $base-color;
            height: 30px;
            > li {
              margin-bottom: -2px;
              float: left;
              > div {
                padding: 4px 8px;
                border: 2px solid transparent;
                height: 20px;
                display: inline-block;
                line-height: 20px;
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1);
                cursor: pointer;
                &:hover {
                  border-color: transparent;
                }
              }
              &.active {
                > div {
                  color: $base-color;
                  border: 2px solid;
                  border-bottom-color: #fff !important;
                }
              }
            }
          }
          .tab-content {
            > .active {
              padding: 10px 0;
            }
            > .tab-pane {
              display: none;
              &.active {
                display: block;
              }
              li {
                float: left;
                width: 25%;
                line-height: 30px;
                &.selected {
                  color: $base-color;
                }
                span {
                  cursor: pointer;
                  &:hover {
                    color: $base-color;
                  }
                }
              }
            }
          }
        }
        dd:hover {
          .region-list {
            display: block;
          }
        }
      }

      &.service a {
        margin: 0 5px;
      }
      &.buy-btn {
        margin: 25px 0 0;
        dt {
          margin: 0;
          padding: 0;
          width: 0;
          height: 0;
        }
        dd {
          height: auto;
        }
        button {
          min-width: 168px;
          height: 52px;
          margin-right: 10px;
          font-size: 20px;
          text-shadow: 0 0 0;
          border-radius: 0;
          &[disabled] {
            cursor: not-allowed;
            pointer-events: initial;
            border: 1px solid;
            opacity: 1;
          }
          i {
            margin-right: 5px;
            font-size: 20px;
          }
        }
      }
      .i-activity-flag {
        height: 32px;
        line-height: 32px;
        padding: 0 8px;
        border: 1px solid;
        font-size: 14px;
        margin-right: 5px;
        white-space: nowrap;
      }
      &.gift-point strong {
        font-size: 14px;
      }
      &.merchant-service {
        overflow: hidden;
        margin: 10px 0;
        align-items: flex-start;
        > div {
          display: inline-block;
        }
        dd {
          display: inline-block;
          margin-right: 18px;
          // margin: 0;
          &:last-child {
            padding-right: 0;
          }
          i {
            color: $base-color;
            font-size: $ns-font-size-base;
            margin-right: 5px;
          }
          span {
            cursor: pointer;
          }
        }
      }
    }
    .manjian-box {
      position: relative;

      .manjian-hide {
        position: relative;
        .manjian-open {
          position: absolute;
          right: 0px;
          color: #838383;
        }
      }
      .manjian-show {
        //background: url(~/assets/images/goods/goods_detail_bg.png) repeat-x;
        background: #eee;
        display: none;
        position: absolute;
        top: 0px;
      }
    }
    .manjian-box .manjian-hide:hover + .manjian-show {
      display: flex;
      pointer-events: none;
      z-index: 9999;
      width: 100%;
    }
    .sku-list {
      dd {
        height: inherit;
      }
      ul {
        overflow: hidden;
        padding: 1px;
        li {
          float: left;
          position: relative;
          margin: 2px 10px 10px 0;
          line-height: 14px;
          vertical-align: middle;

          div {
            padding: 0 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            border: 1px solid #b7b7b7;
            -webkit-transition: none;
            transition: none;
            position: relative;
            cursor: pointer;
            &.selected,
            &:hover {
              border: 1px solid;
              color: $base-color;
            }
            &:hover {
              color: $base-color;
            }
            &.disabled,
            &.disabled:hover {
              border: 1px solid #b7b7b7;
              margin: 0;
              cursor: not-allowed;
              color: #aaa;
            }
            img {
              margin-right: 10px;
              width: 25px;
              height: 25px;
              display: inline-block;
              vertical-align: middle;
            }
            i {
              position: absolute;
              right: -10px;
              bottom: 0px;
              display: none;
              font-size: 32px;
            }

            &:not(.disabled).selected i,
            &:not(.disabled):hover i {
              display: block;
            }
          }
        }
      }
    }
    .buy-number {
      margin: 10px 0;
      .num-wrap {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        margin-right: 10px;
        input {
          width: 126px;
          padding-left: 40px;
          padding-right: 40px;
          font-size: 12px;
          height: 32px;
          line-height: 32px;
          border-radius: 0;
          border-color: #d5d5d5;
          text-align: center;
          &.disabled {
            cursor: not-allowed;
          }
        }
        span {
          position: absolute;
          top: 2px;
          left: 0;
          width: 32px;
          height: 32px;
          overflow: hidden;
          cursor: pointer;
          display: block;
          line-height: 32px;
          text-align: center;
          user-select: none;
          z-index: 1;
          &.decrease {
            border-right: 1px solid #d5d5d5;
          }
          &.increase {
            left: 94px;
            border-left: 1px solid #d5d5d5;
          }
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
      .inventory {
        margin-left: 10px;
      }
    }
    .go-phone {
      position: relative;
      float: right;
      width: 52px;
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border: 1px solid #d8d8d8;
      box-sizing: border-box;
      margin-right: 10px;
      cursor: pointer;
      & > img {
      }
      & > span {
        margin-top: 4px;
        line-height: 1;
        font-size: 12px;
        color: #999;
      }
      &:hover .qrcode-wrap {
        display: block;
      }
      .qrcode-wrap {
        border: 1px solid #e5e5e5;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
        height: 120px;
        overflow: hidden;
        padding: 10px;
        position: absolute;
        width: 120px;
        left: 30px;
        top: -50px;
        display: none;
        background: white;
        z-index: 5;
      }
      &.icon-item {
        .iconfont {
          font-size: 22px;
          font-weight: bold;
          color: #5e5c5c;
          &.selected {
            color: #ff9000;
          }
        }
      }
    }
  }
  .shop-wrap {
    float: right;
    border: 1px solid #e9e9e9;
    padding: 0 20px;
    width: 234px;
    box-sizing: border-box;
    .head-wrap {
      text-align: center;
      padding: 16px 0;
      border-bottom: 1px solid #f1f1f1;
      .img-wrap {
        width: 64px;
        height: 64px;
        line-height: 64px;
        display: inline-block;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
      .site-name {
        font-size: 14px;
        color: #383838;
      }
      .tag {
        margin-left: 10px;
        font-size: 14px;
      }
    }
    .info-wrap {
      padding: 10px 0;
      border-bottom: 1px solid #f1f1f1;
      dl {
        font-size: $ns-font-size-sm;
        dt {
          color: #838383;
          // width: 65px;
          line-height: 25px;
          font-weight: normal;
          float: left;
        }
        dt.site-score {
          padding-right: 5px;
        }
        dd {
          height: 25px;
          line-height: 25px;
          // margin-left: 50px;
          color: #838383;
          span {
            color: #383838;
            font-weight: 600;
          }

          .el-rate {
            padding-top: 2px;
          }
        }
      }
    }
    .operation {
      padding: 12px 0;

      .el-button {
        padding: 10px 16px;
      }
    }
  }
  .detail-wrap {
    padding: 64px 0 35px;
    clear: both;
    .goods-recommended {
      float: left;
      width: 234px;
    }
    .goods-tab {
      .attr-list {
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        li {
          margin-bottom: 5px;
          padding: 0 10px;
          flex: 0 0 30%;
          width: 33%;
        }
      }
      .el-tabs__header {
        margin-bottom: 30px;
      }
      .tablist {
        border-radius: 0;
      }
      .el-tabs__item {
        position: relative;
        width: 168px;
        height: 52px;
        line-height: 52px;
        text-align: center;
        font-size: 18px;
        &.is-active {
          color: $base-color;
          &:after {
            content: '';
            position: absolute;
            top: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: $base-color;
          }
        }
        &:hover {
          color: $base-color;
        }
      }
      .evaluate {
        .empty {
          color: #898989;
          text-align: center;
          font-size: 14px;
        }
        nav {
          padding: 10px;
          overflow: hidden;
          background: #f7f7f7;
          li {
            float: left;
            padding: 0 20px;
            cursor: pointer;

            &:first-child {
              padding-left: 0;
            }
            &:hover,
            &.selected {
              color: $base-color;
            }
          }
        }
        .list {
          margin-top: 10px;
          font-size: 14px;
          li {
            padding: 15px;
            border-bottom: 1px solid #e5e5e5;
            .member-info {
              float: left;
              width: 140px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              img {
                width: 25px;
                height: 25px;
                border-radius: 50%;
                margin-right: 5px;
              }
            }
            .info-wrap {
              margin-left: 150px;
              margin-bottom: 10px;
              .content {
                padding: 10px 0;
              }
              .img-list {
                .el-image {
                  width: 50px;
                  margin-left: 10px;
                  &:first-child {
                    margin-left: 0;
                  }
                }
              }
              .sku-info {
                color: #898989;
                font-size: 12px;
                span {
                  margin-right: 5px;
                  &.create-time {
                    margin-left: 10px;
                  }
                }
              }
              .evaluation-reply {
                margin: 10px 0;
                color: $base-color;
              }
              .review-evaluation {
                .review-time {
                  font-size: $ns-font-size-sm;
                  margin-left: 20px;
                  color: #898989;
                }
              }
            }
          }
        }
        .page-wrap {
          margin-top: 10px;
          text-align: right;
        }
      }
    }
  }

  // 组合套餐
  .bundling-wrap {
    clear: both;
    margin-bottom: 20px;

    .el-tabs__active-bar {
      background-color: $base-color;
    }
    .el-tabs__item {
      font-size: 18px;
      &.is-active {
        color: $base-color;
      }
      &:hover {
        color: $base-color;
      }
    }
    .master {
      float: left;
      width: 150px;
      position: relative;
      .sku-img {
        text-align: center;
        width: 100px;
        height: 100px;
        margin-bottom: 10px;
      }
      .sku-name {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .sku-price {
        font-weight: bold;
        font-size: $ns-font-size-base;
      }
      i {
        color: #cccccc;
        font-size: 24px;
        font-weight: bold;
        width: 23px;
        height: 23px;
        overflow: hidden;
        position: absolute;
        right: 0;
        top: 40px;
      }
    }
    .operation {
      float: right;
      position: relative;
      width: 150px;
      padding-left: 50px;
      padding-top: 35px;
      .price-wrap {
        .bl-price {
          font-size: 18px;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      i {
        overflow: hidden;
        position: absolute;
        left: 0;
        top: 30px;
        text-align: center;
        font-size: 34px;
        font-weight: bold;
        color: #cccccc;
      }
    }
    .suits {
      // margin-left: 170px;
      ul {
        overflow: hidden;
        display: flex;
        flex-wrap: wrap;
        li {
          // float: left;
          width: 110px;
          margin-right: 25px;
          margin-bottom: 20px;
          cursor: pointer;
          &:first-child {
            margin-left: 0;
          }
          .sku-img {
            text-align: center;
            width: 110px;
            height: 110px;
          }
          .sku-name {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-top: 5px;
          }
          .sku-price {
            font-weight: bold;
            font-size: $ns-font-size-base;
          }
        }
      }
    }
  }
}

.restrictions {
  margin-left: 14px;
  color: red;
}

body {
  background-color: #fff !important;
}
