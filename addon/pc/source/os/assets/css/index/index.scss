.index-wrap {
	width: 100%;
	height: 500px;
}

.index {
	width: $width;
	margin: 0 auto;
	box-sizing: border-box;
	display: flex;
	flex-direction: row;
	justify-content: space-between;

	.border-right {
		border-right: 1px solid #f5f5f5;
	}

	.border-bottom {
		border-bottom: 1px solid #f5f5f5;
	}

	.banner {
		height: 430px;
		width: 100%;
    .el-carousel__indicators--horizontal{
      padding-left: 240px;
    }
		.el-image {
			width: 100%;
			height: 100%;
		}
	}

}

.index-content-wrap {
	width: $width;
	margin: auto;
  // 中部广告位
  .adv-middle{
    display: flex;
    margin-top: 30px;
    .adv-middle-item{
      margin-right: 15px;
      height: 210px;
      // flex: 1;
      transition: all 0.2s linear 0s;
      cursor: pointer;
      .el-image{
        height: 210px;
      }
      &:last-of-type{
        margin-right: 0;
      }
      &:hover{
        z-index: 2;
        box-shadow: rgb(0 0 0 / 10%) 0 15px 30px;
        transform: translate3d(0px, -2px, 0px);
      }
      img{
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

	// 广告
	.ad-wrap {
		width: $width;
		margin: 20px auto;
		display: flex;
		justify-content: space-between;

		> div {
			width: calc((100% - 15px) / 2);
			height: 372px;
		}

		.ad-big {
			display: flex;
			justify-content: space-between;

			.ad-big-img {
				width: calc((100% - 15px) / 2);
				cursor: pointer;
			}
		}

		.ad-small {
			display: flex;
			justify-content: space-between;
			align-content: space-between;
			flex-wrap: wrap;

			.ad-small-img {
				width: calc((100% - 15px) / 2);
				height: calc((100% - 15px) / 2);
				cursor: pointer;
			}
		}
	}

	// 限时秒杀
	.seckill-wrap {
		height: 376px;
		width: $width;
		background-color: #ffffff;
		margin: 20px auto;
		border: 1px solid #e9e9e9;
	}

	.seckill-time {
		width: 100%;
		height: 45px;
		line-height: 44px;
		border-bottom: 1px solid #e9e9e9;
		display: flex;
		justify-content: space-between;
		padding: 0 20px;
		box-sizing: border-box;

		.seckill-time-left {
			color: #383838;

			i,
			.seckill-time-title {
				font-size: 18px;
				font-weight: 600;
				margin-right: 5px;
			}

			.seckill-time-title {
				margin-right: 15px;
			}
		}

		.count-down {
			display: inline-block;
			margin-left: 7px;
		}

		.seckill-time-right {
			color: #838383;
			cursor: pointer;
		}
	}

	.seckill-content {
		padding: 20px 20px 0;
	}

	.seamless-warp2 {
		overflow: hidden;

		ul.item {
			li {
				float: left;
				width: 250px;
				padding: 0 25px;
				box-sizing: border-box;
				border-right: 1px solid #f1f1f1;

				.seckill-goods {
					cursor: pointer;

					.seckill-goods-img {
						width: 100%;
						height: 200px;
					}
					img {
						object-fit: cover;
					}

					> p {
						line-height: 24px;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						height: 50px;
					}

					.seckill-price-wrap {
						display: flex;
						align-items: flex-end;
						height: 24px;
						line-height: 24px;
						margin-top: 10px;

						p span {
							font-size: 24px;
						}

						.primary-price {
							text-decoration: line-through;
							color: #838383;
							margin-left: 10px;
						}
					}
				}
			}
		}
	}
}

.floor {
	width: $width;
	margin: 20px auto;

	.floor_item {
		margin-top: 10px;
	}
}

.floatLayer-wrap {
	position: fixed;
	height: 100vh;
	width: 100vw;
	left: 0;
	top: 0;
	background: rgba($color: #000000, $alpha: 0.5);
	z-index: 999;

	.floatLayer {
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 999;
		cursor: pointer;
		.img-wrap {
			width: 456px;
			height: 274px;
			line-height: 274px;
			overflow: hidden;
			text-align: center;
			img{
				max-height: 100%;
			}
		}

		i {
			color: #ffffff;
			font-size: 30px;
			position: absolute;
			top: -30px;
			right: -30px;
		}
	}
}

// 悬浮搜索
.fixed-box {
	width: 100vw;
	background-color: #ffffff;
	position: fixed;
	top: 0;
  left: 0;
	z-index: 999;
	border-bottom: 2px solid $base-color;
}
