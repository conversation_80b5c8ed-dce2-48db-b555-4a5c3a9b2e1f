{"name": "nuxt", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate"}, "dependencies": {"@nuxtjs/axios": "^5.13.6", "core-js": "^3.25.3", "element-ui": "^2.13.2", "js-cookie": "^2.2.1", "jsencrypt": "^3.0.0-rc.1", "nuxt": "^2.15.8", "path-to-regexp": "^6.1.0", "sass-resources-loader": "^2.0.3", "vue": "^2.7.10", "vue-piczoom": "^1.0.6", "vue-seamless-scroll": "^1.1.23", "vue-server-renderer": "^2.7.10", "vue-template-compiler": "^2.7.10", "vue-video-player": "^5.0.2", "vue2-countdown": "^1.0.8", "vuex-persistedstate": "^3.0.1"}, "devDependencies": {"@nuxtjs/style-resources": "^1.2.1", "sass": "^1.39.0", "sass-loader": "^10.1.1"}}