<link rel="stylesheet" href="STATIC_CSS/promotion_detail.css">

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">基本信息</span>
	</div>
	<div class="layui-card-body">
		<div class="promotion-view">
			<div class="promotion-view-item">
				<label>活动名称：</label>
				<span>{$info.pintuan_name}</span>
			</div>
			<div class="promotion-view-item">
				<label>活动状态：</label>
				<span>{if condition="$info.status == 0"}未开始{/if}{if condition="$info.status == 1"}进行中{/if}{if condition="$info.status == 2"}已结束{/if}{if condition="$info.status == 3"}已失效{/if}</span>
			</div>
			<div class="promotion-view-item">
				<label>拼团类型：</label>
				<span>{$info.pintuan_type == 'ordinary' ? '普通拼团' : '阶梯拼团'}</span>
			</div>
			<div class="promotion-view-item">
				<label>拼团人数：</label>
				<span>{$info.pintuan_num}人团</span>
				{if $info.pintuan_type == 'ladder'}<span> {$info.pintuan_num_2}人团</span>{/if}
				{if $info.pintuan_type == 'ladder' && $info.pintuan_num_3 > 0}<span> {$info.pintuan_num_3}人团</span>{/if}
			</div>
			<div class="promotion-view-item">
				<label>开始时间：</label>
				<span>{:date('Y-m-d H:i:s',$info.start_time)}</span>
			</div>
			<div class="promotion-view-item">
				<label>结束时间：</label>
				<span>{:date('Y-m-d H:i:s',$info.end_time)}</span>
			</div>
			<div class="promotion-view-item">
				<label>单人限制购买数量：</label>
				<span>{$info.buy_num}</span>
			</div>
			<div class="promotion-view-item">
				<label>是否是单独购买：</label>
				<span>{$info.is_single_buy == 0 ? '否' : '是'}</span>
			</div>
			<div class="promotion-view-item">
				<label>是否是虚拟成团：</label>
				<span>{$info.is_virtual_buy == 0 ? '否' : '是'}</span>
			</div>
			<div class="promotion-view-item">
				<label>是否有团长优惠：</label>
				<span>{$info.is_promotion == 0 ? '否' : '是'}</span>
			</div>
		</div>
	</div>
</div>

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">数据统计</span>
	</div>
	<div class="layui-card-body">
		<div class="promotion-stat-view todo-list">
			<div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">开团数</div>
				<div class="promotion-stat-item-value">{$info.group_num}</div>
			</div>
			<div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">成团数</div>
				<div class="promotion-stat-item-value">{$info.success_group_num}</div>
			</div>

			<div class="promotion-stat-item" >
				<div class="promotion-stat-item-title">订单数</div>
				<div class="promotion-stat-item-value">{$info.order_num}</div>
			</div>
		</div>
	</div>
</div>

<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
        <span class="card-title">活动商品</span>
	</div>
	<div class="layui-card-body">
		<div class='promotion-view-list'>
			<table id="promotion_list"></table>
		</div>
	</div>
</div>

<script type='text/html' id="promotion_list_item_box_html">
	<div class="promotion-list-item-title">
		<div class="promotion-list-item-title-icon">
			<img src="{{ ns.img(d.sku_image) }}" alt="">
		</div>
		<p class="promotion-list-item-title-name multi-line-hiding">{{ d.sku_name }}</p>
	</div>
</script>
<script>
	var list = {:json_encode($info.sku_list, JSON_UNESCAPED_UNICODE)};
	layui.use('table', function() {
		new Table({
			elem: '#promotion_list',
			cols: [
				[{
					field: 'sku_name',
					title: '商品名称',
					width: '26%',
					unresize: 'false',
					templet: "#promotion_list_item_box_html"
				}, {
					title: '商品价格(元)',
					unresize: 'false',
					align: 'left',
					width: '20%',
					templet: function(data) {
						return '￥' + data.price;
					}
				}
				{if $info.pintuan_type == 'ordinary'}
					, {
						title: '拼团价格(元)',
						unresize: 'false',
						width: '20%',
						templet: function(data) {
							return '￥' + data.pintuan_price;
						}
					},
					{if condition="$info.is_promotion == 1"}
					{
						title: '团长优惠价(元)',
						unresize: 'false',
						templet: function(data) {
							return '￥' + data.promotion_price;
						}
					}
					{/if}
				{/if}
				{if $info.pintuan_type == 'ladder'}
					,{
						title: '{$info.pintuan_num}人团(元)',
						unresize: 'false',
						width: '20%',
						templet: function(data) {
							return '￥' + data.pintuan_price;
						}
					},
					{
						title: '{$info.pintuan_num_2}人团(元)',
						unresize: 'false',
						templet: function(data) {
							return '￥' + data.pintuan_price_2;
						}
					}
					{if condition="$info.pintuan_num_3 > 0"}
					,{
						title: '{$info.pintuan_num_3}人团(元)',
						unresize: 'false',
						templet: function(data) {
							return '￥' + data.pintuan_price_3;
						}
					}
					{/if}
				{/if}
				]
			],
			data: list
		});
	});
</script>