<style type="text/css">
.limit .layui-input{display: inline-block;}
.limit .layui-form-mid{float: none;margin-right: 0}
.limit .layui-form-radio{padding: 0;margin: 0;}
.limit .layui-input[disabled]{background: #eee;cursor:not-allowed}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>积分抵现：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" value="1" lay-filter="is_use" lay-skin="switch" {if condition="$is_use == 1"} checked {/if} >
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>积分抵现比例：</label>
		<div class="layui-input-inline limit">
			<input type="number" name="cash_rate" value="{$config.cash_rate}" lay-verify="cash_rate" autocomplete="off" class="layui-input len-short">
			<div class="layui-form-mid">积分可抵1元</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>订单金额门槛：</label>
		<div class="layui-input-inline">
			<input type="radio" name="is_limit" value="0" title="不限制" lay-filter="is_limit" {if condition="$config.is_limit == 0"}checked{/if}>
			<input type="radio" name="is_limit" value="1" title="限制" lay-filter="is_limit" {if condition="$config.is_limit == 1"}checked{/if}>
		</div>
	</div>

	<div class="layui-form-item {if condition='$config.is_limit == 0'}layui-hide{/if} is-limit">
		<label class="layui-form-label"></label>
		<div class="layui-input-block limit">
			<div class="layui-form-mid">订单金额超出</div>
			<input type="number" name="limit" value="{$config.limit}" lay-verify="" autocomplete="off" class="layui-input len-short">
			<div class="layui-form-mid">元，可使用积分抵现</div>
		</div>
		<!-- <div class="word-aux">
			<p>订单金额最低为1元</p>
		</div> -->
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>抵现金额上限：</label>
		<div class="layui-input-inline">
			<input type="radio" name="is_limit_use" value="0" title="不限制" lay-filter="is_limit_use" {if condition="$config.is_limit_use == 0"}checked{/if}>
			<input type="radio" name="is_limit_use" value="1" title="限制" lay-filter="is_limit_use" {if condition="$config.is_limit_use == 1"}checked{/if}>
		</div>
	</div>

	<div class="layui-form-item {if condition='$config.is_limit_use == 0'}layui-hide{/if} is-limit-use">
		<label class="layui-form-label"></label>
		<div class="layui-input-block limit">
			<div>
				<input type="radio" name="type" value="0" lay-filter="type" {if condition="$config.type == 0"}checked{/if}>
				<div class="layui-form-mid">每笔订单最多抵扣</div>
				<input type="number" name="money" 
					{if condition="$config.type == 0"} value="{$config.max_use}"{else/} value="{$config.max_use}" disabled{/if}
					{if condition="$config.type == 0 && $config.is_limit_use == 1"} lay-verify="money" {else/} lay-verify="" {/if}
					autocomplete="off" class="layui-input len-short">
				<div class="layui-form-mid">元</div>
			</div>
			<div style="margin-top: 10px;">
				<input type="radio" name="type" value="1" lay-filter="type" {if condition="$config.type == 1"}checked{/if}>
				<div class="layui-form-mid">每笔订单最多抵扣订单金额的</div>
				<input type="number" name="ratio" 
					{if condition="$config.type == 1"} value="{$config.max_use}"{else/} value="{$config.max_use}" disabled lay-verify=""{/if}
					{if condition="$config.type == 1 && $config.is_limit_use == 1"} lay-verify="ratio" {else/} lay-verify="" {/if} 
					autocomplete="off" class="layui-input len-short" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
				<div class="layui-form-mid">%</div>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backPromotionIndex()">返回</button>
	</div>
</div>

<script>
	layui.use(['form', 'laydate'], function() {
		var form = layui.form,repeat_flag = false; //防重复标识

		/**
		 * 表单验证
		 */
		form.verify({
			cash_rate: function(value){
				if (!/[\S]+/.test(value)) {
					return '请输入积分抵现比例';
				}
				if (Number(value) <= 0) {
					return '积分抵现比不能小于等于0';
				}
			},
			limit: function(value) {
				if (!/[\S]+/.test(value)) {
					return '请输入订单金额';
				}
				if (Number(value) <= 1) {
					return '订单金额不能小于1元';
				}
				
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '订单金额最多保留两位小数';
				}
			},
			money: function(value){
				if (!/[\S]+/.test(value)) {
					return '请输入每单最大可抵金额';
				}
				if (Number(value) <= 0) {
					return '可抵金额不能小于等于0元';
				}
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				if (val.length > 2) {
					return '可抵金额最多保留两位小数';
				}
			},
			ratio: function(value){
				if (!/[\S]+/.test(value)) {
					return '请输入每单最大抵现比率';
				}
				if (Number(value) <= 0 || Number(value) > 100) {
					return '抵现比率需在1-100之间';
				}
			}
		});
		
		form.on('radio(is_limit)', function(data){
			if (data.value == 1) {
				$(".is-limit").removeClass("layui-hide");
				$("[name='limit']").attr("lay-verify", 'limit');
			} else {
				$(".is-limit").addClass("layui-hide");
				$("[name='limit']").removeAttr("lay-verify");
			}
		});

		form.on('radio(is_limit_use)', function(data){
			if (data.value == 1) {
				$(".is-limit-use").removeClass("layui-hide");
			} else {
				$(".is-limit-use").addClass("layui-hide");
				$("[name='ratio'],[name='money']").removeAttr("lay-verify");
			}
		});

		form.on('radio(type)', function(data){
			if (data.value == 1) {
				$("[name='ratio']").prop({'disabled': false, 'lay-verify': 'ratio'});
				$("[name='money']").prop({'disabled': true, 'lay-verify': ''});
			} else {
				$("[name='money']").prop({'disabled': false, 'lay-verify': 'ratio'});
				$("[name='ratio']").prop({'disabled': true, 'lay-verify': ''});
			}
		});
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data){
			data.field.max_use = data.field.type == 0 ? data.field.money : data.field.ratio;
			
			if(repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("pointcash://shop/config/index"),
				data: data.field,
				async: false,
				success: function(res){
					repeat_flag = false;
					layer.msg(res.message);
				}
			})
		});
	});
	
	function backPromotionIndex() {
		location.hash = ns.hash("shop/promotion/index");
	}
</script>
