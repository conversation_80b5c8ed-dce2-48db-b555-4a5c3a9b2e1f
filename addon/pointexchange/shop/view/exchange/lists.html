<style>
    .edit-sort{width: 70px !important;}
    .contraction span{cursor: pointer;display: inline-block;width: 17px;height: 17px;text-align: center;line-height: 14px;user-select: none;}
    .contraction-disable span{cursor: pointer;display: inline-block;width: 17px;height: 17px;text-align: center;line-height: 14px;user-select: none;}
    .sku-list{overflow: hidden;padding: 0 45px;max-width: 100%;}
    .sku-list li .img-wrap{vertical-align: middle;margin-right: 8px;width: 120px;height: 120px;text-align: center;line-height: 120px;}
    .sku-list li .img-wrap img{max-width: 100%;max-height: 100%;}
    .sku-list li .info-wrap span.sku-name{-webkit-line-clamp: 2;margin-bottom: 5px;}
    .sku-list li .info-wrap span{display: -webkit-box;margin-bottom: 5px;overflow: hidden;text-overflow: ellipsis;white-space: normal;word-break: break-all;-webkit-box-orient: vertical;-webkit-line-clamp: 1;}
    .sku-list li{float: left;display: flex;padding: 10px;margin-right: 10px;margin-bottom: 10px;border: 1px solid #EFEFEF;width: 294px;height: 180px;align-items: center;}
    .layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加积分商品</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
    <div class="layui-colla-item">
        <form class="layui-colla-content layui-form layui-show">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">商品名称：</label>
                    <div class="layui-input-inline">
                        <input type="text" name="search_text" placeholder="兑换商品名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">商品状态：</label>
                    <div class="layui-input-inline">
                        <select name="state" lay-filter="state">
                            <option value="">全部</option>
                            <option value="1">上架</option>
                            <option value="0">下架</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
    </div>
</div>

<div class="layui-tab table-tab" lay-filter="type_name">
    <ul class="layui-tab-title">
        <li class="layui-this" lay-id="">全部</li>
        <li lay-id="1">商品</li>
        <li lay-id="2">优惠券</li>
        <li lay-id="3">红包</li>
    </ul>

    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="exchange_list" lay-filter="exchange_list"></table>
    </div>
</div>

<!-- 兑换信息 -->
<script type="text/html" id="exchange_info">

    <div class="table-title">
        {{# if(d.type == 1){ }}
        <div class="contraction" data-id="{{d.id}}" data-open="0">
            <span>+</span>
        </div>
        {{# }else{  }}
        <div class="contraction-disable">
            <span></span>
        </div>
        {{# } }}
        {{# if(d.type == 1){ }}
        <div class="title-pic">
            {{#  if(d.image){  }}
            <img layer-src="{{ns.img(d.image.split(',')[0],'big')}}" src="{{ns.img(d.image.split(',')[0],'small')}}"/>
            {{#  }  }}
        </div>
        {{# } }}
        {{# if(d.type == 2){  }}
        <div class="title-pic">
            {{#  if(d.image){  }}
            <img layer-src src="{{ns.img(d.image)}}"/>
            {{# }else{  }}
            <img layer-src src="__STATIC__/img/coupon_default.png"/>
            {{#  }  }}
        </div>
        {{# } }}
        {{# if(d.type == 3){  }}
        <div class="title-pic">
            {{#  if(d.image){  }}
            <img layer-src src="{{ns.img(d.image)}}"/>
            {{# }else{  }}
            <img layer-src src="__STATIC__/img/hongbao_default.png"/>
            {{#  }  }}
        </div>
        {{# } }}
        <div class="contraction-disable">
            <span></span>
        </div>
        <div class="title-content">
            {{# if(d.type == 1){  }}
            <a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.name}}">{{d.g_name}}</a>
            {{# }else{  }}
            <a href="javascript:;" class="multi-line-hiding text-color-sub" title="{{d.name}}">{{d.name}}</a>
            {{# } }}
        </div>
    </div>

</script>

<!-- 状态 -->
<script type="text/html" id="state">
    {{d.state == 1?'上架':'下架'}}
</script>

<!-- 批量操作 -->
<script type="text/html" id="toolbarAction">
    <button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
</script>

<!-- 编辑删除操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        <a class="layui-btn" lay-event="order">兑换记录</a>
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>

<script type="text/html" id="skuList">
    <tr class="js-list-{{d.index}}" id="sku_img_{{d.index}}">
        <td colspan="10">
            <ul class="sku-list">
                {{# for(var i=0; i<d.list.length; i++){ }}
                <li>
                    <div class="img-wrap">
                        <img layer-src src="{{ns.img(d.list[i].sku_image)}}">
                    </div>
                    <div class="info-wrap">
                        <span class="sku-name" title="{{d.list[i].sku_name}}">{{d.list[i].sku_name}}</span>
                        <span class="price">商品价格：￥{{d.list[i].market_price}}</span>

                        {{# if(d.list[i].price != 0 ){ }}
                        <span class="sale_num">兑换：{{d.list[i].point}}积分 +  {{d.list[i].price}}元 </span>
                        {{# }else{  }}
                        <span class="sale_num">兑换：{{d.list[i].point}}积分 </span>
                        {{# } }}

                        <span class="price">库存：{{d.list[i].stock}}</span>
                    </div>
                </li>
                {{# } }}
            </ul>
        </td>
    </tr>
</script>

<!-- 编辑排序 -->
<script type="text/html" id="editSort">
    <input name="sort" type="number" onchange="editSort({{d.id}}, this)" value="{{d.sort}}" class="layui-input edit-sort len-short">
</script>

<script>
    var table,laytpl;
    $("body").off("click", ".contraction").on("click", ".contraction", function () {
        var exchange_id = $(this).attr("data-id");
        var open = $(this).attr("data-open");
        var tr = $(this).parent().parent().parent().parent();
        var index = tr.attr("data-index");
        if (open == 1) {
            $(this).children("span").text("+");
            $(".js-list-" + index).remove();
        } else {
            $(this).children("span").text("-");
            $.ajax({
                url: ns.url("pointexchange://shop/exchange/getSkuList"),
                data: {exchange_id: exchange_id},
                dataType: 'JSON',
                type: 'POST',
                async: false,
                success: function (res) {
                    var sku_list = $("#skuList").html();
                    var data = {
                        list: res.data,
                        index: index
                    };
                    laytpl(sku_list).render(data, function (html) {
                        tr.after(html);
                    });
                    layer.photos({
                        photos: '.img-wrap',
                        anim: 5
                    });
                }
            });
        }
        $(this).attr("data-open", (open == 0 ? 1 : 0));
    });

    layui.use(['form', 'element', 'laytpl'], function() {
        laytpl = layui.laytpl;
        var form = layui.form,
            element = layui.element,
            repeat_flag = false; //防重复标识
        form.render();

        //监听Tab切换
        element.on('tab(type_name)', function(data) {
            var type = $(this).attr("lay-id");
            table.reload( {
                page: {
                    curr: 1
                },
                where: {
                    'type': type
                }
            });
        });

        table = new Table({
            elem: '#exchange_list',
            url: ns.url("pointexchange://shop/exchange/lists"),
            cols: [
                [{
                    type: 'checkbox',
                    width: '3%',
                },{
                    field: 'name',
                    title: '兑换信息',
                    unresize: 'false',
                    width: '30%',
                    templet: '#exchange_info'
                }, {
                    field: 'type_name',
                    title: '兑换类型',
                    unresize: 'false',
                    width: '10%',
                },  {
                    title: '兑换价格',
                    unresize: 'false',
                    width: '10%',
                    templet: function(data) {
                        if(data.pay_type == 1){
                            return data.point+'积分 + '+data.price + "元";
                        }else{
                            return data.point+'积分';
                        }
                    }
                }, {
                    field: 'create_time',
                    title: '添加时间',
                    unresize: 'false',
                    width: '15%',
                    align: 'center',
                    templet: function(data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
                    field: 'state',
                    title: '状态',
                    unresize: 'false',
                    width: '10%',
                    align: 'center',
                    templet: '#state',
                }, {
                    field: 'sort',
                    unresize:'false',
                    title: '排序',
                    width: '10%',
                    align: 'center',
                    templet: '#editSort',
                    sort: true
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
                    width: '12%',
                    align:'right'
                }]
            ],
            toolbar: '#toolbarAction'
        });

        // 监听工具栏操作
        table.toolbar(function (obj) {
            var data = obj.data;
            if(data.length <= 0) return;
            var exchangeIdAll = [];
            for (var i in data){
                exchangeIdAll.push(data[i].id);
            }

            switch (obj.event) {
                case 'delete':
                    deleteExchangeAll(exchangeIdAll)
                    break;
            }
        })

        function deleteExchangeAll(data){
            layer.confirm('确定要删除商品吗?', function(index) {
                if (repeat_flag) return false;
                repeat_flag = true;

                layer.close(index);

                $.ajax({
                    url: ns.url("pointexchange://shop/exchange/deleteAll"),
                    data: {exchange_id: data},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        table.reload({
                            page: {
                                curr: 1
                            },
                        });
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        }

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'order': //管理
                    location.hash = ns.hash("pointexchange://shop/pointexchange/lists?exchange_id=" + data.id);
                    break;
                case 'edit': //管理
                    location.hash = ns.hash("pointexchange://shop/exchange/edit?id=" + data.id);
                    break;
                case 'delete': //删除
                    deleteGift(data.id);
                    break;
            }
        });

        table.on("sort",function (obj) {
                table.reload({
                    page: {
                        curr: 1
                    },
                    where: {
                        order:obj.field,
                        sort:obj.type
                    }
                });
        });

        /**
         * 删除
         */
        function deleteGift(id) {
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要删除该商品吗?', function(index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("pointexchange://shop/exchange/delete"),
                    data: {id},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload({
                                page: {
                                    curr: 1
                                },
                            });
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        }
        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });
    });

    function add() {
        location.hash = ns.hash("pointexchange://shop/exchange/add");
    }

    // 监听单元格编辑
    function editSort(id, event){
        var data = $(event).val();

        if (data == '') {
            $(event).val(0);
            data = 0;
        }

        if(!new RegExp("^-?[0-9]\\d*$").test(data)){
            layer.msg("排序号只能是整数");
            return ;
        }
        if(data<0){
            layer.msg("排序号必须大于0");
            return ;
        }
        $.ajax({
            type: 'POST',
            url: ns.url("pointexchange://shop/exchange/modifySort"),
            data: {
                sort: data,
                id: id
            },
            dataType: 'JSON',
            success: function(res) {
                layer.msg(res.message);
                if(res.code==0){
                    table.reload();
                }
            }
        });
    }
</script>
