<!-- 订单物流发货 -->
<style>

    .layui-table-body {
        overflow: unset;
    }

    .delivery-content {
        padding: 7px 0 !important;
    }

    .layui-table-view {
        border-top: 1px solid #eee;
        /*border-bottom: 1px solid #eee;*/
    }

    .order-delivery .layui-table {
        /*margin-bottom: 30px;*/
    }

    .layui-form #order_goods_list thead th, .layui-form #order_goods_list tbody tr {
        border-bottom: 1px solid #E6E6E6;
    }

    .layui-form #order_goods_list thead th {
        background-color: #F5F5F5;
        line-height: 30px;
    }

    .order-delivery .input-text {
        height: auto;
        min-height: 34px;
    }
</style>
<!--发货订单弹出框-->
<script type="text/html" id="order_delivery_html">
    <div class="order-delivery">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">收货地址：</label>
                <div class="layui-input-block">
                    <p class="input-text len-long"> {{ d.full_address }}{{ d.address }}</p>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">发货编号：</label>
                <div class="layui-input-block">
                    <input type="text" name="delivery_code" lay-verify="required" placeholder="" autocomplete="off" class="layui-input len-mid">
                </div>
            </div>

            <input type="hidden" name="order_id" value="{{ d.order_id }}" class="layui-input" />
            <div class="form-row">
                <button type="button" class="layui-btn" lay-submit id="button_delivery_order" lay-filter="button_delivery_order" style="display:none;">保存
                </button>
            </div>
        </div>
    </div>
</script>

<script>
    /**
     * 外卖配送订单发货
     */
    var submitting = false;

    function orderDelivery(data) {
        layui.use(['form', 'laytpl'], function () {
            var laytpl = layui.laytpl, form = layui.form;
            form.render();
            //获取模板
            var getTpl = $("#order_delivery_html").html();

            laytpl(getTpl).render(data, function (html) {
                layer.open({
                    type: 1,
                    shadeClose: true,
                    shade: 0.3,
                    fixed: false,
                    scrollbar: false,
                    title: "订单发货",
                    area: '800px',
                    btn: ['保存'],
                    yes: function (index, layero) {
                        $("#button_delivery_order").click();
                    },
                    content: html,
                    cancel: function (index, layero) {
                        //右上角关闭回调
                        layer.close(index);
                        //return false 开启该代码可禁止点击该按钮关闭
                    },
                    success: function (layero, index) {
                        form.render();

                        form.on('submit(button_delivery_order)', function (data) {
                            if (submitting) return false;
                            submitting = true;

                            $.ajax({
                                type: "post",
                                url: ns.url("pointexchange://shop/pointexchange/delivery"),
                                async: true,
                                dataType: 'json',
                                data: data.field,
                                success: function (res) {
                                    layer.msg(res.message, {}, function () {
                                        submitting = false;
                                        if (res.code == 0) {
                                            listenerHash(); // 刷新页面
                                            layer.closeAll();
                                        }
                                    });
                                }
                            })
                        });

                    }

                });
            })
        })

    }

    function delivery(order_info){
        //只有商品订单才需要发货
        if(order_info.type == 1){
            if(order_info.delivery_type == 'express'){
                orderDelivery(order_info);
            }else if(order_info.delivery_type == 'store'){
                deliveryAction(order_info.order_id);
            }else if(order_info.delivery_type == 'local'){
                deliveryAction(order_info.order_id);
            }
        }
    }

    /**
     * 配送操作
     * @param order_id
     */
    function deliveryAction(order_id){
        $.ajax({
            type: "post",
            url: ns.url("pointexchange://shop/pointexchange/delivery"),
            async: true,
            dataType: 'json',
            data: {order_id:order_id},
            success: function (res) {
                layer.msg(res.message, {}, function () {
                    if (res.code == 0) {
                        listenerHash(); // 刷新页面
                        layer.closeAll();
                    }
                });
            }
        })
    }
</script>