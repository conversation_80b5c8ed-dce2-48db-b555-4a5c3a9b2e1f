<style>
	.screen{margin-bottom: 15px;}
	.contraction span{cursor: pointer;display: inline-block;width: 17px;height: 17px;text-align: center;line-height: 14px;user-select: none;}
	.sku-list{overflow: hidden;padding: 0 45px;}
	.sku-list li .img-wrap{vertical-align: middle;margin-right: 8px;width: 120px;height: 120px;text-align: center;line-height: 120px;}
	.sku-list li .img-wrap img{max-width: 100%;max-height: 100%;}
	.sku-list li .info-wrap span.sku-name{-webkit-line-clamp: 2;margin-bottom: 5px;}
	.sku-list li .info-wrap span{display: -webkit-box;margin-bottom: 5px;overflow: hidden;text-overflow: ellipsis;white-space: normal;word-break: break-all;-webkit-box-orient: vertical;-webkit-line-clamp: 1;}
	.sku-list li{float: left;display: flex;padding: 10px;margin-right: 10px;margin-bottom: 10px;border: 1px solid #EFEFEF;width: 294px;height: 280px;align-items: center;}
	.single-filter-box button{margin-bottom: 20px;}
	.layui-layout-admin .single-filter-box button{margin-bottom: 0;}
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
</style>

<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加预售</button>
</div>

<!-- 搜索框 -->
<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">商品名称：</label>
					<div class="layui-input-inline">
						<input type="text" name="goods_name" placeholder="请输入商品名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">预售时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button type="button" class="layui-btn" lay-filter="search" lay-submit>筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<div class="layui-tab table-tab" lay-filter="presale_tab">
	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		{foreach $presale_status as $k=>$v}
		<li data-status="{$k}">{$v}</li>
		{/foreach}
	</ul>
	<div class="layui-tab-content">
		<!-- 列表 -->
		<table id="presale_list" lay-filter="presale_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods_info">
	<div class="table-title">

		<div class="contraction" data-id="{{d.presale_id}}" data-open="0">
			<span>+</span>
		</div>

		<div class="title-pic">
			{{#  if(d.goods_image){  }}
			<img layer-src="{{ns.img(d.goods_image.split(',')[0],'big')}}" src="{{ns.img(d.goods_image.split(',')[0],'small')}}"/>
			{{#  }  }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color" title="{{d.goods_name}}">{{d.goods_name}}</a>
		</div>
	</div>
</script>
<!-- 时间 -->
<script id="time" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.end_time)}}</div>
</script>

<!-- 时间 -->
<script id="payTime" type="text/html">
	<div class="layui-elip">开始：{{ns.time_to_date(d.pay_start_time)}}</div>
	<div class="layui-elip">结束：{{ns.time_to_date(d.pay_end_time)}}</div>
</script>

<!-- 推广 -->
{include file="app/shop/view/component/promote_show.html"}

<!-- 批量操作 -->
<script type="text/html" id="toolbarAction">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
	<button class="layui-btn layui-btn-primary" lay-event="close">批量关闭</button>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="operation-wrap" data-presale-id="{{d.presale_id}}">
		<div class="popup-qrcode-wrap" style="display: none"><img class="popup-qrcode-loadimg" src="__STATIC__/loading/loading.gif"/></div>
		<div class="table-btn">
			{{# if(d.status == 1){ }}
			<a class="layui-btn text-color" lay-event="select">推广</a>
			{{# } }}
			<a class="layui-btn" lay-event="detail">详情</a>
			<a class="layui-btn" lay-event="edit">编辑</a>
			{{# if(d.status == 0){ }}
			<a class="layui-btn" lay-event="del">删除</a>
			{{# }else if(d.status == 1){ }}
			<a class="layui-btn" lay-event="launch">预售列表</a>
			<a class="layui-btn" lay-event="close">结束</a>
			{{# }else{ }}
			<a class="layui-btn" lay-event="launch">预售列表</a>
			<a class="layui-btn" lay-event="del">删除</a>
			{{# } }}
		</div>
	</div>
</script>

<!-- 商品sku -->
<script type="text/html" id="skuList">
	<tr class="js-list-{{d.index}}" id="sku_img_{{d.index}}">
		<td colspan="8">
			<ul class="sku-list">
				{{# for(var i=0; i<d.list.length; i++){ }}
				<li>
					<div class="img-wrap">
						<img layer-src src="{{ns.img(d.list[i].sku_image)}}">
					</div>
					<div class="info-wrap">
						<span class="sku-name" title="{{d.list[i].sku_name}}">{{d.list[i].sku_name}}</span>
						<span class="price">商品价格：￥{{d.list[i].price}}</span>
						<span class="price">预售库存：￥{{d.list[i].presale_stock}}</span>
						<span class="price">定金：￥{{d.list[i].presale_deposit}}</span>
						<span class="sale_num">定金抵扣金额：{{d.list[i].presale_price}}</span>
					</div>
				</li>
				{{# } }}
			</ul>
		</td>
	</tr>
</script>

<script>
	var laytpl;
	$(function () {
		$("body").off("click", ".contraction").on("click", ".contraction", function () {
			var presale_id = $(this).attr("data-id");
			var open = $(this).attr("data-open");
			var tr = $(this).parent().parent().parent().parent();
			var index = tr.attr("data-index");
			if (open == 1) {
				$(this).children("span").text("+");
				$(".js-list-" + index).remove();
			} else {
				$(this).children("span").text("-");
				$.ajax({
					url: ns.url("presale://shop/presale/getSkuList"),
					data: {presale_id: presale_id},
					dataType: 'JSON',
					type: 'POST',
					async: false,
					success: function (res) {

						var sku_list = $("#skuList").html();
						var data = {
							list: res.data,
							index: index
						};
						laytpl(sku_list).render(data, function (html) {
							tr.after(html);
						});
						layer.photos({
							photos: '.img-wrap',
							anim: 5
						});
					}
				});
			}
			$(this).attr("data-open", (open == 0 ? 1 : 0));
		});

		layui.use(['form', 'element', 'laytpl', 'laydate'], function () {
			laytpl = layui.laytpl;
			var table,
				form = layui.form,
				element = layui.element,
				laydate = layui.laydate,
				repeat_flag = false; //防重复标识
			form.render();

			element.on('tab(presale_tab)', function () {
				table.reload({
					page: {
						curr: 1
					},
					where: {
						'status': this.getAttribute('data-status')
					}
				});
			});

			//开始时间
			laydate.render({
				elem: '#start_time', //指定元素
            	type: 'datetime'
			});
			//结束时间
			laydate.render({
				elem: '#end_time', //指定元素
                type: 'datetime'
			});

			table = new Table({
				elem: '#presale_list',
				url: ns.url("presale://shop/presale/lists"),
				cols: [
					[{
						type: 'checkbox',
						width: '3%',
					},{
						title: '商品信息',
						unresize: 'false',
						width: '20%',
						templet: '#goods_info'
					}, {
						title: '商品价格',
						unresize: 'false',
						templet: function (data) {
							return '￥' + data.price;
						}
					}, {
						field: 'presale_stock',
						title: '预售总库存',
						unresize: 'false'
					}, {
						field: 'sale_num',
						title: '销量',
						unresize: 'false',
						sort : true
					}, {
						title: '定金支付时间',
						unresize: 'false',
						width: '15%',
						templet: '#time'
					}, {
						title: '尾款支付时间',
						unresize: 'false',
						width: '15%',
						templet: '#payTime'
					}, {
						field: 'status_name',
						title: '状态',
						unresize: 'false',
						width: '8%'
					}, {
						title: '操作',
						toolbar: '#operation',
						unresize: 'false',
						align:'right'
					}]
				],
				toolbar: '#toolbarAction'
			});

			/**
			 * 搜索功能
			 */
			form.on('submit(search)', function (data) {
				table.reload({
					page: {
						curr: 1
					},
					where: data.field
				});
			});

			table.on("sort",function (obj) {
				table.reload({
					page: {
						curr: 1
					},
					where: {
						order:obj.field,
						sort:obj.type
					}
				});
			});

			//监听Tab切换
			element.on('tab(status)', function (data) {
				var status = $(this).attr("data-status");
				table.reload({
					page: {
						curr: 1
					},
					where: {
						'status': status
					}
				});
			});

			// 监听工具栏操作
			table.toolbar(function (obj) {
				var data = obj.data;
				if(data.length <= 0) return;
				var presaleIdAll = [];
				for (var i in data){
					presaleIdAll.push(data[i].presale_id);
				}

				switch (obj.event) {
					case 'delete':
						deletePresaleAll(presaleIdAll)
						break;
					case 'close':
						closePresaleAll(presaleIdAll)
						break;
				}
			})

			function deletePresaleAll(data){
				layer.confirm('确定要删除预售活动吗?', function (index) {
					if (repeat_flag) return;
					repeat_flag = true;
					layer.close(index);

					$.ajax({
						url: ns.url("presale://shop/presale/deleteAll"),
						data: {
							presale_id: data
						},
						dataType: 'JSON',
						type: 'POST',
						success: function (res) {
							layer.msg(res.message);
							repeat_flag = false;
							table.reload({
								page: {
									curr: 1
								},
							});
						}
					});
				}, function () {
					layer.close();
					repeat_flag = false;
				});
			}

			function closePresaleAll(data){
				layer.confirm('确定要结束预售活动吗?', function (index) {
					if (repeat_flag) return;
					repeat_flag = true;
					layer.close(index);

					$.ajax({
						url: ns.url("presale://shop/presale/finishAll"),
						data: {
							presale_id: data
						},
						dataType: 'JSON',
						type: 'POST',
						success: function (res) {
							layer.msg(res.message);
							repeat_flag = false;
							table.reload();
						}
					});
				}, function () {
					layer.close();
					repeat_flag = false;
				});
			}

			/**
			 * 监听工具栏操作
			 */
			table.tool(function (obj) {
				var data = obj.data;
				switch (obj.event) {
					case 'detail': //详情
						location.hash = ns.hash("presale://shop/presale/detail", {"presale_id": data.presale_id});
						break;
					case 'edit': //编辑
						location.hash = ns.hash("presale://shop/presale/edit", {"presale_id": data.presale_id});
						break;
					case 'del': //删除
						deletePresale(data.presale_id);
						break;
					case 'close': // 结束
						closePresale(data.presale_id);
						break;
					case 'select': //推广
						presaleUrl(data);
						break;
					case 'launch': //预售订单
						location.hash = ns.hash("presale://shop/order/lists", {"presale_id": data.presale_id});
						break;
				}
			});

			/**
			 * 删除
			 */
			function deletePresale(presale_id) {
				layer.confirm('确定要删除该预售活动吗?', function (index) {
					if (repeat_flag) return;
					repeat_flag = true;
					layer.close(index);

					$.ajax({
						url: ns.url("presale://shop/presale/delete"),
						data: {
							presale_id: presale_id
						},
						dataType: 'JSON',
						type: 'POST',
						success: function (res) {
							layer.msg(res.message);
							repeat_flag = false;
							if (res.code == 0) {
								table.reload({
									page: {
										curr: 1
									},
								});
							}
						}
					});
				}, function () {
					layer.close();
					repeat_flag = false;
				});
			}

			// 结束
			function closePresale(presale_id) {

				layer.confirm('确定要结束该预售活动吗?', function (index) {
					if (repeat_flag) return;
					repeat_flag = true;
					layer.close(index);

					$.ajax({
						url: ns.url("presale://shop/presale/finish"),
						data: {
							presale_id: presale_id
						},
						dataType: 'JSON',
						type: 'POST',
						success: function (res) {
							layer.msg(res.message);
							repeat_flag = false;
							if (res.code == 0) {
								table.reload();
							}
						}
					});
				}, function () {
					layer.close();
					repeat_flag = false;
				});
			}

			function presaleUrl(data){
				new PromoteShow({
					url:ns.url("presale://shop/presale/presaleUrl"),
					param:{presale_id:data.presale_id},
				})
			}
		});

	});

	function add() {
		location.hash = ns.hash("presale://shop/presale/add");
	}
</script>
