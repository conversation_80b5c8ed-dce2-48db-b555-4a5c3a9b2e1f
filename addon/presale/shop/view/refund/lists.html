<style>
	.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
	.layui-layout-admin .table-tab .layui-tab-title{margin-bottom: 15px;}
	.layui-layout-admin .screen{margin-top: 15px;}
</style>

<div class="layui-collapse tips-wrap">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>商品预售展示商品预售相关信息</li>
		</ul>
	</div>
</div>

<div class="screen layui-collapse" lay-filter="selection_panel">
	<div class="layui-colla-item">
		<form class="layui-colla-content layui-form layui-show">

			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">搜索方式：</label>
					<div class="layui-input-inline">
						<select name="order_label" >
							{foreach $order_label_list as $k => $label_val}
							<option value="{$k}">{$label_val}</option>
							{/foreach}
						</select>
					</div>
					<div class="layui-input-inline">
						<input type="text" name="search" autocomplete="off" class="layui-input" />
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">下单时间：</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="start_time" placeholder="开始时间" id="start_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<div class="layui-form-mid">-</div>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" name="end_time" placeholder="结束时间" id="end_time" readonly>
						<i class=" iconrili iconfont calendar"></i>
					</div>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(7, this);return false;">近7天</button>
					<button class="layui-btn layui-btn-primary date-picker-btn" onclick="datePick(30, this);return false;">近30天</button>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">订单类型：</label>
					<div class="layui-input-inline">
						<select name="order_type" lay-filter="order_type">
							{foreach $order_type_list as $order_type_k => $order_type_val}
							<option value="{$order_type_val.type}">{$order_type_val.name}</option>
							{/foreach}
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">订单来源：</label>
					<div class="layui-input-inline">
						<select name="order_from">
							<option value="">全部</option>
							{foreach $order_from_list as $order_from_k => $order_from_v}
							<option value="{$order_from_k}">{$order_from_v['name']}</option>
							{/foreach}
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">定金付款方式：</label>
					<div class="layui-input-inline">
						<select name="deposit_pay_type" >
							<option value="">全部</option>
							{foreach pay_type_list as $pay_type_k => $pay_type_v}
							<option value="{$pay_type_k}">{$pay_type_v}</option>
							{/foreach}
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">尾款付款方式：</label>
					<div class="layui-input-inline">
						<select name="final_pay_type" >
							<option value="">全部</option>
							{foreach pay_type_list as $pay_type_k => $pay_type_v}
							<option value="{$pay_type_k}">{$pay_type_v}</option>
							{/foreach}
						</select>
					</div>
				</div>
			</div>

			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="search">筛选</button>
				<button type="reset" class="layui-btn layui-btn-primary">重置</button>
			</div>
		</form>
	</div>
</div>

<!-- 搜索框 -->
<!-- <div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加商品预售</button>
</div> -->

<div class="layui-tab table-tab" lay-filter="presale_tab">

	<ul class="layui-tab-title">
		<li class="layui-this" data-status="">全部</li>
		{foreach $order_status_list as $status_val}
		<li data-status="{$status_val.status}">{$status_val.name}</li>
		{/foreach}
	</ul>

	<div class="layui-tab-content">
		<table id="presale_list" lay-filter="presale_list"></table>
	</div>
</div>

<!-- 商品 -->
<script type="text/html" id="goods">
	<div class="table-title">
		<div class="title-pic">
			{{#  if(d.sku_image){  }}
			<img layer-src src="{{ns.img(d.sku_image.split(',')[0],'small')}}"/>
			{{#  }  }}
		</div>
		<div class="title-content">
			<a href="javascript:;" class="multi-line-hiding text-color" title="{{d.sku_name}}">{{d.sku_name}}</a>
			<a href="javascript:;" class="multi-line-hiding" title="">￥ {{ d.price }}</a>
		</div>
	</div>
</script>

<!-- 金额 -->
<script id="price" type="text/html">
	<div class="layui-elip">定金：￥{{ d.presale_deposit_money }}</div>
	<div class="layui-elip">尾款：￥{{ d.final_money }}</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="edit">详情</a>
		{{# if(d.refund_status == 1){ }}
		<a class="layui-btn" lay-event="agree">同意退款</a>
		<a class="layui-btn" lay-event="refuse">拒绝退款</a>
		{{# } }}
	</div>
</script>

<script>
	var laytpl,table,form,laydate,element,repeat_flag;
	layui.use(['form', 'element','laydate','laytpl'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		laydate = layui.laydate;
        element = layui.element;
		repeat_flag = false; //防重复标识

		form.render();

        //渲染时间
        laydate.render({
            elem: '#start_time',
            type: 'datetime'
        });

        laydate.render({
            elem: '#end_time',
            type: 'datetime'
        });

        element.on('tab(presale_tab)', function() {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'refund_status': this.getAttribute('data-status')
                }
            });
        });

		table = new Table({
			elem: '#presale_list',
			url: ns.url("presale://shop/refund/lists"),
			cols: [
				[{
                    field: 'deposit_refund_no',
                    title: '退款编号',
                    unresize: 'false',
                    width:'10%'
                },{
                    field: 'order_no',
                    title: '订单编号',
                    unresize: 'false',
					width:'10%'
                },{
					title: '商品信息',
					unresize: 'false',
					templet:'#goods',
					width:'25%'
				}, {
			    	field:'num',
					title: '购买数量',
					unresize: 'false',
					templet:function(data){
			    	    return data.num + '件';
					},
                    width:'6%'
				}, {
					title: '金额',
					unresize: 'false',
					templet:'#price',
					width:'10%'
				}, {
					title: '退款金额',
					templet:function(data){
						return '￥' + data.refund_money;
					}
				}, {
			    	field:'refund_status_name',
                    title: '退款状态',
                    unresize: 'false'
                }, {
			    	title: '申请时间',
                    unresize: 'false',
					templet:function(data){
			    	    return ns.time_to_date(data.apply_refund_time);
					},
					width:"10%"
                }, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]

		});
		
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
            return false;
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //查看
					window.open(ns.href("presale://shop/refund/detail", {"id": data.id}));
					break;
				case 'agree': //同意
                    agreeRefund(data.id);
					break;
				case 'refuse'://拒绝
					refuseRefund(data.id);
					break;
			}
		});

		//同意退款
		function agreeRefund(id){
            layer.confirm('确定同意该退款申请吗?', function(index) {
                if (repeat_flag) return;
                repeat_flag = true;
				layer.close(index);

                $.ajax({
                    url: ns.url("presale://shop/refund/agree"),
                    data: {
                        id: id
                    },
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload();
                        }
                    }
                });
            }, function() {
                layer.close();
                repeat_flag = false;
            });
        }

        //拒绝退款
        var refuse_repeat_flag = false;
        function refuseRefund(id) {

            layer.prompt({
                title: '拒绝理由',
                formType: 2,
                yes: function(index, layero) {
                    var value = layero.find(".layui-layer-input").val();

                    if (value) {
                       if(refuse_repeat_flag) return false;
                       refuse_repeat_flag = true;

                        $.ajax({
                            url: ns.url("presale://shop/refund/refuse"),
                            data: {id:id,refuse_reason:value},
                            dataType: 'JSON', //服务器返回json格式数据
                            type: 'POST', //HTTP请求类型
                            success: function(res) {
                                layer.msg(res.message);
                               refuse_repeat_flag = false;

                                if (res.code >= 0) {
                                    table.reload({
                                        page: {
                                            curr: 1
                                        },
                                    });
                                }
                            }
                        });
                        layer.close(index);
                    } else {
                        layer.msg('请输入拒绝理由!', {icon: 5, anim: 6});
                    }
                }
            });
        }

	});

    /**
     * 七天时间
     */
    function datePick(date_num,event_obj){
        $(".date-picker-btn").removeClass("selected");
        $(event_obj).addClass('selected');
        // alert(new Date().format("yyyy-MM-dd hh:mm"));
        Date.prototype.Format = function (fmt,date_num) { //author: meizz
            this.setDate(this.getDate()-date_num);
            var o = {
                "M+": this.getMonth() + 1, //月份
                "d+": this.getDate(), //日
                "H+": this.getHours(), //小时
                "m+": this.getMinutes(), //分
                "s+": this.getSeconds(), //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds() //毫秒
            };
            if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
                if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
        };
        // var now_time =  new Date().Format("yyyy-MM-dd HH:mm:ss",0);//当前日期
        var now_time =  new Date().Format("yyyy-MM-dd 23:59:59",0);//当前日期
        var before_time =  new Date().Format("yyyy-MM-dd 00:00:00",date_num-1);//前几天日期
        $("input[name=start_time]").val(before_time,0);
        $("input[name=end_time]").val(now_time,date_num-1);
    }

	function add() {
        location.hash = ns.hash("presale://shop/presale/add");
	}
</script>
