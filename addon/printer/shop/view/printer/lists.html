<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加打印机</button>
</div>

<table id="printer_list" lay-filter="printer_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="del">删除</a>
		<a class="layui-btn" lay-event="print">测试打印</a>

		{{# if(d.brand == 'yilianyun'){ }}
		<a class="layui-btn" lay-event="refresh_token">授权</a>
		{{# } }}
	</div>
</script>

<script>
	var table,form,repeat_flag = false; //防重复标识
	layui.use(['form'], function() {
		form = layui.form;
		form.render();

		table = new Table({
			elem: '#printer_list',
			url: ns.url("printer://shop/printer/lists"),
			cols: [
				[{
			    	'field':'printer_name',
					title: '打印机名称',
					unresize: 'false'
				},{
					'field':'printer_name',
					title: '打印机类型',
					unresize: 'false',
					templet: function(data) {
						if(data.printer_type == 'local'){
							return '本地打印机';
						} else {
							return '云打印机';
						}
					}
				},  {
					field: 'brand',
					title: '打印机品牌',
					unresize: 'false',
                    templet: function(data) {
                        if(data.brand == '365'){
                            return '365';
                        } else if(data.brand == 'yilianyun'){
                            return '易联云';
                        } else if (data.brand == 'feie') {
                            return '飞鹅';
						}
                    }
				}, {
					field: 'is_default',
					title: '一次打印',
					unresize: 'false',
                    templet: function(data) {
						return data.print_num + '张';
                    }
				}, {
                    title: '添加时间',
                    unresize: 'false',
                    templet: function(data) {
                        return ns.time_to_date(data.create_time);
                    }
                }, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align:'right'
				}]
			]
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit': //编辑
					location.hash = ns.hash("printer://shop/printer/edit", {"printer_id": data.printer_id});
					break;
				case 'del': //删除
                    deletePrinter(data.printer_id);
					break;
                case 'print': //测试打印
                    testPrint(data.printer_id);
                    break;
				case 'refresh_token': //测试打印
					refreshToken(data.printer_id);
                    break;
			}
		});

		function refreshToken(printer_id){
			layer.confirm('若易联云打印机在 "测试打印" 时提示 "access_token过期或错误"，请重新授权，是否重新授权', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("printer://shop/printer/refreshtoken"),
					data: {
						printer_id: printer_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload({
								page: {
									curr: 1
								},
							});
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

		/**
		 * 删除
		 */
		function deletePrinter(printer_id) {
			layer.confirm('确定要删除该打印机吗?', function(index) {
				if (repeat_flag) return;
				repeat_flag = true;
				layer.close(index);

				$.ajax({
					url: ns.url("printer://shop/printer/delete"),
					data: {
                        printer_id: printer_id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						repeat_flag = false;
						if (res.code == 0) {
							table.reload({
							    page: {
							        curr: 1
							    },
							});
						}
					}
				});
			}, function() {
				layer.close();
				repeat_flag = false;
			});
		}

	});

    /**
     * 测试打印
     */
    function testPrint(printer_id)
    {
        layer.confirm('确定测试打印吗?', function(index) {
            if (repeat_flag) return;
            repeat_flag = true;
			layer.close(index);

            $.ajax({
                url: ns.url("printer://shop/printer/testPrint"),
                data: {
                    printer_id: printer_id
                },
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
					repeat_flag = false;
                    layer.msg(res.message);
                }
            });
        }, function() {
            layer.close();
            repeat_flag = false;
        });

    }

	function add() {
		location.hash = ns.hash("printer://shop/printer/add");
	}
</script>
