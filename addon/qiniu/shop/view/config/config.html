<div class="layui-form form-wrap">
    <div class="layui-form-item">
        <label class="layui-form-label">是否开启：</label>
        <div class="layui-input-block" id="isOpen">
			<input type="checkbox" name="status" lay-filter="isOpen" value="1" lay-skin="switch" {if condition="$info.is_use == 1"} checked {/if} />
        </div>
		<div class="word-aux">当前使用七牛云上传配置</div>
    </div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>Accesskey：</label>
		<div class="layui-input-block">
			<input type="text" name="access_key" placeholder="请输入Accesskey" lay-verify="required" value="{$info.value.access_key ?? ''}" autocomplete="off" class="layui-input len-long">
		</div>
        <div class="word-aux">用于签名的公钥</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>Secretkey：</label>
		<div class="layui-input-block">
			<input type="text" name="secret_key" placeholder="请输入Secretkey" lay-verify="required" value="{$info.value.secret_key ?? ''}" autocomplete="off" class="layui-input len-long">
		</div>
        <div class="word-aux">用于签名的私钥</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>Bucket：</label>
		<div class="layui-input-block">
			<input type="text" name="bucket" placeholder="请输入库的名称" lay-verify="required" value="{$info.value.bucket ?? ''}" autocomplete="off" class="layui-input len-long">
		</div>
        <div class="word-aux">请保证bucket为可公共读取的</div>
	</div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="required">*</span>Domain：</label>
        <div class="layui-input-block">
            <input type="text" name="domain" placeholder="请输入domain" lay-verify="required" value="{$info.value.domain ?? ''}" autocomplete="off" class="layui-input len-long">
        </div>
        <div class="word-aux">七牛支持用户自定义访问域名。注：url开头加http://或https://，结尾不加 ‘/’，例：http://abc.com</div>
    </div>
    <!-- 表单操作 -->
    <div class="form-row">
        <button class="layui-btn" lay-submit lay-filter="save">保存</button>
        <button class="layui-btn layui-btn-primary" onclick="backUploadOss()">返回</button>
    </div>

</div>

<script>
    layui.use('form', function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {
            if (repeat_flag) return;
            repeat_flag = true;
			
            $.ajax({
                url: ns.url("qiniu://shop/config/config"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero){
								location.hash = ns.hash("shop/upload/oss")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
                }
            });
        });
    });

    function backUploadOss() {
        location.hash = ns.hash("shop/upload/oss");
    }
</script>
