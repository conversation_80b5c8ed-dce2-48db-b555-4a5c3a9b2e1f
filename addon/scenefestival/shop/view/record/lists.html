<div class="layui-collapse tips-wrap">
    <div class="layui-colla-item">
        <ul class="layui-colla-content layui-show">
            <li>活动名称：{$game_info.activity_name} ({:date('Y-m-d H:i:s',$game_info.start_time)} -- {:date('Y-m-d H:i:s',$game_info.end_time)}) </li>
            <li>参与条件：{if $game_info.level_id != 0} {$game_info.level_name} {else/}全体会员 {/if}</li>
            <li>奖励发放时间：{if $game_info.join_type == 1} 节日当天{else/} 节日前{$game_info.join_frequency}天{/if}</li>
        </ul>
    </div>
</div>

<!-- 搜索框 -->
<div class="single-filter-box">

    <div class="layui-form">
        <div class="layui-inline">
            <div class="layui-input-inline">
                <input type="text" name="member_nick_name" placeholder="会员昵称" class="layui-input" autocomplete="off">
            </div>
        </div>
        <div class="layui-inline">
            <div class="layui-input-inline">
                <input type="text" name="start_time" id="start_time" placeholder="开始时间" class="layui-input" autocomplete="off" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
            <div class="layui-input-inline end-time">
                <input type="text" name="end_time" id="end_time" placeholder="结束时间" class="layui-input" autocomplete="off" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="search">搜索</button>
        </div>
    </div>

</div>

<div class="layui-tab table-tab" lay-filter="record_tab">
<!--    <ul class="layui-tab-title">-->
<!--        <li class="layui-this" data-status="">全部</li>-->
<!--        <li data-status="1">中奖</li>-->
<!--        <li data-status="0">未中奖</li>-->
<!--    </ul>-->
    <div class="layui-tab-content">
        <!-- 列表 -->
        <table id="record_list" lay-filter="record_list"></table>
    </div>
</div>

<script>
    var game_id = "{$festival_id}";
    layui.use(['form', 'element','laydate'], function() {
        var table,
            form = layui.form,
            laydate = layui.laydate,
            element = layui.element;
        form.render();

        //开始时间
        laydate.render({
            elem: '#start_time' //指定元素
        });
        //结束时间
        laydate.render({
            elem: '#end_time' //指定元素
        });

        element.on('tab(record_tab)', function() {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'status': this.getAttribute('data-status')
                }
            });
        });

        table = new Table({
            elem: '#record_list',
            url: ns.url("scenefestival://shop/record/lists"),
            where:{
                'festival_id':game_id
            },
            cols: [
                [{
                    field:'member_nick_name',
                    title: '会员昵称',
                    unresize: 'false',
                    width:'20%'
                },
                    {
                    field: '',
                    title: '奖励积分',
                        width:'20%',
                    unresize: 'false',
                    templet: function(data){
                        var str = data.award_type;
                            if(str.search("point") != -1 ){
                                return '奖励积分' + "【"+data.point+"积分】";
                            }else {
                                return '--';
                            }
                    }
                },
                    {
                        field: '',
                        title: '奖励红包',
                        width:'20%',
                        unresize: 'false',
                        templet: function(data){
                            var str = data.award_type;
                            if(str.search("balance") != -1 ){
                                if(data.balance_type == 0){
                                    return '奖励储值余额' + "【"+data.balance+"元】";
                                }else{
                                    return '奖励现金余额' + "【"+data.balance_money+"元】";
                                }
                            }else {
                                return '--';
                            }
                        }
                    },
                    {
                        field: '',
                        title: '奖励优惠券',
                        width:'20%',
                        unresize: 'false',
                        templet: function(data){
                            var str = data.award_type;
                            if(str.search("coupon") != -1 ){
                                return '奖励优惠券' + "【"+data.coupon_name+"】";
                            }else {
                                return '--';
                            }
                        }
                    },
                    {
                    title: '领取时间',
                    unresize: 'false',
                    templet: function(data){
                        return ns.time_to_date(data.receive_time);
                    }
                }]
            ]

        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

        //监听Tab切换
        element.on('tab(status)', function(data) {
            var status = $(this).attr("data-status");
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    'status': status
                }
            });
        });

    });

</script>
