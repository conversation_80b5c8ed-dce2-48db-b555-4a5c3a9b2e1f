<div class="single-filter-box">
	<button class="layui-btn" onclick="clickAdd()">添加秒杀场次</button>
</div>

<table id="seckill_list" lay-filter="seckill_list"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
        <a class="layui-btn" lay-event="check">查看商品</a>
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<script>
    layui.use('form', function() {
        var repeat_flag = false; //防重复标识

        var table = new Table({
            elem: '#seckill_list',
            url: ns.url("seckill://shop/seckill/lists"),
            parseData: function(res) { //res 即为原始返回的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.message,
                    "data": res.data
                };
            },
            cols: [
                [{
                    field: 'name',
                    title: '时段名称',
                    unresize: 'false',
                    width: '20%'
                }, {
                    field: 'seckill_start_time_show',
                    title: '开始时间',
                    unresize: 'false',
                    width: '20%'
                }, {
                    field: 'seckill_end_time_show',
                    title: '结束时间',
                    unresize: 'false',
                    width: '20%'
                },{
                    field: 'goods_num',
                    title: '商品数量',
                    unresize: 'false',
                    width: '20%',
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align:'right'
                }]
            ],
            page: false
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'check': //查看
                    location.hash = ns.hash("seckill://shop/seckill/goodslist?seckill_time_id=" + data.id);
                    break;
                case 'delete': //删除
                    layer.confirm('确定要删除该场次吗?', function(index) {
						if (repeat_flag) return false;
						repeat_flag = true;
						layer.close(index);
                        $.ajax({
                            url: ns.url("seckill://shop/seckill/delete?id=" + data.id),
                            data: data,
                            dataType: 'JSON',
                            type: 'POST',
                            success: function(res) {
                                layer.msg(res.message);
                                repeat_flag = false;
                                if (res.code == 0) {
                                    table.reload({
                                    	page: {
                                    		curr: 1
                                    	},
                                    });
                                }
                            }
                        });
                    });
                    break;
                case 'edit': //编辑
                    location.hash = ns.hash("seckill://shop/seckill/edit?id=" + data.id);
                    break;
            }
        });
    });

    function clickAdd() {
        location.hash = ns.hash("seckill://shop/seckill/add");
    }
</script>
