<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace addon\stock\shop\controller;

use addon\stock\model\stock\Transform as TransformModel;

/**
 * 库存转换
 * Class Transform
 * @package addon\stock\shop\controller
 */
class Transform extends Base
{
    /**
     * 列表
     */
    public function lists()
    {
        if (request()->isJson()) {
            $page = input('page', 1);
            $page_size = input('page_size', PAGE_LIST_ROWS);
            $search_text = input('search_text', ''); // 名称或编码

            $condition = [];
            if(!empty($search_text)){
                $condition[] = ['name', 'like', '%'.$search_text.'%'];
            }
            $transform_model = new TransformModel();
            $res = $transform_model->getTransformPageList($condition, $page, $page_size, 'create_time desc', '*');
            $res['data']['list'] = $transform_model->getTransformGoodsData($res['data']['list']);
            return $res;
        } else {
            return $this->fetch('transform/lists');
        }
    }

    /**
     * 添加
     */
    public function add()
    {
        if(request()->isJson()){
            $data = [
                'name' => input('name', ''),
                'goods_list' => json_decode(input('goods_list', ''), true),
            ];
            $transform_model = new TransformModel();
            return $transform_model->addTransform($data);
        }else{
            return $this->fetch('transform/add_or_edit');
        }
    }

    /**
     * 编辑
     */
    public function edit()
    {
        $transform_id = input('transform_id', 0);
        $transform_model = new TransformModel();
        if(request()->isJson()){
            $data = [
                'transform_id' => $transform_id,
                'name' => input('name', ''),
                'goods_list' => json_decode(input('goods_list', ''), true),
            ];
            return $transform_model->editTransform($data);
        }else{
            $transform_info = $transform_model->getTransformInfo([['transform_id', '=', $transform_id]], '*')['data'];
            if(empty($transform_info)) $this->error('转换组信息有误');
            $transform_info = $transform_model->getTransformGoodsData([$transform_info])[0];
            $this->assign('transform_info', $transform_info);
            return $this->fetch('transform/add_or_edit');
        }
    }

    /**
     * 删除
     */
    public function delete()
    {
        if(request()->isJson()){
            $transform_ids = input('transform_ids', '');
            $transform_model = new TransformModel();
            return $transform_model->deleteTransform($transform_ids);
        }
    }
}