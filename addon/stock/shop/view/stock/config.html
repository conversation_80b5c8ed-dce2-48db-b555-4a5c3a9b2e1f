<div class="layui-form form-wrap">

	<div class="layui-form-item">
		<label class="layui-form-label">是否开启单据审核：</label>
		<div class="layui-input-block">
			<input type="radio" name="is_audit" value="1" title="是" {if $stock_config.is_audit eq '1'}checked{/if} >
			<input type="radio" name="is_audit" value="0" title="否" {if $stock_config.is_audit eq '0'}checked{/if}>
		</div>
		<div class="word-aux">开启后，如果存在盘点单据，实物、称重商品将无法编辑库存、规格项（可以新增规格值），操作出入库、调拨、盘点等操作将会进行审核流程</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>
</div>

<script>
	layui.use(['form'], function() {
		var form = layui.form,
		repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("stock://shop/stock/config"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
					layer.msg(res.message);
				}
			});
		});

	});
</script>
