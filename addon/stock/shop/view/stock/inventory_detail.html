<style>
	.name {
		display: flex;
		align-content: center;
	}

	.name img {
		max-width: inherit;
		width: 60px;
		height: 60px;
		margin-right: 10px;
	}
</style>

<div class="layui-form" lay-filter="storeform">
	<div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">基本信息</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">制单人：</label>
                        <div class="layui-input-block">{$inventory_detail.operater_name}</div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">状态：</label>
                        <div class="layui-input-block">{$inventory_detail.status_name}</div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">盘点单号：</label>
                        <div class="layui-input-block">{$inventory_detail.inventory_no}</div>
                    </div>
                </div>

                {if $inventory_detail.verifier_name }
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">审核人：</label>
                        <div class="layui-input-block">{$inventory_detail.verifier_name}</div>
                    </div>
                </div>
                {/if}

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">制单时间：</label>
                        <div class="layui-input-block">{$inventory_detail.create_time}</div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">盘点时间：</label>
                        <div class="layui-input-block">{$inventory_detail.action_time}</div>
                    </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">备注：</label>
                        <div class="layui-input-block">{$inventory_detail.remark}</div>
                    </div>
                </div>

                {if $inventory_detail.audit_time }
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">审核时间：</label>
                        <div class="layui-input-block">{$inventory_detail.audit_time}</div>
                    </div>
                </div>
                {/if}

                {if $inventory_detail.status == -1 }
                <div class="layui-col-md6">
                    <div class="layui-form-item">
                        <label class="layui-form-label">拒绝理由：</label>
                        <div class="layui-input-block">{$inventory_detail.refuse_reason}</div>
                    </div>
                </div>
                {/if}
            </div>
        </div>
	</div>

	<div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">商品明细</span>
        </div>
        <div class="layui-card-body">
            <table class="layui-table order_goods_list" lay-filter="order_goods" lay-skin="line" lay-filter="order_goods_list">
                <div class="layui-tab-content layui-tab" lay-filter="list_tab">
                    <ul class="layui-tab-title tab-status">
                        <li id="all" data-type="state" class="layui-this">全部</li>
                        <li id="1" data-type="state">盘盈</li>
                        <li id="-1" data-type="state">盘亏</li>
                        <li id="0" data-type="state">持平</li>
                    </ul>
                </div>
                <colgroup>
                    <col width="20%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="10%"/>
                    <!--<col width="10%" />-->
                </colgroup>
                <thead>
                <tr>
                    <th>商品名称/条形码</th>
                    <th style="text-align: right;">商品规格</th>
                    <th style="text-align: right;">单位</th>
                    <th style="text-align: right;">实物库存</th>
                    <th style="text-align: right;">实盘数量</th>
                    <th style="text-align: right;">盈亏数量</th>
                    <th style="text-align: right;">盈亏成本总额(元)</th>
                    <!--<th style="text-align: right;">盈亏销售总额(元)</th>-->
                </tr>
                </thead>
            </table>
            <table class="layui-table order_goods_list order_goods_list-bottom" lay-skin="line" lay-filter="order_goods_list">
                <colgroup>
                    <col width="20%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="8%"/>
                    <col width="10%"/>
                    <!--<col width="10%" />-->
                </colgroup>
                <tbody>
                {foreach $inventory_detail.goods_sku_list_array as $k => $v}
                <tr class="table-tr" data-state="{if $v.profitloss_num == 0}0{/if}{if $v.profitloss_num > 0}1{/if}{if $v.profitloss_num < 0}-1{/if}">
                    <td class="name">
                        <img src="{:img($v.goods_img,'small')}" alt="">
                        <span>{$v.goods_sku_name}</span>
                    </td>
                    {if $v.goods_sku_spec && $v.goods_sku_spec !=''}
                    <td>{$v.goods_sku_spec}</td>
                    {else/}
                    <td>-</td>
                    {/if}
                    {if $v.goods_unit && $v.goods_unit !=''}
                    <td style="text-align: right;">{$v.goods_unit}</td>
                    {else/}
                    <td style="text-align: right;">件</td>
                    {/if}
                    <td style="text-align: right;">{$v.stock}</td>
                    <td style="text-align: right;">{$v.inventory_num}</td>
                    <td style="text-align: right;" id="profitloss_num">{$v.profitloss_num}</td>
                    <td style="text-align: right;">{$v.inventory_cost_money}</td>
                    <!--<td style="text-align: right;">{$v.profitloss_sale_money}</td>-->
                </tr>
                {/foreach}
                </tbody>
            </table>
            <label style="display: flex;justify-content: flex-end;padding-right: 13px;">合计：共{$inventory_detail.goods_count}种商品,实盘{$inventory_detail.kinds_num}种, 盘盈：<span style="color: #ff0000;">{$inventory_detail.kinds_profit_num}</span>, 盘亏：<span style="color: #15eb26;">{$inventory_detail.kinds_loss_num}</span>,持平：{$inventory_detail.kinds_even_num}种</label>
        </div>
	</div>
</div>

<script type="text/javascript">
	layui.use(['form', 'laytpl', 'element'], function () {
		var form = layui.form;
		laytpl = layui.laytpl;
		element = layui.element;

		element.on('tab(list_tab)', function () {
			var state = $(this).attr('id');
			if (state == 'all') {
				$(".table-tr").show();
			} else {
				$(".table-tr").hide();
				$(".table-tr[data-state='" + state + "']").show();
			}
		});

	});
</script>