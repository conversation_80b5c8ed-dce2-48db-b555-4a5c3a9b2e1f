<style>
	.single-filter-box {justify-content: left;line-height: 34px}
	.single-filter-box a{cursor:pointer;margin-left: 10px}
</style>

<div class="main-wrap">
	<div class="single-filter-box">
		<button type="button" class="layui-btn bg-color" onclick="add()">添加入库单</button>
	</div>

	<!-- 搜索框 -->
	<div class="screen layui-collapse search-nav" lay-filter="selection_panel">
		<div class="layui-colla-item">
			<form class="layui-colla-content layui-form layui-show">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">入库单号：</label>
						<div class="layui-input-inline">
							<input type="text" name="search_text" placeholder="请输入入库单号" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">门店：</label>
						<div class="layui-input-inline">
							<select name="store_id"  class="len-mid">
								<option value="">全部</option>
								{foreach $store_list as $store_k => $store_v}
								<option value="{$store_v.store_id}">{$store_v.store_name}</option>
								{/foreach}
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">审核状态：</label>
						<div class="layui-input-inline">
							<select name="status" lay-filter="status" class="len-mid">
								<option value="">全部</option>
								{foreach name="$status_list" item="vo"}
								<option value="{$vo.status}">{$vo.name}</option>
								{/foreach}
							</select>
						</div>
					</div>
				</div>

				<div class="form-row">
					<button class="layui-btn bg-color" lay-submit lay-filter="search">筛选</button>
					<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
			</form>
		</div>
	</div>

	<div class="layui-tab table-tab">
		<div class="layui-tab-content">
			<table id="doc_list" lay-filter="doc_list"></table>
		</div>
	</div>
</div>

<script type='text/html' id="import_document">
	<div class="layui-form import-document">
		<div class="layui-form-item">
			<label class="layui-form-label ">文件上传：</label>
			<div class="layui-input-block ">
				<div class="upload-file sub-text disabled-click">
					<div class="upload-img-block" id="addFile">
						<div class="upload-img-box">
							<div class="upload-default">
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>导入模版</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="word-aux">需按照指定模板导入，支持格式xls、xlsx。下载<a href="ADDON_STOCK_FILE/stockin_template.xlsx" class="text-color"> 导入模板</a></div>
		</div>

		<div class="form-row">
			<button class="layui-btn confirm-auto">确定</button>
			<button class="layui-btn layui-btn-primary cancel">取消</button>
		</div>
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="detail">查看</a>

		<!-- 待审核状态下进行审核操作 -->
		{{# if(d.status == 1 && {$is_audit} == 0){ }}
			<!-- 只有管理员和拥有单据审核权限的才能审核 -->
			<a class="layui-btn" lay-event="audit_agree">审核通过</a>
			<a class="layui-btn" lay-event="audit_refuse">审核拒绝</a>
		{{# } }}

		{{# if(d.status == -1){ }}
			<a class="layui-btn" lay-event="detail_refuse">拒绝理由</a>
		{{# } }}

		<!-- 只有经办人才能操作入库单 -->
		{{# if((d.status == 1 || d.status == -1) && d.operater == {$user_info['uid']} ){ }}
			<a class="layui-btn" lay-event="edit">编辑</a>
			<a class="layui-btn" lay-event="delete">删除</a>
		{{# } }}

	</div>
</script>

<script>
	var table, form, laytpl, element, layer_pass, repeat_flag = false, upload; //防重复标识
	layui.use(['form', 'laytpl', 'element', 'upload'], function() {
		form = layui.form;
		laytpl = layui.laytpl;
		element = layui.element;
		upload = layui.upload;
		form.render();

		table = new Table({
			elem: '#doc_list',
			url: ns.url("stock://shop/stock/storage"),
			cols: [
				[{
					field: 'document_no',
					title: '入库单号',
					width: '15%',
				}, {
					field: 'store_name',
					title: '门店',
					width: '15%',
				}, {
					field: 'type_name',
					title: '单据类型',
					width: '10%',
				}, {
					field: 'document_money',
					title: '单据金额',
					width: '10%',
				}, {
					field: 'status_name',
					title: '审核状态',
					width: '10%',
				}, {
					title: '操作人',
					width: '15%',
					templet: function(data) {
						var html = '';
						html += '经办人：' + data.operater_name + '<br/>';
						if(data.verifier_name) html += '审核人：' + data.verifier_name;
						return html;
					}
				}, {
					title: '操作时间',
					width: '15%',
					templet: function(data) {
						var html = '';
						html += '制单时间：' + ns.time_to_date(data.create_time) + '<br/>';
						if (data.time) html += '入库时间：' + ns.time_to_date(data.time) + '<br/>';
						if (data.audit_time) html += '审核时间：' + ns.time_to_date(data.audit_time);
						return html;
					}
				}, {
					title: '操作',
					width: '10%',
					align: 'right',
					toolbar: '#operation',
				}]
			]
		});

		// 监听工具栏操作
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'audit_agree':
					// 审核通过
					agree(data.document_id);
					break;
				case 'audit_refuse':
					// 审核拒绝
					refuse(data.document_id);
					break;
				case 'edit':
					// 编辑
					edit(data.document_id);
					break;
				case 'delete':
					// 删除
					deleteDocument(data.document_id);
					break;
				case 'detail':
					// 查看
					window.open(ns.href("stock://shop/stock/inputdetail?document_id=" + data.document_id));
					break;
				case 'detail_refuse':
					layer.open({
						title: '拒绝理由',
						content: data.refuse_reason,
					})
					break;
			}
		});

		// 搜索功能
		form.on('submit(search)', function(data){
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
			return false;
		});
		
	});

	function importExcel() {
		laytpl($("#import_document").html()).render({}, function (html) {
			cardPup = layer.open({
				type: 1,
				title:"导入入库单",
				area:['700px','350px'],
				content: html,
				success: function(layero, index){
					form.render();

					// 上传文件
					var uploadInst = upload.render({
						elem: '#addFile' //绑定元素
						,url: ns.url("stock://shop/stock/file") //上传接口
						,accept:'file'
						,acceptMime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
						,acceptMime: 'xlsx'
						,exts: 'xlsx'
						,data:{
							type: 'import',
						},
						done: function(res){
							if (res.code >= 0) {
								importing(res.data.path);
							} else {
								layer.msg(res.message);
							}
						}
					});

					$(".import-document .form-row .cancel,.import-document .form-row .confirm-auto").click(function(){
						layer.closeAll();
					});

					function importing(path){
						$.ajax({
							url: ns.url("stock://shop/stock/import"),
							data: {
								path: path,
								type : 'PURCHASE'
							},
							dataType: 'JSON',
							type: 'POST',
							success: function (res) {
								if(res.code == 0){
									// table.reload();
								}else{
									layer.msg(res.message)
								}
							}
						});
					}

				}
			});

		});
	}

	function add() {
		location.hash = ns.hash("stock://shop/stock/stockin");
	}

	function edit(document_id) {
		location.hash = ns.hash("stock://shop/stock/stockin",{document_id});
	}

	// 同意
	var agree_repeat_flag = false;
	function agree(document_id) {
		layer.confirm('确定要通过该单据吗?', function(index) {
			if(agree_repeat_flag) return;
			agree_repeat_flag = true;
			layer.close(index);
			$.ajax({
				url: ns.url("stock://shop/stock/agree"),
				data: {document_id},
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					agree_repeat_flag = false;
					if (res.code >= 0) {
						listenerHash(); // 刷新页面
					} else {
						layer.msg(res.message);
					}
					layer.closeAll();
				}
			});
		});

	}

	// 拒绝
	var refuse_repeat_flag = false;
	function refuse(document_id) {
		layer.prompt({
			title: '拒绝理由',
			formType: 2,
			yes: function (index, layero) {
				var refuse_reason = layero.find(".layui-layer-input").val();
				if (!refuse_reason) {
					layer.msg('请输入拒绝理由!', {icon: 5, anim: 6});
					return;
				}
				if (refuse_repeat_flag) return;
				refuse_repeat_flag = true;

				$.ajax({
					url: ns.url("stock://shop/stock/refuse"),
					data: {document_id, refuse_reason},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						refuse_repeat_flag = false;

						if (res.code >= 0) {
							listenerHash(); // 刷新页面
						}
					}
				});
				layer.close(index);

			}
		});
	}

	// 删除单据
	var delete_repeat_flag = false;
	function deleteDocument(document_id) {
		layer.confirm('确定要删除该单据吗?', function(index) {
			if(delete_repeat_flag) return;
			delete_repeat_flag = true;
			layer.close(index);
			$.ajax({
				url: ns.url("stock://shop/stock/delete"),
				data: {document_id},
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					delete_repeat_flag = false;
					if (res.code >= 0) {
						listenerHash(); // 刷新页面
					} else {
						layer.msg(res.message);
					}
					layer.closeAll();
				}
			});
		});
	}
</script>
