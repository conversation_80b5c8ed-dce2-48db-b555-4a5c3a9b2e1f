<style>
	#goods thead th{ background-color: #f7f7f7;}
	/* 优惠商品 */
	.goods-title{display: flex;align-items: center;}
	.goods-title .goods-img{display: flex;align-items: center;justify-content: center;width: 55px;height: 55px;margin-right: 5px;}
	.goods-title .goods-img img{max-height: 100%;max-width: 100%;}
	.goods-title .goods-name{flex: 1;line-height: 1.6;}
	.goods_num {padding-left: 20px;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>转换名称：</label>
		<div class="layui-input-block">
			<input type="text" name="name" lay-verify="required" autocomplete="off" class="layui-input len-long" maxlength="255" value="{$transform_info.name ?? ''}">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>转换商品：</label>
		<div class="layui-input-block">
			<table class="layui-table" id="goods" lay-skin="line" lay-size="lg">
				<colgroup>
					<col width="60%">
					<col width="30%">
					<col width="10%">
				</colgroup>
				<thead>
					<tr>
						<th>商品名称</th>
						<th>基本单位数量</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody id="tbody">
				</tbody>
			</table>
			<script type="text/html" id="tbody_tpl" >
				{{# d.forEach(function(item, index){ }}
				<tr data-index="{{ index }}">
					<td>
						<div class="goods-title">
							<div class="goods-img">
								<img layer-src="{{ns.img(item.sku_image, 'big')}}" src="{{ns.img(item.sku_image, 'small')}}" alt="">
							</div>
							<p class="multi-line-hiding goods-name">{{item.sku_name}}</p>
						</div>
					</td>
					<td>
						<input name="num" type="number" lay-verify="num" placeholder="请输入基本单位数量" autocomplete="off" class="layui-input len-mid" value="{{item.num}}">
					</td>
					<td class='operation'>
						<div class='table-btn'>
							<a href='javascript:;' class='layui-btn' data-action="delete">删除</a>
						</div>
					</td>
				</tr>
				{{# }) }}
				{{# if(d.length == 0){ }}
				<tr class="goods-empty">
					<td colspan="3">
						<div>未选择商品</div>
					</td>
				</tr>
				{{# } }}
			</script>
			<button class="layui-btn" id="select_goods">选择商品</button>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
	<input type="hidden" name="transform_id" value="{$transform_info.transform_id ?? 0}">
</div>

<script>
	var form, selectGoodsSkuId = [];
	var data_table;
	var transform_id = Number($("input[name='transform_id']").val());
	var goods_list = [];
	if(transform_id > 0){
		goods_list = {:json_encode($transform_info.goods_list ?? [])};
	}
	layui.use("form", function() {
		form = layui.form;
		var repeat_flag = false; //防重复标识
		
		/**
		 * 监听提交
		 */
		form.on('submit(save)', function(data) {
			let goods_list = data_table.getData();
			if(goods_list.length < 2){
				layer.msg('请至少选择两种商品');
				return;
			}
			data.field.goods_list = JSON.stringify(goods_list);

			let action = 'add';
			let action_name = '添加';
			if(transform_id > 0){
				action = 'edit';
				action_name = '编辑';
			}

			if (repeat_flag) return;
			repeat_flag = true;
			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("stock://shop/transform/"+action),
				data: data.field,
				async: false,
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm(action_name+'成功', {
							title: '操作提示',
							btn: ['返回列表', '继续'+action_name],
							closeBtn: 0,
							yes: function(index, layero) {
								location.hash = ns.hash("stock://shop/transform/lists");
								layer.close(index);
							},
							btn2: function(index, layero) {
								listenerHash(); // 刷新页面
								layer.close(index);
							}
						});
					} else {
						layer.msg(res.message);
					}
				}
			})
		});

		form.verify({
			num: function(value) {
				if(value === ''){
					return '请输入基本单位数量';
				}
				if(!ns.getRegexp('>0num').test(value)){
					return '基本单位数量必须为正整数';
				}
			}
		});
	})

	function back() {
		location.hash = ns.hash("stock://shop/transform/lists");
	}

	function DataTable(param){
		param = param || {};
		let that = this;
		that.data = param.data || [];
		that.layui = null;

		that.init();
	}
	DataTable.prototype = {
		select : function (){
			let that = this;
			let num_data = {};
			that.data.forEach((item)=>{
				num_data[item.sku_id] = item.num;
			})
			goodsSelect(function (data) {
				that.data = [];
				Object.values(data).forEach((goods_item)=>{
					Object.values(goods_item.selected_sku_list).forEach((sku_item,index)=>{
						sku_item.num = num_data[sku_item.sku_id] || 1;
						that.data.push(sku_item);
					})
				})
				that.render();
			}, Object.keys(num_data).toString(), {mode: "sku",goods_class:'1,6'});
		},
		delete : function (index){
			let that = this;
			that.data.splice(index, 1);
			that.render();
		},
		render : function (){
			let that = this;
			that.layui.laytpl($('#tbody_tpl').html()).render(that.data,function(html){
				$('#tbody').html(html);
			})
		},
		bindEvent : function (){
			let that = this;
			$('#tbody').on('click', 'a[data-action=delete]', function (){
				let index = $(this).parents('tr').data('index');
				that.delete(index);
			})
			$('#tbody').on('blur', 'input[type=text],input[type=number]', function (){
				let index = $(this).parents('tr').data('index');
				let field = $(this).attr('name');
				let value = $(this).val();
				that.data[index][field] = value;

			})
			$("#select_goods").click(function (){
				that.select();
			})
		},
		getData:function (){
			let that = this;
			let data = [];
			that.data.forEach((item, index)=>{
				data.push({
					sku_id:item.sku_id,
					num:item.num,
				})
			})
			return data;
		},
		init: function (){
			let that = this;
			layui.use("laytpl", function() {
				that.layui = layui;
				that.bindEvent();
				that.render();
			})
		},
	}

	data_table = new DataTable({
		data:goods_list,
	})
</script>
