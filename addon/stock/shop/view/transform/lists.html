<style>
	.goods-title-box{display: flex;flex-wrap: wrap;}
	.goods-title{display: flex;align-items: center;border: 1px solid #ccc;border-radius: 4px;margin: 4px;padding:4px 4px;}
	.goods-title .goods-img{display: flex;align-items: center;justify-content: center;width: 55px;height: 55px;margin-right: 5px;}
	.goods-title .goods-img img{max-height: 100%;max-width: 100%;}
	.goods-title .goods-name{width: 150px;}
	.goods-title .goods-name .name{line-height: 19px !important;}
	.goods-title .goods-name .other{color:#999;}
</style>
<div class="layui-collapse tips-wrap" style="margin-bottom: 15px;">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>1、一个转换关系中可以添加多个商品规格，可以是同一个商品的不同规格，也可以是不同商品的规格</li>
			<li>2、商品类型可以是实物商品和称重商品两种，请根据商品的实际销售包装设置合适的基本单位数量</li>
			<li>3、前台购买商品时可以下单的数量为转换后的库存</li>
		</ul>
	</div>
</div>

<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="add()">添加转换关系</button>

	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_text" placeholder="请输入关键词" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<table id="supplier_list" lay-filter="supplier_list"></table>

<script type="text/html" id="goods_list">
	<div class="goods-title-box">
		{{# d.goods_list.forEach((item)=>{ }}
		<div class="goods-title">
			<div class="goods-img">
				<img layer-src="{{ns.img(item.sku_image, 'big')}}" src="{{ns.img(item.sku_image, 'small')}}" alt="">
			</div>
			<div class="goods-name">
				<p class="multi-line-hiding name">{{item.sku_name}}</p>
				<p class="multi-line-hiding other">基本单位：{{item.num}}</p>
				<p class="multi-line-hiding other">原始库存：{{item.stock}}</p>
				<p class="multi-line-hiding other">转换库存：{{item.transform_stock}}</p>
			</div>
		</div>
		{{# }) }}
	</div>
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<script>
	var form, table;
	layui.use(['table', 'form'], function() {
		form = layui.form;
		form.render();
		table = new Table({
			elem: '#supplier_list',
			url: ns.url("stock://shop/transform/lists"),
			cols: [
				[{
					title: '转换名称',
					field: 'name',
					width: '12%',
					unresize: 'false',
					templet: function (data){
						return '<div class="text">'+ data.name +'</div>';
					},
				},{
					title: '基本单位总数',
					field: 'transform_stock',
					width: '10%',
					unresize: 'false',
					templet: function (data){
						return data.transform_stock;
					},
				},{
					title: '转换商品',
					field: 'goods_list',
					width: '68%',
					unresize: 'false',
					templet: '#goods_list',
				}, {
					title: '操作',
					width: '10%',
					toolbar: '#operation',
					unresize: 'false',
					align : 'right'
				}]
			],
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit':
					location.hash = ns.hash("stock://shop/transform/edit", {transform_id: data.transform_id});
					break;
				case 'delete':
					deleteTransform(data);
					break;
			}
		});

		/**
		 * 删除
		 */
		function deleteTransform(data) {
			layer.confirm('确定要删除该库存转换吗？', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("stock://shop/transform/delete"),
					data: {transform_ids: data.transform_id},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data){
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});

	function add() {
		location.hash = ns.hash("stock://shop/transform/add");
	}
</script>
