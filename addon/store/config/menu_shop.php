<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com

 * =========================================================
 */
return [
    [
        'name' => 'ADDON_STORE_BASE',
        'title' => '门店',
        'url' => 'store://shop/store/index',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'sort' => 3,
        'picture' => 'iconmendianzhuye',
        'picture_selected' => '',
        'child_list' => [
            [
                'name' => 'ADDON_STORE_INDEX',
                'title' => '门店概况',
                'url' => 'store://shop/store/index',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'sort' => 1,
            ],
            [
                'name' => 'ADDON_STORE_MANAGE',
                'title' => '门店管理',
                'url' => 'store://shop/store/lists',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'sort' => 2,
                'child_list' => [
                    [
                        'name' => 'ADDON_STORE_MANAGE_LIST',
                        'title' => '门店列表',
                        'url' => 'store://shop/store/lists',
                        'is_show' => 1,
                        'parent' => 'CONFIG_BASE',
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/store_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/store_select.png',
                        'child_list' => [
                            [
                                'name' => 'STORE_ADD',
                                'title' => '添加门店',
                                'url' => 'store://shop/store/addstore',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_EDIT',
                                'title' => '基础设置',
                                'url' => 'store://shop/store/editstore',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_DELETE',
                                'title' => '删除门店',
                                'url' => 'store://shop/store/deletestore',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_FROZEN',
                                'title' => '关闭门店',
                                'url' => 'store://shop/store/frozenStore',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_MODIFY_PASSWORD',
                                'title' => '重置密码',
                                'url' => 'store://shop/store/modifyPassword',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_OPERATE',
                                'title' => '运营设置',
                                'url' => 'store://shop/store/operate',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_DELIVERY',
                                'title' => '同城配送',
                                'url' => 'store://shop/store/local',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 4,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_SETTLEMENT', 
                                'title' => '结算设置',
                                'url' => 'store://shop/store/settlement',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_DELIVER',
                                'title' => '配送员',
                                'url' => 'store://shop/store/deliverlists',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 5,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_DELIVER_ADD',
                                'title' => '配送员添加',
                                'url' => 'store://shop/store/addDeliver',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_DELIVER_EDIT',
                                'title' => '配送员编辑',
                                'url' => 'store://shop/store/editDeliver',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ADDON_STORE_CATEGORY',
                        'title' => '门店分类',
                        'url' => 'store://shop/store/category',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 2,
                    ],
                    [
                        'name' => 'ADDON_STORE_TAG',
                        'title' => '门店标签',
                        'url' => 'store://shop/store/tag',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 3,
                    ]
                ]
            ],

            [
                'name' => 'ADDON_STORE_SHOP_STORE_CONFIG',
                'title' => '门店设置',
                'url' => 'store://shop/config/index',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'sort' => 3,
            ],
            [
                'name' => 'ADDON_STORE_SHOP_STORE_ACCOUNT',
                'title' => '门店账户',
                'url' => 'store://shop/account/lists',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'sort' => 4,
                'child_list' => [
                    [
                        'name' => 'ADDON_STORE_SHOP_STORE_ACCOUNT_EXPORT_ADD',
                        'title' => '账户导出',
                        'url' => 'store://shop/account/addexport',
                        'is_show' => 0,
                        'is_control' => 1,
                        'sort' => 1,
                        'type' => 'button',
                    ],
                    [
                        'name' => 'ADDON_STORE_SHOP_STORE_ACCOUNT_EXPORT_LIST',
                        'title' => '导出记录',
                        'url' => 'store://shop/account/exportlist',
                        'is_show' => 0,
                        'is_control' => 1,
                        'sort' => 1,
                        'type' => 'button',
                    ],
                    [
                        'name' => 'ADDON_STORE_SHOP_STORE_ACCOUNT_EXPORT_DELETE',
                        'title' => '导出删除',
                        'url' => 'store://shop/account/deleteexport',
                        'is_show' => 0,
                        'is_control' => 1,
                        'sort' => 1,
                        'type' => 'button',
                    ],
                ]
            ],
            [
                'name' => 'ADDON_STORE_SHOP_STORE_WITHDRAW',
                'title' => '门店结算',
                'url' => 'store://shop/withdraw/lists',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'sort' => 5,
                'child_list' => [
                    [
                        'name' => 'ADDON_STORE_SHOP_STORE_WITHDRAW_INFO',
                        'title' => '结算详情',
                        'url' => 'store://shop/withdraw/detail',
                        'is_show' => 0,
                        'is_control' => 1,
                        'sort' => 1,
                        'type' => 'button',
                    ],
                ]
            ],
         ]
    ],
    [
        'name' => 'SERVICE_YUYUE',
        'title' => '预约',
        'url' => 'store://shop/reserve/index',
        'parent' => 'ORDER_ROOT',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconyuyueguanli',
        'picture_select' => '',
        'sort' => 3,
        'child_list' => [
            [
                'name' => 'YUYUE_MAMAGE',
                'title' => '预约管理',
                'url' => 'store://shop/reserve/index',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'iconyuyueguanli',
                'picture_select' => '',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'YUYUE_DETAIL',
                        'title' => '预约详情',
                        'url' => 'store://shop/reserve/detail',
                        'is_show' => 0,
                        'type' => 'button',
                    ],
                    [
                        'name' => 'YUYUE_CONFIRM',
                        'title' => '确认预约',
                        'url' => 'store://shop/reserve/confirm',
                        'is_show' => 0,
                        'type' => 'button',
                    ],
                    [
                        'name' => 'YUYUE_CANCEL',
                        'title' => '取消预约',
                        'url' => 'store://shop/reserve/cancel',
                        'is_show' => 0,
                        'type' => 'button',
                    ],
                    [
                        'name' => 'YUYUE_TO_STORE',
                        'title' => '确认到店',
                        'url' => 'store://shop/reserve/confirmtostore',
                        'is_show' => 0,
                        'type' => 'button',
                    ]
                ]
            ],

        ]
    ],
    [
        'name' => 'STORE_STAT',
        'title' => '门店数据',
        'parent' => 'STAT_SHOP',
        'url' => 'store://shop/stat/store',
        'picture' => '',
        'picture_selected' => '',
        'is_show' => 1,
    ],
    [
        'name' => 'STORE_DIY',
        'title' => '门店装修',
        'parent' => 'SHOP_DIY',
        'url' => 'store://shop/store/diy',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'sort' => 20,
        'picture' => '',
        'picture_selected' => '',
        'child_list' => []
    ],
];
