<style>
	#detail_list {
		margin-top: 15px;
	}
</style>
<div class="layui-collapse tips-wrap">
    <div class="layui-colla-item">
        <ul class="layui-colla-content layui-show">
            <li>账期时间：{if $info.start_time > 0}{:date('Y-m-d H:i:s', $info.start_time)} {else/} {:date('Y-m-d H:i:s', $store_info['create_time'])} {/if} 至 {:date('Y-m-d H:i:s', $info.end_time)}</li>
            <li>线上结算金额 = 线上付款金额(<span style="color: red">￥{$info.order_money}</span>)</li>
            <li>线下付款金额<span style="color: red">￥{$info.offline_order_money}</span></li>
            <li>线下退款金额<span style="color: red">￥{$info.offline_refund_money}</span></li>
            <li>门店抽成金额<span style="color: red">￥{$info.store_commission}</span></li>
        </ul>
    </div>
</div>
<table id="detail_list" lay-filter="detail_list"></table>
<script>
    layui.use(['laydate','form'], function () {
        var form = layui.form;
		form.render();
        var table = new Table({
            elem: '#detail_list',
            url: ns.url("store://shop/settlement/detail", {'settlement_id': {$info.id}}),
            where:{id:"{$info.id}"},
            cols: [
                [{
                    field: 'order_no',
                    title: '订单编号',
                    unresize: 'false',
					width: '18%'
                }, {
                    field: 'pay_type_name',
                    title: '支付方式',
                    unresize: 'false',
                    width: '12%'
                }, {
                    field:'order_money',
                    title: '订单销售额（元）',
                    unresize: 'false',
					align: 'right',
					templet: function (res){
					    return '￥'+res.order_money;
					},
					width: '12%'
                }, {
                    field:'refund_money',
                    title: '订单退款（元）',
                    unresize: 'false',
					align: 'right',
					templet: function (res){
					    return '￥'+res.refund_money;
					},
					width: '14%'
                }, {
                    title: '门店抽成（元）',
                    unresize: 'false',
                    align: 'right',
                    templet: function (res){
                        return '￥'+res.store_commission;
                    },
                    width: '12%'
                }, {
                    unresize: 'false',
					width: '12%'
                },  {
                    field: 'finish_time',
                    title: '订单完成时间',
                    unresize: 'false',
                    templet: function (res){
                        if(res.finish_time == 0){
                            return '--';
                        }else{
                            return ns.time_to_date(res.finish_time)
                        }
                    },
					width: '20%'
                }]
            ]
        });

    });
</script>
