<style>
    .calendar{text-align: center;line-height: 34px;}
    .layui-layout-admin .single-filter-box{padding-bottom: 0;}
</style>
<!-- 筛选面板 -->
<div class="single-filter-box">
    <div class="layui-form">
        <div class="layui-inline">
            <div class="layui-inline">
                <div class="layui-input-inline"></div>
            </div>
            <div class="layui-input-inline">
                <select name="store_id" lay-filter="store_id">
                    <option value="">请选择门店</option>
                    {foreach name="$store_list" item="store"}
                    <option value="{$store['store_id']}">{$store['store_name']}</option>
                    {/foreach}
                </select>
            </div>
            <div class="layui-input-inline">
                <input type="text" name="start_time" id="start_time" placeholder="结算开始时间" class="layui-input" autocomplete="off" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
            <div class="layui-input-inline end-time">
                <input type="text" name="end_time" id="end_time" placeholder="结算结束时间" class="layui-input" autocomplete="off" readonly>
                <i class=" iconrili iconfont calendar"></i>
            </div>
            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="search">搜索</button>
        </div>
    </div>
</div>

<div class="layui-tab table-tab">

    <div class="layui-tab-content">
        <table id="account_list" lay-filter="account_list"></table>
    </div>

</div>

<script type="text/html" id="operation">
    <div class="table-btn">
        {{# if(d.is_settlement == 0){ }}
        <a class="layui-btn" lay-event="settlement">结算</a>
        {{# } }}
        <a class="layui-btn" lay-event="detail">详情</a>
    </div>
</script>

<script type="text/html" id="shop_money">
    <span style="color: red;">￥{{d.shop_money}}</span>
</script>

<script type="text/html" id="reviewReason">
    <div class="layui-form">
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label mid">结算备注：</label>
            <div class="layui-input-block len-mid">
                <textarea name="remark" placeholder="请输入内容" class="layui-textarea" maxlength="150"></textarea>
            </div>
        </div>
        <input name="settlement_id" value="{{d.id}}" type="hidden"/>
        <div class="form-row mid">
            <button class="layui-btn" lay-submit lay-filter="save">结算</button>
            <button onclick="cancel()" class="layui-btn layui-btn-primary">取消</button>
        </div>
    </div>
</script>

<script>
    var start_time,end_time,
        repeat_flag = false;
    layui.use(['laydate','form','laytpl'], function () {
        var laydate = layui.laydate,
            laytpl = layui.laytpl,
            form = layui.form;
		form.render();

        //开始时间
        laydate.render({
            elem: '#start_time' //指定元素
        });
        //结束时间
        laydate.render({
            elem: '#end_time' //指定元素
        });

        /**
         * 搜索功能
         */
        form.on('submit(search)', function (data) {
            data.field.start_time = start_time;
            data.field.end_time = end_time;
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
            return false;
        });

        function settlement(data) {
            laytpl($("#reviewReason").html()).render(data, function(html){
                layer.open({
                    type: 1,
                    title:'结算',
                    content: html,
                    area: ['500px']
                });
            })
        }

        form.on('submit(save)', function (data) {
            if (repeat_flag) return;
            repeat_flag = true;
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: ns.url("store://shop/settlement/settlement"),
                data: data.field,
                success: function(res) {
                    repeat_flag = false;
                    layer.msg(res.message);
                    if (res.code == 0) {
                        listenerHash(); // 刷新页面
                    }
                }
            });
        });
        
        var table = new Table({
            elem: '#account_list',
            url: ns.url("store://shop/settlement/index"),
            cols: [
                [{
                    field:'settlement_no',
                    title: '账单编号',
                    unresize: 'false',
                    width:'16%',
                },{
                    field: 'store_name',
                    title: '门店',
                    width:'10%',
                }, {
                    title: '订单总额',
                    unresize: 'false',
                    width:'8%',
					align: 'right',
                    templet: function (res){
                        return '￥' + (res.order_money*1 + res.offline_order_money*1 - res.refund_shop_money*1- res.offline_refund_money*1);
                    }
                }, {
                    title: '线上结算金额',
                    unresize: 'false',
                    width:'9%',
					align: 'right',
                    templet: function (res) {
                        return '<span style="color: red;">￥'+ (res.order_money*1  - res.refund_shop_money*1 -res.commission*1) + '</span>';
                    }
                }, {
                    title: '线下结算金额',
                    unresize: 'false',
                    width:'9%',
					align: 'right',
                    templet: function (res){
                        return '<span style="color: red;">￥'+ (res.offline_order_money*1 - res.offline_refund_money*1) + '</span>';
                    }
                },{
                    title: '门店抽成金额',
                    unresize: 'false',
                    width:'10%',
                    align: 'right',
                    templet: function (res){
                        return '<span style="color: red;">￥'+ (res.store_commission) + '</span>';
                    }
                }, {
                    title: '结算开始时间',
                    unresize: 'false',
                    width:'14%',
                    templet: function (res){
                        if(res.start_time == 0){
                            return '--';
                        }else{
                            return ns.time_to_date(res.start_time)
                        }
                    }
                }, {
                    title: '结算结束时间',
                    unresize: 'false',
                    width:'14%',
                    templet: function (res){
                        if(res.end_time == 0){
                            return '--';
                        }else{
                            return ns.time_to_date(res.end_time)
                        }
                    }
                }, {
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
					align:'right'
                }]
            ]
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'settlement': //编辑
                    settlement(data);
                    break;
                case 'detail':
                    location.hash = ns.hash("store://shop/settlement/detail",{'settlement_id': data.id});
                    break;
            }
        });
    });

    function cancel(){
        layer.closeAll()
    }
</script>
