<style>
	/*时间选择*/
	.time-screen .screen{display: flex}
	.time-screen .screen .item {height: 32px;line-height: 32px;padding: 0 20px;border: 1px solid #D2D2D2;cursor: pointer;border-right: none;border-left: none;position: relative}
	.time-screen .screen .item:after{content: '';position: absolute;top: -1px;left: 0;bottom: -1px;right: -1px;border-right: 1px solid #D2D2D2;border-left: 1px solid #D2D2D2;}
	.time-screen .screen .selected,.time-screen .item:hover{color: #fff;background: var(--base-color);border-color: var(--base-color) }
	.time-screen .screen .selected:after, .time-screen .item:hover:after {border-right: 1px solid var(--base-color);border-left: 1px solid var(--base-color);}

	.data-wrap {display: flex;flex-wrap: wrap; margin-top: 15px;}
	.data-wrap .data-item {display: flex;min-width: 250px;margin-bottom: 15px;}
	.data-wrap .data-item .box {flex: 1;margin-right: 15px;border: 1px solid #eee;box-sizing: border-box;padding: 15px;cursor: pointer}
	.data-wrap .data-item:last-child .box {margin-right: 0}
	.data-wrap .data-item .value {font-size: 25px;font-weight: bolder;line-height: 40px}
	.data-wrap .data-item .js-prompt-top {color:#C8C9CC;font-size:14px;z-index:999;cursor:pointer;}
	.data-wrap .data-item .info {font-size: 12px;color: #999;}
	.data-wrap .info .iconfont {font-size: 12px;}
	.data-wrap .data-item .text-color-green {color: #00A717}
	.data-wrap .data-item .text-color-red {color: #ff0000}
	.data-wrap .data-item.selected .box {border-color: var(--base-color);color: var(--base-color);}
	.statistics-wrap {position: relative}
	.statistics-wrap .loading {background: rgba(255,255,255,.5);position: absolute;left: 0; top: 0;text-align: center;width: 100%;height: 100%;box-sizing: border-box;padding-top: 100px;display: none}
	.statistics-wrap .loading i {font-size: 25px}

	.layui-layer-content .layui-form-label {width: 100px}
	.echart {margin-top: 15px}
	.echart-wrap {margin-top: 30px;display: flex;}
	.echart-wrap .main {flex: 1;height: 400px}
	.echart-wrap .main:nth-child(2) {margin-left: 15px}
	.date-input{width: 300px}
    .time-screen{display: flex;justify-content: space-between; }
</style>

<div class="main-wrap">
	<div class="time-screen statistics">
		<div class="screen">
			<div class="item selected" date-type="today">今日</div>
			<div class="item" date-type="yesterday">昨日</div>
			<div class="item" date-type="seven">7日内</div>
			<div class="item" date-type="thirty">30日内</div>
			<div class="item" date-type="custom">自定义</div>
        </div>
        <div class="layui-form">
            <div class="layui-inline">
                <label class="layui-form-label">门店</label>
                <div class="layui-input-inline">
                    <select name="store_id" lay-filter="store_id">
                        <option value="">全部</option>
                        {foreach name="$store_list" item="store_vo"}
                        <option value="{$store_vo['store_id']}">{$store_vo['store_name']}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
        </div>
	</div>
	<div class="statistics-wrap">
		<div class="data-wrap">
			<div class="data-item selected" data-value="expected_earnings_total_money">
				<div class="box">
					<div class="title">
						<span class="title-text">预计收入(元)</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
			</div>

			<div class="data-item" data-value="billing_money">
				<div class="box">
					<div class="title">
						<span class="title-text">开单金额数(元)</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="billing_count">
				<div class="box">
					<div class="title">
						<span class="title-text">开单数量</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="num">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="buycard_money">
				<div class="box">
					<div class="title">
						<span class="title-text">办卡金额数(元)</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="buycard_count">
				<div class="box">
					<div class="title">
						<span class="title-text">办卡数</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="num">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="recharge_money">
				<div class="box">
					<div class="title">
						<span class="title-text">会员充值金额(元)</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="recharge_count">
				<div class="box">
					<div class="title">
						<span class="title-text">会员充值数量</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="num">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="refund_money">
				<div class="box">
					<div class="title">
						<span class="title-text">会员退款金额(元)</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="refund_count">
				<div class="box">
					<div class="title">
						<span class="title-text">会员退款数量</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="num">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="order_member_count">
				<div class="box">
					<div class="title">
						<span class="title-text">门店下单会员数</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="num">0</div>
				</div>
            </div>
            
			<div class="data-item" data-value="balance_money">
				<div class="box">
					<div class="title">
						<span class="title-text">会员余额消费金额</span>
						<!-- <span class="iconfont iconwenhao js-prompt-top" data-tips="统计时间内，新注册会员的数量"></span> -->
					</div>
					<div class="value" data-type="money">0</div>
				</div>
            </div>
		</div>

		<div class="loading">
			<i class="common-loading-layer layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
		</div>
	</div>

	<div class="echart-wrap">
		<div class="main" id="main"></div>
	</div>
</div>

<script type="text/html" id="custom-box">
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label">选择时间：</label>

			<div class="layui-inline layui-inline-margin" id="time_fission">
				<div class="layui-input-inline">
					<input type="text" id="date" name="date" autocomplete="off" class="layui-input date-input" placeholder="请选择日期">
					<i class="iconfont iconriqi"></i>
				</div>
			</div>
			<input type="hidden" name="start_time" value="">
			<input type="hidden" name="end_time" value="">
		</div>
	</div>
</script>

<script src="SHOP_JS/echarts.min.js"></script>
<script>
	var store_id = "",form;

	initFn();
	function initFn(){
		// 选择门店
		layui.use('form', function () {
			form = layui.form;
			form.render();
			form.on('select(store_id)', function(data){
				store_id = data.value;
				let type = $('.time-screen.statistics .item.selected').attr("date-type");
				cutTimeTemp(type);
			});
		}); 

		// 切换时间
		$('.time-screen.statistics .item').click(function () {
			var type = $(this).attr('date-type');
			if (type != 'custom') $(this).addClass('selected').siblings().removeClass('selected');
			cutTimeTemp(type);
		});

		getShopStatData({}, 'hour');
		getShopStatistics({});

		// 切换数据模块
		$('.statistics-wrap .data-item').click(function () {
			if ($(this).hasClass('selected')) return;
			$('.statistics-wrap .data-item').removeClass('selected');
			$(this).addClass('selected');
			fetchEchart();
		})

	}

    /**
	 * 选择模块
	 * */
    function cutTimeTemp(type){
        var self = this;
		switch (type) {
			case 'today':
				var time = {
					start_time: (new Date('{$today} 00:00:00')).getTime() / 1000,
					end_time: (new Date('{$today} 23:59:59')).getTime() / 1000,
					store_id
				}
				getShopStatistics(time);
				getShopStatData(time, 'hour');
				break;
			case 'yesterday':
				var time = {
					start_time: (new Date('{$yesterday} 00:00:00')).getTime() / 1000,
					end_time: (new Date('{$yesterday} 23:59:59')).getTime() / 1000,
					store_id
				}
				getShopStatistics(time);
				getShopStatData(time, 'hour');
				break;
			case 'seven': 
				var dateObj = new Date(Date.now() - 604800000);
				var date = dateObj.getFullYear() + '-' + (dateObj.getMonth() + 1) + '-' + dateObj.getDate();
				var date1 = new Date(Date.now()).getFullYear() + '-' + (new Date(Date.now()).getMonth() + 1) + '-' + new Date(Date.now()).getDate();
				var data1 = {start_time: new Date(date).getTime() / 1000, end_time:new Date(date1).getTime() / 1000};
				getShopStatistics(data1);
				getShopStatData(data1, 'day');
				break;
			case 'thirty':
				var dateObj = new Date(Date.now() - 2592000000);
				var date = dateObj.getFullYear() + '-' + (dateObj.getMonth() + 1) + '-' + dateObj.getDate();
				var date1 = new Date(Date.now()).getFullYear() + '-' + (new Date(Date.now()).getMonth() + 1) + '-' + new Date(Date.now()).getDate();
				let data = {start_time: new Date(date).getTime() / 1000, end_time:new Date(date1).getTime() / 1000,store_id};
				getShopStatistics(data);
				getShopStatData(data, 'day');
				break;
			case 'custom':
				var _layer = layer.open({
					title: '自定义时间选择',
					type: 1,
					area: ['480px', '160px'], //自定义文本域宽高
					btn: ['确认', '取消'],
					content: $('#custom-box').html(),
					success: function (layero, index) {
						new LayDate({
							elem: '#date',
							type: 'datetime',
							rangeId:['start_time','end_time'],
							max: '{:date("Y-m-d")}',
							value:[],
							done: function(value, date, endDate){
								var time_arr = value.split(' - ');
								var start_time = time_arr[0];
								var end_time = time_arr[1];
								$('input[name="start_time"]').val(time_arr[0]);
								$('input[name="end_time"]').val(time_arr[1]);
							}
						});
					},
					yes: function () {
						var start_time = $('input[name="start_time"]').val();
						var end_time = $('input[name="end_time"]').val();
						if (start_time == ''){
							layer.msg('请选择时间');
							return;
						}
						var time = {
							start_time: (new Date(start_time)).getTime() / 1000,
							end_time: (new Date(end_time)).getTime() / 1000
						};
						getShopStatistics(time);
						getShopStatData(time, 'day');
						$(self).addClass('selected').siblings().removeClass('selected');
						layer.close(_layer);
					}
				})
				break;
		}
    }

	/**
	 * 获取统计数据
	 * */
	function getShopStatistics(data) {
		$('.statistics-wrap .loading').show();
		$.ajax({
			dataType: "JSON",
			type: "POST",
			data: data,
			url: ns.url("store://shop/stat/statTotal"),
			success: function(res){
				$('.statistics-wrap .loading').hide();
				if (res.code == 0) {
					Object.keys(res.data).forEach(function (key) {
						var type = $('[data-value="'+ key +'"] .value').attr('data-type');
						var value = (type == 'money' ? moneyFormat(res.data[key]) : parseInt(res.data[key])) || 0;
						$('[data-value="'+ key +'"] .value').text(value);
					})
				}
			}
		})
	}
	
	/**
	 * 获取趋势数据
	 * */
	var statData = [];
	function getShopStatData(data, type) {   
		$.ajax({
			dataType: 'JSON',
			type: 'POST',
			url: type == 'hour' ? ns.url("store://shop/stat/hourStatData") : ns.url("store://shop/stat/dayStatData"),
			data: data,
			success: function(res) {
                statData = res.data;
				fetchEchart();
			}
		});
	}

	/**
	 * 渲染echart图表
	 */
	// 图表
	if($('#main').length) {
		var baseColor = getComputedStyle(document.documentElement).getPropertyValue('--base-color');
		var myChart = echarts.init(document.getElementById('main'));
		var option = {
			tooltip: {
				trigger: 'axis'
			},
			legend: {
				data: ['新增商品数'],
				textStyle: {
					fontSize: 14,
					color: "#000"
				}
			},
			grid: {
				left: '4%',
				right: '4%'
			},
			xAxis: {
				type: 'category',
				boundaryGap: false,
				data: []
			},
			yAxis: {
				type: 'value',
				axisLabel: {
					formatter: '{value} '
				},
				splitLine: {
					show: false
				},
			},
			color: [baseColor],
			series: [
				{
					name: '新增商品数',
					type: 'line',
					smooth: true,
					data: [],
				}
			]
		};
	}
	// 图表函数
	function fetchEchart() {
		var key = $('.statistics-wrap .data-item.selected').attr('data-value');
		option.xAxis.data = statData.time;
		option.legend.data[0] = $('.statistics-wrap .data-item.selected .title-text').text();
		option.series[0].name = $('.statistics-wrap .data-item.selected .title-text').text();
        option.series[0].data = statData[key];
		myChart.setOption(option);
	}
</script>
