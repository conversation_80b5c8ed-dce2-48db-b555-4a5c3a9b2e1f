<style>
    .form-row{margin-top: 0;margin-left: 220px;}
    .express-sheet-rule .form-row{margin-left: 200px;}
</style>

<div class="layui-form">
    <div class="layui-card card-common card-brief">
        <div class="layui-card-header">
            <span class="card-title">配送员信息</span>
        </div>
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>配送员名称：</label>
                <div class="layui-input-inline">
                    <input type="text" name="deliver_name" lay-verify="required|deliverName" class="layui-input len-long">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span class="required">*</span>配送员手机号：</label>
                <div class="layui-input-block">
                    <input type="text" name="deliver_mobile" lay-verify="required|deliverMobile" class="layui-input len-long">
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" value="{$store_id ?? 0}" name="store_id">
    <div class="form-row">
        <button class="layui-btn" lay-submit lay-filter="save">保存</button>
        <button type="reset" class="layui-btn layui-btn-primary" onclick="back()">返回</button>
    </div>
</div>

<script>
    layui.use(['form'], function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
        form.render();

        form.on('submit(save)', function(data) {
            if (repeat_flag) return;
            repeat_flag = true;

            $.ajax({
                url: ns.url("shop/local/addDeliver"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function (res) {
                    repeat_flag = false;
                    if (res.code == 0) {
                        layer.confirm('添加成功', {
                            title: '操作提示',
                            btn: ['返回列表', '继续添加'],
                            closeBtn: 0,
                            yes: function (index, layero) {
                                if ($('input[name="store_id"]').val() > 0) {
                                    location.hash = ns.hash("store://shop/store/deliverLists", {'store_id': $('input[name="store_id"]').val()});
                                } else {
                                    location.hash = ns.hash("shop/local/deliverLists");
                                }
                                layer.close(index);
                            },
                            btn2: function (index, layero) {
                                listenerHash(); // 刷新页面
                                layer.close(index);
                            }
                        });
                    } else {
                        layer.msg(res.message);
                    }
                }
            });
        });

        /**
         * 表单验证
         */
        form.verify({
            deliverName: function(value){
                if (value == '') {
                    return  '配送员名称不能为空!';
                }
            },
            deliverMobile: function (value) {
                if (value == '') {
                    return '手机号不能为空!';
                }
                if (!ns.parse_mobile(value)) {
                    return '请输入合法的手机号!'
                }
            }
        });
    });

    function back(){
        if($('input[name="store_id"]').val() > 0){
            location.hash = ns.hash("store://shop/store/deliverLists",{'store_id':$('input[name="store_id"]').val()});
        }else{
            location.hash = ns.hash("shop/local/deliverLists");
        }
    }
</script>
