<div class="single-filter-box">
    <button class="layui-btn" onclick="add()">添加配送员</button>

    <div class="layui-form">
        <div class="layui-input-inline">
            <input type="text" name="search_text" placeholder="请输入配送员名称" class="layui-input">
            <button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
                <i class="layui-icon">&#xe615;</i>
            </button>
        </div>
    </div>
</div>

<!-- 列表 -->
<table id="deliverLists" lay-filter="deliverLists"></table>

<!-- 操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        <a class="layui-btn" lay-event="edit">编辑</a>
        <a class="layui-btn" lay-event="delete">删除</a>
    </div>
</script>

<script>
    var form, table;
    layui.use(['form'], function() {
        form = layui.form;
        var repeat_flag = false; //防重复标识

        table = new Table({
            elem: '#deliverLists',
            url: ns.url("store://shop/store/deliverlists"),
            where:{'store_id':"{$store_id}"},
            cols: [
                [{
                    title: '配送员名称',
                    unresize: 'false',
                    field: 'deliver_name',
                    width: '40%'
                }, {
                    field:'deliver_mobile',
                    title: '配送员电话',
                    unresize: 'false',
                    width: '40%'
                },{
                    title: '操作',
                    toolbar: '#operation',
                    unresize: 'false',
				    align:'right'
                }]
            ],
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'edit': //编辑
                    location.hash = ns.hash("store://shop/store/editDeliver?store_id={$store_id}&deliver_id=" + data.deliver_id);
                    break;
                case 'delete': //删除
                    deleteDeliver(data.deliver_id);
                    break;
            }
        });

        /**
         * 删除
         */
        function deleteDeliver(deliver_ids) {
            if (repeat_flag) return false;
            repeat_flag = true;

            layer.confirm('确定要删除该配送员吗?', function(index) {
				layer.close(index);
                $.ajax({
                    url: ns.url("shop/local/deleteDeliver"),
                    data: {deliver_ids},
                    dataType: 'JSON',
                    type: 'POST',
                    success: function(res) {
                        layer.msg(res.message);
                        repeat_flag = false;
                        if (res.code == 0) {
                            table.reload(
                                {
                                    page: {
                                        curr: 1
                                    }
                                }
                            );
                        }
                    }
                });
            }, function () {
                layer.close();
                repeat_flag = false;
            });
        }

        /**
         * 批量操作
         */
        table.bottomToolbar(function(obj) {

            if (obj.data.length < 1) {
                layer.msg('请选择要操作的数据');
                return;
            }
            switch (obj.event) {
                case "del":
                    var id_array = new Array();
                    for (i in obj.data) id_array.push(obj.data[i].company_id);
                    deleteCompany(id_array.toString());
                    break;
            }
        });

        /**
         * 批量操作
         */
        table.toolbar(function(obj) {

            if (obj.data.length < 1) {
                layer.msg('请选择要操作的数据');
                return;
            }
            switch (obj.event) {
                case "del":
                    var id_array = new Array();
                    for (i in obj.data) id_array.push(obj.data[i].company_id);
                    deleteCompany(id_array.toString());
                    break;
            }
        });


        /**
         * 搜索功能
         */
        form.on('submit(search)', function(data) {
            table.reload({
                page: {
                    curr: 1
                },
                where: data.field
            });
        });

    });

    function add() {
        location.hash = ns.hash("store://shop/store/addDeliver?store_id={$store_id}");
    }
</script>
