<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com

 * =========================================================
 */

namespace addon\supermember\model;

use app\model\BaseModel;
use app\model\system\Stat;

/**
 * 会员卡订单统计
 */
class MemberLevelStat extends BaseModel
{

    /**
     * 写入礼品卡统计数据
     * @param $params
     */
    public function addMemberLevelStat($params){
        $order_id = $params['order_id'];

        $site_id = $params['site_id'] ?? 0;
        $order_condition = array(
            ['order_id', '=', $order_id],
            ['site_id', '=', $site_id]
        );
        $order_info = model('member_level_order')->getInfo($order_condition);

        //余额支付不统计
        if(empty($order_info) || $order_info['pay_type'] == 'BALANCE')
            return $this->error();

        $order_money = $order_info['order_money'];
        $stat_data = array(
            'site_id' => $site_id,
            'member_level_count' => 1,
            'member_level_total_money' => $order_money
        );

        $stat_model = new Stat();

        $result = $stat_model->addShopStat($stat_data);
        return $result;
    }
}