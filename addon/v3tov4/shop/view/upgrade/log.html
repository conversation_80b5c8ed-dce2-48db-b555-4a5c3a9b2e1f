<style>
	.layui-layout-admin .tips-wrap{margin-bottom: 15px;}
</style>

<div class="layui-collapse tips-wrap">
	<div class="layui-colla-item">
		<h2 class="layui-colla-title">操作提示</h2>
		<ul class="layui-colla-content layui-show">
			<li>迁移数据日志</li>
		</ul>
	</div>
</div>
<div>
	<table id="upgrade_log" lay-filter="upgrade_log"></table>
</div>
<!--操作-->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<!-- 批量删除 -->
<script type="text/html" id="batchOperation">
	<button class="layui-btn layui-btn-primary" lay-event="delete">批量删除</button>
</script>

<script>
	var repeat_flag = false;
	var table = new Table({
		elem: '#upgrade_log',
		filter: "upgrade_log",
		url: ns.url("v3tov4://shop/upgrade/log"),
		cols: [[{
			width: "3%",
			type: 'checkbox',
			field: 'id',
			unresize: 'false'
		}, {
			field: 'title',
			width: '25%',
			title: '迁移模块',
			unresize: 'true'
		}, {
			field: 'remark',
			width: '35%',
			title: '备注',
			unresize: 'true'
		}, {
			width: '17%',
			title: '迁移时间',
			unresize: 'true',
			templet: function (d) {
				return ns.time_to_date(d.create_time);
			}
		}, {
			width: '10%',
			title: '迁移状态',
			unresize: 'true',
			templet: function (d) {
				return d.status ? "完成" : "未完成";
			}
		}, {
			title: '操作',
			toolbar: '#operation',
			unresize: 'false',
			align:'right'
		}]],
		bottomToolbar: "#batchOperation"
	});

	/**
	 * 批量操作
	 */
	table.bottomToolbar(function (obj) {
		if (obj.data.length < 1) {
			layer.msg('请选择要操作的数据');
			return;
		}
		switch (obj.event) {
			case "delete":
				var id_array = new Array();
				for (i in obj.data) id_array.push(obj.data[i].id);
				deleteLog(id_array.toString());
				break;
		}
	});

	/**
	 * 监听工具栏操作
	 */
	table.tool(function (obj) {
		var data = obj.data;
		switch (obj.event) {
			case 'delete':
				deleteLog(data.id);
				break;
		}
	});

	function deleteLog(id) {
		if (repeat_flag) return;
		repeat_flag = true;

		layer.confirm('确定要删除该日志吗?', function (index) {
			layer.close(index);
			$.ajax({
				url: ns.url("v3tov4://shop/upgrade/deleteLog"),
				data: {
					"ids": id
				},
				dataType: 'JSON',
				type: 'POST',
				success: function (res) {
					layer.msg(res.message);
					repeat_flag = false;

					if (res.code == 0) {
						table.reload({
						    page: {
						        curr: 1
						    },
						});
					}
				}
			});
		});
	}
</script>
