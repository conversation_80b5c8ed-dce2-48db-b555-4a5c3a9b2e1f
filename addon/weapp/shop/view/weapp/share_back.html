<style>
	.word-aux{
		display: flex;
	}
	.mini-program{
		width: 42px;
		height: 15px;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #666666;
	}
    .share {
		margin-top: 10px;
        padding:20px;
        border: 1px solid #e5e5e5;
        position: relative;
		border-radius: 3px;
		box-sizing: border-box;
    }
    .layui-input-block, .word-aux {
        margin:20px auto 0;
    }
	
    textarea {
        margin-top:10px;
    }
	
	.picture-introduce{
		width: 293px;
		height: 13px;
		font-size: 12px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #B3B3B3;
		margin-top: 13px;
	}
	
	.top-text{
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e5e5e5;
		padding-bottom: 20px;
	}
	
	.top-text .logo{
		width: 40px;
		height: 40px;
		color: #fff;
		background-color: #e5e5e5;
		font-size: 12px;
		text-align: center;
		line-height: 40px;
		margin-right: 10px;
		border-radius: 50%;
	}
	
	.company-titles{
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		font-size: 16px;
		color: #333333;
		}
	
	.bottom{
		display: flex;
	}
	
	.replace_img js-replace{
		    position: absolute;
		    bottom: 0;
		    width: 100%;
		    height: 24px;
		    color: #fff;
		    background: rgba(0, 0, 0, 0.5);
		    font-size: 12px;
		    line-height: 24px;
	}

	::-webkit-input-placeholder{
		position: absolute;
		top: 5px;
		left: 5px;
	}
	.member-detail-list{
		display: flex;
		flex-wrap: wrap;
	}
	.member-detail-list .detail-item{
		width: 350px;	
		margin-right: 20px;
		margin-bottom: 20px;
	}
	.member-detail-list .main-header{
		overflow: hidden;
		display: block;
		text-overflow: ellipsis;
		margin: 0;
		white-space: nowrap;
		font-size: 14px;
		color: #666;
	}

	.member-detail-list .share-desc{
		margin-top: 20px;
	}
	.member-detail-list .img-upload{
		margin-top: 18px;
	}
	.member-detail-list .upload-img-block{
		border-style: solid;
		border-radius: 2px;
	}
	.member-detail-list .bottom{
		display: flex;
		align-content: center;
		margin-top: 18px;
	}
	.member-detail-list .bottom .iconfont{
		color: #08BA06;
		margin-right: 5px;
	}
	.member-detail-list .upload-img-block{
		width: 310px;
		height:248px; 
		padding: 0;
	}
	.member-detail-list .upload-img-block .operation i{
		line-height: 246px;
	}
</style>

{notempty name="$scene"}
	<div class="member-detail-list">
		{foreach $scene as $scene_item}
			<div class="detail-item" data-key="{$scene_item['key']}">
				<div class="word-aux main-header">{$scene_item['title']}</div>
				<div class="share">
					<div class="top-text">
						<span class="logo">Logo</span>
						<div class="company-titles">小程序名称</div>
					</div>
					<form action="" class="layui-form" lay-filter="example">
						<input type="text" name="share-title" class="layui-input share-desc" placeholder="小程序分享描述" value="{$config[$scene_item['key']]['title'] ?? ''}" maxlength="50">
						<div class="layui-input-block img-upload">
							<div class="upload-img-block">
								<div class="upload-img-box  {if !empty($config[$scene_item['key']]['path'])}hover{/if}" style="position: relative;">
									<div class="upload-default" id="shareUpload_{$scene_item['key']}">
										{if empty($config[$scene_item['key']]['path'])}
										<div class="upload">
											<i class="iconfont iconshangchuan"></i>
											<p>点击上传</p>
										</div>
										{else /}
										<div id="preview_watermark_source" class="preview_img">
											<img layer-src src="{:img($config[$scene_item['key']]['path'])}" class="img_prev" />
										</div>
										{/if}
									</div>
									<div class="operation">
										<div>
											<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
											<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
										</div>
										<div class="replace_img js-replace">点击替换</div>
									</div>
									<input type="hidden" name="shareImg_{$scene_item['key']}" lay-verify="required" value="{$config[$scene_item['key']]['path'] ?? ''}">
								</div>
							</div>
						</div>
						<div class="picture-introduce">支持PNG及JPG。图片长宽比是5:4，不传使用默认截图</div>
						<div class="bottom">
							<i class="iconfont iconweixinxiaochengxu"></i>
							<div class="mini-program">小程序</div>
						</div>
					</form>
				</div>
			</div>
		{/foreach}
	</div>
{else/}
	<div class="no-data">暂无数据</div>
{/notempty}

<script>
	let shareParam = {},
		scene = '{:json_encode($scene)}',
		config = '{:json_encode($config)}';

	initData(); //初始化数据
	function initData(){
		scene = JSON.parse(scene);
		config = JSON.parse(config);
		scene.forEach((item,index) => {
		    
    		var path = config.length != 0 && config[item.key].path ? config[item.key].path : '',
    		    title = config.length != 0 && config[item.key].title ? config[item.key].title : item.title;
    		    
			shareParam[item.key] = {
				'key': item.key,
				'path': path,
				'title': title
			};
			//实例化图片上传
			uploadImg(item.key);
		});
	}

	function uploadImg(key){
		new Upload({
			elem: '#shareUpload_'+key,
			callback: function(res) {
				let key = this.elem.split('_')[1];

				if(res.code > 0){
					shareParam[key].path = res.data.pic_path;
					$("input[name='shareImg_"+ key +"']").val(shareParam[key].path);
					$.ajax({
						type: "post",
						url: ns.url("weapp://shop/weapp/share"),
						data: shareParam[key],
						dataType: 'JSON',
						success: function(res) {
							layer.msg(res.message);
						},
					});
				}else{
					layer.msg(res.message);
				}
			},
			deleteCallback: function(res){
				let key = this.elem.split('_')[1];
				
				shareParam[key].path = '';
				$("input[name='shareImg_"+ key +"']").val(shareParam[key].path);
				$.ajax({
					type: "post",
					url: ns.url("weapp://shop/weapp/share"),
					data: shareParam[key],
					dataType: 'JSON',
					success: function(res) {
						layer.msg(res.message);
					},
				});
			}
		});
	}

	$('[name="share-title"]').change(function(){
		var key = $(this).parents('.detail-item').attr("data-key");
		shareParam[key].title = $(this).val();
		
		$.ajax({
			type: "post",
			url: ns.url("weapp://shop/weapp/share"),
			data: shareParam[key],
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
			},
		});
	});

</script>

