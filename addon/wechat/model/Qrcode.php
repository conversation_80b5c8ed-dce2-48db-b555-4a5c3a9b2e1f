<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace addon\wechat\model;

use app\model\BaseModel;
use think\facade\Cache;

/**
 * 微信二维码
 */
class Qrcode extends BaseModel
{
    /***************************************************************** 微信二维码 start ***************************************************************************/
    /**
     * 添加微信二维码
     * @param $data
     * @return array
     */
    public function addQrcode($data)
    {
        $data[ "update_time" ] = time();
        Cache::tag("wechat_qrcode")->clear();
        $result = model("wechat_qrcode")->add($data);
        return $this->success($result);
    }

    /**
     * 编辑微信二维码
     * @param $data
     * @param $condition
     * @return array
     */
    public function editQrcode($data, $condition)
    {
        $data[ "update_time" ] = time();
        Cache::tag("wechat_qrcode")->clear();
        $result = model("wechat_qrcode")->update($data, $condition);
        return $this->success($result);
    }

    /**
     * 删除微信二维码
     * @param $condition
     * @return array
     */
    public function deleteQrcode($condition)
    {
        Cache::tag("wechat_qrcode")->clear();
        $result = model("wechat_qrcode")->delete($condition);
        return $this->success($result);
    }

    /**
     * 设置默认二维码模板
     * @param $condition
     * @return array
     */
    public function modifyQrcodeDefault($condition)
    {
        //将全部模板设置为非默认
        Cache::tag("wechat_qrcode")->clear();
        model("wechat_qrcode")->update([ "is_default" => 0 ], [ 'is_default' => 1 ]);
        $res = model("wechat_qrcode")->update([ "is_default" => 1 ], $condition);
        return $this->success($res);
    }

    /**
     * 获取二维码模板详情
     * @param $condition
     * @param string $field
     * @return array
     */
    public function getQrcodeInfo($condition, $field = "*")
    {
        $info = model('wechat_qrcode')->getInfo($condition, $field);
        return $this->success($info);
    }

    /**
     * 获取微信二维码列表
     * @param array $condition
     * @param int $page
     * @param int $page_size
     * @param string $order
     * @param string $field
     * @return array
     */
    public function getQrcodePageList($condition = [], $page = 1, $page_size = PAGE_LIST_ROWS, $order = '', $field = '*')
    {
        $list = model('wechat_qrcode')->pageList($condition, $field, $order, $page, $page_size);
        return $this->success($list);
    }

    /***************************************************************** 微信粉丝 end ***************************************************************************/
}