<link rel="stylesheet" href="ADDON_WECHAT_CSS/wx_material.css">

<div class="layui-tab-item layui-show">
	<div class='mg'>
		<button class="layui-btn" onclick="window.open(ns.href('wechat://shop/material/addgraphicmessage'))">新建图文</button>
	</div>
    <div>
        <table id="graphic_message_list" lay-filter="graphic_message"></table>
    </div>
	<!-- 标题 -->
	<script type="text/html" id="title">
		<div class="layui-row grid-demo">
		{{# for (var index in d.value) { }}
            <div class="layui-col-md12">
				<div class="layui-col-md3 article-img">
					<span class="bg-color">图文</span>
				</div>
				<div class="layui-col-md8 title">
					<a class="graphic-message-title" href="javascript:void(0);" onclick="preview({{d.id}}, {{index}})">{{d.value[index].title}}</a>
				</div>
			</div>

		{{# } }}
		{{# if (d.value.length == 1) { }}
			<div class='layui-col-md12 read-all' onclick="preview({{d.id}})">
				<div class='layui-col-md4'>阅读全文</div>
				<div class='layui-col-md4 layui-col-md-offset4'> > </div>
			</div>
		{{# } }}
		</div>
	</script>
	<!-- 创建时间 -->
	<script type="text/html" id="create_time">
		<div>{{ ns.time_to_date(d.create_time) }}</div>
	</script>
	<!-- 修改时间 -->
	<script type="text/html" id="update_time">
		<div>{{ ns.time_to_date(d.update_time) }}</div>
	</script>
	<!-- 列表操作 -->
	<script type="text/html" id="operation">
		<a class="default" lay-event="edit">编辑</a>
		<a class="default" lay-event="delete">删除</a>
	</script>
</div>

<script src="ADDON_WECHAT_JS/wx_material.js"></script>
<script>
loadMaterialList(1);
    layui.use(['table','form'], function() {
        var table,
			form = layui.form;

        table = new Table({
            elem: '#Fans_list',
            url: ns.url("wechat://shop/material/lists"),
            page: false,

            cols: [
                [
				{
					field: 'title',
					title: '标题',
					unresize: 'false'
				}, {
					field: 'create_time',
					title: '创建时间',
					unresize: 'false',
					templet: function(data) {
					return ns.time_to_date(data.create_time);
				}
				}, {
					field: 'update_time',
						title: '更新时间',
						unresize: 'false',
						templet: function(data) {
						return ns.time_to_date(data.update_time);
					}
				}, {
					title: '操作',
					toolbar: '#operation',
					align: 'right',
					unresize: 'false'
				}
                ]
            ],
			bottomToolbar: "#batchOperation"
        });

    });
</script>
