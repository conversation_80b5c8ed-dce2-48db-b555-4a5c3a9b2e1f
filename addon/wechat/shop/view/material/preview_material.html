<html>
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit">
	<meta name="referrer" content="never">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0,viewport-fit=cover"/>
	<meta http-equiv="X-UA-COMPATIBLE" content="IE=edge,chrome=1">
	<title>{$info.value[$index]['title']}</title>
	<meta name="keywords" content="">
	<meta name="description" content="">
	<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/css/layui.css">
	<link rel="stylesheet" href="SITEHOME_CSS/home.css">
	<link rel="stylesheet" href="WECHAT_CSS/wx_preview_graphic_message.css">
	<script src="__STATIC__/js/jquery-2.2.js"></script>
</head>
<body>
<div class='preview-box'>
	<img class='head' src='WECHAT_IMG/mobile_head.png'/>
	<div class='graphic-message'>
		<h1 class='title'>{$info.value[$index]['title']}</h1>
		{if condition="$info.update_time eq 0"}
		<span class='time'>{$info['create_time']|time_to_date='Y-m-d'}</span>
		{else/}
		<span class='time'>{$info['update_time']|time_to_date='Y-m-d'}</span>
		{/if}
		<span class='author'>{$info.value[$index]['autor']}</span>
		<content>
			{if condition="$info.value[$index]['show_cover_pic'] == 1"}
			<div class="rich-media-thumb" id="media">
				<img onerror="this.parentNode.removeChild(this)" src="{$info.value[$index]['cover']['path']}">
			</div>
			{/if}
			{:html_entity_decode($info.value[$index]['content'])}
		</content>
		{if condition="!empty($info.value[$index]['url'])"}
		<a class='original-text' href="{$info.value[$index]['url']}">阅读原文</a>
		{/if}
	</div>
</div>
</body>
<script>
	$(function () {
		$("img").each(function () {
			$(this).replaceWith(this);
		})
	})
</script>
</html>