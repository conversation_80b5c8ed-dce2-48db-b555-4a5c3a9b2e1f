<div class="layui-form">
	<div class="layui-form-item">
		<label class="layui-form-label">是否开启：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="wechat_is_open" value="1" {if $wechat_is_open == 1}checked{/if} lay-skin="switch">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">模板名称：</label>
		<div class="layui-input-block">
			{$message_title}
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">模板消息ID：</label>
		<div class="layui-input-block">
			<input type="text" name="template_id" value="{if $info}{$info.template_id_short}{/if}" placeholder="模板消息ID" autocomplete="off" class="layui-input len-long" readonly>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">类目模板：</label>
		<div class="layui-input-block">
			<input type="text" name="keyword_name_list" value="{if $info && isset($info.keyword_name_list)}{$info.keyword_name_list}{/if}" placeholder="类目模板，多个逗号隔开" autocomplete="off" class="layui-input len-long" readonly>
		</div>
		<div class="word-aux">类目模板的关键词,多个逗号隔开</div>
	</div>

	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label"><span class="required"></span>模板内容：</label>
		<div class="layui-input-inline">
			<textarea  placeholder="" class="layui-textarea len-long" readonly>{if $info}{$info.content}{/if}</textarea>
		</div>
	</div>
	
	<input type="hidden" name="keywords" value="{$keywords}" />

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="backMessageList()">返回</button>
	</div>
</div>

<script>
	layui.use(['form', 'colorpicker'], function() {
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("wechat://shop/Message/edit"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero){
								location.hash = ns.hash("shop/message/lists")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
	});

	function backMessageList() {
		location.hash = ns.hash("shop/message/lists");
	}
</script>
