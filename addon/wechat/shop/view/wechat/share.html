<style>
	.layui-form {overflow: hidden;}
	.card { display: inline-block; margin-right: 8px; width: 300px; height: 192px; border: 1px solid #EEEEEE; border-radius: 3px; vertical-align: top;padding: 0;}
	.layui-card-header { font-size: 14px;font-family: Microsoft YaHei;font-weight: 400;color: #606266;text-align: center; position: relative; height: 55px;display: flex;align-items: center;justify-content: space-between;border-bottom:1px solid #EEEEEE !important;}
	.layui-card-header .edit-btn, .layui-card-header .cancel-btn { position: absolute; right: 15px; }
	.cancel-btn { display: none; }
	.layui-card-body { color: rgba(0, 0, 0, .6); padding:18px 20px 20px;box-sizing: border-box;width: 100%;height: calc(100% - 55px);display: flex;flex-direction: column;justify-content: space-between;}
	.share-con { display: flex;}
	.share-img { flex-shrink: 0; width: 70px; height:70px;line-height: 70px; border: 1px solid #f1f1f1; background-color: #ececec; text-align: center; background-size: cover;background-repeat: no-repeat;background-position: 50% 50%;}
	.share-img span { color: #c0c0c0; font-weight: bold; }
	.share-font { padding-right: 10px; flex:1;color: #909399;}
	.btn-hide { display: none; }
	.layui-textarea{min-height:70px;}
	.share-title {overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
	.share-title span{font-size: 14px;font-family: Microsoft YaHei;font-weight: 400;color: #303133;}
	.share-title input{display: none;}
	.share-font textarea{display: none;}
	.layui-layer-page .layui-layer-content{padding: 25px 0px 40px!important;}
	.layui-form-item .layui-input-inline{width: 300px;}
	.layui-form-item .layui-input-inline .imgUrl{width: 100px;height: 100px;border: 1px solid #E6E6E6;cursor: pointer;}
	.layui-form-item .layui-input-inline .imgUrl img{width: 100%;height: 100%;}
</style>

<div class="layui-form">
	{foreach $config_list as $key=>$config_item}
	<div class="layui-card card" data-key="{$config_item.config.config_key}" data-data='{:json_encode($config_item)}'>
		<div class="layui-card-header" >
			<span class="title">{$config_item.config.title}</span>
			<a href="javascript:void(0)" class="edit-btn text-color">编辑</a>
		</div>
		<div class="layui-card-body">
			<p class="share-title" name = "title">
				<span title="{$config_item.value.title}">{$config_item.value.title}</span>
			</p>
			
			<div class="share-con">
				<div class="share-font">
					<span class="span-show">{php}echo str_replace("\n", '<br/>', $config_item['value']['desc']);{/php}</span>
				</div>
				<div class="share-img" {if isset($config_item.value.imgUrl) && !empty($config_item.value.imgUrl)}style="background-image:url('{:img($config_item.value.imgUrl)}')"{/if}>
					{if !isset($config_item.value.imgUrl) || empty($config_item.value.imgUrl)}
					<span>分享图标</span>
					{/if}
				</div>
			</div>
			
		</div>
	</div>
	{/foreach}

</div>

<script type="text/html" id="openContent">
	<div class="html-open layui-form">
		{{# if(d.variable && d.variable.length){ }}
		<div class="layui-form-item">
			<label class="layui-form-label">变量替换：</label>
			<div class="layui-input-inline">
				{{# layui.each(d.variable,function(index,item){  }}
				<button type="button" class="layui-btn addBtn bg-color" data-tips="{{item.name}}">{{item.title}}</button>
				{{# }) }}
			</div>
		</div>
		{{# } }}
		<div class="layui-form-item">
			<label class="layui-form-label">分享标题：</label>
			<div class="layui-input-inline">
				<input type="text" name="title" id="title" autocomplete="off" placeholder="请输入标题"  value="{{d.value.title}}" class="layui-input addText" />
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">分享描述：</label>
			<div class="layui-input-inline textarea">
				<textarea class="layui-textarea len-long" name="desc" id="desc" maxlength="100" lay-verify="introduction" placeholder="请输入备注，不能超过100个字符" >{{d.value.desc}}</textarea>
			</div>
		</div>
		{{# if(d.value.imgUrl != undefined){ }}
		<div class="layui-form-item">
			<label class="layui-form-label">分享图标：</label>
			<div class="layui-input-block img-upload">
				<div class="upload-img-block">
					<div class="upload-img-box {{# if(d.value.imgUrl){ }}hover{{# } }}">
						<div class="upload-default" id="logoUpload">
							{{# if(d.value.imgUrl){ }}
							<div id="preview_logoUpload" class="preview_img">
								<img layer-src judge = 'true' src="{{ ns.img(d.value.imgUrl) }}" class="img_prev"/>
							</div>
							{{#} else { }}
							<div class="upload">
								<i class="iconfont iconshangchuan"></i>
								<p>点击上传</p>
							</div>
							{{# } }}
						</div>
						<div class="operation">
							<div>
								<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
								<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
							</div>
							<div class="replace_img js-replace">点击替换</div>
						</div>
						<input type="hidden" name="imgUrl" value="{{d.value.imgUrl}}"/>
					</div>
				</div>
			</div>
		</div>
		{{# } }}
		
		<div class="layui-form-item">
			<label class="layui-form-label"></label>
			<div class="layui-input-inline">
				<button type="button" class="layui-btn" lay-submit lay-filter="formDemo">确认</button>
				<button type="button" id="closeOpen" class="layui-btn layui-btn-primary closeOpen">取消</button>
				<a id="save"></a>
			</div>
		</div>
		
	</div>
</script>
<script>
	var layer,laytpl,form,imgUrl,save_data = [], repeat_flag = false;
	layui.use(['layer','laytpl'], function(){
		layer = layui.layer;
		laytpl = layui.laytpl;
		form =layui.form;
		form.render();
	});  
	$(document).ready(function () {
		var edit_num = 0;
		
		$(".edit-btn").each(function(){
			$(this).click(function(){
				let data_key = $(this).parents('.card').attr('data-key');
				let data_data = JSON.parse($(this).parents('.card').attr('data-data'));
				let areaJudge = ['700px','440px']; 
				if(data_data.value.imgUrl == undefined){
					areaJudge = ['700px','330px']; 
				}
				let htmlOpenIndex = layer.open({
				  type: 1, 
				  title:'分享设置',
				  area:areaJudge,
				  content:"<div id='openHtml'></div>",
				  success:res=>{
						laytpl($('#openContent').html()).render(data_data,function(html){
							$('#openHtml').html(html);
						})
						
						//添加标记、用于变量替换时确定节点
						$("input[name='title']").on("focus",function(){
							$(this).addClass('addText');
							$("textarea[name='desc']").removeClass('addText');
						})
						
						$("textarea[name='desc']").on("focus",function(){
							$(this).addClass('addText');
							$("input[name='title']").removeClass('addText');
						})
						
						// 变量数据追加
						$('.layui-input-inline .addBtn').on('click',function(){
							//向后追加所用模板数据
							let addText = $(this).attr('data-tips');
							// 获取需要修改的demo节点name
							let name = $('.addText').attr('name');
							switch(name){
								case 'title':
									var txtArea = $("input[name='title']")[0];
									var content = txtArea.value;//文本域内容
									var start = txtArea.selectionStart;  //光标的初始位置，selectionStart：选区开始位置；selectionEnd：选区结束位置。
									txtArea.value = content.substring(0, txtArea.selectionStart) + addText + content.substring(txtArea.selectionEnd, content.length);
									var position = start + addText.length;
									$("input[name='title']").focus();
									txtArea.setSelectionRange(position+1, position);
									break;
								case 'desc':
									var txtArea = $("textarea[name='desc']")[0];
									var content = txtArea.value;//文本域内容
									var start = txtArea.selectionStart;  //光标的初始位置，selectionStart：选区开始位置；selectionEnd：选区结束位置。
									txtArea.value = content.substring(0, txtArea.selectionStart) + addText + content.substring(txtArea.selectionEnd, content.length);
									var position = start + addText.length;
									$("textarea[name='desc']").focus();
									txtArea.setSelectionRange(position+1, position);
									break;
							}
						})
						
						// 接口上传
						form.on('submit(formDemo)',function(data){
							let obj = data.field;
							obj.config_key = data_data.config.config_key;
							save_data = [];
							save_data.push(obj);
							if(data_data.value.imgUrl == undefined){
								save(save_data);
							}else{
								let judge = $('.preview_img .img_prev').attr('judge');
								if(!judge){
									let imgJudge = $('.upload-default .preview_img .img_prev');
									if(!imgJudge.length){
										save_data[0].imgUrl = '';
										save(save_data);
									}else{
										$('#save').click();
									}
								}else{
									save(save_data);
								}
							}
						})
						
						//选择图片
						var upload = new Upload({
							elem: '#logoUpload',
							auto: false, //选择文件后不自动上传
							bindAction: '#save', //指向一个按钮触发上传
							callback: function (res) {
								if(res.code >= 0){
									save_data[0].imgUrl = res.data.pic_path;
									save(save_data);
								}
							}
						});
						
						//点击取消弹窗关闭
						$('#closeOpen').on('click',function(){
							layer.close(htmlOpenIndex)
						})
						
						form.render();
						
						
					},
				});
				
			})
		})
	});
	
	function save(data){
		if(repeat_flag) return false;
		repeat_flag = true;
		$.ajax({
			type: 'POST',
			url: ns.url("wechat://shop/wechat/share"),
			data: {
				data_json : JSON.stringify(data),
			},
			dataType: 'JSON',
			success: function (res) {
				repeat_flag = false;
				layer.msg(res.message);
				listenerHash(); // 刷新页面
				layer.closeAll();
			}
		});
	}

</script>
