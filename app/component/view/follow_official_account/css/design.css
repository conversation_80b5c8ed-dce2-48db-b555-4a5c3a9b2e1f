@CHARSET "UTF-8";

.follow-official-account-wrap{
	/*position: absolute;*/
	/*width: 370px;*/
	/*z-index: 999;*/
}

.follow-official-account-wrap .preview-draggable{
	/*padding: 0 !important;*/
}

/* 关注公众号 */
.site-info-box {
	padding: 5px 10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.6);
	/*position: absolute;*/
	/*width: calc(100% - 20px);*/
	/*left:0;*/
}
.site-info-box .site-info {
	display: flex;
	align-items: center;
}
.site-info-box .site-info .img-box {
	width: 35px;
	height: 35px;
	line-height: initial;
	border-radius: 50%;
	overflow: hidden;
}
.site-info-box .site-info .img-box img {
	width: 100%;
	height: 100%;
}
.site-info-box .site-info .info-box {
	display: flex;
	flex-direction: column;
	color: #fff;
	justify-content: space-between;
	margin-left: 10px;
	text-align: left;
	width: 200px;
}
.site-info-box .site-info span {
	display: -webkit-box;
	font-size: 12px;
	overflow: hidden;
	opacity: 0.8;
	white-space: normal;
	text-overflow: ellipsis;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}
.site-info-box .site-info span:nth-child(1) {
	 width: 150px;
	 font-weight: bold;
	 opacity: 1;
	font-size: 14px;
 }
.site-info-box button {
	width: 90px;
	height: 28px;
	line-height: 28px;
	border-radius: 33px;
	font-size: 12px;
	padding: 0;
}