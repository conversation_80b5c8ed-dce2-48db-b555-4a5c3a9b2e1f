<nc-component :data="data[index]" class="notice">

	<!-- 预览 -->
	<template slot="preview">
		<template v-if="nc.lazyLoad">
			<div class="preview-box"
			     :style="{
			     borderTopLeftRadius: (nc.componentAngle == 'round' ? nc.topAroundRadius + 'px' : 0),
			     borderTopRightRadius: (nc.componentAngle == 'round' ? nc.topAroundRadius + 'px' : 0),
			     borderBottomLeftRadius: (nc.componentAngle == 'round' ? nc.bottomAroundRadius + 'px' : 0),
			     borderBottomRightRadius: (nc.componentAngle == 'round' ? nc.bottomAroundRadius + 'px' : 0),
			     backgroundColor : nc.componentBgColor }">
				<div :class="['notice-box',nc.contentStyle]">
					<div class="notice-con" v-for="(item, previewIndex) in nc.list" v-if="previewIndex < 1">
						<div :class="['img-wrap',nc.iconType]" v-if="previewIndex == 0">
							<img v-if="nc.iconType == 'img'" :src="changeImgUrl(nc.imageUrl)" />
							<div v-if="nc.iconType == 'icon'" class="icon-box">
								<iconfont :icon="nc.icon" v-if="nc.icon" :value="nc.style ? nc.style : ''"></iconfont>
							</div>
						</div>
						<div class="notice-con-split"></div>
						<span class="notice-con-font" :style="{color: nc.textColor ? nc.textColor : 'rgba(0,0,0,0)',fontWeight: nc.fontWeight,fontSize: nc.fontSize + 'px'}">{{ item.title }}</span>
					</div>
				</div>
			</div>
		</template>
	</template>

	<!-- 内容编辑 -->
	<template slot="edit-content">
		<template v-if="nc.lazyLoad">
			<notice-sources></notice-sources>

			<div class="template-edit-title">
				<h3>公告风格</h3>
				<div class="goods-list-edit layui-form" v-if="nc.tempData.iconList">
					<div class="layui-form-item icon-radio">
						<label class="layui-form-label sm">公告图标</label>
						<div class="layui-input-block">
							<span v-for="(item, sourcesKey) in nc.tempData.iconList" :key="sourcesKey" :class="[sourcesKey == nc.iconSources ? '' : 'layui-hide']">{{item.text}}</span>
							<ul class="icon-wrap">
								<li v-for="(item, sourcesKey) in nc.tempData.iconList" :key="sourcesKey" :class="[sourcesKey == nc.iconSources ? 'text-color border-color' : '']" @click="nc.iconSources=sourcesKey;nc.iconType=item.type;">
									<i class="iconfont" :class="[{'text-color': sourcesKey == nc.iconSources}, item.src]"></i>
								</li>
							</ul>
						</div>
						<div v-if="nc.iconSources == 'initial'" class="system-img">
							<div class="system-img-item" v-for="item in nc.tempData.iconList[nc.iconSources].icon" :class="{'border-color': nc.imageUrl == item}" @click="nc.imageUrl = item">
								<img :src="changeImgUrl(item)"/>
							</div>
						</div>
					</div>
					<div class="diy-img" v-show="nc.iconSources == 'diy'">
						<img-icon-upload :data="{data : nc}"></img-icon-upload>
						<div class="right-wrap">
							<div class="action-box" v-show="nc.iconType == 'icon'">
								<div class="action" @click="nc.tempData.methods.iconStyle($event)"><i class="iconfont iconpifu"></i></div>
								<div class="action" :id="'notice-color-' + nc.index"><i class="iconfont iconyanse"></i></div>
							</div>
							<div class="desc">宽度自适应，高度20px</div>
						</div>
					</div>
<!--					<div class="layui-form-item">-->
<!--						<label class="layui-form-label sm">风格</label>-->
<!--						<div class="layui-input-block">-->
<!--							<div @click="nc.contentStyle='style-1'" :class="{ 'layui-unselect layui-form-radio' : true,'layui-form-radioed' : (nc.contentStyle=='style-1') }">-->
<!--								<i class="layui-anim layui-icon">{{ nc.contentStyle=='style-1' ? "&#xe643;" : "&#xe63f;" }}</i>-->
<!--								<div>风格一</div>-->
<!--							</div>-->
<!--							<div @click="nc.contentStyle='style-2'" :class="{ 'layui-unselect layui-form-radio' : true,'layui-form-radioed' : (nc.contentStyle=='style-2') }">-->
<!--								<i class="layui-anim layui-icon">{{ nc.contentStyle=='style-2' ? "&#xe643;" : "&#xe63f;" }}</i>-->
<!--								<div>风格二</div>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
					<div class="layui-form-item">
						<label class="layui-form-label sm">滚动方式</label>
						<div class="layui-input-block">
							<div @click="nc.scrollWay='upDown'" :class="{ 'layui-unselect layui-form-radio' : true,'layui-form-radioed' : (nc.scrollWay=='upDown') }">
								<i class="layui-anim layui-icon">{{ nc.scrollWay=='upDown' ? "&#xe643;" : "&#xe63f;" }}</i>
								<div>上下滚动</div>
							</div>
							<div @click="nc.scrollWay='horizontal'" :class="{ 'layui-unselect layui-form-radio' : true,'layui-form-radioed' : (nc.scrollWay=='horizontal') }">
								<i class="layui-anim layui-icon">{{ nc.scrollWay=='horizontal' ? "&#xe643;" : "&#xe63f;" }}</i>
								<div>横向滚动</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="template-edit-title">
				<h3>公告内容</h3>
				<div class="layui-form-item" v-if="nc.tempData.noticeSources">
					<label class="layui-form-label sm">数据来源</label>
					<div class="layui-input-block">
						<div class="source-selected">
							<div class="source">{{ nc.tempData.noticeSources[nc.sources].text }}</div>
							<div v-for="(item,sourcesKey) in nc.tempData.noticeSources" :key="sourcesKey" class="source-item" :title="item.text" @click="nc.sources=sourcesKey" :class="{ 'text-color border-color' : (nc.sources == sourcesKey) }">
								<i class='iconfont' :class='item.icon'></i>
							</div>
						</div>
					</div>
				</div>

				<slide :data="{ field : 'count', label: '公告数量', min:1, max: 20}" v-if="nc.sources == 'initial'"></slide>

				<div class="layui-form-item" v-if="nc.sources == 'initial' && nc.tempData.methods">
					<label class="layui-form-label sm">选择公告</label>
					<div class="layui-input-block">
						<div class="input-text selected-style" @click="nc.tempData.methods.selectNotice()">
							<span v-if="nc.noticeIds.length == 0">请选择</span>
							<span v-if="nc.noticeIds.length > 0" class="text-color">已选{{ nc.noticeIds.length }}个</span>
							<i class="iconfont iconyoujiantou"></i>
						</div>
					</div>
				</div>

				<div class="notice-config" v-if="nc.sources == 'diy'">
					<ul>
						<li v-for="(item,index) in nc.list" :key="item.id">
							<div class="content-block">
								<div class="layui-form-item" >
									<label class="layui-form-label sm">内容</label>
									<div class="layui-input-block">
										<input type="text" name='title' v-model="item.title" class="layui-input" />
									</div>
								</div>
								<nc-link :data="{ field : nc.list[index].link, label:'链接' }"></nc-link>
							</div>
							<i class="del" @click="nc.list.splice(index,1)">x</i>
							<div class="iconfont icontuodong"></div>
						</li>
					</ul>
					<div class="add-item text-color border-color" @click="nc.tempData.methods.addNotice()">
						<i>+</i>
						<span>添加一条公告</span>
					</div>
				</div>
			</div>

		</template>
	</template>

	<!-- 样式编辑 -->
	<template slot="edit-style">
		<template v-if="nc.lazyLoad">
			<div class="template-edit-title">
				<h3>文字设置</h3>

				<slide :data="{ field : 'fontSize', label : '文字大小', min: 12, max : 20 }"></slide>

				<div class="layui-form-item icon-radio">
					<label class="layui-form-label sm">文字粗细</label>
					<div class="layui-input-block">
						<span v-for="item in nc.tempData.thicknessList" :class="[item.value == nc.fontWeight ? '' : 'layui-hide']">{{item.name}}</span>
						<ul class="icon-wrap">
							<li v-for="(item, index) in nc.tempData.thicknessList" :class="[item.value == nc.fontWeight ? 'text-color border-color' : '']" @click="nc.fontWeight = item.value">
								<i class="iconfont" :class="[{'text-color': item.value == nc.fontWeight}, item.src]"></i>
							</li>
						</ul>
					</div>
				</div>

				<color :data="{ field : 'textColor', label : '文字颜色',defaultColor: '#303133' }"></color>
			</div>
		</template>
	</template>

	<!-- 资源 -->
	<template slot="resource">
		<js>
			var noticeResourcePath = "{$resource_path}"; // http路径
			var noticeRelativePath = "{$relative_path}"; // 相对路径
		</js>
		<css src="{$resource_path}/css/design.css"></css>
		<js src="{$resource_path}/js/design.js"></js>
	</template>

</nc-component>