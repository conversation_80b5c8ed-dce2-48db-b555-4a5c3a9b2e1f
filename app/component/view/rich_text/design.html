<nc-component :data="data[index]" class="rich-text">

	<!-- 预览 -->
	<template slot="preview">
		<div :style="{backgroundColor: nc.componentBgColor ,
			borderTopLeftRadius: (nc.componentAngle == 'round' ? nc.topAroundRadius + 'px' : 0),
		     borderTopRightRadius: (nc.componentAngle == 'round' ? nc.topAroundRadius + 'px' : 0),
		     borderBottomLeftRadius: (nc.componentAngle == 'round' ? nc.bottomAroundRadius + 'px' : 0),
		     borderBottomRightRadius: (nc.componentAngle == 'round' ? nc.bottomAroundRadius + 'px' : 0)
		}">
			<div v-if="nc.html" v-html="nc.html"></div>
			<template v-else>
				<p>点此编辑『富文本』内容 ——&gt;</p>
				<p>你可以对文字进行<strong>加粗</strong>、<em>斜体</em>、<span style="text-decoration: underline;">下划线</span>、<span style="text-decoration: line-through;">删除线</span>、文字<span style="color: rgb(0, 176, 240);">颜色</span>、<span style="background-color: rgb(255, 192, 0); color: rgb(255, 255, 255);">背景色</span>、以及字号<span style="font-size: 20px;">大</span><span style="font-size: 14px;">小</span>等简单排版操作。</p>
				<p style="text-align: left;">
					<span style="text-align: left;">也可在这里插入图片、并对图片加上超级链接，方便用户点击。</span>
				</p>
			</template>
		</div>
	</template>

	<!-- 内容编辑 -->
	<template slot="edit-content">
		<template v-if="nc.lazyLoad">
			<div class="template-edit-title">
				<h3>内容设置</h3>
				<rich-text></rich-text>
			</div>
		</template>
	</template>

	<!-- 样式编辑 -->
	<template slot="edit-style"></template>
	
	<!-- 资源 -->
	<template slot="resource">
		<css src="{$resource_path}/css/design.css"></css>
		<js src="{$resource_path}/js/design.js"></js>
	</template>
	
</nc-component>