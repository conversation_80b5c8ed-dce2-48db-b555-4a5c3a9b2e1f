<?php

/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */

use GatewayWorker\BusinessWorker;
use Workerman\Worker;

// 自动加载类
require_once __DIR__ . '/../vendor/autoload.php';
$config_info = require __DIR__ . '/../../../config/gateway.php';

// bussinessWorker 进程
$worker = new BusinessWorker();
// worker名称
$worker->name = $config_info['worker']['name'];
// bussinessWorker进程数量
$worker->count = $config_info['worker']['count'];
// 服务注册地址
$worker->registerAddress = $config_info['worker']['register_address'];

// 如果不是在根目录启动，则运行runAll方法
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
