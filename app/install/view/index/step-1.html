{extend name="base"/}
{block name="main"}
<div class="pright layui-form">
	<h3 class="pr-title">阅读许可协议</h3>
	<div class="pr-agreement">

		<strong>版权所有 (c)2016，Niushop开源商城团队保留所有权利。</strong>
		<p>
			感谢您选择Niushop开源商城（以下简称NiuShop）NiuShop基于 PHP + MySQL的技术开发，全部源码开放。 <br/>
			为了使你正确并合法的使用本软件，请你在使用前务必阅读清楚下面的协议条款：
		</p>
		<p>
			<strong>一、本授权协议适用且仅适用于Niushop开源商城系统(以下简称Niushop)任何版本，Niushop开源商城官方对本授权协议的最终解释权。</strong>
		</p>
		<p>
			<strong>二、协议许可的权利 </strong>
		<ol>
			<li>非授权用户允许商用，严禁去除Niushop相关的版权信息。</li>
			<li>请尊重Niushop开发人员劳动成果，严禁使用本系统转卖、销售或二次开发后转卖、销售等商业行为。</li>
			<li>任何企业和个人不允许对程序代码以任何形式任何目的再发布。</li>
			<li>您可以在协议规定的约束和限制范围内修改Niushop开源商城源代码或界面风格以适应您的网站要求。</li>
			<li>您拥有使用本软件构建的网站全部内容所有权，并独立承担与这些内容的相关法律义务。</li>
			<li>
				获得商业授权之后，您可以将本软件应用于商业用途，同时依据所购买的授权类型中确定的技术支持内容，自购买时刻起，在技术支持期限内拥有通过指定的方式获得指定范围内的技术支持服务。商业授权用户享有反映和提出意见的权力，相关意见将被作为首要考虑，但没有一定被采纳的承诺或保证。
			</li>
		</ol>
		</p>
		<p>
			<strong>三、协议规定的约束和限制 </strong>
		<ol>
			<li>未获商业授权之前，允许您对Niushop应用于商业用途，但严禁去除Niushop任何相关的版权信息。</li>
			<li>未经官方许可，不得对本软件或与之关联的商业授权进行出租、出售、抵押或发放子许可证。</li>
			<li>未经官方许可，禁止在Niushop开源商城的整体或任何部分基础上以发展任何派生版本、修改版本或第三方版本用于重新分发。</li>
			<li>如果您未能遵守本协议的条款，您的授权将被终止，所被许可的权利将被收回，并承担相应法律责任。</li>
		</ol>
		</p>
		<p>
			<strong>四、有限担保和免责声明 </strong>
		<ol>
			<li>本软件及所附带的文件是作为不提供任何明确的或隐含的赔偿或担保的形式提供的。</li>
			<li>用户出于自愿而使用本软件，您必须了解使用本软件的风险，在尚未购买产品技术服务之前，我们不承诺对免费用户提供任何形式的技术支持、使用担保，也不承担任何因使用本软件而产生问题的相关责任。</li>
			<li>电子文本形式的授权协议如同双方书面签署的协议一样，具有完全的和等同的法律效力。您一旦开始确认本协议并安装
				Niushop，即被视为完全理解并接受本协议的各项条款，在享有上述条款授予的权力的同时，受到相关的约束和限制。协议许可范围以外的行为，将直接违反本授权协议并构成侵权，我们有权随时终止授权，责令停止损害，并保留追究相关责任的权力。
			</li>
			<li>如果本软件带有其它软件的整合API示范例子包，这些文件版权不属于本软件官方，并且这些文件是没经过授权发布的，请参考相关软件的使用许可合法的使用。</li>
		</ol>
		</p>
	</div>
	<div class="btn-box">
		<div class="btn-box-text">
			<div class="layui-form-item">
				<input type="checkbox" name="readpact" id="readpact" title="我已经阅读并同意此协议" value="1" lay-skin="primary">
			</div>
		</div>
		<button class="layui-btn" lay-submit lay-filter="go">继续</button>
	</div>
</div>
{/block}
{block name="script"}
<script>
	layui.use('form', function () {
		var form = layui.form;

		form.on('submit(go)', function (data) {
			var readpact = data.field.readpact;
			if (readpact == 1) {
				window.location.href = '{$root_url}/install.php/index/index?step=2';
			} else {
				error('您必须同意软件许可协议才能安装！');
			}

		});
	});
</script>
{/block}