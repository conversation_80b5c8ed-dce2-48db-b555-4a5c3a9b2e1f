<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace app\model\goods;

use app\model\BaseModel;

/**
 * 商品核销
 */
class GoodsVerify extends BaseModel
{
    /**
     * 添加待核销商品
     * @param unknown $data
     */
    public function addGoodsVerify($data)
    {

    }

    /**
     * 核销
     * @param unknown $code
     * @param unknown $verifier_id
     */
    public function verify($code, $verifier_id)
    {

    }
}