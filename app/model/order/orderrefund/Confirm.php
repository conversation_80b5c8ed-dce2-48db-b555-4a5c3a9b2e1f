<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 杭州牛之云科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com
 * =========================================================
 */

namespace app\model\order\orderrefund;

use app\model\BaseModel;
use app\model\message\Message;
use app\model\order\OrderCommon;
use app\model\order\OrderLog;
use app\model\order\OrderRefund;
use think\db\exception\DbException;

/**
 * 订单审核退款
 */
class Confirm extends BaseModel
{
    /**
     * 校验
     * @param $data
     * @return array|true
     * @throws DbException
     */
    public static function check($data)
    {
        return true;
    }

    /**
     * 退款执行事件
     * @param $data
     * @return true
     * @throws DbException
     */
    public static function event($data)
    {

        return true;
    }

    /**
     * 后续事件
     * @param $data
     * @return array|true
     */
    public static function after($data)
    {
        $order_info = $data['order_info'];
        $user_info = $data['user_info'];
        $order_goods_info = $data['order_goods_info'];
//        $log_data = $data['log_data'] ?? [];
        $refund_status = $data['refund_status'];
        $order_common_model = new OrderCommon();
        $log_data = [
            'uid' => $user_info['uid'],
            'nick_name' => $user_info['username'],
            'action' => '商品【'.$order_goods_info['sku_name'].'】商家同意了退款申请，等待转账',
            'action_way' => 2,
            'order_id' => $order_goods_info['order_id'],
            'order_status' => $order_info['order_status'],
            'order_status_name' => $order_info['order_status_name']
        ];
        OrderLog::addOrderLog($log_data, $order_common_model);
        $order_refund_model = new OrderRefund();
        //记录订单日志 end
        $order_refund_model->addOrderRefundLog($order_goods_info['order_goods_id'], $refund_status, '卖家确认退款', 2, $user_info['uid'], $user_info['username']);
        //订单退款同意消息
        $message_model = new Message();
        $message_model->sendMessage(['keywords' => 'ORDER_REFUND_AGREE', 'order_id' => $order_goods_info['order_id'], 'order_goods_id' => $order_goods_info['order_goods_id'], 'site_id' => $order_goods_info['site_id']]);
        //后续事件
        event('OrderRefundConfirmAfter', $data);
        return true;
    }
}