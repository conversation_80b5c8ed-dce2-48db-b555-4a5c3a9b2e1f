<?php

namespace app\model\xiaoxia;

use app\model\BaseModel;
use app\model\Model;

/**
 * 小夏商品管理类
 */
class Main extends BaseModel
{
    /**
     * 获取商品列表
     * @param array $condition 查询条件
     * @param string $field 查询字段，默认为所有字段
     * @param string $order 排序方式，默认按创建时间倒序
     * @param int|null $limit 限制数量
     * @return array
     */
    public function getGoodsList($condition = [], $field = '*', $order = 'create_time desc', $limit = null)
    {
        try {
            $goods_model = new Model('goods');
            $list = $goods_model->getList($condition, $field, $order, '', '', '', $limit);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改商品
     * @param array $condition 查询条件
     * @param array $data 要修改的数据
     * @return array
     */
    public function updateGoods($condition, $data)
    {
        try {
            $goods_model = new Model('goods');
            $result = $goods_model->update($data, $condition);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量修改商品排序
     * @param int $site_id 站点ID
     * @param array $sort_data 排序数据，格式：[['wb_spuId' => 'xxx', 'sort' => 100], ['wb_spuId' => 'yyy', 'sort' => 200]]
     * @return array
     */
    public function batchUpdateSort($site_id, $sort_data = [])
    {
        try {
            $goods_model = new Model('goods');

            // 第一步：将所有商品排序改为1000
            $all_condition = [['site_id', '=', $site_id]];
            $all_data = ['sort' => 1000];
            $goods_model->update($all_data, $all_condition);

            // 第二步：根据传入的数据修改指定商品的排序
            if (!empty($sort_data)) {
                foreach ($sort_data as $item) {
                    if (isset($item['wb_spuId']) && isset($item['sort'])) {
                        $condition = [
                            ['wb_spuId', '=', $item['wb_spuId']],
                            ['site_id', '=', $site_id]
                        ];
                        $data = ['sort' => $item['sort']];
                        $goods_model->update($data, $condition);
                    }
                }
            }

            return $this->success('批量修改排序完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 添加商品评价
     * @param array $data 评价数据
     * @return array
     */
    public function addGoodsEvaluate($data)
    {
        try {
            $evaluate_model = new Model('goods_evaluate');

            // 处理评价图片
            $images = '';
            if (isset($data['images'])) {
                if (is_array($data['images'])) {
                    // 如果是数组，转换为逗号分隔的字符串
                    $images = implode(',', $data['images']);
                } else {
                    // 如果是字符串，直接使用
                    $images = $data['images'];
                }
            }

            // 设置默认值
            $evaluate_data = [
                'order_id' => $data['order_id'] ?? 0,
                'order_no' => $data['order_no'] ?? '',
                'member_id' => $data['member_id'] ?? 0,
                'member_name' => $data['member_name'] ?? '匿名用户',
                'member_headimg' => $data['member_headimg'] ?? '',
                'is_anonymous' => $data['is_anonymous'] ?? 0,
                'order_goods_id' => $data['order_goods_id'] ?? 0,
                'goods_id' => $data['goods_id'],
                'sku_id' => $data['sku_id'] ?? 0,
                'site_id' => $data['site_id'],
                'sku_name' => $data['sku_name'] ?? '',
                'sku_price' => $data['sku_price'] ?? 0,
                'sku_image' => $data['sku_image'] ?? '',
                'content' => $data['content'] ?? '此用户没有填写评价。',
                'images' => $images,
                'scores' => $data['scores'] ?? 5,
                'explain_type' => $data['explain_type'] ?? 1, // 1:好评 2:中评 3:差评
                'is_audit' => $data['is_audit'] ?? 1, // 1:已审核 0:未审核
                'create_time' =>$data['create_time'] ?? time(), // 1:已审核 0:未审核
            ];

            $result = $evaluate_model->add($evaluate_data);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}