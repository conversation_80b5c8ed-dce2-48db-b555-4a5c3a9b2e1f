<?php

namespace app\model\xiaoxia;

use app\model\BaseModel;
use app\model\Model;

/**
 * 小夏商品管理类
 */
class Main extends BaseModel
{
    /**
     * 获取商品列表
     * @param array $condition 查询条件
     * @param string $field 查询字段，默认为所有字段
     * @param string $order 排序方式，默认按创建时间倒序
     * @param int|null $limit 限制数量
     * @return array
     */
    public function getGoodsList($condition = [], $field = '*', $order = 'create_time desc', $limit = null)
    {
        try {
            $goods_model = new Model('goods');
            $list = $goods_model->getList($condition, $field, $order, '', '', '', $limit);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改商品
     * @param array $condition 查询条件
     * @param array $data 要修改的数据
     * @return array
     */
    public function updateGoods($condition, $data)
    {
        try {
            $goods_model = new Model('goods');
            $result = $goods_model->update($data, $condition);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量修改商品排序
     * @param int $site_id 站点ID
     * @param array $sort_data 排序数据，格式：[['goods_id' => 1, 'sort' => 100], ['goods_id' => 2, 'sort' => 200]]
     * @return array
     */
    public function batchUpdateSort($site_id, $sort_data = [])
    {
        try {
            $goods_model = new Model('goods');

            // 第一步：将所有商品排序改为1000
            $all_condition = [['site_id', '=', $site_id]];
            $all_data = ['sort' => 1000];
            $goods_model->update($all_data, $all_condition);

            // 第二步：根据传入的数据修改指定商品的排序
            if (!empty($sort_data)) {
                foreach ($sort_data as $item) {
                    if (isset($item['goods_id']) && isset($item['sort'])) {
                        $condition = [
                            ['goods_id', '=', $item['goods_id']],
                            ['site_id', '=', $site_id]
                        ];
                        $data = ['sort' => $item['sort']];
                        $goods_model->update($data, $condition);
                    }
                }
            }

            return $this->success('批量修改排序完成');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}