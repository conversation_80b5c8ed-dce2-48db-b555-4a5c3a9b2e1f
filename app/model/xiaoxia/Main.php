<?php

namespace app\model\xiaoxia;

use app\model\BaseModel;
use app\model\Model;

/**
 * 小夏商品管理类
 * 提供商品列表获取等功能
 */
class Main extends BaseModel
{
    /**
     * 获取商品列表
     * @param array $condition 查询条件
     * @param string $field 查询字段
     * @param string $order 排序
     * @param int|null $limit 限制数量
     * @return array
     */
    public function getGoodsList($condition = [], $field = '*', $order = 'create_time desc', $limit = null)
    {
        try {
            $goods_model = new Model('goods');
            $list = $goods_model->getList($condition, $field, $order, '', '', '', $limit);

            if (!empty($list)) {
                foreach ($list as $k => $v) {
                    // 格式化库存显示
                    if (isset($v['goods_stock'])) {
                        $list[$k]['goods_stock'] = numberFormat($list[$k]['goods_stock']);
                    }
                    // 格式化价格显示
                    if (isset($v['price'])) {
                        $list[$k]['price'] = number_format($v['price'], 2);
                    }
                    if (isset($v['market_price'])) {
                        $list[$k]['market_price'] = number_format($v['market_price'], 2);
                    }
                    // 格式化创建时间
                    if (isset($v['create_time']) && is_numeric($v['create_time'])) {
                        $list[$k]['create_time_format'] = date('Y-m-d H:i:s', $v['create_time']);
                    }
                }
            }

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取商品分页列表
     * @param array $condition 查询条件
     * @param int $page 页码
     * @param int $page_size 每页数量
     * @param string $order 排序
     * @param string $field 查询字段
     * @return array
     */
    public function getGoodsPageList($condition = [], $page = 1, $page_size = 20, $order = 'create_time desc', $field = '*')
    {
        try {
            $goods_model = new Model('goods');
            $result = $goods_model->pageList($condition, $field, $order, $page, $page_size);

            if (!empty($result['list'])) {
                foreach ($result['list'] as $k => $v) {
                    // 格式化库存显示
                    if (isset($v['goods_stock'])) {
                        $result['list'][$k]['goods_stock'] = numberFormat($result['list'][$k]['goods_stock']);
                    }
                    // 格式化价格显示
                    if (isset($v['price'])) {
                        $result['list'][$k]['price'] = number_format($v['price'], 2);
                    }
                    if (isset($v['market_price'])) {
                        $result['list'][$k]['market_price'] = number_format($v['market_price'], 2);
                    }
                    // 格式化创建时间
                    if (isset($v['create_time']) && is_numeric($v['create_time'])) {
                        $result['list'][$k]['create_time_format'] = date('Y-m-d H:i:s', $v['create_time']);
                    }
                }
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 根据站点ID获取商品列表
     * @param int $site_id 站点ID
     * @param int $goods_state 商品状态 1:销售中 0:仓库中
     * @param int $limit 限制数量
     * @return array
     */
    public function getGoodsListBySite($site_id, $goods_state = 1, $limit = 50)
    {
        $condition = [
            ['site_id', '=', $site_id],
            ['goods_state', '=', $goods_state],
            ['is_delete', '=', 0]
        ];

        $field = 'goods_id,goods_name,goods_image,price,market_price,goods_stock,goods_state,create_time,sale_num,virtual_sale,introduction,keywords,unit';

        return $this->getGoodsList($condition, $field, 'create_time desc', $limit);
    }

    /**
     * 根据分类获取商品列表
     * @param int $site_id 站点ID
     * @param string $category_id 分类ID（逗号分隔的字符串，如 ",1,2,"）
     * @param int $goods_state 商品状态
     * @param int $limit 限制数量
     * @return array
     */
    public function getGoodsListByCategory($site_id, $category_id, $goods_state = 1, $limit = 50)
    {
        $condition = [
            ['site_id', '=', $site_id],
            ['category_id', 'like', '%' . $category_id . '%'],
            ['goods_state', '=', $goods_state],
            ['is_delete', '=', 0]
        ];

        $field = 'goods_id,goods_name,goods_image,price,market_price,goods_stock,goods_state,create_time,sale_num,virtual_sale,introduction,keywords,unit,category_id';

        return $this->getGoodsList($condition, $field, 'sort asc,create_time desc', $limit);
    }

    /**
     * 搜索商品
     * @param int $site_id 站点ID
     * @param string $keyword 搜索关键词
     * @param int $goods_state 商品状态
     * @param int $limit 限制数量
     * @return array
     */
    public function searchGoods($site_id, $keyword, $goods_state = 1, $limit = 50)
    {
        $condition = [
            ['site_id', '=', $site_id],
            ['goods_state', '=', $goods_state],
            ['is_delete', '=', 0]
        ];

        // 添加关键词搜索条件
        if (!empty($keyword)) {
            $condition[] = ['goods_name|keywords|introduction', 'like', '%' . $keyword . '%'];
        }

        $field = 'goods_id,goods_name,goods_image,price,market_price,goods_stock,goods_state,create_time,sale_num,virtual_sale,introduction,keywords,unit';

        return $this->getGoodsList($condition, $field, 'sale_num desc,create_time desc', $limit);
    }
}