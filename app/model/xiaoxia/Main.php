<?php

namespace app\model\xiaoxia;

use app\model\BaseModel;
use app\model\Model;

/**
 * 小夏商品管理类
 */
class Main extends BaseModel
{
    /**
     * 获取商品列表
     * @param array $condition 查询条件
     * @param string $field 查询字段，默认为所有字段
     * @param string $order 排序方式，默认按创建时间倒序
     * @param int|null $limit 限制数量
     * @return array
     */
    public function getGoodsList($condition = [], $field = '*', $order = 'create_time desc', $limit = null)
    {
        try {
            $goods_model = new Model('goods');
            $list = $goods_model->getList($condition, $field, $order, '', '', '', $limit);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 修改商品
     * @param array $condition 查询条件
     * @param array $data 要修改的数据
     * @return array
     */
    public function updateGoods($condition, $data)
    {
        try {
            $goods_model = new Model('goods');
            $result = $goods_model->update($data, $condition);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}