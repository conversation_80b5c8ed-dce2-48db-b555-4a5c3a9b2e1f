<style>
	.upload-img-block .upload-img-box .upload-default{position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}
</style>

<div class="layui-form form-wrap">

	<div class="layui-form-item">
		<label class="layui-form-label short-label"><span class="required">*</span>文章标题：</label>
		<div class="layui-input-inline">
			<input type="text" name="article_title" lay-verify="required" maxlength="40" autocomplete="off" placeholder="文章标题最多40个字" class="layui-input len-long">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>文章分类：</label>
		<div class="layui-input-inline">
			<select name="category_id" lay-verify="required">
				<option value="">请选择</option>
				{foreach $category_list as $v}
				<option value="{$v['category_id']}">{$v['category_name']}</option>
				{/foreach}
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label short-label">摘要：</label>
		<div class="layui-input-block">
			<textarea name="article_abstract" class="layui-textarea len-long" maxlength="100"></textarea>
		</div>
		<div class="word-aux">文章摘要最多可输入100个字</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>封面：</label>
		<div class="layui-input-block img-upload">
			<div class="upload-img-block square simple-uploading">
				<div class="upload-img-box" id="img">
					<div class="upload-default">
						<i class="iconfont iconshangchuan"></i>
						<p>点击上传</p>
					</div>
				</div>
				<input type="hidden" name="image" />
				<i class="del">x</i>
			</div>
		</div>
		<div class="word-aux">推荐使用 750x420 像素的图片</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>正文：</label>
		<div class="layui-input-inline">
			<script id="editor" type="text/plain" class="special-length" style="height:500px;"></script>
            <input type="hidden" name="article_content" id="article_content" />
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">发布时间：</label>
		<div class="layui-input-inline">
			<input type="radio" name="is_show_release_time" value="1" title="显示" checked>
			<input type="radio" name="is_show_release_time" value="0" title="不显示">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">阅读次数：</label>
		<div class="layui-input-inline">
			<input type="radio" name="is_show_read_num" value="1" title="显示" checked>
			<input type="radio" name="is_show_read_num" value="0" title="不显示">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">点赞次数：</label>
		<div class="layui-input-inline">
			<input type="radio" name="is_show_dianzan_num" value="1" title="显示" checked>
			<input type="radio" name="is_show_dianzan_num" value="0" title="不显示">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">虚拟阅读数：</label>
		<div class="layui-input-inline">
			<input type="number" min="0" name="initial_read_num" onchange="detectionNumType(this,'integral')" class="layui-input">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">虚拟点赞数：</label>
		<div class="layui-input-inline">
			<input type="number" min="0" name="initial_dianzan_num" onchange="detectionNumType(this,'integral')" class="layui-input">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-inline">
			<input type="number" min="0" name="sort" class="layui-input">
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">立即发布</button>
		<button class="layui-btn" lay-submit lay-filter="saveDrafts">保存至草稿箱</button>
		<button class="layui-btn layui-btn-primary" onclick="backArticleList()">返回</button>
	</div>
</div>

<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script>
    //实例化富文本
    var ue = UE.getEditor('editor');
    if($("#article_content").val()){
        ue.ready(function() {
            ue.setContent($("#article_content").val());
        });
    }

    var form,repeat_flag,
		IMAGE_MAX = 9, //最多可以上传多少张图片
		imageCollection = [], //图片集合
		selectedGoodsId = [],
		goods_id = [],
		goods_list =[];

    layui.use(['form'], function() {
		form = layui.form;
		repeat_flag = false;

		form.render();

		// 单图上传
		$("body").off("click", "#img").on("click", "#img", function () {
		    openAlbum(function (data) {
				imageCollection = [];
				imageCollection.push(data[0].pic_path);
				imageCollection.splice(1, imageCollection.length);
				var val = '<img src="' + ns.img(imageCollection[0]) + '" alt="">';
				$("#img").html(val);
		    }, 1);
		});

        /**
         * 表单提交(立即发布)
         */
        form.on('submit(save)', function(data){
            field = data.field;
            field.status = 1;
            formSubmit(field)
        });

        /**
         * 表单提交(草稿箱)
         */
        form.on('submit(saveDrafts)', function(data){
            field = data.field;
            field.status = 0;
            formSubmit(field)
        });
    });

    /**
     *  提交
     */
    function formSubmit(data){
		if (!imageCollection.length){
			layer.msg('请选择封面图！', {icon: 5, anim: 6});
			return;
		}

		data.cover_img = imageCollection.join();

		if (!ue.getContent()){
			layer.msg("文章内容不能为空");
			return false;
		}

		if(data.sort < 0){
			layer.msg("排序号不能小于0");
			return false;
		}

		data.article_content = ue.getContent();

		if(repeat_flag) return;
		repeat_flag = true;

		$.ajax({
			type: 'POST',
			dataType: 'JSON',
			url: ns.url("shop/article/add"),
			data: data,
			async: false,
			success: function(res){
				repeat_flag = false;

				if (res.code == 0) {
					layer.confirm('添加成功', {
						title:'操作提示',
						btn: ['返回列表', '继续添加'],
						closeBtn: 0,
						yes: function(index, layero){
							if(data.status == 1){
								location.hash = ns.hash("shop/article/lists");
							}else{
								location.hash = ns.hash("shop/article/drafts");
							}
							layer.close(index);
						},
						btn2: function(index, layero) {
							listenerHash(); // 刷新页面
							layer.close(index);
						}
					});
				}else{
					layer.msg(res.message);
				}
			}
		})
    }

    function backArticleList() {
        location.hash = ns.hash("shop/article/lists");
    }

	//检测数据类型
	function detectionNumType(el, type) {
        var value = $(el).val();
        //大于零 且 不是小数
        if (value < 0 && type == 'integral')
            $(el).val(0);
        else if (type == 'integral')
            $(el).val(Math.round(value));

        //大于1 且 不是小数
        if (value < 1 && type == 'positiveInteger')
            $(el).val(1);
        else if (type == 'positiveInteger')
            $(el).val(Math.round(value));
        //大于零可以是小数
        if (type == 'positiveNumber') {
            value = parseFloat(value).toFixed(2);
            if (value < 0)
                $(el).val(0);
            else
                $(el).val(value);
        }
    }
</script>
