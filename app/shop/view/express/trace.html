<div class="layui-form">
    <!-- 基础上传 -->
    <div class="layui-card card-common card-brief">
        <div class="layui-card-body">
            <div class="layui-form-item">
                <label class="layui-form-label">类型选择：</label>
                <div class="layui-input-block">
                    <div class="layui-input-inline">
                        {foreach $traces_type as $k => $v}
                            <input type="radio" name="traces_type" value="{$v.name}" lay-filter="traces_type" title="{$v.title}" {$v.is_use == 1 ? 'checked' : ''} />
                        {/foreach}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item config-item kd100"{if $kd100_config.is_use != 1} style="display:none;"{/if}>
        <label class="layui-form-label">APPKEY：</label>
        <div class="layui-input-block">
            <input type="text" name="appkey" placeholder="请输入内容APPKEY" value="{$kd100_config.value.appkey ?? ''}" autocomplete="off" class="layui-input len-long">
        </div>
        <div class="word-aux">快递100应用密钥</div>
    </div>

    <div class="layui-form-item config-item kd100"{if $kd100_config.is_use != 1} style="display:none;"{/if}>
        <label class="layui-form-label">CUSTOMER：</label>
        <div class="layui-input-block">
            <input type="text" name="customer" placeholder="请输入CUSTOMER" value="{$kd100_config.value.customer ?? ''}" autocomplete="off" class="layui-input len-long">
        </div>
        <div class="word-aux">快递100分配给的公司编号</div>
    </div>

    <div class="layui-form-item config-item kdbird"{if $kdbird_config.is_use != 1} style="display:none;"{/if}>
        <label class="layui-form-label">EBusinessID：</label>
        <div class="layui-input-block">
            <input type="text" name="EBusinessID" placeholder="请输入电商ID" value="{$kdbird_config.value.EBusinessID ?? ''}" autocomplete="off" class="layui-input len-long">
        </div>
        <div class="word-aux">快递鸟电商ID，请到快递鸟官网申请http://kdniao.com/reg</div>
    </div>

    <div class="layui-form-item config-item kdbird"{if $kdbird_config.is_use != 1} style="display:none;"{/if}>
        <label class="layui-form-label">AppKey：</label>
        <div class="layui-input-block">
            <input type="text" name="AppKey" placeholder="请输入AppKey" value="{$kdbird_config.value.AppKey ?? ''}" autocomplete="off" class="layui-input len-long">
        </div>
        <div class="word-aux">快递鸟分配的电商加密私钥，请到快递鸟官网申请http://kdniao.com/reg</div>
    </div>

    <div class="layui-form-item config-item kdbird" {if $kdbird_config.is_use != 1} style="display:none;"{/if}>
        <label class="layui-form-label">套餐类型：</label>
        <div class="layui-input-block">
            <div class="layui-input-inline">
<!--                <input type="radio" name="kdniao_request_type" value="1002" title="免费版" autocomplete="off" class="layui-input len-long" {if $kdbird_config.value.RequestType == 1002} checked {/if}>-->
                <input type="radio" name="kdniao_request_type" value="8002" title="按次付费" autocomplete="off" class="layui-input len-long" {if $kdbird_config.value.RequestType == 8002} checked {/if}>
                <input type="radio" name="kdniao_request_type" value="8001" title="按单付费" autocomplete="off" class="layui-input len-long" {if $kdbird_config.value.RequestType == 8001} checked {/if}>
            </div>
        </div>
        <div class="word-aux">
<!--            <p>1、免费版限500次/天，只支持3家快递，申通、圆通、百世，有效期半年（代码1002）</p>-->
            <p>1、按次付费查询，相同单号查询多次计费多次（代码8002）</p>
            <p>2、按单付费查询，相同单号查询多次计费一次（代码8001）</p>
        </div>
    </div>
    <div class="single-filter-box">
        <div class="form-row">
            <button class="layui-btn" lay-submit lay-filter="save">保存</button>
        </div>
    </div>
</div>

<script>
    layui.use('form', function() {
        var form = layui.form,
            repeat_flag = false; //防重复标识
        form.render();

        form.on('radio(traces_type)', function(data){
            $(".config-item").hide();

            $("."+data.value).show();
        });

        form.on('submit(save)', function(data) {

            if (repeat_flag) return;
            repeat_flag = true;
            $.ajax({
                url: ns.url("shop/express/trace"),
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(res) {
                    repeat_flag = false;
                    layer.msg(res.message);
                }
            });
        });
    });
</script>
