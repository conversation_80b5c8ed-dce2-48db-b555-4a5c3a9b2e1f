<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>品牌名称：</label>
		<div class="layui-input-inline len-long">
			<input name="brand_name" type="text" value="{$brand_info['brand_name']}" lay-verify="required" class="layui-input ">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">品牌简介：</label>
		<div class="layui-input-inline">
			<textarea class="layui-textarea len-long" name="brand_desc" maxlength="100" lay-verify="brand_desc" placeholder="请输入品牌简介，不能超过100个字符">{$brand_info['brand_desc']}</textarea>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">品牌logo：</label>
		<div class="layui-input-block">
			<input type="hidden" name="brand_id" value="{$brand_info['brand_id']}">
			<div class="upload-img-block">
				<div class="upload-img-box {notempty name="$brand_info['image_url']"}hover{/notempty}">
					<div class="upload-default" id="imgUpload">
						{if condition="$brand_info['image_url']"}
						<div id="preview_imgUpload" class="preview_img">
							<img layer-src src="{:img($brand_info['image_url'])}" class="img_prev"/>
						</div>
						{else/}
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
							
						{/if}
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image_url" value="{$brand_info['image_url']}">
				</div>
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：200px * 100px。</p>
			<p>图片格式：jpg、png、jpeg。</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">品牌广告图：</label>
		<div class="layui-input-block">
			<div class="upload-img-block">
				<div class="upload-img-box {notempty name="$brand_info['banner']"}hover{/notempty}">
					<div class="upload-default" id="imgBannerUpload">
						{if condition="$brand_info['banner']"}
						<div id="preview_imgBannerUpload" class="preview_img">
							<img layer-src src="{:img($brand_info['banner'])}" class="img_prev"/>
						</div>
						{else/}
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>

						{/if}
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="banner" value="{$brand_info['banner']}">
				</div>
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：200px * 100px。</p>
			<p>图片格式：jpg、png、jpeg。</p>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-inline">
			<input type="number" name="sort" value="{$brand_info['sort']}" class="layui-input" placeholder="0" autocomplete="off">
		</div>
	</div>
	
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="back()">返回</button>
	</div>
</div>

<script>
	layui.use(['form'], function() {
		var form = layui.form,
			repeat_flag = false;//防重复标识
		form.render();
		
		/**
		 * 表单验证
		 */
		form.verify({
			// 品牌简介
			brand_desc: function (value) {
				if (value.length > 100) {
					element.tabChange('goods_tab', "basic");
					return '品牌简介不能超过100个字符';
				}
			},
			num: function (value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return '排序数值必须为整数';
				}
				if (value < 0) {
					return '排序数值必须为大于0';
				}
			}
		});

		// 普通图片上传
		var logo_upload = new Upload({
			elem: '#imgUpload'
		});

		var banner_upload = new Upload({
			elem: '#imgBannerUpload'
		});
		
		form.on('submit(save)', function (data) {
			
			if (repeat_flag) return false;
			repeat_flag = true;
			
			if(!data.field.image_url) logo_upload.delete();

			if(!data.field.banner) banner_upload.delete();
			
			$.ajax({
				url: '{:addon_url("shop/goodsbrand/editBrand")}',
				data: data.field,
				dataType: 'json',
				type: 'post',
				success: function (res) {
					repeat_flag = false;
					
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero) {
								location.hash = ns.hash("shop/goodsbrand/lists");
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						})
					}else{
						layer.msg(res.message);
					}
				}
			});
			return false;
		});
	});
	
	function back(){
		location.hash = ns.hash("shop/goodsbrand/lists")
	}
</script>
