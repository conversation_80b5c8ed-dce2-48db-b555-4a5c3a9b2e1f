<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="addBrand()">添加品牌</button>
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入品牌名称" autocomplete="off" class="layui-input">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<table id="brand_list" lay-filter="brand_list"></table>

<script type="text/html" id="image_url">
	{{# if(d.image_url){ }}
	<div class="img-box">
		<img layer-src src={{ns.img(d.image_url)}} >
	</div>
	{{# } }}
</script>

<script type="text/html" id="banner">
	{{# if(d.banner){ }}
	<div class="img-box">
		<img layer-src src={{ns.img(d.banner)}} >
	</div>
	{{# } }}
</script>

<!-- 操作 -->
<script type="text/html" id="action">
	<div class="table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<!-- 编辑排序 -->
<script type="text/html" id="editSort">
	<input name="sort" type="number" onchange="editSort({{d.brand_id}}, this)" value="{{d.sort}}" class="layui-input edit-sort">
</script>

<script>
	var form, table;
	layui.use(['table', 'form'], function() {
		form = layui.form;
		form.render();
		
		table = new Table({
			elem: '#brand_list',
			url: ns.url("shop/goodsbrand/lists"),
			cols: [
				[
					/* {
					width: "3%",
					type: 'checkbox',
					unresize: 'false'
				}, */
				{
					field: 'brand_name',
					title: '品牌名称',
					width: '30%',
					unresize: 'false'
				}, {
					field: 'image_url',
					title: '品牌LOGO',
					width: '15%',
					unresize: 'false',
					templet: "#image_url"
				}, {
					field: 'banner',
					title: '品牌广告图',
					width: '15%',
					unresize: 'false',
					templet: "#banner"
				},{
					unresize: 'false',
					title: '排序',
					width: '15%',
					templet: '#editSort'
				}, {
					title: '操作',
					width: '25%',
					toolbar: '#action',
					align:'right',
					unresize: 'false'
				}]
			]
		});
		
		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit':
					location.hash = ns.hash("shop/goodsbrand/editbrand",{"brand_id":data.brand_id});
					break;
				case 'delete':
					deleteBrand(data.brand_id);
					break;
			}
		});
		
		/**
		 * 删除
		 */
		function deleteBrand(brand_id) {
			layer.confirm('确定要删除该品牌吗？', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("shop/goodsbrand/deleteBrand"),
					data: {brand_id: brand_id},
					dataType: 'JSON',
					type: 'POST',
					success: function (res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}
		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data){
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});
	});
	
	// 监听单元格编辑
	function editSort(id, event){
		var data = $(event).val();
		
		if(!new RegExp("^-?[0-9]\\d*$").test(data)){
			layer.msg("排序号只能是整数");
			return ;
		}
		if(data<0){
			layer.msg("排序号必须大于0");
			return ;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("shop/goodsbrand/modifySort"),
			data: {
				sort: data,
				brand_id: id
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}
	
	function addBrand() {
		location.hash = ns.hash("shop/goodsbrand/addBrand");
	}
</script>
