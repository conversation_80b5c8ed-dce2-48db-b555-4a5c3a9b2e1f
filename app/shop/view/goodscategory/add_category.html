<style>
	.layui-input-inline.js-pid a{margin-left: 20px;}
	.link-url-show{margin-right: 10px}
	.click-link{height: 34px;line-height: 34px;display: inline-block;white-space: nowrap;text-align: center;border-radius: 2px;cursor: pointer;padding: 0 16px;border: 1px solid #C9C9C9;background-color: #fff;color: #555;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>分类名称：</label>
		<div class="layui-input-block len-long">
			<input name="category_name" type="text" placeholder="请输入分类名称" maxlength="30" lay-verify="required" class="layui-input" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>分类名称最长不超过30个字符</p>
		</div>
	</div>

	<!-- <div class="layui-form-item">
		<label class="layui-form-label">简称：</label>
		<div class="layui-input-block len-long">
			<input name="short_name" type="text" placeholder="请输入简称" maxlength="20" class="layui-input" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>分类名过长设置简称方便显示，字数设置为不超过20个字符</p>
		</div>
	</div>
	 -->
	<div class="layui-form-item">
		<label class="layui-form-label">上级分类：</label>
		<div class="layui-input-block js-pid">
			<span class="input-text">顶级分类</span>
			<input type="hidden" name="pid" value="0">
			<input type="hidden" name="level" value="1">
			<input type="hidden" name="category_id_1" value="0">
			<input type="hidden" name="category_id_2" value="0">
			<a class="text-color" href="javascript:selectedCategoryPopup();">选择分类</a>
		</div>
		<div class="word-aux">
			<p>如果选择上级分类，那么新增的分类则为被选择上级分类的子分类，不选择上级分类默认为顶级分类</p>
		</div>
	</div>
	
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">商品参数：</label>
		<div class="layui-input-block len-mid">
			<select name="attr_class_id" lay-filter="attr_class_id">
				<option value=""></option>
				{foreach name="$attr_class_list" item="vo"}
				<option value="{$vo.class_id}">{$vo.class_name}</option>
				{/foreach}
			</select>
		</div>

	</div> -->

	<div class="layui-form-item">
		<label class="layui-form-label">分类图片：</label>
		<div class="layui-input-block">
			<div class="upload-img-block">
				<div class="upload-img-box">
					<div class="upload-default" id="imgUpload">
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image">
				</div>
				<!-- <p id="imgUpload" class="no-replace">替换</p>
				<input type="hidden" name="image">
				<i class="del">x</i> -->
			</div>
		</div>
		<div class="word-aux">
			<p>建议图片尺寸：不能大于100k。图片格式：jpg、png、jpeg。</p>
		</div>
		<a id="imageUploadAction"></a>
	</div>

    <div class="layui-form-item">
        <label class="layui-form-label">分类广告图：</label>
        <div class="layui-input-block">
            <div class="upload-img-block">
                <div class="upload-img-box">
					<div class="upload-default" id="imgUploadAdv">
						<div class="upload">
							<i class="iconfont iconshangchuan"></i>
							<p>点击上传</p>
						</div>
					</div>
					<div class="operation">
						<div>
							<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
						<div class="replace_img js-replace">点击替换</div>
					</div>
					<input type="hidden" name="image_adv" value="">
                </div>
			<!-- 	<p id="imgUploadAdv" class="no-replace">替换</p>
	            <i class="del">x</i> -->
            </div>
        </div>
	    <div class="word-aux">
		    <p>该图片只对一级使用, 建议图片尺寸：550px * 250px。图片格式：jpg、png、jpeg。</p>
	    </div>
		<a id="imageAdvUploadAction"></a>
    </div>
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">是否显示：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_show" lay-skin="switch" value="1" checked>
		</div>
		<div class="word-aux">
			<p>用于控制前台是否展示</p>
		</div>
	</div> -->
	<div class="layui-form-item link-url-show-wrap">
		<label class="layui-form-label">广告链接：</label>
		<div class="layui-input-block">
			<span class="link-url-show"></span>
			<input name="link_url" type="hidden" class="layui-input len-long" autocomplete="off">
			<a class="click-link" onclick="selectedLink()">选择链接</a>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">关键字：</label>
		<div class="layui-input-block">
			<input name="keywords" type="text" placeholder="请输入关键字" class="layui-input len-long" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>用于网站搜索引擎的优化，关键字之间请用英文逗号分隔</p>
		</div>
	</div>
	
	<div class="layui-form-item">
		<label class="layui-form-label">分类描述：</label>
		<div class="layui-input-block len-long">
			<textarea name="description" placeholder="请输入分类描述" class="layui-textarea" maxlength="150"></textarea>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">分类图标：</label>
		<div class="layui-input-block">
			<input name="icon" type="text" placeholder="请输入分类图标类名，示例：iconfont home" class="layui-input len-long" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>通过引用外部文件，实现加载字体图标，示例：iconfont home</p>
		</div>
	</div>
	
	<!-- <div class="layui-form-item">
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-block">
			<input name="sort" type="number" value="0" placeholder="请输入排序" lay-verify="num" class="layui-input len-short" autocomplete="off">
		</div>
		<div class="word-aux">
			<p>排序值必须为整数</p>
		</div>
	</div> -->

	<div class="layui-form-item">
		<label class="layui-form-label">是否推荐</label>
		<div class="layui-input-block">
			<input type="radio" name="is_recommend" value="1" title="是">
			<input type="radio" name="is_recommend" value="0" title="否" checked>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">是否显示</label>
		<div class="layui-input-block">
			<input type="radio" name="is_show" value="0" title="显示" checked="">
			<input type="radio" name="is_show" value="-1" title="不显示">
		</div>
		<div class="word-aux">
			<p>如果不显示会导致小程序、h5没有该分类以及该分类下的所有商品</p>
		</div>
	</div>

	<div class="form-row">
		<button class="layui-btn bg-color" lay-submit lay-filter="save">保存</button>
		<button type="reset" class="layui-btn layui-btn-primary" onclick="backGoodsCategoryList()">返回</button>
	</div>
</div>

<script type="text/html" id="selectedCategory">

	<form class="layui-form">

		<div class="layui-form-item">
			<label class="layui-form-label sm">一级分类</label>
			<div class="layui-input-block len-mid">
				<select name="category_id_1" lay-filter="category_id_1">
					<option value="0" data-level="0">顶级分类</option>
					{{# for(var i=0;i<d.category_list_1.length;i++){ }}
						{{# if(d.category_id_1 ==d.category_list_1[i].category_id){ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}" selected>{{ d.category_list_1[i].category_name }}</option>
						{{# }else{ }}
						<option value="{{ d.category_list_1[i].category_id }}" data-level="{{d.category_list_1[i].level}}">{{ d.category_list_1[i].category_name }}</option>
						{{# } }}
					{{# } }}
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label sm">二级分类</label>
			<div class="layui-input-block len-mid">
				<select name="category_id_2" lay-filter="category_id_2"></select>
			</div>
		</div>

		<div class="form-row sm">
			<button class="layui-btn bg-color" lay-submit lay-filter="save_pid">保存</button>
		</div>
		
	</form>
</script>

<script src="SHOP_JS/goods_edit_category.js"></script>
