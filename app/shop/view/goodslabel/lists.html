<style>
	.layui-layer-content {padding-bottom: 20px!important;}
</style>
<!-- 搜索框 -->
<div class="single-filter-box">
	<button class="layui-btn" onclick="addLabel()">添加商品标签</button>
	<div class="layui-form">
		<div class="layui-input-inline">
			<input type="text" name="search_keys" placeholder="请输入商品标签名称" autocomplete="off" class="layui-input" maxlength="10">
			<button type="button" class="layui-btn layui-btn-primary" lay-filter="search" lay-submit>
				<i class="layui-icon">&#xe615;</i>
			</button>
		</div>
	</div>
</div>

<!-- 列表 -->
<table id="attr_class_list" lay-filter="attr_class_list"></table>

<!-- 编辑排序 -->
<script type="text/html" id="editSort">
	<input name="sort" type="number" onchange="editSort({{d.id}}, this)" value="{{d.sort}}" class="layui-input edit-sort len-short">
</script>

<!-- 操作 -->
<script type="text/html" id="operation">
	<div class="table-btn">
		<a class="layui-btn" lay-event="edit">编辑</a>
		<a class="layui-btn" lay-event="delete">删除</a>
	</div>
</script>

<script type="text/html" id="addLabel">

	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>商品标签：</label>
			<div class="layui-input-block">
				<input name="label_name" type="text" placeholder="请输入商品标签名称" lay-verify="required" class="layui-input len-mid" maxlength="10">
			</div>
		</div>
		
		<div class="form-row mid">
			<button class="layui-btn" lay-submit lay-filter="save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="closeAttrLayer()">返回</button>
		</div>
	</div>
	
</script>

<script type="text/html" id="editLabel">

	<div class="layui-form">
		<div class="layui-form-item">
			<label class="layui-form-label mid"><span class="required">*</span>商品标签：</label>
			<div class="layui-input-block">
				<input name="label_name" type="text" value="{{ d.label_name }}" placeholder="请输入商品标签名称" lay-verify="required" class="layui-input len-mid" maxlength="10">
			</div>
		</div>
		
		<input type="hidden" name="id" value="{{ d.id }}">
		<div class="form-row mid">
			<button class="layui-btn" lay-submit lay-filter="edit_save">保存</button>
			<button class="layui-btn layui-btn-primary" onclick="closeAttrLayer()">返回</button>
		</div>
	</div>

</script>

<script>
	var laytpl, add_attr_index = -1,
		form, table;
	layui.use(['form', 'laytpl'], function() {
		var repeat_flag = false; //防重复标识
		laytpl = layui.laytpl;
		form = layui.form;
		form.render();

		table = new Table({
			elem: '#attr_class_list',
			url: ns.url("shop/goodslabel/lists"),
			cols: [
				[ {
					field: 'label_name',
					title: '标签名称',
					unresize: 'false'
				}, {
                    field: 'sort',
					unresize:'false',
					title: '排序',
					width: '20%',
                    sort : true,
					align: 'center',
					templet: '#editSort'
				}, {
					title: '操作',
					toolbar: '#operation',
					unresize: 'false',
					align : 'right'
				}]
			]
		});

		/**
		 * 监听工具栏操作
		 */
		table.tool(function(obj) {
			var data = obj.data;
			switch (obj.event) {
				case 'edit':
                    editLabel(data);
					break;
				case 'delete':
                    deleteLabel(data.id);
					break;
			}
		});

        table.on("sort",function (obj) {
            table.reload({
                page: {
                    curr: 1
                },
                where: {
                    order:obj.field,
                    sort:obj.type
                }
            });
        });

		/**
		 * 删除
		 */
		function deleteLabel(id) {
			layer.confirm('确认删除该标签吗？', function(index) {
				layer.close(index);
				$.ajax({
					url: ns.url("shop/goodslabel/delete"),
					data: {
                        id:id
					},
					dataType: 'JSON',
					type: 'POST',
					success: function(res) {
						layer.msg(res.message);
						if (res.code == 0) {
							table.reload();
						}
					}
				});
			});
		}

		/**
		 * 搜索功能
		 */
		form.on('submit(search)', function(data) {
			table.reload({
				page: {
					curr: 1
				},
				where: data.field
			});
		});

		form.on('submit(save)', function(data) {

			if (repeat_flag) return false;
			repeat_flag = true;

			$.ajax({
				url: '{:addon_url("shop/goodslabel/add")}',
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(data) {
					layer.msg(data.message);
					if (data.code == 0) {
						table.reload();
						layer.close(add_attr_index);
					}
					repeat_flag = false;
				}
			});
			return false;
		});

        form.on('submit(edit_save)', function(data) {

            if (repeat_flag) return false;
            repeat_flag = true;

            $.ajax({
                url: '{:addon_url("shop/goodslabel/edit")}',
                data: data.field,
                dataType: 'JSON',
                type: 'POST',
                success: function(data) {
                    layer.msg(data.message);
                    if (data.code == 0) {
                        table.reload();
                        layer.close(add_attr_index);
                    }
                    repeat_flag = false;
                }
            });
            return false;
        });

		/**
		 * 表单验证
		 */
		form.verify({
			num: function(value) {
				if (value == '') {
					return;
				}
				if (value % 1 != 0) {
					return '排序数值必须为整数';
				}
				if (value < 0) {
					return '排序数值必须为大于0';
				}
			}
		});
	});

	function addLabel() {
		var add_attr = $("#addLabel").html();
		laytpl(add_attr).render({}, function(html) {
			add_attr_index = layer.open({
				title: '添加商品标签',
				skin: 'layer-tips-class',
				type: 1,
				area: ['500px', '200px'],
				content: html
			});
		});

	}

    function editLabel(data) {
        var add_attr = $("#editLabel").html();
        laytpl(add_attr).render(data, function(html) {
            add_attr_index = layer.open({
                title: '编辑商品标签',
                skin: 'layer-tips-class',
                type: 1,
                area: ['500px', '300px'],
                content: html
            });
        });

    }
	
	function closeAttrLayer() {
		layer.close(add_attr_index);
	}
	
	// 监听单元格编辑
	function editSort(id, event){
		var data = $(event).val();
		
		if (data == '') {
			$(event).val(0);
			data = 0;
		}
		
		if(!new RegExp("^-?[0-9]\\d*$").test(data)){
			layer.msg("排序号只能是整数");
			return ;
		}
		if(data<0){
			layer.msg("排序号必须大于0");
			return ;
		}
		$.ajax({
			type: 'POST',
			url: ns.url("shop/goodslabel/modifySort"),
			data: {
				sort: data,
				id: id
			},
			dataType: 'JSON',
			success: function(res) {
				layer.msg(res.message);
				if(res.code==0){
					table.reload();
				}
			}
		});
	}
</script>
