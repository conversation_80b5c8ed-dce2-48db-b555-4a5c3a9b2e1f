<!-- 订单物流发货 -->
<style>
    .layui-table-body {
        overflow: unset;
    }

    .delivery-content {
        padding: 7px 0 !important;
    }

    .layui-table-view {
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .layui-form #order_goods_list thead th, .layui-form #order_goods_list tbody tr {
        border-bottom: 1px solid #E6E6E6;
    }

    .layui-form #order_goods_list thead th {
        background-color: #F5F5F5;
        line-height: 30px;
    }

    .order-delivery .input-text {
        height: auto;
        min-height: 34px;
    }
</style>
<!--发货订单弹出框-->
<script type="text/html" id="local_order_delivery_html">
    <div class="order-delivery">
        <div class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">收货地址：</label>
                <div class="layui-input-block">
                    <p class="input-text len-long"> {{ d.order_info.full_address }}{{ d.order_info.address }}</p>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">发货方式：</label>
                <div class="layui-input-block">
                    <input type="radio" lay-filter="delivery_type" name="delivery_type" value="default" title="商家自配送" checked>
                    <input type="radio" lay-filter="delivery_type" name="delivery_type" value="default" title="第三方配送" disabled>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">配送员：</label>
                <div class="layui-input-block">
                    <input type="text" name="deliverer" lay-verify="required" placeholder="" autocomplete="off" class="layui-input len-mid">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">配送员手机号：</label>
                <div class="layui-input-block">
                    <input type="text" name="deliverer_mobile" lay-verify="required|mobile" placeholder="" autocomplete="off" class="layui-input len-mid">
                </div>
            </div>

            <input type="hidden" name="order_id" value="{{ d.order_info.order_id }}" class="layui-input" />
            <div class="form-row">
                <button type="button" class="layui-btn" lay-submit id="button_local_delivery_order" lay-filter="button_local_delivery_order" style="display:none;">保存</button>
            </div>
        </div>
    </div>
</script>

<script>
    // 外卖配送订单发货
    function orderLocalDelivery(order_id) {
        var submitting = false;
        layui.use(['form', 'laytpl'], function () {
            var laytpl = layui.laytpl, form = layui.form;
            form.render();
            var getTpl = $("#local_order_delivery_html").html();
            var order_info = getOrderInfo(order_id);
            var data = {order_info};
            laytpl(getTpl).render(data, function (html) {
                layer_index = layer.open({
                    type: 1,
                    shadeClose: true,
                    shade: 0.3,
                    fixed: false,
                    scrollbar: false,
                    title: "订单发货",
                    area: '800px',
                    btn: ['保存'],
                    yes: function (index, layero) {
                        $("#button_local_delivery_order").click();
                    },
                    content: html,
                    cancel: function (index, layero) {
                        //右上角关闭回调
                        layer.close(index);
                        //return false 开启该代码可禁止点击该按钮关闭
                    },
                    success: function (layero, index) {
                        form.render();
                        form.verify({
                            mobile:function (value){
                                if(!ns.getRegexp('mobile').test(value)){
                                    return '请输入正确的手机号';
                                }
                            }
                        })
                        form.on('submit(button_local_delivery_order)', function (data) {
                            if (submitting) return false;
                            submitting = true;

                            $.ajax({
                                type: "post",
                                url: ns.url("shop/localorder/delivery"),
                                async: true,
                                dataType: 'json',
                                data: data.field,
                                success: function (res) {
                                    layer.msg(res.message);
                                    if (res.code == 0) {
                                        layer.close(layer_index);
                                        reloadList();
                                    }
                                    submitting = false;
                                }
                            })
                        });

                    }

                });
            })
        })

    }
</script>