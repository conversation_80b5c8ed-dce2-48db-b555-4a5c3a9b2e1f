<style>
	.custom-panel .custom-panel-from { display: flex; }
	.custom-panel .custom-panel-from .layui-form-label{
		text-align: center;
	}
	.custom-panel .custom-panel-from>div{
		text-align: center;
	}
	.custom-panel .custom-panel-from .layui-form-item>div>span{
		margin-right: 10px;
	}
	.screen {
		margin: 15px 0;
	}
	.layui-colla-content {
		border: none;
		background-color: #F2F3F5;
	}
</style>
<div class="custom-panel">
	<div class="screen layui-collapse" lay-filter="selection_panel">
		<div class="layui-colla-item">
			<form class="layui-colla-content layui-form layui-show">
				<div class="layui-form-item flex">
					{if $account_type == 'balance,balance_money'}
						<div class="layui-inline">
							<label class="layui-form-label">账户类型</label>
							<div class="layui-input-inline">
								<select name="account_type" lay-filter="account_type">
									<option value="balance,balance_money">请选择</option>
									<option value="balance">储值余额</option>
									<option value="balance_money">现金余额</option>
								</select>
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">来源类型</label>
							<div class="layui-input-inline">
								<select name="from_type" class="from_type">
									<option value="">请选择</option>
								</select>
							</div>
						</div>
					{else /}
						<div class="layui-inline">
							<label class="layui-form-label">来源类型</label>
							<div class="layui-input-inline">
								<select name="from_type" class="from_type">
									<option value="">请选择</option>
									{foreach $from_type_arr as $from_type_arr_k => $from_type_arr_v}
									<option value="{$from_type_arr_k}">{$from_type_arr_v['type_name']}</option>
									{/foreach}
								</select>
							</div>
						</div>
					{/if}

				</div>
				<div class="layui-form-item flex">
					<div class="layui-inline">
						<label class="layui-form-label">发生时间</label>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" name="start_date" id="start_date_{$table_id}" placeholder="开始时间" autocomplete="off" readonly>
						</div>
					</div>
					<div class="layui-inline">
						<div class="layui-input-inline split">&nbsp;&nbsp;-&nbsp;&nbsp;</div>
						<div class="layui-input-inline">
							<input type="text" class="layui-input" name="end_date" id="end_date_{$table_id}" placeholder="结束时间" autocomplete="off" readonly>
						</div>
					</div>
				</div>
				<div class="form-row">
					<button class="layui-btn" lay-submit lay-filter="search_{$table_id}">筛选</button>
					<button type="reset" class="layui-btn layui-btn-primary">重置</button>
				</div>
				<input type="hidden" name="member_id" value="{$member_id}" id="member_id"/>
			</form>
		</div>
	</div>
</div>
<!-- 列表 -->
<table id="member_account_{$table_id}" lay-filter="member_account_{$table_id}"></table>
<script>
	//余额 积分 成长值 都是同一个页面 变量都是相同的 需要通过闭包避免变量污染
	(function(){
		var form, table, laydate, laytpl;
		var currentDate = new Date();
		currentDate.setDate(currentDate.getDate() - 7);

		layui.use(['form', 'laydate', 'laytpl'], function() {
			form = layui.form;
			laydate = layui.laydate;
			laytpl = layui.laytpl;
			form.render();

			//开始时间
			laydate.render({
				elem: '#start_date_{$table_id}',
				type: 'datetime'
			});

			//结束时间
			laydate.render({
				elem: '#end_date_{$table_id}',
				type: 'datetime'
			});

			laydate.render({
				elem: '#start_date2',
				type: 'datetime'
			});

			//结束时间
			laydate.render({
				elem: '#end_date2',
				type: 'datetime'
			});

			laydate.render({
				elem: '#start_date3',
				type: 'datetime'
			});

			//结束时间
			laydate.render({
				elem: '#end_date3',
				type: 'datetime'
			});

			//根据账户类型获取来源类型
			form.on('select(account_type)', function (data) {
				if (data.value == 'balance,balance_money') return;
				$.ajax({
					type: "POST",
					url: ns.url("shop/member/getfromtype"),
					data: {type: data.value},
					dataType: 'JSON',
					success: function (res) {
						var html = '<option value="">请选择</option>';
						$.each(res, function (k, v) {
							html += '<option value="' + k + '">' + v.type_name + '</option>';
						});

						$('.from_type').html(html);
						form.render();
					}
				});
			});
			table = new Table({
				elem: '#member_account_{$table_id}',
				url: ns.url("shop/member/accountDetail"),
				where:{
					member_id : $("#member_id").val(),
					account_type : '{$account_type}',
				},
				cols: [
					[{
						field: 'account_type_name',
						title: '账户类型',
						width: '15%',
						unresize: 'false'
					}, {
						title: '数据金额',
						width: '15%',
						unresize: 'false',
						align:'right',
						templet: function (d) {
							if (d.account_data > 0) {
								return '+' + (d.account_type == "point" || d.account_type == 'growth' ? parseInt(d.account_data) : d.account_data);
							} else {
								return d.account_type == "point" || d.account_type == 'growth' ? parseInt(d.account_data) : d.account_data;
							}
						}
					}, {
						field: '',
						title: '',
						width: '5%',
						align:'center',
						unresize: 'false'
					}, {
						field: 'type_name',
						title: '发生方式',
						unresize: 'false'
					}, {
						field: 'remark',
						title: '备注',
						width: '35%',
						unresize: 'false',
						templet: function(data) {
							return  `<a href="javascript:showRemark('${data.remark}');" class="text-color" style="margin-right:5px;">查看</a>`+data.remark;
						}
					}, {
						field: 'create_time',
						title: '发生时间',
						unresize: 'false',
						templet: function(data) {
							return ns.time_to_date(data.create_time);
						}
					}]
				]
			});

			form.on('submit(search_{$table_id})', function(data) {
				table.reload({
					page: {
						curr: 1
					},
					where: data.field
				});
				return false;
			});
		});
	})()
	function showRemark(content) {
		layer.open({
			title: '备注',
			content
		});
	}
</script>