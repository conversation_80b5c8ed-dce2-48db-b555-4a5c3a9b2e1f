<div style="margin: 15px 0;">
	<table id="member_address_list" lay-filter="member_address_list"></table>
</div>
<script>
var table = new Table({
	elem: '#member_address_list',
	filter: "member_address_list",
	url: ns.url("shop/member/addressdetail"),
	where:{ member_id : "{$member_id}" },
	cols: [[{
		field: 'name',
		width: '30%',
		title: '名称',
		unresize : 'true'
	}, {
		field: 'mobile',
		width: '10%',
		title: '手机号',
		unresize : 'true'
	}, {
		field: 'telephone',
		width: '20%',
		title: '联系电话',
		unresize : 'true'
	}, {
		width: '30%',
		title: '地址信息',
		unresize : 'true',
		templet: function (d) {
			return '<span title="'+ d.full_address.replace(/-/g, " ") + " " + d.address+'">'+  d.full_address.replace(/-/g, " ") + " " + d.address +'</span>';
		}
	}, {
		width: '10%',
		title: '是否默认地址',
		unresize : 'true',
		templet: function (d) {
			return d.is_default ? "是" : "否";
		}
	}]],
});
</script>