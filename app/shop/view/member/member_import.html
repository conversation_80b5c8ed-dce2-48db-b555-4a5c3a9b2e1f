

<style>
    .single-filter-box {justify-content: left;line-height: 34px}
    .single-filter-box a{cursor:pointer;margin-left: 10px}
</style>


<div class="single-filter-box">
    <button class="layui-btn" id="member_file">导入会员</button>
    <a class="layui-btn layui-btn-primary" onclick="downloadMemberFile()">点击下载模板</a>
</div>

<!-- 列表 -->
<table id="member_import_log_list" lay-filter="member_import_log_list"></table>
<!-- 工具栏操作 -->
<script type="text/html" id="operation">
    <div class="table-btn">
        <a class="layui-btn" lay-event="info">查看</a>
    </div>
</script>

<script>
    var table,upload;
    layui.use(['form', 'upload'], function() {
        var form = layui.form;
            upload = layui.upload;
        form.render();
        /**
         * 表格加载
         */
        table = new Table({
            elem: '#member_import_log_list',
            url: ns.url("shop/member/memberImport"),
            cols: [
                [{
					field: 'create_time',
					title: '导入时间',
                    width: '20%',
					unresize: 'false',
				}, {
					field: 'member_num',
					title: '导入会员数',
					width: '20%',
					unresize: 'false',
				}, {
					field: 'success_num',
					title: '导入成功会员数',
					width: '20%',
					unresize: 'false',
				}, {
                    field: 'error_num',
                    title: '导入失败会员数',
					width: '15%',
                    unresize: 'false',
                }, 
				// {
    //                 field: 'status_name',
    //                 title: '导入状态',
				// 	width: '15%',
    //                 unresize: 'false',
    //             }, 
				{
                    title: '操作',
                    unresize: 'false',
                    toolbar: '#operation',
					align : 'right'
                }]
            ]
        });
        //允许上传的文件后缀
        upload.render({
            elem: '#member_file'
            ,url: ns.url("shop/member/file"),
            accept:'file',
            acceptMime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            exts: 'xlsx',
            done: function(res){
                if (res.code >= 0) {
                    member_import(1, res.data.name, res.data.path);
                }else{
                    layer.msg(res.message);
                }
            }
        });

        /**
         * 监听工具栏操作
         */
        table.tool(function(obj) {
            var data = obj.data;
            switch (obj.event) {
                case 'info': //查看
                    location.hash = ns.hash("shop/member/memberimportlist?id=" + data.id);
                    break
            }
        });
    });

    function member_import(index, name, path, success_num = 0, error_num = 0, record = 0){
        $.ajax({
            url: ns.url("shop/member/import"),
            data: {
                filename: name,
                path: path,
                index: index,
                success_num : success_num,
                error_num : error_num,
                record : record
            },
            dataType: 'JSON',
            type: 'POST',
            success: function (res) {
                index ++;
                if(res.code == 0){

                    if(res.data.num < res.data.allRow){
                        member_import(index, res.data.name, res.data.path, res.data.success_num, res.data.error_num, res.data.record);
                    }else{
                        table.reload();
                    }
                }
				if(res.data.error_num > 0) {
					layer.msg('导入失败');
				} else {
					layer.msg('导入成功');
				}
            }
        });
    }

    function downloadMemberFile(){
        location.href = ns.url("shop/member/downloadMemberFile",{ request_mode: 'download' });
    }
</script>
