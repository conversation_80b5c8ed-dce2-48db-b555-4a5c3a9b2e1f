<div  class="layui-form form-wrap">
	<div class="layui-form-item">   
		<label class="layui-form-label"><span class="required">*</span>标签名称：</label>
		<div class="layui-input-block">  
			<input type="text" name="label_name" lay-verify="required" value="{$label_info.data.label_name}"  class="layui-input len-long">
		</div>   
	</div>  

	<div class="layui-form-item">  
		<label class="layui-form-label">排序：</label>
		<div class="layui-input-block">
			<input type="number" name="sort" class="layui-input len-short" value="{$label_info.data.sort}" lay-verify="num" >
		</div>
		<div class="word-aux">排序值必须为整数</div>
	</div>  

	<div class="layui-form-item"> 
		<label class="layui-form-label">备注：</label>
		<div class="layui-input-block">
			<textarea name="remark" autocomplete="off" placeholder="" class="layui-textarea len-long" maxlength="150">{$label_info.data.remark}</textarea>
		</div> 
	</div> 

	<!-- 表单操作 -->
	<div class="form-row">
		<button type="button" class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button  class="layui-btn layui-btn-primary" onclick="location.hash= ns.hash('shop/memberlabel/labelList')">返回</button>
	</div>
	
	<input type="hidden" name="label_id" lay-verify="required" value="{$label_info.data.label_id}"  class="layui-input">
 </div>  

<script>
	layui.use('form', function(){
		var form = layui.form;
		var repeat_flag = false; //防重复
		form.render();

		form.on('submit(save)', function(data){

			if(repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url:ns.url("shop/memberlabel/editLabel"),
				data: data.field,
				dataType:'JSON',
				type:'POST',
				success:function(res){
					repeat_flag = false;

					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero) {
								location.hash = ns.hash("shop/memberlabel/labelList")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});

		form.verify({
			num: function (value) {
				if (value == '') {
					return;
				}
				if (value%1 != 0) {
					return '排序数值必须为整数';
				}
				if (value < 0) {
					return '排序数值必须为大于0';
				}
			}
		}); 
	});
</script>
