<style>
	.coupon-box{
		padding: 20px;
	}

	.coupon-box .layui-form{
		padding: 0!important;
	}

	.layui-layer-page .layui-layer-content{
		overflow: auto !important;
	}

	.del-btn {
		cursor: pointer;
	}

	.level-equity .layui-input {
		display: inline-block;
	}

	.gods-box table:first-of-type{
		margin-bottom: 0;
	}
	.gods-box table:last-of-type{
		margin-top: 0;
		display: block;
		max-height: 323px;
		overflow: auto;
	}
	.coupon-box .single-filter-box{
		padding-top: 0;
	}
	.coupon-box td,th{
		padding: 15px 10px !important;
		box-sizing: border-box;
	}
	.gods-box .layui-table#goods tbody{
		width: 100%;
	}
</style>

<div class="layui-form">

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">基础信息</span>
		</div>
		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>等级名称：</label>
				<div class="layui-input-block">
					<input name="level_name" value="{$level_info.level_name}" type="text" lay-verify="required" class="layui-input len-long" maxlength="40">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">等级说明：</label>
				<div class="layui-input-block len-long">
					<textarea name="remark" class="layui-textarea" maxlength="150">{$level_info.remark}</textarea>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">等级封面背景色：</label>
				<div class="layui-input-block flex">
					<input name="bg_color" type="hidden"  value="{$level_info.bg_color}" class="layui-input len-short" id="bg_color_input">
					<div id="bg_color"></div>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">等级文字颜色：</label>
				<div class="layui-input-block ">
					<input name="level_text_color" type="hidden" value="{$level_info.level_text_color}" class="layui-input len-short" id="level_text_color_input">
					<div id="level_text_color"></div>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">背景图：</label>
				<div class="layui-input-block img-upload">
					<div class="upload-img-block">
						<div class="upload-img-box {if condition="!empty($level_info.level_picture)"}hover{/if} ">
							<div class="upload-default" id="imgUpload">
								{if condition="!empty($level_info.level_picture)"}
								<div id="preview_logoUpload" class="preview_img">
									<img layer-src src="{:img($level_info.level_picture)}" class="img_prev"/>
								</div>
								{else/}
								<div class="upload">
									<i class="iconfont iconshangchuan"></i>
									<p>点击上传</p>
								</div>
								{/if}
							</div>

							<div class="operation">
								<div>
									<i title="图片预览" class="iconfont iconreview js-preview" style="margin-right: 20px;"></i>
									<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
								</div>
								<div class="replace_img js-replace">点击替换</div>
							</div>
							<input type="hidden" name="level_picture" value="{$level_info.level_picture}"/>
						</div>
					</div>
				</div>

				<div class="word-aux">
					<p>尺寸：1000*525像素，小于1M，支持jpg、png、jpeg格式，上传背景图后，手机端将不会展示背景颜色，优先展示背景图</p>
				</div>
			</div>

			<div class="gratis-wrap {if $level_info['level_type'] == 1}layui-hide{/if}">
				<div class="layui-form-item">
					<label class="layui-form-label"><span class="required">*</span>所需成长值：</label>
					<div class="layui-input-block">
						<input name="growth" value="{:round($level_info.growth)}" type="number" {if $level_info['level_type'] == 0}lay-verify="required|growth"{/if}  min="0" class="layui-input len-short" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
					</div>
					{if $growth_up > 0 && $growth_down > 0}
					<div class="word-aux">所设成长值需在{:round($growth_up + 1)}~{:round($growth_down - 1)}之间</div>
					{else/}
						{if $growth_up > 0}
						<div class="word-aux">所设成长值需大于{:round($growth_up)}</div>
						{/if}
						{if $growth_down > 0}
						<div class="word-aux">所设成长值需小于{:round($growth_down)}</div>
						{/if}
					{/if}
					<div class="word-aux">修改等级所需成长值后，部分客户会因无法达到该成长值要求而发生会员等级的变化</div>
				</div>
			</div>

		</div>
	</div>

	<div class="layui-card card-common card-brief level-equity">
		<div class="layui-card-header">
			<span class="card-title">权益</span>
		</div>
		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label">是否包邮：</label>
				<div class="layui-input-block">
					<input type="checkbox" name="is_free_shipping" value="1" {if $level_info.is_free_shipping == 1} checked {/if} lay-skin="switch" />
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span class="required">*</span>消费折扣：</label>
				<div class="layui-input-block">
					<input type="number" name="consume_discount" value="{$level_info.consume_discount}" lay-verify="fl" min="0" max="100" autocomplete="off" class="layui-input len-short"> %
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">积分回馈倍率：</label>
				<div class="layui-input-block">
					<input type="number" name="point_feedback" value="{$level_info.point_feedback}" lay-verify="jf" min="0" max="100" autocomplete="off" class="layui-input len-short"> 倍
				</div>
				<div class="word-aux">回馈积分 = 消费金额 * 积分回馈倍率</div>
			</div>
		</div>
	</div>

	<div class="layui-card card-common card-brief">
		<div class="layui-card-header">
			<span class="card-title">等级礼包</span>
		</div>
		<div class="layui-card-body">

			<div class="layui-form-item">
				<label class="layui-form-label">赠送积分：</label>
				<div class="layui-input-block">
					<input name="send_point" value="{$level_info.send_point}" type="number" lay-verify="num" min="0" class="layui-input len-short">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">赠送红包：</label>
				<div class="layui-input-block len-long">
					<input name="send_balance" value="{$level_info.send_balance}" type="number" lay-verify="num" min="0" class="layui-input len-short">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">赠送优惠券：</label>
				<div class="layui-input-block">
					<div id="coupon_list"></div>
					<button class="layui-btn" id="select_coupon">选择优惠券</button>
				</div>
			</div>

		</div>
	</div>
	<input type="hidden" name="level_id" value="{$level_info.level_id}">
	
	<div class="layui-card card-common">
		<div class="layui-card-body">
			<div class="form-row">
				<button class="layui-btn" lay-submit lay-filter="save">保存</button>
				<button class="layui-btn layui-btn-primary" onclick="back()">返回</button>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" src="__STATIC__/ext/colorPicker/js/colorpicker.js"></script>
<script type="text/javascript" src="STATIC_JS/coupon_select.js"></script>
<script>
    var coupon_id = [], addCoupon;
	var form,colorpicker;

	var coupon_select = new CouponSelect({
		tableElem:'#coupon_list',
		selectElem:'#select_coupon',
		selectedIds:'{$level_info.send_coupon}',
	})

	layui.use(['form', 'laytpl', 'colorpicker'], function() {
		form = layui.form;
		colorpicker = layui.colorpicker;
		var laytpl = layui.laytpl,
			repeat_flag = false; //防重复标识
		form.render();

        /*couponId();*/

		var logo_upload = new Upload({
			elem: '#imgUpload'
		});
		colorpicker.render({
			elem: '#bg_color',  //绑定元素
			color: "{$level_info.bg_color}",
			done: function(color) {
				$("#bg_color_input").attr("value", color);
			}
		});

		colorpicker.render({
			elem: '#level_text_color',  //绑定元素
			color: "{$level_info.level_text_color}",
			done: function(color) {
				$("#level_text_color_input").attr("value", color);
			}
		});

		/**
		 * 监听保存
		 */
		form.on('submit(save)', function(data) {
            data.field.send_coupon = coupon_select.getSelectedData().selectedIds.toString();
			data.field.growth = parseInt(data.field.growth);
			if(repeat_flag) return false;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/memberlevel/editLevel"),
				data: data.field,
				type: "POST",
				dataType: "JSON",
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('编辑成功', {
							title:'操作提示',
							btn: ['返回列表', '继续操作'],
							yes: function(index, layero) {
								location.hash = ns.hash("shop/memberlevel/levelList")
								layer.close(index);
							},
							btn2: function(index, layero) {
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		
		/**
		 * 表单验证
		 */
		form.verify({
			num: function(value) {
				var arrMen = value.split(".");
				var val = 0;
				if (arrMen.length == 2) {
					val = arrMen[1];
				}
				
				if (value == "") {
					return false;
				}
				if (value < 0 || val.length > 2) {
					return '请输入大于0的数，保留小数点后两位'
				}
			},
			fl: function(value, item) {
                var str = $(item).parents(".layui-form-item").find("label").text().split("*").join("");
                str = str.substring(0, str.length - 1);

            	if (isNaN(parseFloat(value))) {
                	return  "请设置" + str;
                }

                if (value <= 0) {
                    return str + "不能小于等于0";
                }

                if (value > 100) {
                    return str + "不能大于100";
                }

                var arrMen = value.split(".");
                var val = 0;
                if (arrMen.length == 2) {
                    val = arrMen[1];
                }
                if (val.length > 2) {
                    return str + "最多可保留两位小数";
                }
            },
            jf: function(value, item) {
                var str = $(item).parents(".layui-form-item").find("label").text().split("*").join("");
                str = str.substring(0, str.length - 1);

                if (value < 0) {
                    return str + "不能小于0";
                }

                if (value > 100) {
                    return str + "不能大于100";
                }

                var arrMen = value.split(".");
                var val = 0;
                if (arrMen.length == 2) {
                    val = arrMen[1];
                }
                if (val.length > 2) {
                    return str + "最多可保留两位小数";
                }
            },
			growth: function (value, item) {
				let min = parseInt("{$growth_up}");
				let max = parseInt("{$growth_down}");
				if (isNaN(parseInt(value))) return "请设置成长值";

				if(value > 99999999) return '成长值最大99999999';

				if (min > 0 && max > 0) {
					if (value <= min || value >= max) {
						return '成长值需设置为' + min + '~' + max + '之间的值';
					}
				} else {
					if (value <= min) {
						return '成长值不能小于等于' + min;
					}
					if (max > 0 && value >= max) {
						return '成长值不能大于等于' + max;
					}
				}
			}

		});
	});

    function back(){
		location.hash = ns.hash("shop/memberlevel/levelList");
	}

	//添加付费规则
	function addRule() {
		let level_select = {:json_encode($level_time, JSON_UNESCAPED_UNICODE)};
		$('#level-rule tr').each(function (i, e) {
			if($(e).find('select').val() && $(e).find('select').val() != undefined){
				delete level_select[$(e).find('select').val()];
			}
		});

		var select_html = '';
		for (let i in level_select){
			select_html += '<option value="'+i+'">'+level_select[i]+'</option>';
		}

		var html = `
			<tr>
				<td>
					<div class="len-short">
						<select lay-filter="selectRule">
							${select_html}
						</select>
					</div>
				</td>
				<td class="align-center">
					<input  type="text" value="0" lay-verify="num" class="layui-input len-short price" placeholder="价格/元">
				</td>
				<td>
					<a href="javascript:void(0)" class="text-color" onclick="deleteRule(this)">删除</a>
				</td>
			</tr>
		`;

		$('#level-rule tbody').append(html);
		if($('#level-rule tr').length == 5){
			$('#add_rule_btn').hide()
		}
		form.render();
		form.on('select(selectRule)', function(data){
			let num = 0;
			$('#level-rule tr').each(function (i, e) {
				if($(e).find('select').val() == data.value){
					num++;
					$('.delete-rule').removeClass('delete-rule');
					$(e).find('select').addClass('delete-rule');
				}
			})
			if(num > 1) deleteRule($('.delete-rule'));
		});

		form.verify({
			num: function (value) {
				var arrmen = value.split(".");
				var val = 0;
				if (arrmen.length == 2) {
					val = arrmen[1];
				}

				if (value == "") {
					return false;
				}
				if (value <= 0 || val.length > 2) {
					return '请输入大于0的数，保留小数点后两位'
				}
			}
		})

		return false;
	}

	function deleteRule(obj) {
		$(obj).parents('tr').remove();
		if($('#level-rule tr').length < 5){
			$('#add_rule_btn').show()
		}
	}

</script>
