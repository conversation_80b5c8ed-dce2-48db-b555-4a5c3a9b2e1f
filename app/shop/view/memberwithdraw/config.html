<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label">是否启用提现：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_use" lay-filter="is_use" value="1" lay-skin="switch" {if !empty($config) && $config.is_use==1 } checked {/if} >
		</div>
		<div class="word-aux">会员可以将<span class="text-color">现金余额账户</span>的金额申请提现</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">提现审核：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_auto_audit" lay-filter="is_auto_audit" value="1" lay-skin="switch" {if !empty($config.value) && $config.value.is_auto_audit==1 } checked {/if} >
		</div>
	</div>
	{if $is_exist}
	<div class="layui-form-item">
		<label class="layui-form-label">自动转账：</label>
		<div class="layui-input-block">
			<input type="checkbox" name="is_auto_transfer" lay-filter="is_auto_transfer" value="1" lay-skin="switch" {if !empty($config.value) && $config.value.is_auto_transfer==1 } checked {/if} >
		</div>
		<div class="word-aux">只有微信和支付宝支付支持自动转账</div>
	</div>
	{/if}
	<div class="layui-form-item">
		<label class="layui-form-label">提现手续费比率：</label>
		<div class="layui-input-block">
		<div class="layui-input-inline">
			<input type="number" name="rate" value="{if condition="!empty($config.value)"}{$config.value.rate}{else/}0{/if}" lay-verify="positivEinteger" autocomplete="off" class="layui-input len-short">
		</div>
			<span class="layui-form-mid">%</span>
		</div>
		<div class="word-aux">比率必须为0-100的整数</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">最低提现金额：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				<input type="number" name="min" id="min" value="{if condition="!empty($config.value)"}{$config.value.min ?: 0}{else/}0{/if}" lay-verify="growthMinInteger" autocomplete="off" class="layui-input len-short">
			</div>
		</div>
		<div class="word-aux">最低提现金额必须大于0</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">最高提现额度：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				<input type="number" name="max"  value="{if condition="!empty($config.value)"}{$config.value.max ?: 0}{else/}0{/if}" lay-verify="growthMaxInteger" autocomplete="off" class="layui-input len-short">
			</div>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">转账方式：</label>
		<div class="layui-input-block">
			<div class="layui-input-inline">
				{foreach $transfer_type_list as $k => $v}
					<input type="checkbox"  lay-filter="transfer_type" name="transfer_type[]" title="{$v}" lay-skin="primary" value="{$k}" {if !empty($config['value']) && stripos($config['value']['transfer_type'], $k) !== false}checked{/if}>
				{/foreach}

			</div>
		</div>
	</div>
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
	</div>
</div>

<script >
	layui.use('form', function(){
		var form = layui.form,
			repeat_flag = false; //防重复标识
		form.render();

		form.on('submit(save)', function(data){
			if (repeat_flag) return;
			repeat_flag = true;
			
			var transfer_type =$("input[lay-skin=primary]:checked()");
			if(transfer_type.length <= 0){
				repeat_flag = false;
				layer.msg("转账类型至少选择一种！");
				return
			}
			$.ajax({
				type: 'POST',
				dataType: 'JSON',
				url: ns.url("shop/memberwithdraw/config"),
				data: data.field,
				success: function(res) {
			        repeat_flag = false;
					if (res.code == 0) {
						layer.msg(res.message);
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
		
		
		// 验证正整数
		form.verify({
			positivEinteger: function(value){
				if(!new RegExp("^(\\d|[1-9]\\d|100)$").test(value)){
					return '请输入0-100之间的正整数';
				}
			},
			growthMinInteger: function (value) {
				if(value <= 0){
					return '最低提现金额必须大于0';
				}
				if (value.split(".").length > 1) {
					let len = value.split(".")[1].length;
					if (len > 2) {
						return '最低提现金额最多保留两位小数';
					}
				}
			},

			growthMaxInteger: function (value) {
				var min = $('#min').val();
				if(!new RegExp("(^[1-9]\\d*$)").test(value)){
					return '请输入正整数';
				}

				if(parseInt(value) < parseInt(min)){
					return '不能小于最低提现额度';
				}
			},
		});
	});

</script>
