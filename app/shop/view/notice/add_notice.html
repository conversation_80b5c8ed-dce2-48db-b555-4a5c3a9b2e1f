<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>公告标题：</label>
		<div class="layui-input-block">
			<input type="text" name="title" lay-verify="required" placeholder="请输入公告标题" class="layui-input len-long" maxlength="30">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">是否置顶：</label>
		<div class="layui-input-block">
			<input id="is_top" type="checkbox" name="is_top" lay-skin="switch" value="0" lay-filter="isTop">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>公告内容：</label>
		<div class="layui-input-block special-length">
			<script id="container" name="content" type="text/plain" style="width: 800px; height: 300px;"></script>
		</div>
	</div>
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backNoticeIndex()">返回</button>
	</div>
</div>

<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.config.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/ueditor.all.js?time=20240614"></script>
<script type="text/javascript" charset="utf-8" src="STATIC_EXT/ueditor/lang/zh-cn/zh-cn.js"></script>
<script type="text/javascript">
	//实例化富文本
	var ue = UE.getEditor('container');
	layui.use('form', function() {
		var form = layui.form;
		var repeat_flag = false; //防重复标识
		form.render();

		form.on('switch(isTop)', function(data) {
			if (data.elem.checked) {
				$("#is_top").val(1);
			} else {
				$("#is_top").val(0);
			}
		});

		form.on('submit(save)', function(data) {
			var html;
			ue.ready(function() {   //对编辑器的操作最好在编辑器ready之后再做
				html = ue.getContent();   //获取html内容，返回: <p>hello</p>
			});
			data.field.content = html;
			if (data.field.content == "") {
				layer.msg('请输入公告内容', {
					icon: 5
				});
				return;
			}
			
			data.field.is_top = $("#is_top").val();
			
			if (repeat_flag) return;
			repeat_flag = true;
			
			$.ajax({
				url: ns.url("shop/notice/addNotice"),
				data: data.field,
				type: "POST",
				dataType: "JSON",
				success: function(res) {
					repeat_flag = false;
					if (res.code == 0) {
						layer.confirm('添加成功', {
							title:'操作提示',
							btn: ['返回列表', '继续添加'],
							closeBtn: 0,
							yes: function(index, layero) {
								location.hash = ns.hash("shop/notice/index")
								layer.close(index);
							},
							btn2: function(index, layero) {
								listenerHash(); // 刷新页面
								layer.close(index);
							}
						});
					}else{
						layer.msg(res.message);
					}
				}
			});
		});
	});
	
	function backNoticeIndex() {
		location.hash = ns.hash("shop/notice/index");
	}
</script>
