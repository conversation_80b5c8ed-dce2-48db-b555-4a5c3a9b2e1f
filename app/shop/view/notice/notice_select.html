<style>
	.notice-list {
		padding: 0 20px;
	}
</style>

<div class="notice-list">
	<!-- 列表 -->
	<table id="notice_list" lay-filter="notice_list"></table>
</div>

<script type="text/html" id="checkbox">
	{{# if($.inArray(d.id.toString(), selected_id_arr) != -1){ }}
	<input type="checkbox" data-notice-id="{{d.id}}" name="notice_checkbox" lay-skin="primary" lay-filter="notice_checkbox" checked>
	{{# }else{ }}
	<input type="checkbox" data-notice-id="{{d.id}}" name="notice_checkbox" lay-skin="primary" lay-filter="notice_checkbox">
	{{# } }}
	<input type="hidden" data-notice-id="{{d.id}}" name="notice_json" value='{{ JSON.stringify(d) }}' />
</script>

<script>
	var table, form, laytpl,
		select_id = "{$select_id}", //选中商品id
		selected_id_arr = select_id.length ? select_id.split(',') : [],
		select_list = []; //选中商品所有数据

	$(function () {
		layui.use(['form', 'laytpl'], function () {
			form = layui.form;
			laytpl = layui.laytpl;

			table = new Table({
				elem: '#notice_list',
				url: ns.url("shop/notice/index"),
				cols: [
					[
						{
							unresize: 'false',
							width: '10%',
							templet: '#checkbox'
						}, {
							width: '55%',
							title: '公告标题',
							unresize: 'false',
							templet: function(data) {
								var html = data.is_top ? '<span class="required">[ 置顶 ] </span>' : '';
								html += data.title;
								return html;
							}
						}, {
							width: '35%',
							title: '创建时间',
							unresize: 'false',
							templet: function(data) {
								return ns.time_to_date(data.create_time);
							}
						}
					]
				],
				callback : function () {
					// 更新商品复选框状态
					for (var i=0;i<selected_id_arr.length;i++) {
						var selected_notices = $("input[name='notice_checkbox'][data-notice-id='" + selected_id_arr[i] + "']");
						
						if (selected_notices.length) {
							$("input[name='notice_checkbox'][data-notice-id='" + selected_id_arr[i] + "']").prop("checked", true);
						}
					}
					
					form.render();
					initData();
				}

			});

			// 勾选商品
			form.on('checkbox(notice_checkbox)', function(data) {
				var notice_id = $(data.elem).attr("data-notice-id"), json = {};
				form.render();
				
				var noticeLen = $("input[name='notice_checkbox'][data-notice-id="+ notice_id +"]:checked").length;
				if (noticeLen){
					json = JSON.parse($("input[name='notice_json'][data-notice-id="+ notice_id +"]").val());
					delete json.LAY_INDEX;
					delete json.LAY_TABLE_INDEX;
					delete json.create_time;
					select_list.push(json);
				} else{
					for (var i = 0; i < select_list.length; i++) {
						if (select_list[i].id == notice_id) {
							select_list.splice(i, 1);
							break;
						}
					}
				}
				$.unique(select_list);
			});

			//初始化数据
			function initData(){
				var noticeLen = $("input[name='notice_checkbox'][data-notice-id]:checked").length;
				
				for (var i = 0; i < noticeLen; i++){
					var noticeId = $("input[name='notice_checkbox'][data-notice-id]:checked").eq(i).attr("data-notice-id");
					var ident = false;
					for (var k = 0; k < select_list.length; k++){
						if(select_list[k].id == noticeId){
							ident = true;
							break;
						}
					}

					if (ident) return;
					json = JSON.parse($("input[name='notice_json'][data-notice-id="+ noticeId +"]").val());
					delete json.LAY_INDEX;
					delete json.LAY_TABLE_INDEX;
					delete json.create_time;
					
					select_list.push(json);
				}
			}
		});
	});

	function selectNoticeListener(callback) {
		var res = select_list;
		callback(res);
	}
</script>
