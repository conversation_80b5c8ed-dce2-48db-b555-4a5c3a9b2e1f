<style>
	.item-block-parent .item-poa-pic {background-image:linear-gradient(to right,var(--base-color),var(--base-color));text-align:center;color:#FFFFFF;width:70px;height:30px;line-height:30px;border-bottom-left-radius:3px;}
	.item-block .item-hide {position:absolute;top:0;right:-1px;}
	.item-block-parent .item-block {background: #fff;border: 1px solid #eee;border-radius: 2px;}
	.item-block-parent .item-block:hover {background: #fff}
	.common-addon {position: absolute;right: 10px;top: 10px;font-size: 12px;cursor: pointer;display: none}
	.item-block-parent .item-block:hover .common-addon {display: block;}
</style>

<div class="layui-card card-common card-brief" id="promotion">
	<div class="layui-card-header ">
		<span class="card-title">应用工具</span>
	</div>

	<div class="layui-card-body">
		<div class="item-block-parent item-five">
			{foreach $promotion as $list_k => $list_v}
			{if condition="$list_v['show_type'] eq 'tool'"}
			{empty name="$list_v['is_developing']"}
			<a class="item-block item-block-hover-a" href="javascript:;" onclick="location.hash = ns.hash('{$list_v[\'url\']}')">
				<div class="item-block-wrap">
					<div class="item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="item-con">
						<div class="item-content-title">{$list_v.title}</div>
						<p class="item-content-desc line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
				</div>
				{if in_array($list_v.name, $common_addon)}
				<span class="iconfont iconshixian common-addon" data-addon="{$list_v.name}" title="取消快捷方式"></span>
				{else/}
				<span class="iconfont iconadd_light common-addon" data-addon="{$list_v.name}" title="添加快捷方式"></span>
				{/if}
			</a>
			{else/}
			<a class="item-block item-block-hover-a" href="javascript:;">
				<div class="item-block-wrap">
					<div class="item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="item-con">
						<div class="item-content-title">{$list_v.title}</div>
						<p class="item-content-desc line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
					<div class="item-poa-pic">敬请期待</div>
				</div>
			</a>
			{/empty}
			{/if}
			{/foreach}
			{if condition="$user_info['is_admin'] eq '1'"}
			{foreach $tool_addon as $tool_addon_k => $tool_addon_v}

			<div class="item-block item-block-hover">
				<div class="item-block-wrap">
					<div class="item-pic">
						<img src="{$tool_addon_v.logo_img}" />
					</div>
					<div class="item-con">
						<div class="item-content-title">{$tool_addon_v.goods_name}</div>
						<p class="item-content-desc line-hiding" title="{$tool_addon_v.package_name}">{$tool_addon_v.introduction}</p>
					</div>
					<img class="item-hide" src="SHOP_IMG/recommend.png" alt="">
				</div>
				<div class="item-float-wrap ">
					<div class="item-float">
						<div class="item-float-con"></div>
						<div class="item-float-con now_btn" >
							<a onclick="alertAddon()" target="_blank">立即升级</a>
						</div>
					</div>
				</div>
			</div>
			{/foreach}
			{/if}
		</div>
	</div>
</div>

<script >
	$('.now_btn').click(function() {
		layer.confirm('当前插件可在官网订购', {
			title: "插件提示",
			btn: ['去订购', '取消'] //按钮
		}, function () {
			window.location.href = 'https://www.niushop.com/web/addon/lists.html?product_key=B2C_V4'
		});
	})

	$('.common-addon').click(function (event) {
		event.stopPropagation();

		$.ajax({
			dataType: 'JSON',
			type: 'POST',
			data: {
				addon: $(this).attr('data-addon'),
				type: 'tool'
			},
			url: ns.url("shop/promotion/commonAddonSetting"),
			success: function(res) {
				if (res.code == 0) {
					layer.msg(res.message)
					listenerHash(); // 刷新页面
				}
			}
		})
	})
</script>