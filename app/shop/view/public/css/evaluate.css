.evaluate-table {
	border-width: 0 !important;
}

.evaluate-table[lay-size=lg] th,
.evaluate-table[lay-size=lg] td {
	padding: 13px 15px;
}
.evaluate-table[lay-size=lg] .operate{
	text-align: right;
}
.evaluate-title {
	display: flex;
}

.evaluate-title p {
	margin-right: 30px;
}

.evaluate-title p:last-child {
	margin-right: 0;
}

/* 评价等级 */
.evaluate-box {
	position: relative;
	padding-left: 70px;
}

.evaluate-title img {
	margin-right: 5px;
}

.evaluate-box p {
	line-height: 24px;
}

.evaluate-level-good span {
	color: #FFCA10;
}

.evaluate-level-middel span {
	color: var(--base-color);
}

.evaluate-level-bad span {
	color: var(--base-color);
}


/* 追加评价 */
.evaluate-again {
	margin-top: 10px;
}
.font-box {
	line-height: 25px;
	text-align: left;
}

/* 分页 */
#layui-laypage-1{
	float: right;
}
#laypage {
	overflow: hidden;
}

.evaluate-explain, .evaluate-box, .evaluate-again {
	overflow: hidden;
}

.evaluate-img .title-pic {
	margin-right: 5px;
	margin-bottom: 5px;
	display: inline-block;
	width: 50px;
	height: 50px;
	text-align: center;
	line-height: 50px;
}

.evaluate-img .title-pic img {
	max-width: 100%;
	max-height: 100%;
}

.evaluate p, .evaluate-explain p, .evaluate-again p, .evaluate-again-explain p {
	width: 100%;
	height: auto;
	word-wrap: break-word;
	word-break: break-all;
}

.evaluate-img p{height:30px; line-height: 30px}
.evaluate-img p img{margin-right: 8px}

.evaluate-img{padding: 5px 0px}
.evaluate-explain{font-size: 12px;}
.evaluate-explain .again-evaluate{margin-left: 8px}
.required{color:var(--base-color)}
.order-list-top-line{justify-content:flex-end;}
.order-list-top-line a{padding-right: 0px}
.layui-table{margin: 15px 0;}
.layui-inline{margin-bottom: 15px !important;}
.tab-bottom{display: flex;justify-content: space-between;}
.evaluate-table tr td:nth-child(5),.evaluate-table tr td:nth-child(6){padding-right: 0px;}
.evaluate-table tr td:nth-child(6){padding-left: 0px;}
.layui-layout-admin .layui-form-item .layui-input-inline{background-color: #fff;}
.layui-layout-admin .table-bottom{padding: 15px 0 10px;}
.title-content .sku-name {overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;}