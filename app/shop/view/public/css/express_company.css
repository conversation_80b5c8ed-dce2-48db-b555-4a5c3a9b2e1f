.layui-field-box {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	padding: 10px !important;
}

.express-company-block {
	width: 190px;
	height: 135px;
	border-width: 1px;
	border-style: solid;
	border-radius: 3px;
	padding: 9px;
	margin-right: 10px;
	margin-top: 10px;
	box-sizing: border-box;
	position: relative;
}

.express-company-top {
	width: 170px;
	height: 85px;
	text-align: center;
	line-height: 85px;
}

.express-company-top img {
	max-width: 100%;
	max-height: 100%;
}

.express-company-bottom {
	height: 30px;
	display: flex;
	justify-content: space-between;
	font-size: 12px;
}

.express-company-name {
	text-align: center;
	line-height: 30px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.express-company-operation {
	margin-left: 10px;
	flex-shrink: 0;
}

.express-company-operation .layui-btn {
	background-color: transparent;
	padding: 0;
	line-height: 30px;
	font-size: 12px;
}

/* 公共平台 */
.express-company-box {
	width: 100%;
	height: 30px;
	text-align: center;
}

/* 遮罩 */
.express-company-shade {
	display: none;
	width: 190px;
	height: 135px;
	border-radius: 3px;
	padding: 10px;
	box-sizing: border-box;
	background-color: rgba(0, 0, 0, .5);
	position: absolute;
	top: 0;
	left: 0;
	text-align: center;
}

.express-company-img {
	width: 100%;
	height: 85px;
}

.express-company-img i {
	font-size: 50px;
	line-height: 85px;
	color: #FFFFFF;
	cursor: pointer;
}

.express-company-name-box {
	height: 30px;
}

.express-company-name-box .express-company-name {
	color: #FFFFFF;
}