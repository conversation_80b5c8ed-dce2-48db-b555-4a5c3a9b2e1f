#page {
	text-align: right;
	padding: 10px;
}

.custom-panel {
	background: #fff;
	padding: 10px 20px;
}

.custom-panel .custom-panel-title {
	display: flex;
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid #f5f5f5;
}

.custom-panel .custom-panel-title .panel-pic {
	overflow: hidden;
	width: 80px;
	height: 80px;
	line-height: 80px;
	text-align: center;
	border-radius: 50%;
	margin-left: 15px;
}

.custom-panel .custom-panel-title .panel-pic img {
	max-width: 100%;
	max-height: 100%;
}

.custom-panel .custom-panel-title .panel-content {
	align-self: center;
	width: calc(100% - 130px);
}

.custom-panel .custom-panel-title .panel-content li {
	display: flex;
	margin-bottom: 5px;
	height: 20px;
	line-height: 20px;
}

.custom-panel .custom-panel-title .panel-content li:first-of-type {
	margin-top: 5px;
}

.custom-panel .custom-panel-title .panel-content li div {
	width: 33.3%;
}

.custom-panel .custom-panel-title .panel-content li span {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #333;
}

.custom-panel .custom-panel-title .panel-content li span:first-of-type {
	width: 90px;
}

.custom-panel .custom-panel-title .panel-operation {
	align-self: center;
	margin-left: auto;
	font-size: 14px;
}

.custom-panel .custom-panel-from {
	display: flex;
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid #f5f5f5;
}

.custom-panel .custom-panel-content {
	display: flex;
	padding-top: 10px;
	padding-bottom: 10px;
	border-bottom: 1px solid #f5f5f5;
}

.custom-panel .custom-panel-content .panel-content {
	width: calc(100% - 30px);
}

.custom-panel .custom-panel-content .panel-content li {
	display: flex;
	margin-bottom: 5px;
	height: 20px;
	line-height: 20px;
}

.custom-panel .custom-panel-content .panel-content li:first-of-type {
	margin-top: 5px;
}

.custom-panel .custom-panel-content .panel-content li div {
	width: 25%;
}

.custom-panel .custom-panel-content .panel-content li span {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #333;
}

.custom-panel .custom-panel-content .panel-content li span:first-of-type {
	width: 90px;
}

.custom-panel .custom-panel-content .panel-operation {
	align-self: center;
	margin-left: auto;
	font-size: 14px;
}

.attribute-value-list {
	margin-bottom: 10px;
}

.attribute-value-list .table-wrap {
	margin-bottom: 10px;
}

.attribute-value-list .layui-table {
	margin-bottom: 0;
}

.attribute-value-list .layui-table:first-child th {
	border-bottom: 0;
}

.attribute-value-list .layui-table:last-child {
	margin-top: 0;
}

.custom-panel .custom-panel-content.attribute {
	display: block
}

.goods-type-edit {
	text-align: center;
}