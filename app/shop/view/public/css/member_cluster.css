.layui-form-search {
	width: 100%;
	background-color: #F8F8F8;
	padding: 15px;
	border: 1px solid #e6e6e6;
	border-radius: 5px;
	box-sizing: border-box;
	margin-bottom: 12px;
}

.search-form {
	display: none;
}

.layui-form-search .layui-btn-container {
	padding-left: 155px;
}

.layui-form-search .layui-form-item {
	margin-bottom: 25px;
}

.required {
	margin-right: 3px;
}

.layui-form-item>p {
	line-height: 34px;
	color: #999;
}

.layui-form-radio>i:hover,
.layui-form-radioed>i {
	color: var(--base-color);
}


.layui-layer-content {
	height: auto !important;
}


.layui-input-inline.split {
	line-height: 34px;
}

.layui-table-view .layui-table[lay-size=lg] td .layui-table-cell {
	line-height: 25px;
}

#member_label_dl {
	display: flex;
	flex-wrap: wrap;
}

#member_label_dl span {
	margin: 3px 5px 3px 0;
	background-color: var(--base-color);
	color: white;
	padding: 0 5px;
	border-radius: 5px;
}

.table-tuwen-box .img-box {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	overflow: hidden;
}

.layui-table-view td:last-child>div {
	overflow: inherit;
}

.layui-table-box {
	overflow: inherit;
}

.layui-table-body {
	overflow: inherit;
}

.table-btn {
	position: relative;
}

.table-btn .more-operation {
	display: none;
	font-size: 14px;
	line-height: 20px;
	background-color: #fff;
	border-radius: 2px;
	box-shadow: 0 2px 8px 0 rgba(200, 201, 204, .5);
	position: absolute;
	z-index: 2000;
	border-radius: 2px;
	padding: 13px 12px;
	top: 40px;
	transform: translateX(10px);
	right: 0px;
}

.table-btn .more-operation:before {
	left: 50px;
	top: -14px;
	border: solid transparent;
	content: "";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: transparent;
	border-bottom-color: #fff;
	border-width: 8px;
}

.table-btn .more-operation .operation {
	display: block;
	text-align: right;
	margin-bottom: 12px;
	cursor: pointer;
}

.table-btn .more-operation .operation:last-child {
	margin-bottom: 0
}

.coupon-modal {
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 25px 0;
}

.coupon-modal .coupon-list {
	width: 255px;
	height: 375px;
	align-items: center;
	border: 1px solid #ccc;
}

.coupon-modal .title {
	height: 35px;
	line-height: 35px;
	text-align: center;
}

.bg-color-gray {
	background-color: #EFEFEF !important;
}

.coupon-modal .box {
	overflow-y: auto;
	padding: 10px 0;
	height: 340px;
	box-sizing: border-box;
	overflow-x: hidden;
}

.coupon-modal .add {
	background-color: transparent;
	border: 1px solid #ccc;
	padding: 5px 10px;
	cursor: pointer;
}

.modal-operation {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 50px;
}

.coupon-list .box ul li {
	cursor: pointer;
	background-color: #fff;
	padding: 5px 10px;
	position: relative;
}

.coupon-list.all-coupon .box ul li .left-selected {
	background: #d7d7d7;
}

.coupon-list .coupon-delete {
	position: absolute;
	top: -2px;
	right: 4px;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background-color: #d7d7d7;
	color: #fff;
	line-height: 15px;
	text-align: center;
	cursor: pointer;
}

.coupon-list .box ul li.selected:hover .coupon-delete {
	display: block;
}

.coupon-list .coupon-box {
	border: 1px dashed #DDDDDD;
	padding: 5px 15px;
}

.coupon-list .coupon-box .coupon-name {
	font-size: 14px;
	line-height: 30px;
}

.coupon-list .coupon-box .coupon-money {
	font-size: 20px;
	color: red;
}

.coupon-list .coupon-box .coupon-time {
	font-size: 12px;
	color: #999999;
	line-height: 14px;
}

.give-num {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.give-num span {
	font-size: 12px;
	color: #999;
}

.give-num .layui-input {
	margin-left: 5px;
	width: 60px!important;
	height: 25px;
	text-align: center;
}