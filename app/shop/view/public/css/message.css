.layui-field-box {
	display: flex;
	flex-wrap: wrap;
}

/* 一行4个卡片时的排布 */
@media screen and (min-width: 1501px) {
	.block-list {
		width: 24%;
		margin-right: 1.3%;
		margin-bottom: 25px;
	}
	
	.block-list:nth-child(4n) {
		margin-right: 0;
	}
}

/* 一行3个卡片时的排布 */
@media screen and (max-width: 1500px) {
	.block-list {
		width: 32%;
		margin-right: 2%;
		margin-bottom: 25px;
	}
  
	.block-list:nth-child(3n) {
		margin-right: 0;
	} 
}

.block-list .block-title {
	height: 52px;
	line-height: 52px;
	font-size: 14px;
	color: #333;
	background-color: #F7F8FA;
	padding-left: 25px;
}

.block-list .block-content {
	padding: 20px 25px 0;
	display: flex;
	flex-wrap: wrap;
}

.block-list .block-content a {
	height: 25px;
	line-height: 25px;
	width: 33.3%;
	margin-bottom: 20px;
	display: inline-block;
	vertical-align: middle;
	color: #454545;
	font-size: 12px;
	position: relative;
}

.layui-form-checkbox[lay-skin=primary] {
	padding-left: 18px;
}

.layui-form-checkbox[lay-skin=primary] i {
	width: 11px;
	height: 11px;
	line-height: 12px;
	border-radius: 0px;
	margin-top: 2px;
	border: 1px solid #787878;
}

.layui-icon-ok:before {
	display: inline-block;
	width: 100%;
	-webkit-transform: scale(0.80);
}

.layui-form-checkbox[lay-skin=primary] span {
	font-size: 12px;
	color: #454545;
	padding-right: 0;
}

.block-list .block-content a:hover .sms-hide {
	display: inline-block;
}

.block-list .sms-hide {
	display: none;
	position: absolute;
	width: 340px;
	top: 45px;
	left: 0;
	background-color: #fff;
	z-index: 2;
	border: 1px solid #e5e5e5;
	padding: 5px 10px;
	margin-bottom: 30px;
}

.block-list .sms-hide p {
	width: 100%;
	word-break: break-all;
	white-space: normal;
}

.block-list .sms-hide:before, .block-list .sms-hide:after {
	content: '';
	display: inline-block;
	border: solid transparent;
	width: 0;
	height: 0;
	border-width: 12px;
	left: 16px;
	position: absolute;
}

.block-list .sms-hide:before {
	border-bottom-color: #e4e4e4;
	top: -25px;
}

.block-list .sms-hide:after {
	border-bottom-color: #FFFFFF;
	top: -23px;
}