/* 商城列表 */
.store-list {
	width: 100%;
	padding: 25px;
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;
}

.store-li {
	display: inline-block;
	width: calc(25% - 19px);
	margin-right: 25px;
	margin-bottom: 25px;
	border: 1px solid #EAEAEA;
	border-bottom-color: #EAEAEA !important;
	border-left-color: #EAEAEA !important;
	border-right-color: #EAEAEA !important;
	box-sizing: border-box;
	border-radius: 2px;
	padding: 20px 15px 10px 20px;
	position: relative;
	cursor: pointer;
	border-top-width: 3px;
	transition: all .3s;
}
.store-li:hover{
	border-right-color: transparent !important;
	border-left-color: transparent !important;
	border-bottom-color: transparent !important;
	box-shadow: 0 5px 10px rgb(from var(--base-color) r g b / 20%);
}

.store-li:nth-child(4n) {
	margin-right: 0;
}

.store-name {
	font-size: 14px;
	line-height: 20px;
	margin-bottom: 15px;
	position: relative;
	font-weight :600;
	color: #333333;
}
.store-name .store-name{
	display:inline-block;
	white-space: nowrap;
	width: 80%;
	overflow: hidden;
	text-overflow:ellipsis;
}
.store-default{
	color: var(--base-color);
	background: #fff;
	border: 1px solid var(--base-color);
	font-weight: 500;
	padding: 1px 5px;
	border-radius: 2px;
	margin-right: 5px;
}
.store-time {
	font-size: 14px;
	color: #999999;
	line-height: 20px;
	white-space: nowrap;
	width: 100%;
	overflow: hidden;
	text-overflow:ellipsis;
}

.store-operation {
	position: relative;
	color: #7E7E7E;
	height: 30px;
	line-height: 30px;
	overflow: hidden;
	margin-top: 15px;
}
.store-operation a {
	margin: 0 3px;
	color: #7E7E7E;
}

.store-operation button {
	font-size: 14px;
	height: 24px;
	line-height: 23px;
	padding-left: 6px;
	padding-right: 6px;
	margin-left: 6px;
	position: absolute;
	top: 3px;
	right: 0px;
}

/* 分页 */
#list_page {
	padding-right: 25px;
	text-align: right;
}

.store-list .empty{
	font-size: 18px;
	color: #999;
	text-align: center;
	margin: 100px auto;
}
.edit-password{
	color: var(--base-color);
	margin-left: 15px;
}
.type-name{
	position: absolute;
	right: 0px;
	font-size: 12px;
	border: 1px solid #08ba06;
	color: #08ba06;
	padding: 0px 8px;
	border-radius: 2px;
}
.type-name-cloe{
	position: absolute;
	right: 0px;
	font-size: 12px;
	border: 1px solid #dddddd;
	color: #999999;
	padding: 0px 8px;
	border-radius: 2px;
}
.operation-type{
	color: var(--base-color);
	font-size: 14px;
	margin-right: 20px;
	display: none;
}
.store-li:hover .store-time{
	color: #666666 !important;
}
.store-li:hover .operation-type{
	display: inline-block !important;
}
.layui-tab-title{
	margin-bottom: 20px;
}