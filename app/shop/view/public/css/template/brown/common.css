@charset "UTF-8";
/**
 Sass函数--颜色函数--HSL函数：https://www.sass.hk/skill/sass25.html
 Scss转换：https://www.sassmeister.com
*/
/* 文字基本颜色 */
:root {
  --base-color: #CFAF70;
}

.draggable-element.selected {
  box-shadow: 0 0 5px #f5efe3;
}

.component-list ul li:not(.disabled):hover {
  background: white;
  color: #dcc496 !important;
}
.component-list ul li:not(.disabled):hover i {
  color: #dcc496 !important;
}
.component-list ul li:not(.disabled):hover span:last-child {
  color: #dcc496;
}

.border-color-light-9 {
  border-color: #f5efe3 !important;
}

.bg-color-light-9 {
  background: #f5efe3 !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-item a:hover {
  background-color: rgba(207, 175, 112, 0.08) !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-tree > .layui-this > .second-selected-nav {
  background-color: rgba(207, 175, 112, 0.08) !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-child dd.layui-this .layui-left-nav {
  background-color: rgba(207, 175, 112, 0.08);
}