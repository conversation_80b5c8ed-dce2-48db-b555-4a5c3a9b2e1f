@charset "UTF-8";
/**
 Sass函数--颜色函数--HSL函数：https://www.sass.hk/skill/sass25.html
 Scss转换：https://www.sassmeister.com
*/
/* 文字基本颜色 */
:root {
  --base-color: #19C650;
}

.draggable-element.selected {
  box-shadow: 0 0 5px #88f0a9;
}

.component-list ul li:not(.disabled):hover {
  background: #b5f6ca;
  color: #2de568 !important;
}
.component-list ul li:not(.disabled):hover i {
  color: #2de568 !important;
}
.component-list ul li:not(.disabled):hover span:last-child {
  color: #2de568;
}

.border-color-light-9 {
  border-color: #b5f6ca !important;
}

.bg-color-light-9 {
  background: #b5f6ca !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-item a:hover {
  background-color: rgba(25, 198, 80, 0.08) !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-tree > .layui-this > .second-selected-nav {
  background-color: rgba(25, 198, 80, 0.08) !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-child dd.layui-this .layui-left-nav {
  background-color: rgba(25, 198, 80, 0.08);
}