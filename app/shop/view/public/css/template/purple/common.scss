/**
 Sass函数--颜色函数--HSL函数：https://www.sass.hk/skill/sass25.html
Scss转换：https://www.sassmeister.com
*/

/* 文字基本颜色 */
$base-color: #A253FF; //主色调
:root {
   --base-color: #A253FF;
}

$alpha: 0.08;
$base-color-alpha-8: rgba(red($base-color), green($base-color), blue($base-color), $alpha);

.draggable-element.selected {
  box-shadow: 0 0 5px lighten($base-color, 30%);
}

.component-list ul li:not(.disabled):hover {
  background: lighten($base-color, 40%);
  color: lighten($base-color, 10%) !important;

  i {
    color: lighten($base-color, 10%) !important;
  }

  span:last-child {
    color: lighten($base-color, 10%);
  }
}

.border-color-light-9{
  border-color:  lighten($base-color, 40%) !important;
}

.bg-color-light-9{
  background: lighten($base-color, 30%) !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-item a:hover {
  background-color: $base-color-alpha-8 !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-tree > .layui-this > .second-selected-nav {
  background-color: $base-color-alpha-8 !important;
}

.layui-layout-admin .second-nav .layui-side-scroll .layui-nav-child dd.layui-this .layui-left-nav {
  background-color: $base-color-alpha-8;
}