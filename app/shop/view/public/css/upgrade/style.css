body {
    font: 13px/1.4 'Helvetica Neue', 'Helvetica','Arial', sans-serif;
    color: #333;
}

.container {
    width: 520px;
    margin: auto;
}

h1 {
    border-bottom: 1px solid #d9d9d9;
}

.chart {
    float: left;
    margin: 10px;
}

.percentage,
.label {
    text-align: center;
    color: #333;
    font-weight: 100;
    font-size: 1.2em;
    margin-bottom: 0.3em;
}

.credits {
    padding-top: 0.5em;
    clear: both;
    color: #999;
}

.credits a {
    color: #333;
}

.dark {
    background: #333;
}

.dark .percentage-light,
.dark .label {
    text-align: center;
    color: #999;
    font-weight: 100;
    font-size: 1.2em;
    margin-bottom: 0.3em;
}


.button {
  -webkit-box-shadow: inset 0 0 1px #000, inset 0 1px 0 1px rgba(255,255,255,0.2), 0 1px 1px -1px rgba(0, 0, 0, .5);
  -moz-box-shadow: inset 0 0 1px #000, inset 0 1px 0 1px rgba(255,255,255,0.2), 0 1px 1px -1px rgba(0, 0, 0, .5);
  box-shadow: inset 0 0 1px #000, inset 0 1px 0 1px rgba(255,255,255,0.2), 0 1px 1px -1px rgba(0, 0, 0, .5);
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  padding: 6px 20px;
  font-weight: bold;
  text-transform: uppercase;
  display: block;
  margin: auto;
  max-width: 200px;
  text-align: center;
  background-color: #5c5c5c;
  background-image: -moz-linear-gradient(top, #666666, #4d4d4d);
  background-image: -ms-linear-gradient(top, #666666, #4d4d4d);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#666666), to(#4d4d4d));
  background-image: -webkit-linear-gradient(top, #666666, #4d4d4d);
  background-image: -o-linear-gradient(top, #666666, #4d4d4d);
  background-image: linear-gradient(top, #666666, #4d4d4d);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#666666', endColorstr='#4d4d4d', GradientType=0);
  color: #ffffff;
  text-shadow: 0 1px 1px #333333;
}
.button:hover {
  color: #ffffff;
  text-decoration: none;
  background-color: #616161;
  background-image: -moz-linear-gradient(top, #6b6b6b, #525252);
  background-image: -ms-linear-gradient(top, #6b6b6b, #525252);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#6b6b6b), to(#525252));
  background-image: -webkit-linear-gradient(top, #6b6b6b, #525252);
  background-image: -o-linear-gradient(top, #6b6b6b, #525252);
  background-image: linear-gradient(top, #6b6b6b, #525252);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#6b6b6b', endColorstr='#525252', GradientType=0);
}
.button:active {
  background-color: #575757;
  background-image: -moz-linear-gradient(top, #616161, #474747);
  background-image: -ms-linear-gradient(top, #616161, #474747);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#616161), to(#474747));
  background-image: -webkit-linear-gradient(top, #616161, #474747);
  background-image: -o-linear-gradient(top, #616161, #474747);
  background-image: linear-gradient(top, #616161, #474747);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#616161', endColorstr='#474747', GradientType=0);
  -webkit-transform: translate(0, 1px);
  -moz-transform: translate(0, 1px);
  -ms-transform: translate(0, 1px);
  -o-transform: translate(0, 1px);
  transform: translate(0, 1px);
}
.button:disabled {
  background-color: #dddddd;
  background-image: -moz-linear-gradient(top, #e7e7e7, #cdcdcd);
  background-image: -ms-linear-gradient(top, #e7e7e7, #cdcdcd);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#e7e7e7), to(#cdcdcd));
  background-image: -webkit-linear-gradient(top, #e7e7e7, #cdcdcd);
  background-image: -o-linear-gradient(top, #e7e7e7, #cdcdcd);
  background-image: linear-gradient(top, #e7e7e7, #cdcdcd);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e7e7e7', endColorstr='#cdcdcd', GradientType=0);
  color: #939393;
  text-shadow: 0 1px 1px #fff;
}
