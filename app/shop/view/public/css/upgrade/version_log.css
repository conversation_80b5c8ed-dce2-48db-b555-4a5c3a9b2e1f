.log-content-block{padding:20px;background-color:#FFF}
.log-content{display:flex;margin-left: 120px;}
.log-step-text{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;margin-left: 10px;}
.log-step-text-title{
    line-height:16px!important;
    color:#000!important;
    font-size:14px!important;
    padding-bottom:8px;
}
.log-step-text-content{color:#999;font-size:12px}

.log-detail{
    color:#000;
    font-size:12px;
    display: flex;
    align-items: baseline;
    padding-top: 8px;
}
.log-no-block{padding:20px}
.log-body{
    margin-left: 60px;
    margin-bottom:26px;
	display: inline-block;
	cursor: pointer;
}
.log-version-date{
    left: -120px;
    width: 100px;
    color: #000 !important;
    font-size: 16px !important;
}
.layui-timeline-axis{
    z-index: 1 !important;
}
.layui-icon{

}
.layui-icon.layui-timeline-axis{
    color:#fff !important;
    width: 12px;
    height: 12px;
    line-height: 12px;
    padding: 2px;
    font-size: 9px;
    text-align: center;
    left:-3px;
}
.load-more .log-step-text-title{
    cursor: pointer;
}

.load-more-img{
    position: absolute;
    width: 20px;
    left: -4px;
}
.open{
    display: none
}

.item-block-parent {
	padding-left: 60px;
	box-sizing: border-box;
}

.item-block-btn {
	position: absolute;
	top: 15px;
	right: 15px;
	font-size: 12px;
	cursor: pointer;
}

/* .item-block-parent .item-block .prompt-block {
	position: absolute;
	top: 15px;
	right: 15px;
	font-size: 12px;
	cursor: pointer;
}

.item-block-parent .item-block .prompt-block .prompt {
	width: auto;
	height: auto;
	line-height: unset;
}

.item-block-parent .item-block .prompt-block .prompt-box {
	top: 30px;
	left: -50px;
	width: 150px;
}

.prompt-block .prompt-box:before, .prompt-block .prompt-box:after {
	left: 62px;
	top: unset;
}

.prompt-block .prompt-box:before {
	border-bottom-color: #e4e4e4;
	border-right-color: transparent;
	top: -25px;
}

.prompt-block .prompt-box:after {
	border-bottom-color: #FFFFFF;
	border-right-color: transparent;
	top: -23px;
} */

.item-block-parent .item-block:hover {
	background-color: #f7f8fa;
}

.item-block-parent .item-block-wrap {
	align-items: center;
}

.item-block-parent .item-pic {
	width: 58px;
	height: 58px;
	line-height: 58px;
}

.item-block-parent .item-pic img {
	vertical-align: top;
}

.item-block-parent .item-con {
	height: auto;
}

.item-block-parent .item-con .item-content-title {
	margin-bottom: 6px;
}

@media screen and (min-width: 1683px) and (max-width: 1920px) {
	.item-block {
		width: 27%;
		margin-right: 2%;
		margin-bottom: 25px;
	}

	.item-block:nth-child(3n) {
		margin-right: 0;
	}
	
	.item-block:nth-child(4n),
	.item-block:nth-child(5n) {
		margin-right: 2% !important;
	}
}

@media screen and (max-width: 1682px) {
	.item-block {
		width: 31.5%;
		margin-right: 2%;
		margin-bottom: 25px;
	}

	.item-block:nth-child(2n) {
		margin-right: 0;
	}
	
	.item-block:nth-child(3n) {
		margin-right: 2% !important;
	}
}

.layui-layer-page .layui-layer-content {
	line-height: 26px;
	overflow: auto !important;
}