<style>
	.item-con { height: auto!important; }
	.item-block-parent .item-block-wrap { align-items: center; }
	.auth-mark{
		display:inline-block;
		margin-left:4px;
		color:red;
		font-size:12px;
	}
</style>

{notempty name="$uninstall"}
<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
		<span class="card-title">未安装插件</span>
	</div>
	<div class="layui-card-body layui-field-box">
		<div class="site_list item-block-parent item-five">
			{foreach $uninstall as $list_k => $list_v}
			<a class="item-block item-block-hover" href="javascript:;">
				<div class="item-block-wrap">
					<div class="item-pic">
						{if isset($list_v['is_quick']) && $list_v['is_quick'] == 1}
						<img src="https://www.niushop.com/{$list_v.icon}" />
						{else/}
						<img src="{:img($list_v.icon)}" />
						{/if}
					</div>
					<div class="item-con">
						<div class="item-content-title">
							{$list_v.title}
							{if $list_v.download == 1}
							<div class="auth-mark">未下载</div>
							{/if}
						</div>
						<p class="item-content-desc line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
				</div>
				<div class="item-float-wrap">
					<div class="item-float">
						<div class="item-float-con">
							<i class="bg-color-red"></i>
							<span>
								{if $list_v.download == 1}未下载{else/}未安装{/if}
							</span>
						</div>
						{if $list_v.download == 0}
						<div class="item-float-con" onclick="manage('{$list_v.name}', 'install')">
							<span>安装</span>
						</div>
						{/if}
					</div>
				</div>
			</a>
			{/foreach}
		</div>
	</div>
</div>
{/notempty}

{notempty name="$addons"}
<div class="layui-card card-common card-brief">
	<div class="layui-card-header">
		<span class="card-title">已安装插件</span>
	</div>
	<div class="layui-card-body layui-field-box">
		<div class="site_list item-block-parent item-five">
			{foreach $addons as $list_k => $list_v}
			<a class="item-block item-block-hover" href="javascript:;">
				<div class="item-block-wrap">
					<div class="item-pic">
						<img src="{:img($list_v.icon)}" />
					</div>
					<div class="item-con">
						<div class="item-content-title">{$list_v.title}
						</div>
						<p class="item-content-desc line-hiding" title="{$list_v.description}">{$list_v.description}</p>
					</div>
				</div>
				<div class="item-float-wrap">
					<div class="item-float">
						<div class="item-float-con">
							<i class="bg-color-blue"></i>
							<span>已安装</span>
						</div>
						<div class="item-float-con" onclick="manage('{$list_v.name}', 'uninstall')">
							<span>卸载</span>
						</div>
					</div>
				</div>
			</a>
			{/foreach} 
		</div>
	</div>
</div>
{/notempty}

<script>
	var repeat_flag = false; //防重复标识
	function manage(addon_name, tag) {
		if (repeat_flag) return;
		repeat_flag = true;
		$.ajax({
			url: ns.url("shop/system/addon"),
			data: {
				addon_name,
				tag
			},
			type: "POST",
			dataType: "JSON",
			success: function(res) {
				layer.msg(res.message);
				if (res.code == 0) {
					location.reload();
				}else{
					repeat_flag = false;
				}
			}
		});
	}

	//查看插件信息
	function addonInfo(name){
		location.hash = ns.hash('shop/upgrade/addonInfo', {name : name});
	}
</script>