<style>
	/* 关联会员 */
	.search-result { border: 1px solid; padding: 15px 30px 15px 15px; display: flex; align-items: center; position: relative;margin-top:10px;border-color: #e5e5e5 !important; }
	.search-res-img { width: 50px; height: 50px; margin-right: 5px; text-align: center; line-height: 50px; }
	.search-res-img img { max-width: 100%; max-height: 100%; }
	.search-res-intro p { line-height: 24px; }
	.search-res-close { position: absolute; top: 5px; right: 5px; }
	.check-member .layui-btn {position: absolute;top: 0;border-color: #e5e5e5;padding: 0 10px;border-right: 0;left: 207px;}
	.check-admin .layui-btn {position: absolute;top: 0;border-color: #e5e5e5;padding: 0 10px;border-right: 0;left: 207px;}
	.layui-input-block {overflow: hidden;}
</style>

<div class="layui-form form-wrap">
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="required">*</span>核销人员姓名：</label>
		<div class="layui-input-block">
			<input name="verifier_name" type="text" value="{$data.verifier_name}" placeholder="请输入核销人员姓名" lay-verify="required" class="layui-input len-long" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item check-member-box">
		<label class="layui-form-label"><span class="required">*</span>关联前台会员：</label>
		<div class="layui-input-block check-member">
			<input type="text" id="search_text" name="search_text" placeholder="请输入用户名或手机" class="layui-input len-mid member-name" value="{$data.member_name}">
			<button type="button" class="layui-btn layui-btn-primary" onclick="checkMember()">
				<i class="layui-icon">&#xe615;</i>
			</button>
			<input type="hidden" name="member_id" value="{$data.member_id}" />
		</div>
		<p class="word-aux">关联会员后才能在手机上使用核销台功能，否则无法在手机上核销</p>

		{if addon_is_exit('store') == 1}
		<div class="layui-form-item">
			<label class="layui-form-label">核销员类型：</label>
			<div class="layui-input-block">
				<input class="verifier_type" type="radio" name="verifier_type" value="0" title="平台核销员" lay-filter="verifier_type" {if $data.verifier_type == 0} checked {/if}>
				<input class="verifier_type" type="radio" name="verifier_type" value="1" title="门店核销员" lay-filter="verifier_type" {if $data.verifier_type == 1} checked {/if}>
			</div>
		</div>
		<div class="layui-form-item js-store-template">
			<label class="layui-form-label"><span class="required">*</span>门店选择：</label>
			<div class="layui-input-inline">
				<select name="store_id" lay-search="" lay-verify="store_id">
					<option value="">请选择门店</option>
					{foreach name="$store_list" item="vo"}
					<option value="{$vo['store_id']}" {if $vo['store_id'] == $data['store_id']} selected {/if} >{$vo['store_name']}</option>
					{/foreach}
				</select>
			</div>
		</div>
		{/if}

	</div>
	<div class="form-row">
		<button class="layui-btn" lay-submit lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary" onclick="backVerify()">返回</button>
	</div>
	<!-- 隐藏域 -->
	<input type="hidden" name="verifier_id" value="{$data.verifier_id}"/>
</div>

<script>
	var form,repeat_flag = false;//防重复标识
	layui.use('form', function() {
		form = layui.form;
		form.render();

		// 隐藏
		switchVerierType($("input[name='verifier_type']:checked").val());
		checkMember();

		function switchVerierType(type){
			if (type == 0) {
				$(".js-store-template").hide();
			}else{
				$(".js-store-template").show();
			}
		}

		form.on('radio(verifier_type)', function(data) {
			switchVerierType(data.value);
		});

		// 表单验证
		form.verify({
			search_text:function(value){
				if(value == ''){
					return '请选择前台会员';
				}
			},
			member_id:function(value){
				if(value == ''){
					return '请选择前台会员';
				}
			},
			store_id: function(value) {
				var verifier_type = $("input[name='verifier_type']:checked").val();
				if(verifier_type == 1){
					if(value == ''){
						return '请选择门店';
					}
				}
			},
		});

		// 监听提交
		form.on('submit(save)', function(data) {
			if (repeat_flag) return;
			repeat_flag = true;

			$.ajax({
				url: ns.url("shop/verify/editUser"),
				data: data.field,
				dataType: 'JSON',
				type: 'POST',
				success: function(data){
					layer.msg(data.message);
					repeat_flag = false;
					if(data.code == 0){
						location.hash = ns.hash("shop/verify/user");
					}
				}
			});
		});
	});

	$('.check-member .layui-input').focus(function (){
		$(this).next('.layui-btn').addClass('border-color');
	}).blur(function () {
		$(this).next('.layui-btn').removeClass('border-color');
	});

	/**
	 * 点击搜索
	 */
	var repeat_flag_member = false;
	var html, val;

	function checkMember() {
		var parent = $(".check-member");
		var con = parent.find(".member-name").val();
		$(".layui-word-aux").remove();
		$(".search-result").remove();

		if (repeat_flag_member) return false;
		repeat_flag_member = true;

		if (con == "" || con == null || con.trim() == "") {
			repeat_flag = false;
		} else {
			$.ajax({
				type: 'POST',
				url: ns.url("shop/verify/searchMember"),
				data: {
					'search_text': con
				},
				dataType: 'JSON',
				success: function(res) {
					repeat_flag_member = false;

					if (res.data == null) {
						html = '<span class="layui-word-aux">未找到该用户</span>';
						val = res.data;
					} else {
						html = '<div class="search-result layui-input-inline">' +
							'<div class="search-res-img">' +
							'<img src="' + ( res.data.headimg ? ns.img(res.data.headimg) : ns.img("{$default_headimg}")) + '" />' +
							'</div>' +
							'<div class="search-res-intro">' +
							'<p>用户名：' + res.data.username + '</p>' +
							'<p>电话：' + res.data.mobile + '</p>' +
							'</div>' +
							'<div class="search-res-close" onclick="closeMember()">' +
							'<i class="iconfont iconclose_light"></i>' +
							'</div>' +
							'</div>';
						val = res.data.member_id;
					}

					$("input[name='member_id']").attr("value", val);
					$(".check-member").append(html);
				}
			});
		}
	}

	function closeMember() {
		$(".search-result").hide();
	}

	function backVerify() {
		location.hash = ns.hash("shop/verify/user");
	}
</script>