<link rel="stylesheet" href="__STATIC__/ext/video/video.css">
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/searchable_select/searchable_select.css" />
<link rel="stylesheet" type="text/css" href="__STATIC__/ext/layui/extend/cascader/cascader.css"/>
<link rel="stylesheet" type="text/css" href="SHOP_CSS/goods_edit.css?time=20250527" />

<div class="layui-form">
	<div class="layui-tab layui-tab-brief" lay-filter="goods_tab">
		<ul class="layui-tab-title">
			<li class="layu1i-this" lay-id="basic">基础设置</li>
			<li lay-id="price-stock">价格库存</li>
			<li lay-id="detail">商品详情</li>
			<li lay-id="attr">商品参数</li>
			<li lay-id="senior">高级设置</li>
		</ul>
		<div class="layui-tab-content">
			<!-- 基础设置 -->
			<div class="layui-tab-item layui-show">

				<!-- 商品类型 -->
				<div class="layui-card card-common card-brief head">
					<div class="layui-card-header">
						<span class="card-title">商品类型</span>
					</div>

					<div class="layui-card-body commodity-type-box" >
						{foreach name="all_goodsclass" item="vo"}
						<div class="commodity-type-item {if $vo.goods_class eq $goods_class.id}border-color{/if}" onclick="location.hash = ns.hash('{$vo.add_url}')">
							<span>{$vo.goods_class_name}</span>
							<span>{$vo.is_virtual ? '(无需物流)' : '(需要物流)'}</span>
						</div>
						{/foreach}
					</div>

				</div>

				<div class="layui-card card-common card-brief head">
					<div class="layui-card-header">
						<span class="card-title">基础信息</span>
					</div>

					<div class="layui-card-body">
						<div class="layui-form-item">
							<label class="layui-form-label"><span class="required">*</span>商品名称：</label>
							<div class="layui-input-inline">
								<input name="goods_name" type="text" placeholder="请输入商品名称，不能超过60个字符" maxlength="60" autocomplete="off" lay-verify="goods_name" class="layui-input len-long">
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">促销语：</label>
							<div class="layui-input-inline">
								<textarea class="layui-textarea len-long" name="introduction" maxlength="100" lay-verify="introduction" placeholder="请输入促销语，不能超过100个字符"></textarea>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">关键词：</label>
							<div class="layui-input-block">
								<input type="text" name="keywords" placeholder="商品关键词用于SEO搜索，不能超过100个字符" maxlength="100" autocomplete="off" class="layui-input len-long">
							</div>
						</div>
						
						<div class="layui-form-item goods-image-wrap">
							<label class="layui-form-label"><span class="required">*</span>商品主图：</label>
							<div class="layui-input-block">
								<!--商品主图项-->
								<div class="js-goods-image"></div>
							</div>
							<div class="word-aux">第一张图片将作为商品主图,支持同时上传多张图片,多张图片之间可随意调整位置；</div>
							<div class="word-aux">支持jpg、gif、png格式上传或从图片空间中选择，建议使用尺寸800x800像素以上、大小不超过1M的正方形图片；</div>
							<div class="word-aux">上传后的图片将会自动保存在图片空间的默认分类中，最多上传10张（至少1张）</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">商品视频：</label>
							<div class="layui-input-block">

								<div class="video-thumb">
									<video id="goods_video" class="video-js vjs-big-play-centered" controls="" poster="SHOP_IMG/goods_video_preview.png" preload="auto"></video>
								</div>
								<div id="videoUpload2" class="up-video " title="商品视频" >
									<span class="delete-video hide" onclick="deleteVideo()"><img class="del-img" src="SHOP_IMG/delete.png">删除</span>
									<span  class=" replace-video hide js-add-goods-video" ><img class="up-img" src="SHOP_IMG/upload.png">上传视频</span>
								</div>

							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label"></label>
							<div class="layui-input-block">
								<input type="text" name="video_url" placeholder="在此输入外链视频地址" autocomplete="off" class="layui-input len-long">
							</div>
							<div class="file-title word-aux">
								<div>注意事项：</div>
								<ul>
									<li>1、检查upload文件夹是否有读写权限。</li>
									<li>2、PHP默认上传限制为2MB，需要在php.ini配置文件中修改“post_max_size”和“upload_max_filesize”的大小。</li>
									<li>3、视频支持手动输入外链视频地址或者上传本地视频文件</li>
									<li>4、必须上传.mp4视频格式</li>
									<li>5、视频文件大小不能超过500MB</li>
								</ul>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">商品品牌：</label>
							<div class="layui-input-inline">
								<select name="brand_id" lay-search="" lay-filter="brand_id">
									<option value="">无</option>
									{foreach name="$brand_list" item="vo"}
									<option value="{$vo['brand_id']}">{$vo['brand_name']}</option>
									{/foreach}
								</select>
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">商品标签：</label>
							<div class="layui-input-inline">
								<select name="label_id" lay-search="" lay-verify="label_id">
									<option value="">请选择商品标签</option>
									{foreach name="$label_list" item="vo"}
									<option value="{$vo['id']}">{$vo['label_name']}</option>
									{/foreach}
								</select>
							</div>
						</div>

						{notempty name="$service_list"}
						<div class="layui-form-item">
							<label class="layui-form-label">商品服务：</label>
							<div class="layui-input-block">
								{foreach name="$service_list" item="vo"}
								<input type="checkbox" name="goods_service_ids" value="{$vo.id}" title="{$vo.service_name}" lay-skin="primary">
								{/foreach}
							</div>
						</div>
						{/notempty}

						<div class="layui-form-item goods-category-wrap">
							<label class="layui-form-label"><span class="required">*</span>商品分类：</label>
							<div class="layui-input-block" id="category_select_box">
							</div>
							<input type="hidden" lay-verify="category_id"/>
							<div class="word-aux">商品可以属于多个分类，最多10个</div>
						</div>

						{if $is_install_supply}
						<div class="layui-form-item">
							<label class="layui-form-label">供应商：</label>
							<div class="layui-input-inline">
								<select name="supplier_id" lay-search="" lay-verify="supplier_id">
									<option value="">请选择供应商</option>
									{foreach name="$supplier_list" item="vo"}
									<option value="{$vo['supplier_id']}">{$vo['title']}</option>
									{/foreach}
								</select>
							</div>
						</div>
						{/if}

						<div class="layui-form-item goods_state">
							<label class="layui-form-label"><span class="required">*</span>是否上架：</label>
							<div class="layui-input-block">
								<input type="radio" name="goods_state" value="1" title="立刻上架" checked lay-filter="goods_state">
								<input type="radio" name="goods_state" value="0" title="放入仓库" lay-filter="goods_state">
							</div>
						</div>

						<div class="layui-form-item">
							<label class="layui-form-label">定时下架：</label>
							<div class="layui-input-block">
								<input type="radio" name="timer_off_status" value="1" title="启用" lay-filter="timer_off">
								<input type="radio" name="timer_off_status" value="2" title="不启用" lay-filter="timer_off" checked>
							</div>
							<div class="word-aux">启用定时下架后，到达设定时间，此商品将自动下架。</div>
						</div>

						<div class="layui-form-item timer_off" style="display: none;">
							<label class="layui-form-label"></label>
							<div class="layui-input-inline">
								<input type="text" id="timer_off" name="timer_off" class="layui-input len-mid" autocomplete="off" readonly>
								<i class=" iconrili iconfont calendar"></i>
							</div>
						</div>


					</div>
				</div>

				<div class="layui-card card-common card-brief head">
					<div class="layui-card-header">
						<span class="card-title">收发货设置</span>
					</div>

					<div class="layui-card-body">
						<div class="layui-form-item">
							<label class="layui-form-label">发货设置：</label>
							<div class="layui-input-block">
								<input type="radio" name="virtual_deliver_type" value="auto_deliver" title="自动发货" checked lay-filter="virtual_deliver_type">
								<input type="radio" name="virtual_deliver_type" value="artificial_deliver" title="手动发货" lay-filter="virtual_deliver_type">
								<input type="radio" name="virtual_deliver_type" value="verify" title="到店核销" lay-filter="virtual_deliver_type">
							</div>
						</div>

						<div class="layui-form-item need-receive">
							<label class="layui-form-label">收货设置：</label>
							<div class="layui-input-block">
								<input type="radio" name="virtual_receive_type" value="auto_receive" title="自动收货" checked lay-filter="virtual_deliver_type">
								<input type="radio" name="virtual_receive_type" value="artificial_receive" title="买家确认收货" lay-filter="virtual_deliver_type">
							</div>
						</div>

						<div class="need-verify layui-hide">
							<div class="layui-form-item">
								<label class="layui-form-label">核销有效期：</label>
								<div class="layui-input-block">
									<input type="radio" name="verify_validity_type" value="0" title="永久" checked lay-filter="verify_validity_type">
									<input type="radio" name="verify_validity_type" value="1" title="购买后几日有效" lay-filter="verify_validity_type">
									<input type="radio" name="verify_validity_type" value="2" title="指定过期日期" lay-filter="verify_validity_type">
								</div>
							</div>

							<div class="layui-form-item validity-type validity-type-1 layui-hide">
								<label class="layui-form-label"><span class="required">*</span>有效期：</label>
								<div class="layui-input-inline">
									<input type="text" name="virtual_indate" placeholder="0" class="layui-input len-short" lay-verify="virtual_indate" autocomplete="off">
								</div>
								<div class="layui-form-mid layui-word-aux">天</div>
							</div>

							<div class="layui-form-item validity-type validity-type-2 layui-hide">
								<label class="layui-form-label"><span class="required">*</span>有效期：</label>
								<div class="layui-input-inline">
									<input type="text" id="virtual_time" name="virtual_time" class="layui-input len-mid" lay-verify="virtual_time" autocomplete="off" readonly>
									<i class=" iconrili iconfont calendar"></i>
								</div>
								<div class="word-aux" style="clear:both;top: 5px;position: relative;">无论何时购买此商品，到达指定时间后都将过期，无法核销。</div>
							</div>
							
						</div>

					</div>
				</div>

			</div>

			<!-- 价格库存 -->
			<div class="layui-tab-item">
				<div class="layui-form-item">
					<label class="layui-form-label">启用多规格：</label>
					<div class="layui-input-inline">
						<input type="checkbox" value="1" lay-skin="switch" name="spec_type" lay-filter="spec_type" lay-verify="spec_type">
						<input type="hidden" id="spec_type_status" value="0">
					</div>
				</div>

				<!-- 单规格 -->
				<div class="js-single-spec">

					<div class="layui-form-item">
						<label class="layui-form-label"><span class="required">*</span>销售价：</label>
						<div class="layui-input-block">
							<input type="text" name="price" placeholder="0.00" lay-verify="price" class="layui-input len-short" autocomplete="off">
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">商品没有相关优惠活动的实际卖价</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">划线价：</label>
						<div class="layui-input-block">
							<input type="text" name="market_price" placeholder="0.00" lay-verify="market_price" class="layui-input len-short" autocomplete="off">
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">商品没有优惠活动显示的划线价格，如果商品有折扣等优惠活动划线价显示销售价</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">成本价：</label>
						<div class="layui-input-block">
							<input type="text" name="cost_price" placeholder="0.00" class="layui-input len-short" lay-verify="cost_price" autocomplete="off">
							<div class="layui-form-mid">元</div>
						</div>
						<div class="word-aux">成本价将不会对前台会员展示，用于商家统计使用</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">商品编码：</label>
						<div class="layui-input-block">
							<input type="text" name="sku_no" placeholder="请输入商品编码" maxlength="50" class="layui-input len-long" autocomplete="off">
						</div>
						<div class="word-aux">多个编码以英文逗号分割</div>
					</div>

				</div>

				<!-- 多规格 -->
				<div class="js-more-spec">

					<!--规格项/规格值-->
					<div class="spec-edit-list"></div>

					<div class="layui-form-item js-add-spec">
						<label class="layui-form-label"></label>
						<div class="layui-input-inline">
							<button class="layui-btn" type="button">添加规格</button>
						</div>
					</div>

					<div class="layui-form-item batch-operation-sku">
						<label class="layui-form-label">批量操作：</label>
						<div class="layui-input-inline">
							<span class="text-color sku_image" data-field="sku_images">SKU图片</span>
							<span class="text-color" data-field="spec_name">副标题</span>
							<span class="text-color" data-field="price" data-verify="price">销售价</span>
							<span class="text-color" data-field="market_price" data-verify="market_price">划线价</span>
							<span class="text-color" data-field="cost_price" data-verify="cost_price">成本价</span>
							<span class="text-color" data-field="stock" data-verify="stock">库存</span>
							<span class="text-color" data-field="stock_alarm" data-verify="stock_alarm">库存预警</span>
							<span class="text-color" data-field="sku_no" data-verify="">商品编码</span>
							<div class="layui-inline select_spec_value" style="float:left;display: none"></div>
							<input type="text" class="layui-input len-short" name="batch_operation_sku" autocomplete="off" />
							<button class="layui-btn confirm" type="button">确定</button>
							<button class="layui-btn layui-btn-primary cancel" type="button">取消</button>
							<div id="batch_set_sku_image" class="batch-set-sku-image" style="clear: both;display: none;"></div>
						</div>
					</div>

					<!--sku列表-->
					<div class="layui-form-item sku-table">
						<label class="layui-form-label"></label>
						<div class="layui-input-block"></div>
					</div>

				</div>

				<div class="layui-form-item js-goods-stock-wrap">
					<label class="layui-form-label"><span class="required">*</span>库存：</label>
					<div class="layui-input-block">
						<input type="number" name="goods_stock" placeholder="0" lay-verify="goods_stock" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
				</div>

				<div class="layui-form-item js-goods-stock-wrap">
					<label class="layui-form-label">库存预警：</label>
					<div class="layui-input-block">
						<input type="number" name="goods_stock_alarm" placeholder="0" lay-verify="goods_stock_alarm" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">商品库存少于预警数量，商品列表库存数量标红显示，0为不预警。</div>
				</div>

				<div class="layui-form-item js-goods-stock-wrap verify-num need-verify layui-hide">
					<label class="layui-form-label"><span class="required">*</span>核销次数：</label>
					<div class="layui-input-inline">
						<input type="text" name="verify_num" value="1" class="layui-input len-short" lay-verify="goods_verify_num" autocomplete="off">
					</div>
					<div class="layui-form-mid layui-word-aux">次</div>
					<div class="word-aux">用户购买多件商品时，可核销次数 = 【购买数量】*【单个商品核销次数】</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">商品单位：</label>
					<div class="layui-input-block">
						<input type="text" name="unit" placeholder="请输入商品单位" autocomplete="off" class="layui-input len-short">
					</div>
				</div>

				<div class="layui-form-item ">
					<label class="layui-form-label">虚拟销量：</label>
					<div class="layui-input-block">
						<input type="number" name="virtual_sale" placeholder="0" lay-verify="virtual_sale" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">该设置不计入商品统计数据</div>
				</div>

				<div class="layui-form-item is_limit">
					<label class="layui-form-label">是否限购：</label>
					<div class="layui-input-block">
						<input type="radio" name="is_limit" value="0" title="否" lay-filter="is_limit" checked>
						<input type="radio" name="is_limit" value="1" title="是" lay-filter="is_limit">
					</div>
					<div class="word-aux">启用限购后，购买商品时，会对该商品购买量做限制判断。</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">起售：</label>
					<div class="layui-input-block">
						<input type="number" name="min_buy" placeholder="" lay-verify="min_buy" class="layui-input len-short" autocomplete="off">
						<div class="layui-form-mid">件</div>
					</div>
					<div class="word-aux">起售数量超出商品库存时，买家无法购买该商品</div>
				</div>

				<div class="layui-form-item">
					<label class="layui-form-label">会员等级折扣：</label>
					<div class="layui-input-block">
						<div class="layui-input-inline">
							<input type="radio" name="is_consume_discount" value="1" title="参与" checked>
							<input type="radio" name="is_consume_discount" value="0" title="不参与">
						</div>
					</div>
					<div class="word-aux">按照默认会员等级折扣优惠</div>
				</div>
			</div>

			<!-- 商品详情 -->
			<div class="layui-tab-item">
				<div class="layui-form-item">
					<label class="layui-form-label sm"></label>
					<div class="layui-input-inline special-length">
						<script id="editor" type="text/plain" style="width:100%;height:500px;"></script>
					</div>
				</div>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.config.js"></script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/ueditor.all.js"> </script>
				<script type="text/javascript" charset="utf-8" src="__STATIC__/ext/ueditor/lang/zh-cn/zh-cn.js"></script>
			</div>

			<!-- 商品参数 -->
			<div class="layui-tab-item">
				<div class="layui-form-item">
					<label class="layui-form-label">商品参数模板：</label>
					<div class="layui-input-block len-mid">
						<select name="goods_attr_class" lay-search="" lay-filter="goods_attr_class">
							<option value="">请选择商品参数模板</option>
							{foreach name="$attr_class_list" item="vo"}
							<option value="{$vo['class_id']}">{$vo['class_name']}</option>
							{/foreach}
						</select>
						<input type="hidden" name="goods_attr_name" />
					</div>
					<div class="word-aux">商品可以添加自定义商品参数，也可以通过参数模板批量设置商品参数</div>
				</div>

				<div class="layui-form-item js-new-attr-list">
						<label class="layui-form-label"></label>
						<div class="layui-input-block">
							<div class="layui-form">
								<table class="layui-table">
									<colgroup>
										<col width="30%" />
										<col width="40%" />
										<col width="20%" />
										<col width="10%" />
									</colgroup>
									<thead>
										<tr>
											<th>参数名</th>
											<th>参数名</th>
											<th class="prompt-block">
												排序
												<div class="prompt">
													<i class="iconfont iconwenhao1"></i>
													<div class="prompt-box">
														<div class="prompt-con">设置排序，改变商品规格展示顺序</div>
													</div>
												</div>
											</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody class="attr-new">
										<tr class="null-data">
											<td colspan="4" align="center">无数据</td>
										</tr>
									</tbody>
								</table>
							</div>
							<button class="layui-btn layui-btn-primary" onclick="addNewAttr()">添加商品参数</button>
						</div>
					</div>
			</div>
			
			<div class="layui-tab-item layui-card card-common card-brief head">
				<div class="layui-card-header">
					<span class="card-title">高级设置</span>
				</div>

				<div class="layui-card-body">
				
					<div class="layui-form-item">
						<label class="layui-form-label">排序：</label>
						<div class="layui-input-block">
							<input type="number" name="sort" class="layui-input len-short" value="{$sort_config['default_value']}" placeholder="0" autocomplete="off">
						</div>
						<div class="word-aux">商品默认排序号为0，数字越大，排序越靠前，数字重复，则最新添加的靠前。</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">商品详情显示库存：</label>
						<div class="layui-input-block">
							<input type="radio" name="stock_show" value="1" title="显示" checked >
							<input type="radio" name="stock_show" value="0" title="隐藏" >
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">划线价显示：</label>
						<div class="layui-input-block">
							<input type="radio" name="market_price_show" value="1" title="显示" checked>
							<input type="radio" name="market_price_show" value="0" title="隐藏" >
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">商品详情显示弹幕：</label>
						<div class="layui-input-block">
							<input type="radio" name="barrage_show" value="1" title="显示" checked>
							<input type="radio" name="barrage_show" value="0" title="隐藏" >
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">商品详情显示销量：</label>
						<div class="layui-input-block">
							<input type="radio" name="sale_show" value="1" title="显示" checked>
							<input type="radio" name="sale_show" value="0" title="隐藏" >
						</div>
					</div>

					<div class="layui-form-item">
						<label class="layui-form-label">商品海报：</label>
						<div class="layui-input-inline">
							<select name="template_id" lay-search="" lay-verify="">
								<option value="">请选择商品海报</option>
								{foreach name="$poster_list" item="vo"}
								<option value="{$vo['template_id']}">{$vo['poster_name']}</option>
								{/foreach}
							</select>
						</div>
					</div>

					{if $form_is_exit}
					<div class="layui-form-item">
						<label class="layui-form-label">商品表单：</label>
						<div class="layui-input-block len-mid">
							<select name="form_id">
								<option value="0">请选择商品表单</option>
								{foreach name="$form_list" item="vo"}
								<option value="{$vo.id}">{$vo.form_name}</option>
								{/foreach}
							</select>
						</div>
						<div class="word-aux">
							<a href="{:href_url('form://shop/form/addform?form_type=goods')}" class="text-color" target="_blank">创建商品表单</a>
							<a href="javascript:;" onclick="refreshFormList()" class="text-color">刷新</a>
						</div>
					</div>
					{/if}

				</div>

			</div>
		</div>
	</div>

	<div class="fixed-btn">
		<button class="layui-btn layui-btn-primary border-color text-color js-prev" lay-filter="prev">上一步</button>
		<button class="layui-btn js-save" lay-submit="" lay-filter="save">保存</button>
		<button class="layui-btn layui-btn-primary border-color text-color js-next" lay-submit="" lay-filter="next">下一步</button>
	</div>
</div>

<!--选择商品分类-->
<script type="text/html" id="selectedCategory">

	<div class="category-list">

		<div class="item">
			<!--后续做搜索-->
			<ul>
				{foreach name="$goods_category_list" item="vo"}
				{{# if(d.category_id_1 == '{$vo['category_id']}' ){ }}
				<li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-level="{$vo['level']}" class="selected">
				{{# }else{ }}
				<li data-category-id="{$vo['category_id']}" data-commission-rate="{$vo['commission_rate']}" data-level="{$vo['level']}">
				{{# } }}
					<span class="category-name">{$vo['category_name']}</span>
					<span class="right-arrow"></span>
				</li>
				{/foreach}
			</ul>
		</div>

		<div class="item" data-level="2">
			<!--后续做搜索-->
			<ul></ul>
		</div>

		<div class="item" data-level="3">
			<!--后续做搜索-->
			<ul></ul>
		</div>

	</div>

	<div class="selected-category-wrap">
		<label>您当前选择的是：</label>
		<span class="js-selected-category"></span>
	</div>
</script>

<!--规格项模板-->
<script type="text/html" id="specTemplate">

	{{# for(var i=0;i<d.list.length;i++){ }}
	<div class="spec-item" data-index="{{i}}">
		<div class="layui-form-item spec">
			<label class="layui-form-label">规格项{{i+1}}：</label>
			<div class="layui-input-inline">
				<select name="spec_item">
					<option value="0"></option>
					{{# if(d.list[i].spec_name != ''){ }}
					<option value="{{d.list[i].spec_id}}" data-attr-name="{{d.list[i].spec_name}}" selected>{{d.list[i].spec_name}}</option>
					{{# }else{ }}
					{{# } }}
				</select>
				<i class="layui-icon layui-icon-close" data-index="{{i}}"></i>
			</div>

			{{# if(i==0){ }}
			<div class="layui-input-inline">
				{{# if(d.add_spec_img){ }}
				<input type="checkbox" name="add_spec_img" title="添加规格图片" lay-skin="primary" lay-filter="add_spec_img" checked>
				{{# }else{ }}
				<input type="checkbox" name="add_spec_img" title="添加规格图片" lay-skin="primary" lay-filter="add_spec_img">
				{{# } }}
			</div>
			{{# } }}
		</div>

		{{# if(d.list[i].spec_name != ''){ }}
		<div class="layui-form-item spec-value">
		{{# }else{ }}
		<div class="layui-form-item spec-value" style="display:none;">
		{{# } }}
			<label class="layui-form-label"></label>
			<div class="layui-input-block spec-value">
				{{# if(d.list[i].value.length){ }}
				<ul>
					{{# for(var j=0;j<d.list[i].value.length;j++){ }}
					<li data-index="{{j}}" data-parent-index="{{i}}" >
						{{# if(i==0 && d.add_spec_img){ }}
						<div class="img-wrap">
							{{# if(d.list[i].value[j].image){ }}
							<img src="{{ns.img(d.list[i].value[j].image)}}" alt="">
							{{# }else{ }}
							<img src="SHOP_IMG/goods_spec_value_empty.png" alt="">
							{{# } }}
						</div>
						{{# } }}
						<span title="双击可编辑规格值" ondblclick="$(this).attr('contenteditable',true);$(this).focus()" class="spec-txt" data-spec_value_name="{{d.list[i].value[j].spec_value_name}}"  data-parent-index="{{i}}" data-index="{{j}}">{{d.list[i].value[j].spec_value_name}}</span>
						<i class="layui-icon layui-icon-close" data-parent-index="{{i}}" data-index="{{j}}"></i>
					</li>
					{{# } }}
				</ul>
				{{# } }}

				<a class="text-color" href="javascript:;" data-index="{{i}}">+添加规格值</a>

				<div class="add-spec-value-popup" data-index="{{i}}">
					<select name="spec_value_item"></select>
					<button class="layui-btn layui-btn-primary border-color text-color js-cancel-spec-value">取消</button>
				</div>

			</div>
		</div>

	</div>
	{{# } }}
</script>

<!--SKU列表模板-->
<script type="text/html" id="skuTableTemplate">

	{{# if(d.skuList.length){ }}
	<table class="layui-table">
		<colgroup></colgroup>
		<thead>
			<tr>
				{{# if(d.showSpecName){ }}
				<th colspan="{{d.colSpan}}" style="min-width: 60px;">商品规格</th>
				{{# } }}
				<th rowspan="{{d.rowSpan}}">SKU图片</th>
				<th rowspan="{{d.rowSpan}}">副标题</th>
				<th rowspan="{{d.rowSpan}}"><span class="required">*</span>销售价</th>
				<th rowspan="{{d.rowSpan}}">划线价</th>
				<th rowspan="{{d.rowSpan}}">成本价</th>
				<th rowspan="{{d.rowSpan}}"><span class="required">*</span>库存</th>
				<th rowspan="{{d.rowSpan}}">库存预警</th>
				{{# if(d.isNeedVerify){ }}
				<th rowspan="{{d.rowSpan}}"><span class="required">*</span>核销次数</th>
				{{# } }}
				<th rowspan="{{d.rowSpan}}">商品编码（多个编码以英文逗号分割）</th>
				<th rowspan="{{d.rowSpan}}" style="white-space: nowrap;">默认展示</th>
			</tr>
			{{# if(d.colSpan>1){ }}
			<tr>
				{{# for(var i=0;i<d.specList.length;i++){ }}
				{{# if(d.specList[i].spec_name && d.specList[i].value.length> 0){ }}
				<th>{{d.specList[i].spec_name}}</th>
				{{# } }}
				{{# } }}
			</tr>
			{{# } }}
		</thead>
		<tbody>
			{{# for(var i=0;i<d.skuList.length;i++){ }}
			<tr>
				<td class="sku_imgs" id="sku_img_{{i}}" style="width: 130px;">
					{{# for(var j=0;j<d.skuList[i].sku_images_arr.length;j++){ }}
					<div class="img-wrap" data-index="{{j}}" data-parent-index="{{i}}">
						<a href="javascript:void(0)">
							<img src="{{ns.img(d.skuList[i].sku_images_arr[j])}}" layer-src />
						</a>
						<div class="operation">
							<i title="图片预览" class="iconfont iconreview js-preview"></i>
							<i title="删除图片" class="layui-icon layui-icon-delete js-delete"></i>
						</div>
					</div>
					{{# } }}
					{{# if(d.skuList[i].sku_images_arr.length<d.goods_sku_max){ }}
					<div class="upload-sku-img" data-index="{{i}}"><i class="layui-icon layui-icon-add-1"></i></div>
					{{# } }}
				</td>
				<td>
					<input type="text" name="spec_name" placeholder="副标题" maxlength="100" value="{{d.skuList[i].spec_name}}" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="price" placeholder="销售价" lay-verify="sku_price" value="{{d.skuList[i].price}}" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="market_price" placeholder="划线价" value="{{d.skuList[i].market_price}}" lay-verify="sku_market_price" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="cost_price" placeholder="成本价" value="{{d.skuList[i].cost_price}}" lay-verify="sku_cost_price" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="stock" placeholder="库存" value="{{d.skuList[i].stock}}" lay-verify="sku_stock" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td>
					<input type="text" name="stock_alarm" placeholder="库存预警" value="{{d.skuList[i].stock_alarm}}" lay-verify="sku_stock_alarm" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				{{# if(d.isNeedVerify){ }}
				<td>
					<input type="text" name="verify_num" placeholder="核销次数" value="{{d.skuList[i].verify_num}}" maxlength="50" class="layui-input" autocomplete="off" data-index="{{i}}" lay-verify="verify_num">
				</td>
				{{# } }}
				<td>
					<input type="text" name="sku_no" placeholder="商品编码" value="{{d.skuList[i].sku_no}}" maxlength="50" class="layui-input" autocomplete="off" data-index="{{i}}">
				</td>
				<td style="min-width: 40px;">
					{{# if(d.skuList[i].is_default == 1) { }}
					<input type="checkbox" data-index="{{i}}" name="is_default" lay-filter="is_default" lay-skin="switch" checked>
					{{# }else { }}
					<input type="checkbox" data-index="{{i}}" name="is_default" lay-filter="is_default" lay-skin="switch">
					{{# } }}
				</td>
			</tr>
			{{# } }}

		</tbody>
	</table>
	{{# } }}
	<div class="word-aux text-color" style="margin: 10px 0 0 0;">默认展示，是多规格商品在客户访问商品时，默认显示的商品规格</div>
</script>

<!--商品主图列表-->
<script type="text/html" id="goodsImage">
	{{# if(d.list.length){ }}
		{{# for(var i=0;i<d.list.length;i++){ }}
			<div class="item upload_img_square_item" data-index="{{i}}">
				<div class="img-wrap">
					<img src="{{ns.img(d.list[i],'small')}}" layer-src="{{ns.img(d.list[i],'big')}}">
				</div>
				<div class="operation">
					<i title="图片预览" class="iconfont iconreview js-preview"></i>
					<i title="删除图片" class="layui-icon layui-icon-delete js-delete" data-index="{{i}}"></i>
					<div class="replace_img" data-index="{{i}}">点击替换</div>
				</div>
			</div>
		{{# } }}
		{{# if(d.list.length < d.max){ }}
			<div class="item js-add-goods-image upload_img_square">+</div>
		{{# } }}
	{{# }else{ }}
		<div class="item js-add-goods-image upload_img_square">+</div>
	{{# } }}
</script>

<!--属性列表模板-->
<script type="text/html" id="attrTemplate">
	{{# for(var i=0;i<d.list.length;i++){ }}
	<tr class="goods-attr-tr goods-attr-temp" data-attr-class-id="{{d.list[i].attr_class_id}}" data-attr-class-name="{{d.list[i].attr_class_name}}" data-attr-id="{{d.list[i].attr_id}}" data-attr-name="{{d.list[i].attr_name}}" data-attr-type="{{d.list[i].attr_type}}">
		<td>{{d.list[i].attr_name}}</td>
		<td>
			{{# if(d.list[i].attr_type == 1){ }}
				{{# for(var j=0;j<d.list[i].attr_value_format.length;j++){ }}
				<input type="radio" name="attr_value_{{d.list[i].attr_id}}" value="{{d.list[i].attr_value_format[j].attr_value_id}}" title="{{d.list[i].attr_value_format[j].attr_value_name}}" data-attr-value-name="{{d.list[i].attr_value_format[j].attr_value_name}}" />
				{{# } }}
			{{# }else if(d.list[i].attr_type == 2){ }}
				{{# for(var j=0;j<d.list[i].attr_value_format.length;j++){ }}
				<input type="checkbox" name="attr_value_{{d.list[i].attr_id}}" value="{{d.list[i].attr_value_format[j].attr_value_id}}" title="{{d.list[i].attr_value_format[j].attr_value_name}}" data-attr-value-name="{{d.list[i].attr_value_format[j].attr_value_name}}" lay-skin="primary">
				{{# } }}
			{{# }else if(d.list[i].attr_type == 3){ }}
				<input type="text" name="attr_value_{{d.list[i].attr_id}}" placeholder="{{d.list[i].attr_name}}" class="layui-input len-mid" autocomplete="off">
			{{# } }}
		</td>
		<td><input type="number" name="" value="{{d.list[i].sort ? d.list[i].sort : 0}}" placeholder="" class="layui-input attr-sort" autocomplete="off"></td>
		<td><div class="table-btn"><a class="layui-btn" onclick="delAttr(this)">删除</a></div></td>
	</tr>
	{{# } }}
</script>
<!--批量操作模版-->
<script type="text/html" id="batchOperateTemplate">
	{{# for(var i=0;i<d.length;i++){ }}
	{{# if(d[i].value.length > 0){ }}
	<div class="layui-input-inline">
		<select name="spec_value:{{d[i].spec_id}}" lay-search="" id="spec_value_{{d[i].spec_id}}" data-spec-name="{{d[i].spec_name}}" >
			<option value="all">请选择{{d[i].spec_name}}</option>
			{{# for(var j=0;j< d[i].value.length;j++){ }}
			<option value="{{d[i].value[j].spec_value_id}}">{{d[i].value[j].spec_value_name}}</option>
			{{# } }}
		</select>
	</div>
	{{# } }}
	{{# } }}
</script>
<script src="__STATIC__/ext/drag-arrange.js"></script>
<script src="__STATIC__/ext/video/videojs-ie8.min.js"></script>
<script src="__STATIC__/ext/video/video.min.js"></script>
<script src="__STATIC__/ext/searchable_select/searchable_select.js"></script>
<script src="SHOP_JS/category_select.js?time=20240821"></script>
<script src="SHOP_JS/goods_edit_common.js?time=20250527"></script>
<script src="SHOP_JS/virtual_goods_edit.js"></script>

