<link rel="stylesheet" href="SHOP_CSS/order_detail.css"/>

<!-- 订单详情、订单状态 -->
<div class="order-detail layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">订单详情</span>
    </div>
	<div class="order-information order-information-bottom layui-card-body">
		<div class="order-information-contentOne">
			<div class="contentOne-content">
				<div class="contentOne-content-title">交易流水号：</div>
				<div class="contentOne-content-text text-num">{$order_detail['out_trade_no']}</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单编号：</div>
				<div class="contentOne-content-text">{$order_detail['order_no']}</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单类型：</div>
				<div class="contentOne-content-text">{$order_detail['order_type_name']}</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">订单来源：</div>
				<div class="contentOne-content-text">{$order_detail.order_from_name}</div>
			</div>
			{if $order_detail.pay_status == 1}
			<div class="contentOne-content">
				<div class="contentOne-content-title">付款方式：</div>
				<div class="contentOne-content-text">{$order_detail.pay_type_name}</div>
			</div>
			{/if}
			<div class="contentOne-content">
				<div class="contentOne-content-title">买家：</div>
				<div class="contentOne-content-text"><a class="text-color" href="javascript:toMemberDetail();">{$order_detail.nickname}</a></div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">手机号：</div>
				<div class="contentOne-content-text">{$order_detail.mobile}</div>
			</div>
		</div>
		<div class="order-information-contentTwo">
			<div class="contentOne-content">
				<div class="contentOne-content-title">营销活动：</div>
				<div class="contentOne-content-text">{if empty($order_detail['promotion_type_name'])}-{else/}{$order_detail['promotion_type_name']}{/if}</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">买家留言：</div>
				<div class="contentOne-content-text contentOne-content-text-die">
					{if $order_detail['buyer_message'] == ""}
					-
					{else/}
					{$order_detail['buyer_message']}
					{/if}
				</div>
			</div>
			<div class="contentOne-content">
				<div class="contentOne-content-title">备注：</div>
				<div class="contentOne-content-text">
					{if $order_detail['remark'] == ""}
					-
					{else/}
					{$order_detail['remark']}
					{/if}
				</div>
			</div>
			<div class="contentOne-content">
                <div class="contentOne-content-title">操作人：核销员</div>
                <div class="contentOne-content-text">
                    {if $order_detail['verifier_name'] == ""}
                    -
                    {else/}
                    {$order_detail['verifier_name']}
                    {/if}
                </div>
            </div>
		</div>
	</div>
	
    <div class="layui-card-header">
        <span class="card-title">订单状态</span>
    </div>
	<div class="order-information-contentOne order-orderStatus-contentOne  layui-card-body">
		<div class="contentOne-content">
			<div class="contentOne-content-title">订单状态：</div>
			<div class="contentOne-content-text contentOne-content-textNew">{$order_detail.order_status_name}</div>
		</div>
		<div class="contentTow-operation">
			<div class="contentTow-operation-content bg-color-light-9 contentTow-operation-new" onclick="orderRemark('{$order_detail.order_id}')">备注</div>
			{php}
			$order_json_data = json_decode($order_detail['order_status_action'], true);
			$action = $order_json_data['action'];
			{/php}
			{foreach $action as $action_k => $action_item}
				<a class="contentTow-operation-content bg-color-light-9 contentTow-operation-new" href="javascript:orderAction('{$action_item.action}', '{$order_detail.order_id}')">{$action_item.title}</a>
			{/foreach}
			{if addon_is_exit("printer") && $order_detail.order_status != -1}
			<a class="contentTow-operation-content bg-color-light-9 contentTow-operation-new bg-color-light-9" href="javascript:printTicket('{$order_detail.order_id}');" >打印小票</a>
			{/if}
		</div>
		<div class="orderStatus">
			<div class="orderStatus-content-title">提醒：</div>
			<div class="orderStatus-content-text">
				<p class="text-tile">买家付款成功后，货款将直接进入您的商户号（微信、支付宝）</p>
			</div>
		</div>
	</div>

	{if isset($order_detail.form)}
    	<!-- 表单信息 -->
		<div class="formFields">
            <div class="layui-card-header">
                <span class="card-title">表单信息</span>
            </div>
			<div class="order-information order-formFields layui-card-body">
				{foreach name="$order_detail.form" item="vo"}
				<div class="contentOne-content">
					<div class="contentOne-content-title">{if is_array($vo.value)}{$vo.value.title}{/if}：</div>
					<div class="contentOne-content-text">
					{if isset($vo.controller) && $vo.controller == 'Img'}
                        {foreach name="$vo.img_lists" item="io"}
                            <div class="form-img">
                                <div class="form-img-wrap">
                                    <img src="{:img($io)}" layer-src>
                                </div>  
                            </div>
                        {/foreach}
                    {else/}
						{if isset($vo.val)}
                    	{$vo.val}
						{/if}
                    {/if}
                    </div>
				</div>
				{/foreach}
			</div>
		</div>
	{/if}

	{if  $order_detail['is_invoice'] == 1}
		<!-- 发票信息 -->
		<div class="formFields">
            <div class="layui-card-header">
                <span class="card-title">发票信息</span>
            </div>
			<div class="order-information order-formFields layui-card-body">
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票类型：</div>
					<div class="contentOne-content-text">{if $order_detail['invoice_type'] == 1}纸质{else/}电子{/if}{if $order_detail['is_tax_invoice'] == 1}专票{else/}普票{/if}</div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票抬头：</div>
					<div class="contentOne-content-text">{$order_detail['invoice_title']}</div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票抬头类型：</div>
					<div class="contentOne-content-text">{$order_detail['invoice_title_type'] == 1 ? '个人' : '企业'}</div>
				</div>
				{if $order_detail['invoice_title_type'] == 2}
				<div class="contentOne-content">
					<div class="contentOne-content-title">纳税人识别号：</div>
					<div class="contentOne-content-text">{$order_detail['taxpayer_number']}</div>
				</div>
				{/if}
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票内容：</div>
					<div class="contentOne-content-text">{$order_detail['invoice_content']}</div>
				</div>
				{if $order_detail['invoice_type'] == 1}
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票邮寄地址：</div>
					<div class="contentOne-content-text">{$order_detail['invoice_full_address']}</div>
				</div>
				{else/}
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票接收邮件：</div>
					<div class="contentOne-content-text">{$order_detail['invoice_email']}</div>
				</div>
				{/if}
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票费用：</div>
					<div class="contentOne-content-text">￥{$order_detail.invoice_money}</div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票税率：</div>
					<div class="contentOne-content-text">{$order_detail.invoice_rate}%</div>
				</div>
				<div class="contentOne-content">
					<div class="contentOne-content-title">发票邮寄费用：</div>
					<div class="contentOne-content-text">￥{$order_detail.invoice_delivery_money}</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<!-- 商品信息、订单操作日志 -->
<div class="shop-information layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">商品信息</span>
    </div>
	<div class="shop-information-table layui-card-body">
		<table lay-filter="parse-table-order-product" lay-skin="line">
			<thead>
				<tr class="table-trOne">
					<th lay-data="{field:'product_name', width:200}">商品</th>
					<th lay-data="{field:'price'}">价格</th>
					<th lay-data="{field:'sale_num'}">数量</th>
					<th lay-data="{field:'total_money'}">小计（元）</th>
					<th lay-data="{field:'refund_status'}">退款状态</th>
					{if isset($order_detail.virtual_goods)}
						{if $order_detail.goods_class == 2 }
						<th style="text-align:center">总核销次数</th>
						<th style="text-align:center">剩余次数</th>
						<th style="text-align:center">核销码</th>
						<th>有效期</th>
						<th>操作</th>
						{/if}
						{if $order_detail.goods_class == 3}
						<th>卡密信息</th>
						{/if}
						{if $order_detail.goods_class == 4 }
							<th style="text-align:center">总核销次数</th>
							<th style="text-align:center">剩余次数</th>
							<th style="text-align:center">核销码</th>
							<th>有效期</th>
							<th>操作</th>
						{/if}
					{/if}
				</tr>
			</thead>
			<tbody>
				{foreach $order_detail['order_goods'] as $list_k => $order_goods_item}
				<tr class="table-trTow">
					<td>{$order_goods_item.sku_name}</td>
					<td>{$order_goods_item.price}</td>
					<td>{$order_goods_item.num}</td>
					<td>{$order_goods_item.goods_money}</td>
					<td>
						{if $order_goods_item.refund_status != 0}
						<div><a class="" href='{:href_url("shop/orderrefund/detail?order_goods_id=".$order_goods_item["order_goods_id"])}'>{$order_goods_item.refund_status_name}</a></div>
						{php}
							$refund_money = $order_goods_item['shop_active_refund_money']+$order_goods_item['refund_real_money'];
							$refund_money = sprintf("%.2f",$refund_money);
						{/php}
						{if $refund_money > 0}
						<div style="color:red;">￥{$refund_money}</div>
						{/if}
						{elseif $order_detail.is_enable_refund == 1 && $order_detail.promotion_type != 'blindbox' && $order_goods_item.shop_active_refund == 0 && $order_goods_item.real_goods_money > 0}
						<div><a class="text-color" href="javascript:;" style="border:1px solid;padding:2px;" onclick="shopActiveRefund('{$order_goods_item.order_goods_id}')" >主动退款</a></div>
						{/if}
					</td>
					{if isset($order_detail.virtual_goods)}
						{if $order_detail.goods_class == 2 }
						<td style="text-align:center;">{$order_detail.virtual_goods.verify_total_count}</td>
						<td style="text-align:center;">{$order_detail.virtual_goods.verify_total_count - $order_detail.virtual_goods.verify_use_num}</td>
						<td style="text-align:center;">{$order_detail.virtual_code}</td>
						<td>
							{if $order_detail.virtual_goods.expire_time > 0}{:time_to_date($order_detail.virtual_goods.expire_time)}{else/}永久有效{/if}</td>
						<td><a href="javascript:;" class="text-color" onclick="showVerifyRecord()">核销记录</a></td>
						{/if}
						{if $order_detail.goods_class == 3}
						<td>
							<div class="carmichael">
								{foreach name="$order_detail.virtual_goods" item="vo"}
								<div>卡号：{$vo.card_info.cardno}<br>密码：{$vo.card_info.password}</div>
								{/foreach}
							</div>
						</td>
						{/if}
						{if $order_detail.goods_class == 4 }
						<td style="text-align:center;">{$order_detail.virtual_goods.verify_total_count}</td>
						<td style="text-align:center;">{$order_detail.virtual_goods.verify_total_count - $order_detail.virtual_goods.verify_use_num}</td>
						<td style="text-align:center;">{$order_detail.virtual_code}</td>
						<td>
							{if $order_detail.virtual_goods.expire_time > 0}{:time_to_date($order_detail.virtual_goods.expire_time)}{else/}永久有效{/if}</td>
						<td><a href="javascript:;" class="text-color" onclick="showVerifyRecord()">核销记录</a></td>
						{/if}
					{/if}
				</tr>
				{if isset($order_goods_item.form)}
				<tr>
					{if isset($order_detail.virtual_goods)}
						{if $order_detail.goods_class == 2}
						<td colspan="10">
						{/if}
						{if $order_detail.goods_class == 3}
						<td colspan="11">
						{/if}
						{if $order_detail.goods_class == 4}
						<td colspan="10">
						{/if}
					{else/}
					<td colspan="5">
					{/if}
						<div class="order-goods-form">
							{foreach name="$order_goods_item.form" item="vo"}
							<div class="form-item">
								<div class="field-title">{$vo.value.title}：</div>
								<div class="field-content">
									{if $vo.controller == 'Img'}
									{foreach name="$vo.img_lists" item="io"}
									<div class="form-img">
										<div class="form-img-wrap">
											<img src="{:img($io)}" layer-src>
										</div>
									</div>
									{/foreach}
									{else/}
									{if isset($vo.val)}
									{$vo.val}
									{/if}
									{/if}
								</div>
							</div>
							{/foreach}
						</div>
					</td>
				</tr>
				{/if}
				{/foreach}
			</tbody>
		</table>
		<div class="table-trThree table-trFour">
			<div>
				<p>商品总额：<span>￥{$order_detail["goods_money"]}</span></p>
			</div>
			<div>
				<p>店铺优惠卷：<span>￥{$order_detail["coupon_money"]}</span></p>
			</div>
			<div>
				<p>店铺优惠：<span>￥{$order_detail["promotion_money"]}</span></p>
			</div>
			{if $order_detail["point_money"] > 0}
			<div>
				<p>积分抵扣：<span>￥{$order_detail["point_money"]}</span></p>
			</div>
			{/if}
			{if $order_detail["balance_money"] > 0}
			<div>
				<p>余额：<span>￥{$order_detail["balance_money"]}</span></p>
			</div>
			{/if}
			<div>
				<p>订单调价：<span>￥{$order_detail["adjust_money"]}</span></p>
			</div>
			<div>
				<p>发票费用：<span>￥{$order_detail["invoice_money"]}</span></p>
			</div>
			<div>
				<p>发票邮寄费用：<span>￥{$order_detail["invoice_delivery_money"]}</span></p>
			</div>
			{if $order_detail["member_card_money"] > 0}
			<div>
				<p>会员卡：<span>￥{$order_detail["member_card_money"]}</span></p>
			</div>
			{/if}
		</div>
	    <div class="table-settlement">
			订单共<span>{$order_detail["goods_num"]}</span>件商品,共计<span>￥{$order_detail["order_money"]}</span>
		</div>
	</div>
</div>

{notempty name="$order_detail.order_log"}
<!-- 订单操作 -->
<div class="shop-operation layui-card card-common card-brief">
    <div class="layui-card-header">
        <span class="card-title">订单操作日志</span>
    </div>
	<div class="shop-operation-time layui-card-body">
		<ul class="layui-timeline">
			{foreach name="$order_detail.order_log" item="vo"}
		  	<li class="layui-timeline-item">
				<div class="layui-time-left">
					<p>{:date('Y-m-d', $vo.action_time)}</p>
					<p>{:date('H:i:s', $vo.action_time)}</p>
				</div>
			    <div class="layui-icon layui-timeline-axis">
					<span class="layui-icon-center"></span>
				</div>
			    <div class="layui-timeline-content layui-text">
			      	<div class="layui-timeline-title">{$vo.action}</div>
			    </div>
		 	</li>
		 	{/foreach}
		</ul>
	</div>
</div>
{/notempty}

<script type="text/html" id="verifyRecord">
    <div class="verify-record" style="height: 100%;overflow: auto;">
        <table class="layui-table" lay-skin="nob">
            <thead>
                <tr>
                    <th>核销人</th>
                    <th style="text-align:center">核销次数</th>
                    <th style="text-align:center">核销时间</th>
                </tr>
            </thead>
            <tbody>
                {if isset($order_detail.virtual_goods) && isset($order_detail.virtual_goods.verify_record)}
                    {if is_array($order_detail.virtual_goods.verify_record) && count($order_detail.virtual_goods.verify_record) > 0}
                        {foreach name="$order_detail.virtual_goods.verify_record" item="vo"}
                        <tr>
                            <td>{$vo.verifier_name}</td>
                            <td style="text-align:center">1</td>
                            <td style="text-align:center">{:time_to_date($vo.verify_time)}</td>
                        </tr>
                        {/foreach}
                    {/if}
                {else/}
                <tr>
                    <td colspan="3" style="text-align:center;">暂无核销记录</td>
                </tr>
                {/if}
            </tbody>
        </table>
    </div>
</script>

{include file="order/order_common_action" /}
<!-- 虚拟订单发货 -->
{include file="virtualorder/virtual_order_delivery_action" /}
<!-- 主动退款 -->
{include file="order/shop_active_refund" /}
<script src="SHOP_JS/lodop_funcs.js"></script>
<script>
	function showVerifyRecord(){
	    layer.open({
	        title: '核销记录',
	        skin: 'verify-record',
	        type: 1,
	        area: ['710px', '60%'],
	        content: $('#verifyRecord').html()
	    })
	}
	function toMemberDetail(){
		let member_id = '{$order_detail.member_id}';
		window.open(ns.href("shop/member/editmember", {member_id:member_id}));
	}
</script>