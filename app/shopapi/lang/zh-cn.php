<?php

return [
	
	'SUCCESS' => '操作成功',
	'ERROR' => '操作失败',
	'SAVE_SUCCESS' => '保存成功',
	'SAVE_FAIL' => '保存失败',
	'REQUEST_SUCCESS' => '请求成功',
	'REQUEST_FAIL' => '请求失败',
	'DELETE_SUCCESS' => '删除成功',
	'DELETE_FAIL' => '删除失败',
	'UNKNOW_ERROR' => '未知错误',
	'PARAMETER_ERROR' => '参数错误',
	'REQUEST_SITE_ID' => '缺少必须参数站点id',
	'REQUEST_APP_MODULE' => '缺少必须参数应用模块',
	'TOKEN_NOT_EXIST' => 'token不存在',
	'TOKEN_ERROR' => 'token错误',
	'TOKEN_EXPIRE' => 'token已过期',
	'CAPTCHA_FAILURE' => '验证码已失效',
	'CAPTCHA_ERROR' => '验证码不正确',
	'REQUEST_COUPON_TYPE_ID' => '缺少参数coupon_type_id',
	'REQUEST_CAPTCHA_ID' => '缺少参数captcha_id',
	'REQUEST_CAPTCHA_CODE' => '缺少参数captcha_code',
	'REQUEST_SKU_ID' => '缺少参数sku_id',
	'REQUEST_NUM' => '缺少参数num',
	'REQUEST_CART_ID' => '缺少参数cart_id',
	'REQUEST_CATEGORY_ID' => '缺少参数category_id',
	'REQUEST_ID' => '缺少参数id',
	'REQUEST_ORDER_ID' => '缺少参数order_id',
	'REQUEST_GOODS_EVALUATE' => '缺少参数goods_evaluate',
	'REQUEST_ORDER_STATUS' => '缺少参数order_status',
	'REQUEST_DIY_ID_NAME' => '缺少参数id/name',
	'REQUEST_TOPIC_ID' => '缺少参数topic_id',
	'REQUEST_SECKILL_ID' => '缺少参数seckill_id',
	'REQUEST_KEYWORD' => '缺少参数keyword',
	'REQUEST_GOODS_ID' => '缺少参数goods_id',
	'REQUEST_PINTUAN_ID' => '缺少参数pintuan_id',
	'REQUEST_EMAIL' => '缺少参数email',
	'REQUEST_MOBILE' => '缺少参数mobile',
	'REQUEST_GROUPBUY_ID' => '缺少参数groupbuy_id',
	'REQUEST_RECHARGE_ID' => '缺少参数recharge_id',
	'REQUEST_BL_ID' => '缺少参数bl_id',
	'REQUEST_NAME' => '缺少参数name',
	'REQUEST_STORE_ID' => '缺少参数store_id',
	'REQUEST_REAL_NAME' => '缺少参数real_name',
	'REQUEST_WITHDRAW_TYPE' => '缺少参数withdraw_type',
	'REQUEST_BRANCH_BANK_NAME' => '缺少参数branch_bank_name',
	'REQUEST_BRANCH_BANK_ACCOUNT' => '缺少参数bank_account',
    'ADDON_NOT_EXIST' => '商家手机管理端插件不存在',
    'NO_PERMISSION' => '权限不足'
];