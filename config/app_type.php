<?php
//使用场景 端口
return [
    'wechat'   => ['name' => '微信公众号', 'logo' => 'public/static/img/channel/wechat.png', 'icon' => 'iconfont iconweixin'],
    'weapp'    => ['name' => '微信小程序', 'logo' => 'public/static/img/channel/weapp.png', 'icon' => 'iconfont iconxiaochengxu'],
    'aliapp'   => ['name' => '支付宝小程序', 'logo' => 'public/static/img/channel/aliapp.png', 'icon' => 'iconfont iconxiaochengxu1'],
//    'baiduapp' => ['name' => '百度小程序', 'logo' => 'public/static/img/baidu_small_procedures.png'],
    'pc'       => ['name' => 'PC', 'logo' => 'public/static/img/channel/pc.png', 'icon' => 'iconfont iconshoujishumadiannao'],
    'h5'       => ['name' => 'H5', 'logo' => 'public/static/img/channel/h5.png', 'icon' => 'iconfont iconh'],
    'app'      => ['name' => 'APP', 'logo' => 'public/static/img/channel/app.png', 'icon' => 'iconfont iconapp'],
];