<?php

return [
    //整数
    'num'       => '/^-?(0|[1-9]\d*)(\.0*)?$/',
    '>0num'     => '/^[1-9]\d*(\.0*)?$/',
    '>=0num'    => '/^(0|[1-9]\d*)(\.0*)?$/',
    //两位浮点数
    'float2'    => '/^-?(0|[1-9]\d*)(\.\d{0,2}0*)?$/',
    '>0float2'  => '/^(0\.\d{1,2}|[1-9]\d*(\.\d{0,2}0*)?)$/',
    '>=0float2' => '/^(0|[1-9]\d*)(\.\d{0,2}0*)?$/',
    //3位浮点数
    'float3'    => '/^-?(0|[1-9]\d*)(\.\d{0,3}0*)?$/',
    '>0float3'  => '/^(0\.\d{1,3}|[1-9]\d*(.\d{0,3}0*)?)$/',
    '>=0float3' => '/^(0|[1-9]\d*)(\.\d{0,3}0*)?$/',
    //手机号
    'mobile'    => '/^1\d{10}$/',
    //身份证号
    'idcard15'  => '/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$/',
    'idcard18'  => '/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{4}$/',
];
