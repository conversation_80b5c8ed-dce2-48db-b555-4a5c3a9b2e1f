.layui-layout-admin .layui-body .body-content{padding: 15px;margin: 15px;}
#bottomNav{position: relative;overflow: hidden}
#bottomNav .preview{width: 320px;background-repeat: no-repeat;background-size: 100%;float: left;}
#bottomNav .preview .preview-head{background: url("../img/preview_head_old.png") no-repeat;position: relative;}
#bottomNav .preview .preview-head>span{color: #ffffff;font-size: 16px;display: block;text-align: center;margin-left: 50px;height: 64px;line-height: 82px;margin-right: 40px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;cursor: pointer;}
#bottomNav .preview .preview-block{border-left: 1px solid #e5e5e5;border-right: 1px solid #e5e5e5;border-bottom: 1px solid #e5e5e5;min-height: 100px;position: relative;}
.preview-block ul{overflow: hidden;display: flex;position: absolute;bottom: 0;width: 100%;border-top:1px solid #e5e5e5;}
.preview-block ul li{text-align: center;flex: 1;margin: 5px 0;}
.preview-block ul li>div{height: 30px;line-height: 30px;width: 30px;    display: flex;align-items: center;justify-content: center;margin: 0 auto;}
.preview-block ul li>div>div{height: 20px;width: 20px;}
.preview-block ul li>div.js-icon{font-size: 20px}
.preview-block ul li img{width: 20px;max-height: 100%;}
.preview-block ul li span{text-overflow: ellipsis;white-space: nowrap;overflow: hidden;display: block;}
.preview-block ul li .icon-wrap{font-size: 20px}
#bottomNav .edit-attribute{position: relative;border: 1px solid #e5e5e5;width:400px;float: left;margin-left: 20px;overflow-y: scroll;overflow-x: auto;display: block;}

.edit-attribute .img-block{width: 50px;height: 48px;display: inline-block;padding: 8px;margin-right: 10px;cursor: pointer;vertical-align: top; line-height: 1;background-color: #EEEEEE;text-align: center;font-size: 12px;}
.edit-attribute .img-block i.add{display:block;font-style: normal;text-align: center;font-size:30px;line-height: 48px;}
.edit-attribute .img-block i.del{display: block;}
.edit-attribute .img-block.has-choose-image{width: 66px;height: 64px;margin-right:0;background-color: #EEEEEE;display: inline-block;vertical-align: top;position: relative;line-height: 64px;text-align: center;padding: 0;}
.edit-attribute .img-block:last-child{margin-top:15px;}
.edit-attribute .img-block.has-choose-image>div{width: 66px;height: 48px;line-height: 48px;}
.edit-attribute .img-block.has-choose-image img{width: 30px;height: auto;max-width: 100%;max-height: 100%;}
.edit-attribute .img-block.has-choose-image span{position: absolute;bottom: 0;left: 0;width: 100%;text-align: center;font-size:12px;background: rgba(0,0,0,.6);color:#ffffff;line-height: initial;cursor:pointer;}

.edit-attribute .bottom-menu-config ul>li{display: flex; padding: 10px;background: #ffffff; border: 1px dashed #e5e5e5;position: relative;margin-top: 16px;justify-content: space-between;flex-direction: column;}
.edit-attribute .bottom-menu-config ul>li:first-child{margin-top:0;}
.edit-attribute .bottom-menu-config ul>li .image-block{display: flex;flex-direction: row;margin-right:20px;cursor:pointer;vertical-align: top;line-height: 1;text-align: center;margin-top: 15px;}
.edit-attribute .bottom-menu-config ul>li .content-block{display:inline-block;width:100%;}
.edit-attribute .bottom-menu-config ul>li .content-block .layui-form-label{width:70px;color: #909399 !important;font-size: 14px;}
.edit-attribute .bottom-menu-config ul>li .content-block .layui-input-block{margin-left:80px;}
.edit-attribute .bottom-menu-config ul>li:hover .del{display:block;}
.edit-attribute .bottom-menu-config ul>li .img-hover-block{clear: both;}
.edit-attribute .bottom-menu-config ul>li .img-hover-block .img-block{margin-top: 10px;}
.edit-attribute .bottom-menu-config ul.icon-wrap>li {margin-top: 0px;justify-content: center;align-items: center;}
.edit-attribute .bottom-menu-config .add-item{text-align:center;padding: 10px;border: 1px dashed #e5e5e5;margin: 10px 0;cursor: pointer;}
.edit-attribute .bottom-menu-config .add-item i{font-size: 18px;vertical-align: middle;margin-right: 10px;font-style: normal;}
.edit-attribute .bottom-menu-config .add-item span{vertical-align: middle;}
.edit-attribute .bottom-menu-config p.hint{font-size: 12px;color: #999;margin: 10px;}
.edit-attribute .bottom-menu-config .error-msg{margin-top: 5px;color: #f44;display: none;}
.edit-attribute .bottom-menu-config .layui-form-checkbox span{height: initial;}
.custom-save{margin-top: 20px;padding: 0;}

.layui-form-item .layui-form-checkbox{margin-top: 8px !important;padding-left: 0 !important;}
.bottom-menu-config .layui-form-label.sm{color: #909399 !important;font-size: 14px;}
.edit-attribute .bottom-menu-set li .del{left:unset;right: -10px;top: -10px;}

.edit-attribute .icon-block{display: inline-block;margin-right: 10px;cursor: pointer;vertical-align: top; line-height: 1;text-align: center;font-size: 12px;position: relative;}
.edit-attribute .icon-block i.add{display:block;font-style: normal;text-align: center;font-size:30px;line-height: 48px;}

.edit-attribute .icon-block>div{display: flex;flex-direction: row;height: 60px;align-items: center;padding: 0px;}
.edit-attribute .icon-block>div img{width: 100%;max-height: 100%}

.edit-attribute .icon-block>div .edit-icon{position: absolute;bottom: 0;left: 0;width: 100%;text-align: center;font-size: 12px;background: rgba(0,0,0,.6);color: #ffffff;line-height: initial;cursor: pointer;}
.edit-attribute .icon-block i.del{display: block;}
.edit-attribute .icon-block.has-choose-image{width: 66px;height: 64px;margin-right:0;background-color: #EEEEEE;display: inline-block;vertical-align: top;position: relative;line-height: 64px;text-align: center;padding: 0;}
.edit-attribute .icon-block:last-child{margin-top:0px;}
.edit-attribute .icon-block.has-choose-image>div{width: 66px;height: 48px;line-height: 48px;}
.edit-attribute .icon-block.has-choose-image img{width: 30px;height: auto;max-width: 100%;max-height: 100%;}
.edit-attribute .icon-block.has-choose-image span{position: absolute;bottom: 0;left: 0;width: 100%;text-align: center;font-size:12px;background: rgba(0,0,0,.6);color:#ffffff;line-height: initial;cursor:pointer;}
.edit-attribute .icon-block>div .operation {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.6);
    flex-direction: column;
    display: none;
}
.edit-attribute .icon-block>div .icon-box:hover .operation, .edit-attribute .icon-block>div .upload-box:hover .operation {
    display: flex;
}
.edit-attribute .icon-block>div .operation-warp {
    flex: 1;
    height: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}
.edit-attribute .icon-block>div .iconfont {
    margin: 0 3px;
}
.edit-attribute .icon-block>div .operation .js-replace{
    line-height: 1;
    color: #fff;
    text-align: center;
    padding: 5px 0;
    background: rgba(0,0,0,.7);
    font-size: 12px;
}

.icon-box,.upload-box{
    width: 60px;
    height: 60px;
    font-size: 60px;
    border: 1px dashed #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0!important;
    cursor: pointer;
    position: relative;
}
.icon-box .select-icon, .upload-box .select-icon {
    width: inherit;
    height: inherit;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 1;
}
.icon-box .select-icon .add, .upload-box .select-icon .add {
    font-size: 26px;
    color: var(--base-color);
}
.icon-box .operation, .upload-box .operation {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.6);
    flex-direction: column;
    display: none;
}
.icon-box:hover .operation {
    /*display: flex;*/
}
.upload-box:hover .operation{
    display: flex;
}
.upload-box:hover {
}
.icon-box .operation-warp,.upload-box .operation-warp {
    flex: 1;
    height: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}
.icon-box .iconfont, .upload-box .iconfont {
    margin: 0 3px;
    font-size: 16px!important;
}
.icon-box .operation .js-replace, .upload-box .operation .js-replace{
    line-height: 1;
    color: #fff;
    text-align: center;
    padding: 5px 0;
    background: rgba(0,0,0,.7);
    font-size: 12px;
}

.edit-attribute .image-block .action-box {
    display: flex;
    flex-direction: column;
}
.edit-attribute .image-block .action {
    margin-left: 6px;
    width: 42px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border: 1px solid #EEEEEE;
    cursor: pointer;
}
.edit-attribute .image-block .action:last-child {
    margin-top: 2px;
}
.edit-attribute .image-block .iconfont {
    font-size: 20px;
}
.edit-attribute .image-block .action:hover {
    border-color: var(--base-color);
    color: var(--base-color);
}
.select-icon-style {
    position: fixed;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 9999;
}
.select-icon-style .icon-style-wrap {
    position: absolute;
    background: #fff;
    border: 1px solid #ddd;
    right: 40px;
    margin-top: 15px;
}
.select-icon-style .icon-style-wrap iframe {
    width: 100%;
    height: 100%;
}

.img-upload, .upload-img-block {
    width: 60px;
    height: 60px;
    padding: 0;
}
.upload-img-block .upload-img-box .add {
    font-size: 26px;
    color: var(--base-color);
}
.upload-img-block .operation i {
    font-size: 16px!important;
    margin: 0 2px!important;
    line-height: 1;
}
.upload-img-block .operation > div:first-child {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - 20px);
}

.select-url {
    background: #fff;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: end;
    color: #666;
}
.select-url .text {
    flex: 1;
    width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow:ellipsis;
    text-align: right;
    color: #000;
}
.select-url .layui-icon {
    font-size: 12px;
    margin-top: 1px;
}
.icon-text {
    height: 30px!important;
    text-align: center;
    width: 64px;
    line-height: 30px;
    padding: 0 13px!important;
}
.edit-attribute .bottom-menu-set li{
    padding-left: 25px!important;
}
.edit-attribute .bottom-menu-set li .icontuodong{
    position: absolute;
    top: calc(50% - 10px);
    left: 10px;
    cursor: pointer;
    font-size: 20px;
}

/*滚动条样式*/
.edit-attribute::-webkit-scrollbar {
    width: 4px;
}
.edit-attribute::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    background: rgba(0,0,0,0.2);
}
.edit-attribute::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
    border-radius: 0;
    background: rgba(0,0,0,0.1);
}