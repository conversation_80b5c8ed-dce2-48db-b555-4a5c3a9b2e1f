/**
 * layui_dropdown
 * v2.3.5
 * by Microanswer
 * http://layuidropdown.microanswer.cn/
 **/
html #layuicss-dropdown_css, html #layuicss-mDropdown_css {
	display: none;
	position: absolute;
	width: 1989px
}

.layu-dropdown-root {
	position: fixed;
	outline: 0;
	overflow: hidden
}

.layu-dropdown-pointer {
	width: 0;
	height: 0;
	position: absolute;
	display: block;
	overflow: hidden;
	background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAAgBAMAAABQs2O3AAAAAXNSR0IB2cksfwAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABVQTFRFAAAAgICAgICAgICAhISE8PDw////jUnEegAAAAd0Uk5TAB/2/////74r3wgAAACOSURBVHicfcztDYAgDEVR2MCwgXEDwwbGDYwbyP4jyJfYlvbdvzc5zrH8ti8OFeKxou+384JEiPeDiAykhIgMpASICiCiAoDogE10wCQGYBEDMAgC6AQBVIIBGsEAhRDATAhgIiZAEhMgCAXghAIwQgUooQKEMICfMIBBmMBHmEAnANAIADQCAYVwCCjEC5fhzAE36RUGAAAAAElFTkSuQmCC) bottom center no-repeat;
	background-size: 100% 50%;
	-webkit-transform-origin: center center;
	-moz-transform-origin: center center;
	-ms-transform-origin: center center;
	-o-transform-origin: center center;
	transform-origin: center center
}

.layu-dropdown-pointer.bottom {
	-webkit-transform: rotate(-180deg);
	-moz-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	-o-transform: rotate(-180deg);
	transform: rotate(-180deg)
}

.layu-dropdown-root .layu-dropdown-content {
	background-color: #fff;
	border-radius: 3px;
	box-shadow: 1px 1px 5px #cbcbcb;
	border: 1px solid #D9D9D9;
	overflow-x: auto;
	overflow-y: hidden
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-thead {
	display: table-header-group
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-tbody {
	display: table-row-group
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-tr {
	display: table-row
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table, .layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-td, .layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-th {
	position: relative;
	display: table-cell;
	border: unset;
	text-align: unset;
	font-weight: 400;
	min-height: unset;
	font-size: 12px;
	line-height: 12px;
	padding-top: 0;
	padding-bottom: 0
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-th {
	font-weight: 700
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-tr, .layu-dropdown-root .layu-dropdown-content .layu-dropdown-content-table .layu-dropdown-content-tr:hover {
	background-color: unset
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap {
	border-right: none;
	overflow-y: auto;
	overflow-x: hidden
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-fixed-head {
	border-right: none;
	overflow: hidden
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-fixed-head .layu-menu-fixed-head {
	margin: 6px 14px 2px;
	color: #a8a8a8;
	font-size: 12px
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-fixed-head.layu-menu-splitor::after, .layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap.layu-menu-splitor::after {
	border-right: 1px solid #D9D9D9;
	content: " ";
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap.layu-overflowauto {
	overflow-y: auto;
	overflow-x: hidden
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu {
	padding: 0;
	margin: 10px 0
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap {
	list-style: none;
	outline: 0
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap.layu-nomargin {
	margin-top: 0 !important
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-header {
	margin-top: 6px
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-header.layu-withLine {
	margin-left: 0;
	margin-bottom: 0;
	margin-right: 0
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-header, .layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-header legend {
	font-size: 12px !important;
	line-height: 15px !important;
	padding: 0 14px !important;
	color: #a8a8a8
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-item {
	line-height: 36px
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-item a {
	display: block;
	color: #333;
	padding: 0 20px;
	text-indent: 0;
	font-size: 14px;
	white-space: nowrap
}

.layu-dropdown-root .layu-dropdown-content .layu-dropdown-menu-wrap .layu-dropdown-menu .layu-menu-item-wrap .layu-menu-item a:hover {
	background-color: #D9D9D9
}

.layui-dropdown {
	position: relative;
	display: inline-block;
}
.layui-dropdown-menu {
	display: none;
	z-index: 100;
	min-width: 100%;
	min-height: 1px;
	margin: 0;
	padding: 0;
	border-radius: 2px;
	border: 1px solid #d2d2d2;
	background-color: #fff;
	-webkit-box-shadow: 0 2px 4px rgba(0,0,0,0.12);
	box-shadow: 0 2px 4px rgba(0,0,0,0.12);
	list-style: none;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
.layui-dropdown-menu:before {
	content: "\20";
	position: absolute;
	display: block;
	background-color: transparent;
	z-index: -1;
}
.layui-dropdown-menu.is-show {
	display: block;
}
.layui-dropdown-menu[x-placement^="top"] {
	margin-bottom: 5px;
	-webkit-box-shadow: 0 -1px 4px rgba(0,0,0,0.12);
	box-shadow: 0 -1px 4px rgba(0,0,0,0.12);
}
.layui-dropdown-menu[x-placement^="top"]:before {
	bottom: -5px;
	width: 100%;
	height: 5px;
	left: 0;
}
.layui-dropdown-menu[x-placement^="right"] {
	margin-left: 5px;
}
.layui-dropdown-menu[x-placement^="right"]:before {
	left: -5px;
	width: 5px;
	height: 100%;
	top: 0;
}
.layui-dropdown-menu[x-placement^="bottom"] {
	margin-top: 5px;
}
.layui-dropdown-menu[x-placement^="bottom"]:before {
	top: -5px;
	width: 100%;
	height: 5px;
	left: 0;
}
.layui-dropdown-menu[x-placement^="left"] {
	margin-right: 5px;
}
.layui-dropdown-menu[x-placement^="left"]:before {
	right: -5px;
	width: 5px;
	height: 100%;
	top: 0;
}
[x-out-of-boundaries] {
	pointer-events: none;
	visibility: hidden;
	opacity: 0;
}
.layui-anim-rightbit {
	-webkit-animation-name: layui-rightbit;
	animation-name: layui-rightbit;
}
.layui-anim-leftbit {
	-webkit-animation-name: layui-leftbit;
	animation-name: layui-leftbit;
}
.layui-anim-downbit {
	-webkit-animation-name: layui-downbit;
	animation-name: layui-downbit;
}
@-webkit-keyframes layui-rightbit {
	0% {
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
		opacity: 0.1;
	}
}
@keyframes layui-rightbit {
	0% {
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
		opacity: 0.1;
	}
}
@-webkit-keyframes layui-leftbit {
	100% {
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
		opacity: 0.1;
	}
}
@keyframes layui-leftbit {
	100% {
		-webkit-transform: translate3d(30px, 0, 0);
		transform: translate3d(30px, 0, 0);
		opacity: 0.1;
	}
}
@-webkit-keyframes layui-downbit {
	100% {
		-webkit-transform: translate3d(0, 30px, 0);
		transform: translate3d(0, 30px, 0);
		opacity: 0.3;
	}
}
@keyframes layui-downbit {
	100% {
		-webkit-transform: translate3d(0, 30px, 0);
		transform: translate3d(0, 30px, 0);
		opacity: 0.3;
	}
}
