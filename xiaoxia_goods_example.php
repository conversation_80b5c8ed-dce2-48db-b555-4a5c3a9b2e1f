<?php
/**
 * 小夏商品管理类使用示例
 *
 * 演示如何使用 app\model\xiaoxia\Main 类来获取商品列表
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new think\App();
$app->initialize();

use app\model\xiaoxia\Main;

// 创建小夏商品管理实例
$xiaoxia = new Main();

echo "<h1>小夏商品管理类使用示例</h1>\n";
echo "<style>body{font-family: Arial, sans-serif; margin: 20px;} table{border-collapse: collapse; width: 100%;} th,td{border: 1px solid #ddd; padding: 8px; text-align: left;} th{background-color: #f2f2f2;} .success{color: green;} .error{color: red;}</style>\n";

// 示例1: 获取所有商品（限制10条）
echo "<h2>示例1: 获取所有商品（前10条）</h2>\n";
try {
    $condition1 = [
        ['site_id', '=', 1],
        ['goods_state', '=', 1],
        ['is_delete', '=', 0]
    ];
    $field1 = 'goods_id,goods_name,price,market_price,goods_stock,create_time';
    $result1 = $xiaoxia->getGoodsList($condition1, $field1, 'create_time desc', 10);

    if ($result1['code'] == 0) {
        echo "<p class='success'>✓ 成功获取商品列表，共 " . count($result1['data']) . " 条记录</p>\n";

        if (!empty($result1['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>市场价</th><th>库存</th><th>创建时间</th></tr>\n";

            foreach ($result1['data'] as $goods) {
                $create_time = is_numeric($goods['create_time']) ? date('Y-m-d H:i:s', $goods['create_time']) : $goods['create_time'];
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . number_format($goods['price'], 2) . "</td>";
                echo "<td>¥" . number_format($goods['market_price'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_stock']) . "</td>";
                echo "<td>" . htmlspecialchars($create_time) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>暂无商品数据</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 获取商品列表失败: " . htmlspecialchars($result1['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例2: 只获取指定字段
echo "<h2>示例2: 只获取指定字段（商品ID、名称、价格）</h2>\n";
try {
    $condition2 = [
        ['site_id', '=', 1],
        ['goods_state', '=', 1],
        ['is_delete', '=', 0]
    ];
    $field2 = 'goods_id,goods_name,price';
    $result2 = $xiaoxia->getGoodsList($condition2, $field2, 'goods_id desc', 5);

    if ($result2['code'] == 0) {
        echo "<p class='success'>✓ 成功获取指定字段，共 " . count($result2['data']) . " 条记录</p>\n";

        if (!empty($result2['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th></tr>\n";

            foreach ($result2['data'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . number_format($goods['price'], 2) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } else {
        echo "<p class='error'>✗ 获取数据失败: " . htmlspecialchars($result2['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例3: 筛选条件查询（价格范围）
echo "<h2>示例3: 筛选条件查询（价格在50-200之间的商品）</h2>\n";
try {
    $condition3 = [
        ['site_id', '=', 1],
        ['goods_state', '=', 1],
        ['is_delete', '=', 0],
        ['price', '>=', 50],
        ['price', '<=', 200]
    ];
    $field3 = 'goods_id,goods_name,price,goods_stock';
    $result3 = $xiaoxia->getGoodsList($condition3, $field3, 'price asc', 8);

    if ($result3['code'] == 0) {
        echo "<p class='success'>✓ 筛选查询完成，共找到 " . count($result3['data']) . " 条记录</p>\n";

        if (!empty($result3['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>库存</th></tr>\n";

            foreach ($result3['data'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . number_format($goods['price'], 2) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_stock']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>未找到符合条件的商品</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 筛选查询失败: " . htmlspecialchars($result3['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>使用说明</h2>\n";
echo "<div style='background-color: #f9f9f9; padding: 15px; border-left: 4px solid #2196F3;'>\n";
echo "<h3>方法说明：</h3>\n";
echo "<ul>\n";
echo "<li><strong>getGoodsList(\$condition, \$field, \$order, \$limit)</strong></li>\n";
echo "<li>\$condition - 查询条件数组，如：[['site_id', '=', 1], ['price', '>', 100]]</li>\n";
echo "<li>\$field - 查询字段，如：'goods_id,goods_name,price' 或 '*'</li>\n";
echo "<li>\$order - 排序方式，如：'create_time desc' 或 'price asc'</li>\n";
echo "<li>\$limit - 限制数量，如：10</li>\n";
echo "</ul>\n";
echo "<h3>返回数据格式：</h3>\n";
echo "<pre>{\n";
echo "  'code': 0,           // 0表示成功，负数表示失败\n";
echo "  'message': '成功',    // 返回消息\n";
echo "  'data': []           // 返回的商品数据数组\n";
echo "}</pre>\n";
echo "</div>\n";

echo "<p style='margin-top: 30px; color: #666; font-size: 12px;'>示例文件路径: " . __FILE__ . "</p>\n";
?>
