<?php
/**
 * 小夏商品管理类使用示例
 * 
 * 这个文件演示了如何使用 app\model\xiaoxia\Main 类来获取商品列表
 * 
 * 使用方法：
 * 1. 确保项目已正确配置数据库连接
 * 2. 在浏览器中访问此文件或在命令行中运行
 * 3. 根据需要修改示例中的参数
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 启动应用
$app = new think\App();
$app->initialize();

use app\model\xiaoxia\Main;

// 创建小夏商品管理实例
$xiaoxia = new Main();

echo "<h1>小夏商品管理类使用示例</h1>\n";
echo "<style>body{font-family: Arial, sans-serif; margin: 20px;} table{border-collapse: collapse; width: 100%;} th,td{border: 1px solid #ddd; padding: 8px; text-align: left;} th{background-color: #f2f2f2;} .success{color: green;} .error{color: red;}</style>\n";

// 示例1: 获取指定站点的商品列表
echo "<h2>示例1: 获取站点ID为1的商品列表（前10条）</h2>\n";
try {
    $result1 = $xiaoxia->getGoodsListBySite(1, 1, 10);
    
    if ($result1['code'] == 0) {
        echo "<p class='success'>✓ 成功获取商品列表，共 " . count($result1['data']) . " 条记录</p>\n";
        
        if (!empty($result1['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>市场价</th><th>库存</th><th>状态</th><th>创建时间</th></tr>\n";
            
            foreach ($result1['data'] as $goods) {
                $status = $goods['goods_state'] == 1 ? '销售中' : '仓库中';
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['price']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['market_price']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_stock']) . "</td>";
                echo "<td>" . htmlspecialchars($status) . "</td>";
                echo "<td>" . htmlspecialchars($goods['create_time_format'] ?? '未知') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>暂无商品数据</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 获取商品列表失败: " . htmlspecialchars($result1['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例2: 获取分页商品列表
echo "<h2>示例2: 获取分页商品列表（第1页，每页5条）</h2>\n";
try {
    $condition2 = [
        ['site_id', '=', 1],
        ['goods_state', '=', 1],
        ['is_delete', '=', 0]
    ];
    
    $result2 = $xiaoxia->getGoodsPageList($condition2, 1, 5, 'create_time desc', 'goods_id,goods_name,price,market_price,goods_stock,create_time');
    
    if ($result2['code'] == 0) {
        $data = $result2['data'];
        echo "<p class='success'>✓ 成功获取分页数据</p>\n";
        echo "<p>总记录数: " . $data['count'] . " | 总页数: " . $data['page_count'] . " | 当前页: 1</p>\n";
        
        if (!empty($data['list'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>市场价</th><th>库存</th><th>创建时间</th></tr>\n";
            
            foreach ($data['list'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['price']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['market_price']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_stock']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['create_time_format'] ?? '未知') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } else {
        echo "<p class='error'>✗ 获取分页数据失败: " . htmlspecialchars($result2['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例3: 根据分类获取商品
echo "<h2>示例3: 根据分类获取商品（分类ID包含1的商品）</h2>\n";
try {
    $result3 = $xiaoxia->getGoodsListByCategory(1, ',1,', 1, 5);
    
    if ($result3['code'] == 0) {
        echo "<p class='success'>✓ 成功获取分类商品，共 " . count($result3['data']) . " 条记录</p>\n";
        
        if (!empty($result3['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>分类ID</th></tr>\n";
            
            foreach ($result3['data'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['price']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['category_id'] ?? '') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>该分类下暂无商品</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 获取分类商品失败: " . htmlspecialchars($result3['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例4: 搜索商品
echo "<h2>示例4: 搜索商品（关键词：手机）</h2>\n";
try {
    $result4 = $xiaoxia->searchGoods(1, '手机', 1, 5);
    
    if ($result4['code'] == 0) {
        echo "<p class='success'>✓ 搜索完成，共找到 " . count($result4['data']) . " 条记录</p>\n";
        
        if (!empty($result4['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>销量</th><th>关键词</th></tr>\n";
            
            foreach ($result4['data'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['price']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['sale_num'] + $goods['virtual_sale']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['keywords'] ?? '') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>未找到相关商品</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 搜索失败: " . htmlspecialchars($result4['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// 示例5: 自定义条件查询
echo "<h2>示例5: 自定义条件查询（价格大于100的商品）</h2>\n";
try {
    $condition5 = [
        ['site_id', '=', 1],
        ['goods_state', '=', 1],
        ['is_delete', '=', 0],
        ['price', '>', 100]
    ];
    
    $result5 = $xiaoxia->getGoodsList($condition5, 'goods_id,goods_name,price,market_price,goods_stock', 'price desc', 5);
    
    if ($result5['code'] == 0) {
        echo "<p class='success'>✓ 查询完成，共找到 " . count($result5['data']) . " 条记录</p>\n";
        
        if (!empty($result5['data'])) {
            echo "<table>\n";
            echo "<tr><th>商品ID</th><th>商品名称</th><th>价格</th><th>市场价</th><th>库存</th></tr>\n";
            
            foreach ($result5['data'] as $goods) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($goods['goods_id']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_name']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['price']) . "</td>";
                echo "<td>¥" . htmlspecialchars($goods['market_price']) . "</td>";
                echo "<td>" . htmlspecialchars($goods['goods_stock']) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>未找到符合条件的商品</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 查询失败: " . htmlspecialchars($result5['message']) . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>使用说明</h2>\n";
echo "<div style='background-color: #f9f9f9; padding: 15px; border-left: 4px solid #2196F3;'>\n";
echo "<h3>主要方法说明：</h3>\n";
echo "<ul>\n";
echo "<li><strong>getGoodsList()</strong> - 获取商品列表，支持自定义查询条件</li>\n";
echo "<li><strong>getGoodsPageList()</strong> - 获取分页商品列表</li>\n";
echo "<li><strong>getGoodsListBySite()</strong> - 根据站点ID获取商品列表</li>\n";
echo "<li><strong>getGoodsListByCategory()</strong> - 根据分类获取商品列表</li>\n";
echo "<li><strong>searchGoods()</strong> - 搜索商品</li>\n";
echo "</ul>\n";
echo "<h3>返回数据格式：</h3>\n";
echo "<p>所有方法都返回统一格式：</p>\n";
echo "<pre>{\n";
echo "  'code': 0,           // 0表示成功，负数表示失败\n";
echo "  'message': '成功',    // 返回消息\n";
echo "  'data': []           // 返回的数据\n";
echo "}</pre>\n";
echo "</div>\n";

echo "<p style='margin-top: 30px; color: #666; font-size: 12px;'>示例文件路径: " . __FILE__ . "</p>\n";
?>
