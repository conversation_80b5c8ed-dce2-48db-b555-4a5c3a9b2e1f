<?php
require_once __DIR__ . '/vendor/autoload.php';

$app = new think\App();
$app->initialize();

use app\model\xiaoxia\Main;

$xiaoxia = new Main();

/*// 示例1: 获取商品列表
$condition = [
    ['site_id', '=', 1],
    ['goods_state', '=', 1],
    ['is_delete', '=', 0]
];
$field = 'goods_id,goods_name,price';
$result = $xiaoxia->getGoodsList($condition, $field, 'goods_id desc', 5);

var_dump($result);

// 示例2: 筛选条件
$condition2 = [
    ['site_id', '=', 1],
    ['price', '>', 100]
];
$result2 = $xiaoxia->getGoodsList($condition2, '*', 'price asc', 3);

var_dump($result2);

// 示例3: 修改商品
$update_condition = [
    ['goods_id', '=', 1],
    ['site_id', '=', 1]
];
$update_data = [
    'goods_name' => '新的商品名称',
    'price' => 199.99,
    'goods_state' => 1
];
$result3 = $xiaoxia->updateGoods($update_condition, $update_data);

var_dump($result3);*/

// 示例4: 批量修改排序
//1275929
//1275962
//1283485
//1806744
//1865455
$site_id = 1;
$sortListFile = __DIR__ .'/sort.json';
$sort_data = file_get_contents($sortListFile);
$sort_data = json_decode($sort_data, true);
$result4 = $xiaoxia->batchUpdateSort($site_id, $sort_data);
var_dump($result4);
?>
