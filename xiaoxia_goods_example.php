<?php
require_once __DIR__ . '/vendor/autoload.php';

$app = new think\App();
$app->initialize();

use app\model\xiaoxia\Main;

$xiaoxia = new Main();

/*// 示例1: 获取商品列表
$condition = [
    ['site_id', '=', 1],
    ['goods_state', '=', 1],
    ['is_delete', '=', 0]
];
$field = 'goods_id,goods_name,price';
$result = $xiaoxia->getGoodsList($condition, $field, 'goods_id desc', 5);

var_dump($result);

// 示例2: 筛选条件
$condition2 = [
    ['site_id', '=', 1],
    ['price', '>', 100]
];
$result2 = $xiaoxia->getGoodsList($condition2, '*', 'price asc', 3);

var_dump($result2);

// 示例3: 修改商品
$update_condition = [
    ['goods_id', '=', 1],
    ['site_id', '=', 1]
];
$update_data = [
    'goods_name' => '新的商品名称',
    'price' => 199.99,
    'goods_state' => 1
];
$result3 = $xiaoxia->updateGoods($update_condition, $update_data);

var_dump($result3);*/

// 示例4: 批量修改排序
/*$site_id = 1;
$sort_data = file_get_contents(__DIR__ .'/sort_data.json');
$sort_data = json_decode($sort_data, true);
$result4 = $xiaoxia->batchUpdateSort($site_id, $sort_data);
var_dump($result4);*/

// 示例5: 添加商品评价
$commentImageUrls = ["https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/d05ec05216b1411a9fd678a29c6f915b-1749275933319-1749275230.png?imageMogr2/thumbnail/1125x","https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/357abb9f28374d6381789214a4d53ed2-1749275933320-1749275230.png?imageMogr2/thumbnail/1125x","https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/92cbf01223bf43c6aaa3b383d327709c-1749275933320-1749275231.png?imageMogr2/thumbnail/1125x","https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/3cacd2bc74084c9484411561e2a194bb-1749275933320-1749275231.png?imageMogr2/thumbnail/1125x","https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/6ea321c0ab8e4ea190cd2404c4600b37-1749275933320-1749275231.png?imageMogr2/thumbnail/1125x","https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/269848976/material/1/91fa698173c8457693a5a308e3418f3f-1749275933319-1749275231.png?imageMogr2/thumbnail/1125x"];
$evaluate_data = [
    'goods_id' => 1749805324,
    'site_id' => 1,
    'sku_id' => 1,
    'member_id' => 1,
    'member_name' => '测试用户',
    'content' => '商品质量很好，非常满意！',
    'scores' => 5,
    'explain_type' => 1, // 1:好评 2:中评 3:差评
    'is_audit' => 1
];
$result5 = $xiaoxia->addGoodsEvaluate($evaluate_data);
var_dump($result5);
?>
